// Simple script to fetch and display GA4 campaign data
const https = require('https');
const { GA4Service } = require('./server/ga4-service');

// Load environment variables
require('dotenv').config();

async function fetchAllCampaigns() {
  try {
    console.log('🔍 Fetching all GA4 campaign data...\n');
    
    // Initialize GA4 service
    const ga4Service = new GA4Service({
      propertyId: process.env.GA4_PROPERTY_ID,
      serviceAccountKey: JSON.parse(process.env.GA4_SERVICE_ACCOUNT_KEY)
    });
    
    // Fetch campaign data for last 30 days
    const campaigns = await ga4Service.getCampaignData('30daysAgo', 'today');
    
    console.log(`📊 Found ${campaigns.length} active campaigns:\n`);
    
    // Display all campaigns with detailed information
    campaigns.forEach((campaign, index) => {
      console.log(`${index + 1}. ${campaign.campaignName}`);
      console.log(`   Source: ${campaign.source}`);
      console.log(`   Medium: ${campaign.medium}`);
      console.log(`   Sessions: ${campaign.sessions.toLocaleString()}`);
      console.log(`   Users: ${campaign.users.toLocaleString()}`);
      console.log(`   Conversions: ${campaign.conversions}`);
      console.log(`   Conversion Rate: ${campaign.sessions > 0 ? ((campaign.conversions / campaign.sessions) * 100).toFixed(2) : '0.00'}%`);
      console.log('   ─────────────────────────────────────');
    });
    
    // Summary statistics
    const totalSessions = campaigns.reduce((sum, c) => sum + c.sessions, 0);
    const totalUsers = campaigns.reduce((sum, c) => sum + c.users, 0);
    const totalConversions = campaigns.reduce((sum, c) => sum + c.conversions, 0);
    const avgConversionRate = totalSessions > 0 ? (totalConversions / totalSessions * 100) : 0;
    
    console.log('\n📈 CAMPAIGN SUMMARY:');
    console.log(`Total Active Campaigns: ${campaigns.length}`);
    console.log(`Total Sessions: ${totalSessions.toLocaleString()}`);
    console.log(`Total Users: ${totalUsers.toLocaleString()}`);
    console.log(`Total Conversions: ${totalConversions}`);
    console.log(`Average Conversion Rate: ${avgConversionRate.toFixed(2)}%`);
    
    // Top performing campaign
    if (campaigns.length > 0) {
      const topCampaign = campaigns[0];
      console.log(`\n🏆 TOP PERFORMER: ${topCampaign.campaignName}`);
      console.log(`   ${topCampaign.sessions.toLocaleString()} sessions, ${topCampaign.conversions} conversions`);
    }
    
    return campaigns;
    
  } catch (error) {
    console.error('❌ Error fetching campaign data:', error.message);
    return [];
  }
}

// Run the script
fetchAllCampaigns();