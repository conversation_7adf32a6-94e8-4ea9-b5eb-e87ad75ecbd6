<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Facebook Conversion Funnel Test</title>
    
    <!-- Facebook Pixel Code -->
    <script>
    !function(f,b,e,v,n,t,s)
    {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
    n.callMethod.apply(n,arguments):n.queue.push(arguments)};
    if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
    n.queue=[];t=b.createElement(e);t.async=!0;
    t.src=v;s=b.getElementsByTagName(e)[0];
    s.parentNode.insertBefore(t,s)}(window, document,'script',
    'https://connect.facebook.net/en_US/fbevents.js');
    
    fbq('init', '2478007082599449');
    fbq('track', 'PageView');
    </script>
    <noscript><img height="1" width="1" style="display:none"
    src="https://www.facebook.com/tr?id=2478007082599449&ev=PageView&noscript=1"
    /></noscript>
    <!-- End Facebook Pixel Code -->
    
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #B199E8 0%, #8B6FD1 100%);
            color: white;
        }
        .step {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        button {
            background: #C0A4F9;
            color: #000;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: bold;
            cursor: pointer;
            margin: 10px 10px 10px 0;
            font-size: 14px;
        }
        button:hover {
            background: #AD8FF7;
        }
        .log {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        .success {
            color: #4CAF50;
        }
        .info {
            color: #2196F3;
        }
        h1, h2 {
            color: white;
        }
        .event-count {
            background: rgba(255, 255, 255, 0.2);
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🎯 Facebook Conversion Funnel Testing</h1>
    <p>This page tests all Facebook pixel events used in the perfume trial booking system.</p>
    
    <div class="event-count">
        <strong>Events Fired: <span id="eventCount">0</span></strong>
    </div>
    
    <div class="log" id="eventLog">
        <div class="info">📋 Event log will appear here...</div>
    </div>
    
    <div class="step">
        <h2>1. Landing Page Events</h2>
        <p>Events fired when customer visits the booking page</p>
        <button onclick="testPageView()">Test PageView</button>
    </div>
    
    <div class="step">
        <h2>2. Postal Code Check</h2>
        <p>Events fired when customer enters postal code and checks availability</p>
        <button onclick="testCheckAvailability()">Test Check Availability</button>
        <button onclick="testViewContent()">Test ViewContent</button>
    </div>
    
    <div class="step">
        <h2>3. Date Selection</h2>
        <p>Events fired when customer selects appointment date</p>
        <button onclick="testDateSelection()">Test Date Selection</button>
        <button onclick="testInitiateCheckout()">Test InitiateCheckout</button>
        <button onclick="testAddToCart()">Test AddToCart</button>
    </div>
    
    <div class="step">
        <h2>4. Time Selection</h2>
        <p>Events fired when customer selects appointment time</p>
        <button onclick="testTimeSelection()">Test Time Selection</button>
        <button onclick="testAddPaymentInfo()">Test AddPaymentInfo</button>
    </div>
    
    <div class="step">
        <h2>5. OTP Verification</h2>
        <p>Events fired during phone verification process</p>
        <button onclick="testOTPRequested()">Test OTP Requested</button>
        <button onclick="testCompleteRegistration()">Test CompleteRegistration</button>
        <button onclick="testBookMyTrial()">Test Book My Trial</button>
        <button onclick="testBeginCheckout()">Test BeginCheckout</button>
    </div>
    
    <div class="step">
        <h2>6. Booking Confirmation</h2>
        <p>Events fired when booking is successfully completed</p>
        <button onclick="testBookingConfirmed()">Test Booking Confirmed</button>
        <button onclick="testPurchase()">Test Purchase Event</button>
    </div>
    
    <div class="step">
        <h2>7. Test Complete Funnel</h2>
        <p>Fire all events in sequence to simulate complete customer journey</p>
        <button onclick="testCompleteFunnel()">🚀 Test Complete Funnel</button>
        <button onclick="clearLog()">Clear Log</button>
    </div>

    <script>
        let eventCounter = 0;
        
        function logEvent(eventName, params = {}) {
            eventCounter++;
            document.getElementById('eventCount').textContent = eventCounter;
            
            const log = document.getElementById('eventLog');
            const timestamp = new Date().toLocaleTimeString();
            const paramStr = Object.keys(params).length > 0 ? JSON.stringify(params, null, 2) : 'No parameters';
            
            log.innerHTML += `
                <div class="success">
                    [${timestamp}] ✅ ${eventName}
                    <br><small style="margin-left: 20px; color: #ccc;">${paramStr}</small>
                </div>
            `;
            log.scrollTop = log.scrollHeight;
        }
        
        function testPageView() {
            fbq('track', 'PageView', {
                content_name: 'Perfume Trial Booking Landing Page'
            });
            logEvent('PageView', { content_name: 'Perfume Trial Booking Landing Page' });
        }
        
        function testCheckAvailability() {
            fbq('trackCustom', 'CheckAvailabilityPressed', {
                content_name: 'Check Availability Button Pressed',
                postal_code: '110001',
                city: 'New Delhi',
                state: 'Delhi'
            });
            logEvent('CheckAvailabilityPressed (Custom)', { postal_code: '110001', city: 'New Delhi' });
        }
        
        function testViewContent() {
            fbq('track', 'ViewContent', {
                content_name: 'Postal Code Availability Check',
                content_type: 'service',
                content_ids: ['110001'],
                postal_code: '110001',
                city: 'New Delhi',
                state: 'Delhi'
            });
            logEvent('ViewContent', { content_type: 'service', postal_code: '110001' });
        }
        
        function testDateSelection() {
            fbq('trackCustom', 'DateSelected', {
                content_name: 'Date Selected',
                appointment_date: '2025-07-05',
                postal_code: '110001'
            });
            logEvent('DateSelected (Custom)', { appointment_date: '2025-07-05' });
        }
        
        function testInitiateCheckout() {
            fbq('track', 'InitiateCheckout', {
                content_name: 'Perfume Trial Booking Started',
                content_type: 'appointment',
                appointment_date: '2025-07-05',
                postal_code: '110001',
                value: 0,
                currency: 'INR'
            });
            logEvent('InitiateCheckout', { content_type: 'appointment', value: 0, currency: 'INR' });
        }
        
        function testAddToCart() {
            fbq('track', 'AddToCart', {
                content_name: 'Date Selected for Appointment',
                content_type: 'appointment',
                content_ids: ['2025-07-05'],
                appointment_date: '2025-07-05',
                postal_code: '110001'
            });
            logEvent('AddToCart', { content_type: 'appointment', content_ids: ['2025-07-05'] });
        }
        
        function testTimeSelection() {
            fbq('trackCustom', 'TimeSlotSelected', {
                content_name: 'Time Slot Selected',
                appointment_time: '2:00 PM',
                appointment_date: '2025-07-05',
                postal_code: '110001'
            });
            logEvent('TimeSlotSelected (Custom)', { appointment_time: '2:00 PM' });
        }
        
        function testAddPaymentInfo() {
            fbq('track', 'AddPaymentInfo', {
                content_name: 'Time Slot Selected for Appointment',
                content_type: 'appointment',
                appointment_time: '2:00 PM',
                appointment_date: '2025-07-05',
                postal_code: '110001'
            });
            logEvent('AddPaymentInfo', { content_type: 'appointment', appointment_time: '2:00 PM' });
        }
        
        function testOTPRequested() {
            fbq('trackCustom', 'OTPRequested', {
                content_name: 'OTP Requested',
                phone_number: '+919876543210'
            });
            logEvent('OTPRequested (Custom)', { phone_number: '+919876543210' });
        }
        
        function testCompleteRegistration() {
            fbq('track', 'CompleteRegistration', {
                content_name: 'Customer Registration Started',
                registration_method: 'whatsapp_otp',
                phone_number: '+919876543210'
            });
            logEvent('CompleteRegistration', { registration_method: 'whatsapp_otp' });
        }
        
        function testBookMyTrial() {
            fbq('trackCustom', 'BookMyTrialPressed', {
                content_name: 'Book My Trial Button Clicked',
                phone_number: '+919876543210'
            });
            logEvent('BookMyTrialPressed (Custom)', { phone_number: '+919876543210' });
        }
        
        function testBeginCheckout() {
            fbq('track', 'BeginCheckout', {
                content_name: 'Begin Booking Checkout',
                content_type: 'appointment',
                phone_number: '+919876543210'
            });
            logEvent('BeginCheckout', { content_type: 'appointment' });
        }
        
        function testBookingConfirmed() {
            fbq('trackCustom', 'BookingConfirmed', {
                content_name: 'Booking Confirmed',
                booking_id: 'TEST123',
                postal_code: '110001',
                city: 'New Delhi',
                state: 'Delhi',
                appointment_date: '2025-07-05',
                appointment_time: '2:00 PM',
                sales_rep: 'Test Rep',
                phone_number: '+919876543210'
            });
            logEvent('BookingConfirmed (Custom)', { booking_id: 'TEST123', appointment_date: '2025-07-05' });
        }
        
        function testPurchase() {
            fbq('track', 'Purchase', {
                content_name: 'Perfume Trial Appointment Booked',
                content_type: 'appointment',
                content_ids: ['TEST123'],
                value: 0,
                currency: 'INR',
                booking_id: 'TEST123',
                postal_code: '110001',
                city: 'New Delhi',
                state: 'Delhi',
                appointment_date: '2025-07-05',
                appointment_time: '2:00 PM',
                sales_rep: 'Test Rep',
                phone_number: '+919876543210'
            });
            logEvent('Purchase', { value: 0, currency: 'INR', booking_id: 'TEST123' });
        }
        
        function testCompleteFunnel() {
            logEvent('🚀 STARTING COMPLETE FUNNEL TEST');
            
            setTimeout(() => testPageView(), 500);
            setTimeout(() => testCheckAvailability(), 1000);
            setTimeout(() => testViewContent(), 1500);
            setTimeout(() => testDateSelection(), 2000);
            setTimeout(() => testInitiateCheckout(), 2500);
            setTimeout(() => testAddToCart(), 3000);
            setTimeout(() => testTimeSelection(), 3500);
            setTimeout(() => testAddPaymentInfo(), 4000);
            setTimeout(() => testOTPRequested(), 4500);
            setTimeout(() => testCompleteRegistration(), 5000);
            setTimeout(() => testBookMyTrial(), 5500);
            setTimeout(() => testBeginCheckout(), 6000);
            setTimeout(() => testBookingConfirmed(), 6500);
            setTimeout(() => testPurchase(), 7000);
            
            setTimeout(() => {
                logEvent('🎉 COMPLETE FUNNEL TEST FINISHED');
            }, 7500);
        }
        
        function clearLog() {
            document.getElementById('eventLog').innerHTML = '<div class="info">📋 Event log cleared...</div>';
            eventCounter = 0;
            document.getElementById('eventCount').textContent = '0';
        }
        
        // Log initial page view
        logEvent('🎯 TESTING PAGE LOADED');
    </script>
</body>
</html>