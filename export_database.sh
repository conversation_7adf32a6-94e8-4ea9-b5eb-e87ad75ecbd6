#!/bin/bash

# Database Export Script for KultBookings
# Run this in Replit Shell

echo "Starting database export..."

# Create export directory
mkdir -p exports/$(date +%Y%m%d_%H%M%S)
EXPORT_DIR="exports/$(date +%Y%m%d_%H%M%S)"

# Export schema
echo "Exporting schema..."
pg_dump $DATABASE_URL --schema-only --no-owner --no-privileges > "$EXPORT_DIR/schema.sql"

# Export data for each table
echo "Exporting table data..."

# Core tables
psql $DATABASE_URL -c "\COPY users TO '$EXPORT_DIR/users.csv' WITH CSV HEADER;"
psql $DATABASE_URL -c "\COPY bookings TO '$EXPORT_DIR/bookings.csv' WITH CSV HEADER;"
psql $DATABASE_URL -c "\COPY sales_reps TO '$EXPORT_DIR/sales_reps.csv' WITH CSV HEADER;"
psql $DATABASE_URL -c "\COPY postal_codes TO '$EXPORT_DIR/postal_codes.csv' WITH CSV HEADER;"
psql $DATABASE_URL -c "\COPY settings TO '$EXPORT_DIR/settings.csv' WITH CSV HEADER;"

# Analytics tables
psql $DATABASE_URL -c "\COPY user_tracking TO '$EXPORT_DIR/user_tracking.csv' WITH CSV HEADER;"
psql $DATABASE_URL -c "\COPY long_term_user_sessions TO '$EXPORT_DIR/long_term_user_sessions.csv' WITH CSV HEADER;"
psql $DATABASE_URL -c "\COPY event_logs TO '$EXPORT_DIR/event_logs.csv' WITH CSV HEADER;"

# Support tables
psql $DATABASE_URL -c "\COPY whatsapp_opt_ins TO '$EXPORT_DIR/whatsapp_opt_ins.csv' WITH CSV HEADER;"
psql $DATABASE_URL -c "\COPY customer_interactions TO '$EXPORT_DIR/customer_interactions.csv' WITH CSV HEADER;"
psql $DATABASE_URL -c "\COPY support_tickets TO '$EXPORT_DIR/support_tickets.csv' WITH CSV HEADER;"

# Session tables
psql $DATABASE_URL -c "\COPY admin_sessions TO '$EXPORT_DIR/admin_sessions.csv' WITH CSV HEADER;"
psql $DATABASE_URL -c "\COPY session_store TO '$EXPORT_DIR/session_store.csv' WITH CSV HEADER;"

# Create a complete SQL dump
echo "Creating complete SQL dump..."
pg_dump $DATABASE_URL --no-owner --no-privileges --clean --if-exists > "$EXPORT_DIR/complete_database.sql"

# Create a compressed archive
echo "Creating compressed archive..."
tar -czf "$EXPORT_DIR.tar.gz" -C exports $(basename $EXPORT_DIR)

echo "Export completed!"
echo "Files created:"
echo "- Schema: $EXPORT_DIR/schema.sql"
echo "- Complete dump: $EXPORT_DIR/complete_database.sql"
echo "- CSV files: $EXPORT_DIR/*.csv"
echo "- Compressed: $EXPORT_DIR.tar.gz"

# Show export summary
echo ""
echo "Export Summary:"
psql $DATABASE_URL -c "
SELECT 
    schemaname,
    tablename,
    n_tup_ins as inserts,
    n_tup_upd as updates,
    n_tup_del as deletes,
    n_live_tup as live_rows,
    n_dead_tup as dead_rows
FROM pg_stat_user_tables 
ORDER BY n_live_tup DESC;
"
