import axios from 'axios';

interface FacebookConfig {
  accessToken: string;
  pixelId: string;
}

interface FacebookUserData {
  ph?: string; // hashed phone number
  em?: string; // hashed email
  fn?: string; // hashed first name
  ln?: string; // hashed last name
  ct?: string; // city
  st?: string; // state
  zp?: string; // postal code
  country?: string;
  external_id?: string;
}

interface FacebookCustomData {
  value?: number;
  currency?: string;
  content_name?: string;
  content_category?: string;
  content_ids?: string[];
  content_type?: string;
  postal_code?: string;
  appointment_date?: string;
  appointment_time?: string;
  sales_rep?: string;
  booking_id?: string;
  [key: string]: any;
}

interface FacebookEvent {
  event_name: string;
  event_time: number;
  event_source_url: string;
  action_source: 'website' | 'app' | 'system_generated';
  user_data: FacebookUserData;
  custom_data?: FacebookCustomData;
  event_id?: string;
}

export class FacebookService {
  private accessToken: string;
  private pixelId: string;
  private baseUrl = 'https://graph.facebook.com/v18.0';

  constructor(config: FacebookConfig) {
    this.accessToken = config.accessToken;
    this.pixelId = config.pixelId;
  }

  private async sendEvent(events: FacebookEvent[]) {
    try {
      const response = await axios.post(
        `${this.baseUrl}/${this.pixelId}/events`,
        {
          data: events,
          test_event_code: process.env.NODE_ENV === 'development' ? 'TEST12345' : undefined
        },
        {
          params: {
            access_token: this.accessToken
          },
          headers: {
            'Content-Type': 'application/json'
          }
        }
      );

      console.log('Facebook event sent successfully:', response.data);
      return response.data;
    } catch (error: any) {
      console.error('Facebook event error:', error.response?.data || error.message);
      throw error;
    }
  }

  async trackPageView(userData: FacebookUserData, customData: FacebookCustomData = {}) {
    const event: FacebookEvent = {
      event_name: 'PageView',
      event_time: Math.floor(Date.now() / 1000),
      event_source_url: customData.page_url || 'https://perfumestrial.kult.app',
      action_source: 'website',
      user_data: userData,
      custom_data: {
        content_name: 'Kult Perfume Trial',
        content_category: 'Beauty',
        ...customData
      },
      event_id: `pageview_${Date.now()}_${Math.random()}`
    };

    return this.sendEvent([event]);
  }

  async trackViewContent(userData: FacebookUserData, customData: FacebookCustomData = {}) {
    const event: FacebookEvent = {
      event_name: 'ViewContent',
      event_time: Math.floor(Date.now() / 1000),
      event_source_url: 'https://perfumestrial.kult.app',
      action_source: 'website',
      user_data: userData,
      custom_data: {
        content_type: 'product',
        content_name: 'Perfume Trial Service Area',
        content_category: 'Beauty',
        ...customData
      },
      event_id: `viewcontent_${Date.now()}_${Math.random()}`
    };

    return this.sendEvent([event]);
  }

  async trackInitiateCheckout(userData: FacebookUserData, customData: FacebookCustomData = {}) {
    const event: FacebookEvent = {
      event_name: 'InitiateCheckout',
      event_time: Math.floor(Date.now() / 1000),
      event_source_url: 'https://perfumestrial.kult.app/book',
      action_source: 'website',
      user_data: userData,
      custom_data: {
        content_type: 'product',
        content_name: 'Perfume Trial Booking',
        content_category: 'Beauty',
        value: 0,
        currency: 'INR',
        ...customData
      },
      event_id: `initiatecheckout_${Date.now()}_${Math.random()}`
    };

    return this.sendEvent([event]);
  }

  async trackAddPaymentInfo(userData: FacebookUserData, customData: FacebookCustomData = {}) {
    const event: FacebookEvent = {
      event_name: 'AddPaymentInfo',
      event_time: Math.floor(Date.now() / 1000),
      event_source_url: 'https://perfumestrial.kult.app/book',
      action_source: 'website',
      user_data: userData,
      custom_data: {
        content_type: 'product',
        content_name: 'Contact Details Entry',
        content_category: 'Beauty',
        ...customData
      },
      event_id: `addpaymentinfo_${Date.now()}_${Math.random()}`
    };

    return this.sendEvent([event]);
  }

  async trackCompleteRegistration(userData: FacebookUserData, customData: FacebookCustomData = {}) {
    const event: FacebookEvent = {
      event_name: 'CompleteRegistration',
      event_time: Math.floor(Date.now() / 1000),
      event_source_url: 'https://perfumestrial.kult.app/book',
      action_source: 'website',
      user_data: userData,
      custom_data: {
        content_type: 'product',
        content_name: 'OTP Verification Complete',
        content_category: 'Beauty',
        registration_method: 'whatsapp_otp',
        ...customData
      },
      event_id: `completeregistration_${Date.now()}_${Math.random()}`
    };

    return this.sendEvent([event]);
  }

  async trackPurchase(userData: FacebookUserData, customData: FacebookCustomData = {}) {
    const event: FacebookEvent = {
      event_name: 'Purchase',
      event_time: Math.floor(Date.now() / 1000),
      event_source_url: 'https://perfumestrial.kult.app/booking/confirmation',
      action_source: 'website',
      user_data: userData,
      custom_data: {
        content_type: 'product',
        content_name: 'Perfume Trial Booking Completed',
        content_category: 'Beauty',
        content_ids: ['perfume_trial'],
        value: 0,
        currency: 'INR',
        ...customData
      },
      event_id: `purchase_${customData.booking_id || Date.now()}_${Math.random()}`
    };

    return this.sendEvent([event]);
  }

  // Custom events for specific business actions
  async trackCustomEvent(eventName: string, userData: FacebookUserData, customData: FacebookCustomData = {}) {
    const event: FacebookEvent = {
      event_name: eventName,
      event_time: Math.floor(Date.now() / 1000),
      event_source_url: customData.event_source_url || 'https://perfumestrial.kult.app',
      action_source: 'website',
      user_data: userData,
      custom_data: {
        content_category: 'Beauty',
        ...customData
      },
      event_id: `${eventName.toLowerCase()}_${Date.now()}_${Math.random()}`
    };

    return this.sendEvent([event]);
  }

  // Batch event sending for performance
  async trackMultipleEvents(events: Array<{
    eventName: string;
    userData: FacebookUserData;
    customData: FacebookCustomData;
  }>) {
    const facebookEvents: FacebookEvent[] = events.map((event, index) => ({
      event_name: event.eventName,
      event_time: Math.floor(Date.now() / 1000),
      event_source_url: event.customData.event_source_url || 'https://perfumestrial.kult.app',
      action_source: 'website',
      user_data: event.userData,
      custom_data: {
        content_category: 'Beauty',
        ...event.customData
      },
      event_id: `batch_${event.eventName.toLowerCase()}_${Date.now()}_${index}`
    }));

    return this.sendEvent(facebookEvents);
  }

  // Test event functionality
  async testPixelFiring() {
    const testEvent: FacebookEvent = {
      event_name: 'PageView',
      event_time: Math.floor(Date.now() / 1000),
      event_source_url: 'https://perfumestrial.kult.app',
      action_source: 'website',
      user_data: {
        ph: this.hashData('+919999999999'),
        ct: 'Delhi',
        st: 'Delhi',
        country: 'IN'
      },
      custom_data: {
        content_name: 'Test Event',
        content_category: 'Beauty'
      },
      event_id: `test_${Date.now()}`
    };

    return this.sendEvent([testEvent]);
  }

  // Helper methods
  private hashData(data: string): string {
    // In production, use proper SHA-256 hashing
    // For now, simple hash function
    let hash = 0;
    for (let i = 0; i < data.length; i++) {
      const char = data.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash;
    }
    return Math.abs(hash).toString();
  }

  hashPhoneNumber(phone: string): string {
    // Remove all non-digits and add country code if needed
    const cleanPhone = phone.replace(/\D/g, '');
    const fullPhone = cleanPhone.startsWith('91') ? `+${cleanPhone}` : `+91${cleanPhone}`;
    return this.hashData(fullPhone);
  }

  hashEmail(email: string): string {
    return this.hashData(email.toLowerCase().trim());
  }

  // Campaign attribution tracking
  async trackCampaignAttribution(userData: FacebookUserData, campaignData: {
    utm_source?: string;
    utm_medium?: string;
    utm_campaign?: string;
    utm_content?: string;
    utm_term?: string;
    fbclid?: string;
  }) {
    const event: FacebookEvent = {
      event_name: 'Lead',
      event_time: Math.floor(Date.now() / 1000),
      event_source_url: 'https://perfumestrial.kult.app',
      action_source: 'website',
      user_data: userData,
      custom_data: {
        content_name: 'Campaign Attribution',
        content_category: 'Beauty',
        ...campaignData
      },
      event_id: `attribution_${Date.now()}_${Math.random()}`
    };

    return this.sendEvent([event]);
  }
}

export default FacebookService;