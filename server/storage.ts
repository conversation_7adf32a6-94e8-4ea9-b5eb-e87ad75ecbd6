import { 
  users, bookings, salesReps, postalCodes, settings, whatsappOptIns, eventLogs, blockedTimeSlots,
  customerInteractions, supportTickets, businessMetrics, adminSessions, bookingHistory,
  userTracking, longTermUserSessions,
  type User, type InsertUser, type Booking, type InsertBooking,
  type SalesRep, type InsertSalesRep, type PostalCode, type InsertPostalCode,
  type Settings, type InsertSettings, type WhatsappOptIn, type InsertWhatsappOptIn,
  type EventLog, type InsertEventLog, type UserSession, type InsertUserSession,
  type BlockedTimeSlot, type InsertBlockedTimeSlot, type BookingHistory, type InsertBookingHistory,
  type CustomerInteraction, type InsertCustomerInteraction,
  type SupportTicket, type InsertSupportTicket,
  type BusinessMetrics, type InsertBusinessMetrics,
  type UserTracking, type InsertUserTracking,
  type LongTermUserSession, type InsertLongTermUserSession
} from "@shared/schema";
import { db } from "./db";
import { eq, and, gte, lte, desc, asc, or, sql, count, ne } from "drizzle-orm";
import { randomBytes } from "crypto";

// Generate secure token for booking links
function generateSecureToken(): string {
  return randomBytes(24).toString('hex'); // 48-character secure token
}

export interface IStorage {
  // Users
  getUser(id: number): Promise<User | undefined>;
  getUserById(userId: number): Promise<any>;
  getUserByUsername(username: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  getUsers(): Promise<User[]>;
  updateUser(id: number, updates: Partial<User>): Promise<User | undefined>;
  updateUserPassword(id: number, hashedPassword: string): Promise<boolean>;
  deleteUser(id: number): Promise<boolean>;

  // Admin Sessions
  createAdminSession(userId: number, sessionId: string, ipAddress?: string, userAgent?: string): Promise<void>;
  getAdminSessions(userId: number): Promise<any[]>;
  getAllAdminSessions(): Promise<any[]>;
  endAdminSession(sessionId: string): Promise<void>;

  // Bookings
  createBooking(booking: InsertBooking): Promise<Booking>;
  getBooking(id: number): Promise<Booking | undefined>;
  getBookingByToken(token: string): Promise<Booking | undefined>;
  getBookings(): Promise<Booking[]>;
  getBookingsByDate(date: string): Promise<Booking[]>;
  getBookingsByDateRange(startDate: Date, endDate: Date): Promise<Booking[]>;
  getBookingsByStatus(status: string): Promise<Booking[]>;
  updateBooking(id: number, updates: Partial<Booking>): Promise<Booking | undefined>;
  deleteBooking(id: number): Promise<boolean>;
  getBookingsCount(date: Date): Promise<number>;

  // Sales Reps
  createSalesRep(rep: InsertSalesRep): Promise<SalesRep>;
  getSalesReps(): Promise<SalesRep[]>;
  getActiveSalesReps(): Promise<SalesRep[]>;
  getSalesRep(id: number): Promise<SalesRep | undefined>;
  updateSalesRep(id: number, updates: Partial<SalesRep>): Promise<SalesRep | undefined>;
  deleteSalesRep(id: number): Promise<boolean>;
  getAvailableReps(date: Date, timeSlot: string): Promise<SalesRep[]>;
  assignRepWithRoundRobin(date: Date, timeSlot: string): Promise<string | null>;

  // Postal Codes
  createPostalCode(postalCode: InsertPostalCode): Promise<PostalCode>;
  getPostalCodes(): Promise<PostalCode[]>;
  getActivePostalCodes(): Promise<PostalCode[]>;
  isPostalCodeValid(code: string): Promise<boolean>;
  getPostalCodeDetails(code: string): Promise<PostalCode | undefined>;
  updatePostalCode(id: number, updates: Partial<PostalCode>): Promise<PostalCode | undefined>;
  deletePostalCode(id: number): Promise<boolean>;

  // Settings
  getSettings(): Promise<Settings>;
  updateSettings(updates: Partial<Settings>): Promise<Settings>;

  // WhatsApp Opt-ins
  createWhatsappOptIn(optIn: InsertWhatsappOptIn): Promise<WhatsappOptIn>;
  getWhatsappOptIn(phoneNumber: string): Promise<WhatsappOptIn | undefined>;
  updateWhatsappOptIn(id: number, updates: Partial<WhatsappOptIn>): Promise<WhatsappOptIn | undefined>;
  isPhoneOptedIn(phoneNumber: string): Promise<boolean>;

  // Event tracking for marketing analytics
  createEventLog(event: InsertEventLog): Promise<EventLog>;
  getEventLogs(filters?: { dateFrom?: Date; dateTo?: Date; eventType?: string; sessionId?: string; }): Promise<EventLog[]>;
  getEventLogsBySession(sessionId: string): Promise<EventLog[]>;
  
  // User sessions for drop-off analysis
  createUserSession(session: InsertUserSession): Promise<UserSession>;
  getUserSession(sessionId: string): Promise<LongTermUserSession | undefined>;
  updateUserSession(sessionId: string, updates: Partial<LongTermUserSession>): Promise<LongTermUserSession | undefined>;
  getAnalyticsUserSessions(filters?: { dateFrom?: Date; dateTo?: Date; finalOutcome?: string; }): Promise<UserSession[]>;
  getDropOffAnalytics(dateFrom?: Date, dateTo?: Date): Promise<any>;
  getConversionFunnel(dateFrom?: Date, dateTo?: Date): Promise<any>;
  getPostalCodeAnalytics(dateFrom?: Date, dateTo?: Date): Promise<any>;

  // Time slot generation with global booking rules
  generateAvailableTimeSlots(date: Date, excludeBookingId?: number): Promise<string[]>;

  // Blocked time slots
  createBlockedTimeSlot(blockedSlot: InsertBlockedTimeSlot): Promise<BlockedTimeSlot>;
  getBlockedTimeSlots(startDate?: Date, endDate?: Date): Promise<BlockedTimeSlot[]>;
  deleteBlockedTimeSlot(id: number): Promise<boolean>;

  // Booking History
  createBookingHistoryEntry(historyEntry: InsertBookingHistory): Promise<BookingHistory>;
  getBookingHistoryByPhone(phoneNumber: string): Promise<BookingHistory[]>;
  getBookingHistoryById(bookingId: number): Promise<BookingHistory[]>;

  // Customer Support - Interactions
  createCustomerInteraction(interaction: InsertCustomerInteraction): Promise<CustomerInteraction>;
  getCustomerInteractions(bookingId?: number, customerPhone?: string): Promise<CustomerInteraction[]>;
  updateCustomerInteraction(id: number, updates: Partial<CustomerInteraction>): Promise<CustomerInteraction | undefined>;
  
  // Customer Support - Tickets
  createSupportTicket(ticket: InsertSupportTicket): Promise<SupportTicket>;
  getSupportTickets(filters?: { status?: string; assignedAgent?: string; priority?: string; }): Promise<SupportTicket[]>;
  getSupportTicket(ticketId: string): Promise<SupportTicket | undefined>;
  updateSupportTicket(ticketId: string, updates: Partial<SupportTicket>): Promise<SupportTicket | undefined>;
  
  // Business Metrics
  createBusinessMetrics(metrics: InsertBusinessMetrics): Promise<BusinessMetrics>;
  getBusinessMetrics(dateFrom?: Date, dateTo?: Date): Promise<BusinessMetrics[]>;
  getDailyBusinessSummary(date: Date): Promise<any>;
  
  // Enhanced Booking Support
  markAppointmentCompleted(bookingId: number, satisfaction?: number, notes?: string): Promise<boolean>;
  addTransaction(bookingId: number, amount: number, orderId: string, paymentMethod: string): Promise<boolean>;
  recordCommunication(bookingId: number, method: string, notes?: string): Promise<boolean>;

  // Detailed postal code analytics
  getDetailedInvalidPostalCodes(dateFrom?: Date, dateTo?: Date): Promise<any>;

  // Long-term user tracking
  createOrUpdateUserTracking(trackingData: any): Promise<UserTracking>;
  getUserTracking(userId: string): Promise<UserTracking | undefined>;
  createOrUpdateLongTermSession(sessionData: any): Promise<LongTermUserSession>;
  getLongTermUserSessions(userId: string): Promise<LongTermUserSession[]>;
}

export class DatabaseStorage implements IStorage {
  // Users
  async getUser(id: number): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.id, id));
    return user || undefined;
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.username, username));
    return user || undefined;
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const [user] = await db.insert(users).values(insertUser).returning();
    return user;
  }

  async getUsers(): Promise<User[]> {
    const result = await db.select().from(users).orderBy(asc(users.createdAt));
    return result;
  }

  async updateUser(id: number, updates: Partial<User>): Promise<User | undefined> {
    const [user] = await db.update(users).set(updates).where(eq(users.id, id)).returning();
    return user;
  }

  async updateUserPassword(id: number, hashedPassword: string): Promise<boolean> {
    const result = await db.update(users)
      .set({ password: hashedPassword })
      .where(eq(users.id, id));
    return (result.rowCount ?? 0) > 0;
  }

  async deleteUser(id: number): Promise<boolean> {
    const result = await db.delete(users).where(eq(users.id, id));
    return (result.rowCount ?? 0) > 0;
  }

  // Bookings
  async createBooking(booking: InsertBooking): Promise<Booking> {
    const secureToken = generateSecureToken();
    const bookingWithToken = { ...booking, secureToken };
    const [newBooking] = await db.insert(bookings).values(bookingWithToken).returning();
    
    // Track booking creation in history
    await this.trackBookingHistory({
      bookingId: newBooking.id,
      phoneNumber: newBooking.phone,
      action: 'created',
      actionBy: 'customer',
      newValues: newBooking,
    });
    
    return newBooking;
  }

  async getBooking(id: number): Promise<Booking | undefined> {
    const [booking] = await db.select().from(bookings).where(eq(bookings.id, id));
    return booking || undefined;
  }

  async getBookingByToken(token: string): Promise<Booking | undefined> {
    const [booking] = await db.select().from(bookings).where(eq(bookings.secureToken, token));
    return booking || undefined;
  }

  async getBookings(): Promise<Booking[]> {
    return await db.select().from(bookings).orderBy(desc(bookings.createdAt));
  }

  async getBookingsByDate(date: string): Promise<Booking[]> {
    const targetDate = new Date(date);
    const startOfDay = new Date(targetDate.setHours(0, 0, 0, 0));
    const endOfDay = new Date(targetDate.setHours(23, 59, 59, 999));
    
    return await db.select().from(bookings)
      .where(and(
        gte(bookings.date, startOfDay),
        lte(bookings.date, endOfDay)
      ))
      .orderBy(asc(bookings.date));
  }

  async getBookingsByDateRange(startDate: Date, endDate: Date, status?: string): Promise<Booking[]> {
    console.log('Storage getBookingsByDateRange called with:', {
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString(),
      status
    });
    
    // Normalize dates to handle timezone and time component variations
    const startOfStart = new Date(startDate.getFullYear(), startDate.getMonth(), startDate.getDate(), 0, 0, 0, 0);
    const endOfEnd = new Date(endDate.getFullYear(), endDate.getMonth(), endDate.getDate(), 23, 59, 59, 999);
    
    console.log('Normalized date range:', {
      startOfStart: startOfStart.toISOString(),
      endOfEnd: endOfEnd.toISOString()
    });
    
    const conditions = [
      gte(bookings.date, startOfStart),
      lte(bookings.date, endOfEnd)
    ];
    
    // Add status filtering if provided
    if (status && status !== 'all') {
      conditions.push(eq(bookings.status, status));
    }
    // If status === 'all' or not provided, don't add any status filtering to show all bookings
    
    const result = await db.select().from(bookings)
      .where(and(...conditions))
      .orderBy(asc(bookings.date));
      
    console.log(`Found ${result.length} bookings in date range:`, result.map(b => ({
      id: b.id,
      name: b.name,
      date: b.date,
      timeSlot: b.timeSlot,
      status: b.status
    })));
    
    return result;
  }

  async getBookingsByStatus(status: string): Promise<Booking[]> {
    return await db.select()
      .from(bookings)
      .where(eq(bookings.status, status))
      .orderBy(asc(bookings.date), asc(bookings.timeSlot));
  }

  async updateBooking(id: number, updates: Partial<Booking>, actionBy: 'customer' | 'admin' = 'admin', adminUserId?: number, adminUserName?: string, reason?: string): Promise<Booking | undefined> {
    // Get the original booking for history tracking
    const originalBooking = await this.getBooking(id);
    if (!originalBooking) return undefined;

    const [updatedBooking] = await db.update(bookings)
      .set(updates)
      .where(eq(bookings.id, id))
      .returning();

    if (!updatedBooking) return undefined;

    // Determine the action type based on what was updated
    let action: 'rescheduled' | 'cancelled' | 'completed' = 'rescheduled';
    
    if (updates.status === 'cancelled') {
      action = 'cancelled';
    } else if (updates.status === 'completed') {
      action = 'completed';
    } else if (updates.date || updates.timeSlot) {
      action = 'rescheduled';
    }

    // Track the change in history
    await this.trackBookingHistory({
      bookingId: updatedBooking.id,
      phoneNumber: updatedBooking.phone,
      action,
      actionBy,
      adminUserId,
      adminUserName,
      previousValues: originalBooking,
      newValues: updatedBooking,
      reason,
    });

    return updatedBooking;
  }

  async deleteBooking(id: number): Promise<boolean> {
    const result = await db.delete(bookings).where(eq(bookings.id, id));
    return result.rowCount > 0;
  }

  async getBookingsCount(date: Date): Promise<number> {
    const startOfDay = new Date(date.getFullYear(), date.getMonth(), date.getDate());
    const endOfDay = new Date(date.getFullYear(), date.getMonth(), date.getDate(), 23, 59, 59);
    
    const result = await db.select().from(bookings)
      .where(and(
        gte(bookings.date, startOfDay),
        lte(bookings.date, endOfDay),
        eq(bookings.status, "confirmed")
      ));
    
    return result.length;
  }

  // Sales Reps
  async createSalesRep(rep: InsertSalesRep): Promise<SalesRep> {
    const [newRep] = await db.insert(salesReps).values(rep).returning();
    return newRep;
  }

  async getSalesReps(): Promise<SalesRep[]> {
    return await db.select().from(salesReps).orderBy(asc(salesReps.name));
  }

  async getActiveSalesReps(): Promise<SalesRep[]> {
    return await db.select().from(salesReps)
      .where(eq(salesReps.isActive, true))
      .orderBy(asc(salesReps.name));
  }

  async getSalesRep(id: number): Promise<SalesRep | undefined> {
    const [rep] = await db.select().from(salesReps).where(eq(salesReps.id, id));
    return rep || undefined;
  }

  async updateSalesRep(id: number, updates: Partial<SalesRep>): Promise<SalesRep | undefined> {
    // Get the current rep data before updating
    const currentRep = await this.getSalesRep(id);
    if (!currentRep) return undefined;

    // Update the rep
    const [updatedRep] = await db.update(salesReps)
      .set(updates)
      .where(eq(salesReps.id, id))
      .returning();

    if (!updatedRep) return undefined;

    // Check if working days or shift times changed
    const workingDaysChanged = updates.workingDays && 
      JSON.stringify(updates.workingDays?.sort()) !== JSON.stringify(currentRep.workingDays?.sort());
    const shiftTimesChanged = updates.shiftStart !== currentRep.shiftStart || 
      updates.shiftEnd !== currentRep.shiftEnd;

    if (workingDaysChanged || shiftTimesChanged) {
      // Find future bookings that are affected by the schedule change
      const futureBookings = await db.select()
        .from(bookings)
        .where(and(
          eq(bookings.repAssigned, updatedRep.name),
          gte(bookings.date, new Date()),
          eq(bookings.status, "confirmed")
        ));

      for (const booking of futureBookings) {
        const bookingDate = new Date(booking.date);
        const dayOfWeek = bookingDate.toLocaleDateString('en-US', { weekday: 'long' });
        
        // Check if this booking is now invalid due to schedule changes
        const isWorkingDay = updatedRep.workingDays.includes(dayOfWeek);
        const bookingTime = booking.timeSlot;
        const [bookingHour, bookingMinute] = bookingTime.split(':').map(Number);
        const [shiftStartHour, shiftStartMinute] = updatedRep.shiftStart.split(':').map(Number);
        const [shiftEndHour, shiftEndMinute] = updatedRep.shiftEnd.split(':').map(Number);
        
        const bookingTimeMinutes = bookingHour * 60 + bookingMinute;
        const shiftStartMinutes = shiftStartHour * 60 + shiftStartMinute;
        const shiftEndMinutes = shiftEndHour * 60 + shiftEndMinute;
        
        const isWithinShiftHours = bookingTimeMinutes >= shiftStartMinutes && 
          bookingTimeMinutes <= shiftEndMinutes;

        if (!isWorkingDay || !isWithinShiftHours) {
          // Need to reassign this booking
          const availableReps = await this.getAvailableReps(bookingDate, booking.timeSlot);
          const alternativeReps = availableReps.filter(rep => rep.name !== updatedRep.name);
          
          if (alternativeReps.length > 0) {
            const newRepName = await this.assignRepWithRoundRobin(bookingDate, booking.timeSlot);
            if (newRepName && newRepName !== updatedRep.name) {
              await this.updateBooking(booking.id, { repAssigned: newRepName });
            }
          } else {
            // Mark as needing reassignment if no alternative available
            await this.updateBooking(booking.id, { 
              status: "pending_reassignment",
              repAssigned: "Unassigned - Schedule Conflict"
            });
          }
        }
      }
    }

    return updatedRep;
  }

  async deleteSalesRep(id: number): Promise<boolean> {
    // First, get the rep being deleted to check for pending bookings
    const repToDelete = await this.getSalesRep(id);
    if (!repToDelete) return false;

    // Find all future bookings assigned to this rep
    const futureBookings = await db.select()
      .from(bookings)
      .where(and(
        eq(bookings.repAssigned, repToDelete.name),
        gte(bookings.date, new Date()),
        eq(bookings.status, "confirmed")
      ));

    // Reassign each booking to available reps
    for (const booking of futureBookings) {
      const bookingDate = new Date(booking.date);
      
      // Find available reps for this booking's date and time
      const availableReps = await this.getAvailableReps(bookingDate, booking.timeSlot);
      const alternativeReps = availableReps.filter(rep => rep.name !== repToDelete.name);
      
      if (alternativeReps.length > 0) {
        // Use round robin to assign to another rep
        const newRepName = await this.assignRepWithRoundRobin(bookingDate, booking.timeSlot);
        if (newRepName && newRepName !== repToDelete.name) {
          await this.updateBooking(booking.id, { repAssigned: newRepName });
        }
      } else {
        // If no alternative rep available, mark booking as needs reassignment
        await this.updateBooking(booking.id, { 
          status: "pending_reassignment",
          repAssigned: "Unassigned - Rep Unavailable"
        });
      }
    }

    // Now delete the rep
    const result = await db.delete(salesReps).where(eq(salesReps.id, id));
    return result.rowCount > 0;
  }

  // Global booking rules helper functions
  private convertTimeToMinutes(timeStr: string): number {
    const [hours, minutes] = timeStr.split(':').map(Number);
    return hours * 60 + minutes;
  }

  private convertMinutesToTime(minutes: number): string {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
  }

  private formatTimeToAMPM(time24: string): string {
    const [hours, minutes] = time24.split(':').map(Number);
    const period = hours >= 12 ? 'PM' : 'AM';
    const displayHours = hours === 0 ? 12 : hours > 12 ? hours - 12 : hours;
    return `${displayHours}:${minutes.toString().padStart(2, '0')} ${period}`;
  }

  async generateAvailableTimeSlots(date: Date, excludeBookingId?: number): Promise<string[]> {
    const dayName = date.toLocaleDateString('en-US', { weekday: 'long' });
    // Get settings for configurable booking rules
    const settings = await this.getSettings();
    
    // Get all active reps who work on this day
    const allReps = await db.select().from(salesReps)
      .where(eq(salesReps.isActive, true));
    
    const workingReps = allReps.filter(rep => rep.workingDays.includes(dayName));
    
    if (workingReps.length === 0) {
      return [];
    }

    // Get existing bookings for this date
    const existingBookings = await this.getBookingsByDate(date.toISOString().split('T')[0]);

    // Generate all possible time slots based on configurable global rules
    const allPossibleSlots = new Set<string>();
    
    for (const rep of workingReps) {
      const shiftStartMinutes = this.convertTimeToMinutes(rep.shiftStart);
      const shiftEndMinutes = this.convertTimeToMinutes(rep.shiftEnd);
      
      // First appointment starts after configured shift buffer
      const firstSlotMinutes = shiftStartMinutes + settings.shiftStartBuffer;
      
      // Generate slots at configured intervals until shift end
      // Each appointment has configured duration, ensure it fits within shift and shift end buffer
      const shiftEndBufferMinutes = settings.shiftEndBuffer || 30;
      const latestAppointmentEnd = shiftEndMinutes - shiftEndBufferMinutes;
      
      let slotsGenerated = 0;
      for (let slotMinutes = firstSlotMinutes; 
           slotMinutes + settings.appointmentDuration <= latestAppointmentEnd; 
           slotMinutes += settings.timeSlotInterval) {
        
        const timeSlot24 = this.convertMinutesToTime(slotMinutes);
        const timeSlotAMPM = this.formatTimeToAMPM(timeSlot24);
        
        // Check if this rep can take this slot (considering existing bookings and configured gap)
        const canTakeSlot = await this.canRepTakeSlot(rep, slotMinutes, existingBookings, settings, date, excludeBookingId);
        
        if (canTakeSlot) {
          allPossibleSlots.add(timeSlotAMPM);
          slotsGenerated++;
        }
      }
    }
    
    // Convert Set to Array and sort by time
    return Array.from(allPossibleSlots).sort((a, b) => {
      const timeA = this.convertAMPMTo24Hour(a);
      const timeB = this.convertAMPMTo24Hour(b);
      return this.convertTimeToMinutes(timeA) - this.convertTimeToMinutes(timeB);
    });
  }

  private async canRepTakeSlot(rep: SalesRep, slotMinutes: number, existingBookings: any[], settings: any, date: Date, excludeBookingId?: number): Promise<boolean> {
    // Check existing bookings for this rep (excluding the booking being edited if specified)
    const repBookings = existingBookings.filter(booking => 
      booking.repAssigned === rep.name && 
      booking.status === 'confirmed' &&
      (excludeBookingId ? booking.id !== excludeBookingId : true)
    );
    
    for (const booking of repBookings) {
      const bookingTime24 = this.convertAMPMTo24Hour(booking.timeSlot);
      const bookingMinutes = this.convertTimeToMinutes(bookingTime24);
      
      // Calculate appointment end times
      const existingAppointmentEndMinutes = bookingMinutes + settings.appointmentDuration;
      const newAppointmentEndMinutes = slotMinutes + settings.appointmentDuration;
      
      // Fixed logic: Only block appointment duration + gap after appointment ends
      // Check if appointments overlap or don't have sufficient gap between them
      if (slotMinutes < existingAppointmentEndMinutes + settings.gapBetweenAppointments && 
          newAppointmentEndMinutes > bookingMinutes) {
        return false;
      }
    }
    
    // Check blocked time slots for this date and rep
    const blockedSlots = await this.getBlockedTimeSlots(date, date);
    const relevantBlockedSlots = blockedSlots.filter(blocked => 
      (!blocked.selectedReps || blocked.selectedReps.includes(rep.name)) &&
      blocked.date.toDateString() === date.toDateString()
    );
    
    for (const blocked of relevantBlockedSlots) {
      const blockedStartMinutes = this.convertTimeToMinutes(blocked.startTime);
      const blockedEndMinutes = this.convertTimeToMinutes(blocked.endTime);
      
      // Check if the slot overlaps with blocked time
      if (slotMinutes < blockedEndMinutes && slotMinutes + settings.appointmentDuration > blockedStartMinutes) {
        return false;
      }
    }
    
    return true;
  }

  private convertAMPMTo24Hour(time12h: string): string {
    const [time, modifier] = time12h.split(' ');
    let [hours, minutes] = time.split(':');
    if (hours === '12') {
      hours = '00';
    }
    if (modifier === 'PM' && hours !== '12') {
      hours = String(parseInt(hours, 10) + 12);
    }
    return `${hours.padStart(2, '0')}:${minutes}`;
  }

  async getAvailableReps(date: Date, timeSlot: string, excludeBookingId?: number): Promise<SalesRep[]> {
    const dayName = date.toLocaleDateString('en-US', { weekday: 'long' });
    const time24h = this.convertAMPMTo24Hour(timeSlot);
    const slotMinutes = this.convertTimeToMinutes(time24h);
    
    // Get settings for configurable booking rules
    const settings = await this.getSettings();
    
    // Get all active reps who work on this day
    const allReps = await db.select().from(salesReps)
      .where(eq(salesReps.isActive, true));
    
    const workingReps = allReps.filter(rep => rep.workingDays.includes(dayName));
    
    // Get existing bookings for this date
    const existingBookings = await this.getBookingsByDate(date.toISOString().split('T')[0]);
    
    // Filter reps who can take this time slot based on configurable rules
    const repAvailabilityPromises = workingReps.map(async (rep) => {
      // Check if time slot is within shift hours with proper buffer
      const shiftStartMinutes = this.convertTimeToMinutes(rep.shiftStart);
      const shiftEndMinutes = this.convertTimeToMinutes(rep.shiftEnd);
      
      // First appointment starts after configured shift buffer
      const earliestSlotMinutes = shiftStartMinutes + settings.shiftStartBuffer;
      
      // Appointment must fit within shift (configurable duration)
      const appointmentEndMinutes = slotMinutes + settings.appointmentDuration;
      
      if (slotMinutes < earliestSlotMinutes || appointmentEndMinutes > shiftEndMinutes) {
        return null; // Not available due to shift hours
      }
      
      // Check conflicts with existing bookings (configurable gap rule)
      const canTakeSlot = await this.canRepTakeSlot(rep, slotMinutes, existingBookings, settings, date, excludeBookingId);
      return canTakeSlot ? rep : null;
    });

    const repResults = await Promise.all(repAvailabilityPromises);
    const availableReps = repResults.filter(rep => rep !== null);

    return availableReps;
  }

  async assignRepWithRoundRobin(date: Date, timeSlot: string): Promise<string | null> {
    // Get available reps for this time slot
    const availableReps = await this.getAvailableReps(date, timeSlot);
    
    if (availableReps.length === 0) {
      return null;
    }

    // Get current week's bookings to calculate assignment counts
    const startOfWeek = new Date(date);
    startOfWeek.setDate(date.getDate() - date.getDay()); // Start from Sunday
    startOfWeek.setHours(0, 0, 0, 0);
    
    const endOfWeek = new Date(startOfWeek);
    endOfWeek.setDate(startOfWeek.getDate() + 6); // End on Saturday
    endOfWeek.setHours(23, 59, 59, 999);

    // Get all confirmed bookings for this week
    const weekBookings = await this.getBookingsByDateRange(startOfWeek, endOfWeek);
    const confirmedBookings = weekBookings.filter(booking => booking.status === 'confirmed');

    // Count assignments per rep for this week
    const assignmentCounts = new Map<string, number>();
    availableReps.forEach(rep => {
      assignmentCounts.set(rep.name, 0);
    });

    confirmedBookings.forEach(booking => {
      if (booking.repAssigned && assignmentCounts.has(booking.repAssigned)) {
        assignmentCounts.set(booking.repAssigned, assignmentCounts.get(booking.repAssigned)! + 1);
      }
    });

    // Find rep(s) with minimum assignments
    const minAssignments = Math.min(...Array.from(assignmentCounts.values()));
    const repsWithMinAssignments = availableReps.filter(rep => 
      assignmentCounts.get(rep.name) === minAssignments
    );

    // If multiple reps have same minimum assignments, pick randomly for fairness
    const selectedRep = repsWithMinAssignments[Math.floor(Math.random() * repsWithMinAssignments.length)];
    
    return selectedRep.name;
  }

  // Postal Codes
  async createPostalCode(postalCode: InsertPostalCode): Promise<PostalCode> {
    const [newCode] = await db.insert(postalCodes).values(postalCode).returning();
    return newCode;
  }

  async getPostalCodes(): Promise<PostalCode[]> {
    return await db.select().from(postalCodes).orderBy(asc(postalCodes.code));
  }

  async getPostalCodeByCode(code: string): Promise<PostalCode | null> {
    const result = await db.select().from(postalCodes).where(eq(postalCodes.code, code)).limit(1);
    return result[0] || null;
  }

  async getActivePostalCodes(): Promise<PostalCode[]> {
    return await db.select().from(postalCodes)
      .where(eq(postalCodes.isActive, true))
      .orderBy(asc(postalCodes.code));
  }

  async isPostalCodeValid(code: string): Promise<boolean> {
    const [result] = await db.select().from(postalCodes)
      .where(and(
        eq(postalCodes.code, code),
        eq(postalCodes.isActive, true)
      ));
    return !!result;
  }

  async getPostalCodeDetails(code: string): Promise<PostalCode | undefined> {
    const [result] = await db.select().from(postalCodes)
      .where(and(
        eq(postalCodes.code, code),
        eq(postalCodes.isActive, true)
      ));
    return result || undefined;
  }

  async updatePostalCode(id: number, updates: Partial<PostalCode>): Promise<PostalCode | undefined> {
    const [updatedCode] = await db.update(postalCodes)
      .set(updates)
      .where(eq(postalCodes.id, id))
      .returning();
    return updatedCode || undefined;
  }

  async deletePostalCode(id: number): Promise<boolean> {
    const result = await db.delete(postalCodes).where(eq(postalCodes.id, id));
    return result.rowCount > 0;
  }

  // Settings
  async getSettings(): Promise<Settings> {
    const result = await db.select().from(settings).limit(1);
    if (result.length === 0) {
      // Create default settings if none exist
      const [newSettings] = await db.insert(settings).values({
        smsTemplate: "Hi {{name}}, your Perfume Trial is confirmed for {{date}} at {{time}}. See you soon! 💜 – Team Kult",
        remindersEnabled: true,
        maxKits: 20,
        backgroundGradientStart: "#D4B9FC",
        backgroundGradientEnd: "#AD8FF7",
        backgroundGradientDirection: "135deg",
        buttonInactiveColor: "rgb(192, 161, 240)",
        buttonActiveColor: "rgb(170, 138, 219)",
        buttonDisabledColor: "#E5E7EB",
        buttonTextColor: "#FFFFFF",
        primaryTextColor: "#1F2937",
        secondaryTextColor: "#6B7280",
        headingColor: "#111827",
        accentTextColor: "#8B5CF6",
        inputBorderColor: "#D1D5DB",
        inputFocusColor: "#D4B9FC",
        inputBackgroundColor: "#FFFFFF",
        primaryBrandColor: "#D4B9FC",
        secondaryBrandColor: "#AD8FF7",
        successColor: "#10B981",
        errorColor: "#EF4444",
        warningColor: "#F59E0B",
        infoColor: "#3B82F6"
      }).returning();
      return newSettings;
    }
    return result[0];
  }

  async updateSettings(updates: Partial<Settings>): Promise<Settings> {
    const currentSettings = await this.getSettings();
    const [updatedSettings] = await db.update(settings)
      .set(updates)
      .where(eq(settings.id, currentSettings.id))
      .returning();
    return updatedSettings;
  }

  // WhatsApp Opt-in methods
  async createWhatsappOptIn(optIn: InsertWhatsappOptIn): Promise<WhatsappOptIn> {
    const [created] = await db.insert(whatsappOptIns).values(optIn).returning();
    return created;
  }

  async getWhatsappOptIn(phoneNumber: string): Promise<WhatsappOptIn | undefined> {
    const [optIn] = await db.select().from(whatsappOptIns)
      .where(eq(whatsappOptIns.phoneNumber, phoneNumber))
      .limit(1);
    return optIn;
  }

  async updateWhatsappOptIn(id: number, updates: Partial<WhatsappOptIn>): Promise<WhatsappOptIn | undefined> {
    const [updated] = await db.update(whatsappOptIns)
      .set({ ...updates, updatedAt: new Date() })
      .where(eq(whatsappOptIns.id, id))
      .returning();
    return updated;
  }

  async isPhoneOptedIn(phoneNumber: string): Promise<boolean> {
    const optIn = await this.getWhatsappOptIn(phoneNumber);
    return optIn?.isActive === true;
  }

  // Event tracking methods
  async createEventLog(event: InsertEventLog): Promise<EventLog> {
    const [created] = await db.insert(eventLogs).values(event).returning();
    return created;
  }

  async getEventLogs(filters?: { dateFrom?: Date; dateTo?: Date; eventType?: string; sessionId?: string; }): Promise<EventLog[]> {
    let query = db.select().from(eventLogs);
    
    if (filters) {
      const conditions = [];
      if (filters.dateFrom) {
        conditions.push(gte(eventLogs.timestamp, filters.dateFrom));
      }
      if (filters.dateTo) {
        conditions.push(lte(eventLogs.timestamp, filters.dateTo));
      }
      if (filters.eventType) {
        conditions.push(eq(eventLogs.eventType, filters.eventType));
      }
      if (filters.sessionId) {
        conditions.push(eq(eventLogs.sessionId, filters.sessionId));
      }
      
      if (conditions.length > 0) {
        query = query.where(and(...conditions));
      }
    }
    
    return await query.orderBy(desc(eventLogs.timestamp));
  }

  async getEventLogsBySession(sessionId: string): Promise<EventLog[]> {
    return await db.select().from(eventLogs)
      .where(eq(eventLogs.sessionId, sessionId))
      .orderBy(asc(eventLogs.timestamp));
  }

  // User session methods
  async createUserSession(session: InsertUserSession): Promise<UserSession> {
    // This method now creates a longTermUserSession instead
    const mappedSession = {
      sessionId: session.sessionId,
      converted: session.finalOutcome === 'completed',
      sessionDuration: session.timeSpent || 0,
      // Map other fields as needed
    };
    const [created] = await db.insert(longTermUserSessions).values(mappedSession).returning();
    // Return the session in the expected format
    return {
      id: created.id,
      sessionId: created.sessionId,
      firstVisit: created.startedAt || new Date(),
      lastActivity: created.endedAt || new Date(),
      currentStep: 'landing', // Default value
      completedSteps: [],
      finalOutcome: created.converted ? 'completed' : null,
      bookingId: null,
      totalEvents: 0,
      timeSpent: created.sessionDuration || 0
    };
  }

  async getUserSession(sessionId: string): Promise<LongTermUserSession | undefined> {
    const [session] = await db.select().from(longTermUserSessions)
      .where(eq(longTermUserSessions.sessionId, sessionId))
      .limit(1);
    return session;
  }

  async updateUserSession(sessionId: string, updates: Partial<LongTermUserSession>): Promise<LongTermUserSession | undefined> {
    const [updated] = await db.update(longTermUserSessions)
      .set({ ...updates, updatedAt: new Date() })
      .where(eq(longTermUserSessions.sessionId, sessionId))
      .returning();
    return updated;
  }

  async getAnalyticsUserSessions(filters?: { dateFrom?: Date; dateTo?: Date; finalOutcome?: string; }): Promise<UserSession[]> {
    let query = db.select().from(longTermUserSessions);
    
    if (filters) {
      const conditions = [];
      if (filters.dateFrom) {
        conditions.push(gte(longTermUserSessions.startedAt, filters.dateFrom));
      }
      if (filters.dateTo) {
        conditions.push(lte(longTermUserSessions.startedAt, filters.dateTo));
      }
      if (filters.finalOutcome) {
        const mappedOutcome = filters.finalOutcome === 'completed' ? true : false;
        conditions.push(eq(longTermUserSessions.converted, mappedOutcome));
      }
      
      if (conditions.length > 0) {
        query = query.where(and(...conditions));
      }
    }
    
    const sessions = await query.orderBy(desc(longTermUserSessions.startedAt));
    
    // Map longTermUserSessions to UserSession format
    return sessions.map(session => ({
      id: session.id,
      sessionId: session.sessionId,
      firstVisit: session.startedAt || new Date(),
      lastActivity: session.updatedAt || session.startedAt || new Date(),
      currentStep: 'landing', // Default for compatibility
      completedSteps: [],
      finalOutcome: session.converted ? 'completed' : null,
      bookingId: null, // This would need additional logic to link bookings
      totalEvents: 0,
      timeSpent: session.sessionDuration || 0,
    }));
  }

  async getDropOffAnalytics(dateFrom?: Date, dateTo?: Date): Promise<any> {
    // Get ALL sessions (both analytics-tracked and untracked)
    const analyticsSessions = await this.getAnalyticsUserSessions({ dateFrom, dateTo });
    
    // Get ALL event logs to capture complete user activity
    const eventLogs = await this.getEventLogs({ dateFrom, dateTo });
    
    // Get ALL bookings to include admin-created and manual bookings
    const bookings = dateFrom && dateTo 
      ? await this.getBookingsByDateRange(dateFrom, dateTo, 'all')
      : await this.getBookings();
    
    // Create comprehensive session list from all sources
    const allSessionIds = new Set<string>();
    
    // Add analytics sessions
    analyticsSessions.forEach(session => allSessionIds.add(session.sessionId));
    
    // Add sessions from event logs
    eventLogs.forEach(event => allSessionIds.add(event.sessionId));
    
    // Link bookings to their sessions using phone number matching
    const phoneToSessionMap = new Map<string, string>();
    
    // Build phone-to-session mapping from event logs
    eventLogs.forEach(event => {
      if (event.eventType === 'otp_requested' && event.eventData) {
        try {
          const eventData = JSON.parse(event.eventData);
          if (eventData.phone) {
            phoneToSessionMap.set(eventData.phone, event.sessionId);
          }
        } catch (e) {
          // Ignore parsing errors
        }
      }
    });
    
    // Link bookings to sessions using last 4 digits of phone
    bookings.forEach(booking => {
      const last4Digits = booking.phone.slice(-4);
      const matchingSessionId = phoneToSessionMap.get(last4Digits);
      
      if (matchingSessionId) {
        allSessionIds.add(matchingSessionId);
      } else {
        // Create synthetic session ID only for truly untracked bookings
        const syntheticSessionId = `booking_${booking.id}`;
        allSessionIds.add(syntheticSessionId);
      }
    });
    
    // Convert to sessions array with all sources included
    const sessions = Array.from(allSessionIds).map(sessionId => {
      const analyticsSession = analyticsSessions.find(s => s.sessionId === sessionId);
      return analyticsSession || { 
        sessionId, 
        source: 'direct_or_manual',
        completedSteps: 0 
      };
    });
    
    // Create single comprehensive analytics calculation for all dashboards
    const sessionsWithEvents = new Map<string, {
      postalCode: boolean;
      validPostal: boolean;
      invalidPostal: boolean;
      dateTime: boolean;
      contactInfo: boolean;
      booking: boolean;
      events: string[];
    }>();
    
    // Initialize all sessions
    sessions.forEach(session => {
      sessionsWithEvents.set(session.sessionId, {
        postalCode: false,
        validPostal: false,
        invalidPostal: false,
        dateTime: false,
        contactInfo: false,
        booking: false,
        events: []
      });
    });
    
    // Process events for sessions that have events
    eventLogs.forEach(event => {
      if (!sessionsWithEvents.has(event.sessionId)) {
        sessionsWithEvents.set(event.sessionId, {
          postalCode: false,
          validPostal: false,
          invalidPostal: false,
          dateTime: false,
          contactInfo: false,
          booking: false,
          events: []
        });
      }
      
      const sessionData = sessionsWithEvents.get(event.sessionId)!;
      sessionData.events.push(event.eventType);
      
      switch (event.eventType) {
        case 'postal_code_valid':
          sessionData.postalCode = true;
          sessionData.validPostal = true;
          break;
        case 'postal_code_invalid':
          sessionData.postalCode = true;
          sessionData.invalidPostal = true;
          break;
        case 'time_slot_selected':
          sessionData.dateTime = true;
          break;
        case 'otp_verified':
          sessionData.contactInfo = true;
          break;
        case 'booking_complete':
          sessionData.booking = true;
          break;
      }
    });
    
    // Calculate step counts with logical flow validation
    let postalCodeSessions = 0;
    let validPostalSessions = 0;
    let invalidPostalSessions = 0;
    let dateTimeSessions = 0;
    let contactInfoSessions = 0;
    let bookingSessions = 0;
    
    sessionsWithEvents.forEach((data, sessionId) => {
      // Step 1: Postal Code (any postal code attempt) - DIRECT from events
      if (data.postalCode) {
        postalCodeSessions++;
        
        if (data.validPostal) validPostalSessions++;
        if (data.invalidPostal) invalidPostalSessions++;
        
        // Step 2: Date Time (requires postal code) - DIRECT from events
        if (data.dateTime) {
          dateTimeSessions++;
          
          // Step 3: Contact Info (requires postal + datetime) - DIRECT from events
          if (data.contactInfo) {
            contactInfoSessions++;
            
            // Step 4: Booking (requires all previous steps) - Count both event-tracked and phone-linked bookings
            if (data.booking) {
              bookingSessions++;
            } else {
              // Check if this session is linked to a booking via phone number
              const linkedBooking = bookings.find(booking => {
                const last4Digits = booking.phone.slice(-4);
                return phoneToSessionMap.get(last4Digits) === sessionId;
              });
              
              if (linkedBooking) {
                bookingSessions++;
                // Mark this session as having a booking to avoid double-counting
                data.booking = true;
              }
            }
          }
        }
      }

      
      // Log problematic sessions for debugging
      if (data.booking && !data.contactInfo) {
        console.log(`Warning: Session ${sessionId} has booking without OTP verification - likely test/legacy data`);
      }
    });
    
    // Add ALL user bookings that have session tracking (regardless of status)
    const totalUserBookings = bookings.filter(booking => {
      const last4Digits = booking.phone.slice(-4);
      return phoneToSessionMap.get(last4Digits);
    }).length;
    
    // Use the higher count between event-tracked bookings and actual user bookings with sessions
    const actualCompletedBookings = Math.max(bookingSessions, totalUserBookings);
    
    const stepCounts = {
      landing: sessions.length,
      postal_code: postalCodeSessions,
      valid_postal: validPostalSessions,
      invalid_postal: invalidPostalSessions,
      date_time: dateTimeSessions,
      contact_info: contactInfoSessions,
      confirmation: actualCompletedBookings,
      completed: actualCompletedBookings
    };

    const dropOffPoints = {
      landing_to_postal: sessions.length - postalCodeSessions,
      postal_to_datetime: postalCodeSessions - dateTimeSessions,
      datetime_to_contact: dateTimeSessions - contactInfoSessions,
      contact_to_confirmation: contactInfoSessions - actualCompletedBookings,
      confirmation_to_completed: 0 // Same as confirmation
    };



    // Create event session map for drop-off analysis
    const sessionEvents = new Map<string, string[]>();
    eventLogs.forEach(event => {
      if (!sessionEvents.has(event.sessionId)) {
        sessionEvents.set(event.sessionId, []);
      }
      sessionEvents.get(event.sessionId)!.push(event.eventType);
    });
    
    // Analyze drop-off reasons by examining event patterns
    const dropOffReasons = await this.analyzeDropOffReasons(sessionEvents, sessions, { dateFrom, dateTo });

    return {
      stepCounts,
      dropOffPoints,
      dropOffReasons,
      totalSessions: sessions.length,
      conversionRate: sessions.length > 0 ? (stepCounts.completed / sessions.length) * 100 : 0
    };
  }

  async analyzeDropOffReasons(sessionEvents: Map<string, string[]>, sessions: any[], filters?: { dateFrom?: Date; dateTo?: Date }): Promise<any> {
    const eventLogs = await this.getEventLogs(filters);
    
    const reasons = {
      invalid_postal_code: 0,
      valid_postal_but_no_date_selection: 0,
      date_selected_but_no_contact_info: 0,
      otp_requested_but_not_verified: 0,
      otp_verified_but_booking_failed: 0,
      booking_started_but_abandoned: 0,
      unknown_abandonment: 0
    };

    const sessionEventMap = new Map<string, any[]>();
    eventLogs.forEach(event => {
      if (!sessionEventMap.has(event.sessionId)) {
        sessionEventMap.set(event.sessionId, []);
      }
      sessionEventMap.get(event.sessionId)!.push(event);
    });

    sessions.forEach(session => {
      const events = sessionEventMap.get(session.sessionId) || [];
      const eventTypes = events.map(e => e.eventType);
      const eventData = events.reduce((acc, e) => {
        acc[e.eventType] = e.eventData;
        return acc;
      }, {});

      // Skip completed bookings
      if (session.finalOutcome === 'completed' || eventTypes.includes('booking_complete')) {
        return;
      }

      // Analyze drop-off patterns with more granular tracking
      if (eventTypes.includes('postal_code_invalid')) {
        // User entered invalid postal code
        reasons.invalid_postal_code++;
      } else if (eventTypes.includes('postal_code_valid') && !eventTypes.includes('time_slot_selected')) {
        // User entered valid postal code but never selected a time slot
        reasons.valid_postal_but_no_date_selection++;
      } else if (eventTypes.includes('time_slot_selected') && !eventTypes.includes('otp_requested')) {
        // User selected time slot but never requested OTP
        reasons.date_selected_but_no_contact_info++;
      } else if (eventTypes.includes('otp_requested') && !eventTypes.includes('otp_verified')) {
        // User requested OTP but never verified it
        reasons.otp_requested_but_not_verified++;
      } else if (eventTypes.includes('otp_verified') && !eventTypes.includes('booking_complete')) {
        // User verified OTP but booking never completed
        reasons.otp_verified_but_booking_failed++;
      } else if (eventTypes.includes('form_start') || eventTypes.includes('form_submit')) {
        // User started booking process but abandoned at some point
        reasons.booking_started_but_abandoned++;
      } else if (eventTypes.length > 0) {
        // User had some interaction but dropped off at unclear point
        reasons.booking_started_but_abandoned++;
      } else {
        // No events tracked - true unknown abandonment
        reasons.unknown_abandonment++;
      }
    });

    // Calculate percentages and identify biggest drop-off reason
    const totalDropOffs = Object.values(reasons).reduce((sum, count) => sum + count, 0);
    const reasonsWithPercentages = Object.entries(reasons).map(([reason, count]) => ({
      reason: reason.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
      count,
      percentage: totalDropOffs > 0 ? ((count / totalDropOffs) * 100).toFixed(1) : '0.0'
    }));

    const biggestReason = reasonsWithPercentages.reduce((max, current) => 
      current.count > max.count ? current : max, reasonsWithPercentages[0]);

    return {
      breakdown: reasonsWithPercentages.sort((a, b) => b.count - a.count),
      biggestReason: biggestReason ? {
        reason: biggestReason.reason,
        count: biggestReason.count,
        percentage: biggestReason.percentage,
        impact: this.getDropOffImpact(biggestReason.reason)
      } : null,
      totalDropOffs,
      insights: this.generateDropOffInsights(reasonsWithPercentages)
    };
  }

  private getDropOffImpact(reason: string): string {
    const impacts: Record<string, string> = {
      'Invalid Postal Code': 'Users entering unsupported postal codes. Consider expanding service areas or clearer messaging.',
      'Valid Postal But No Date Selection': 'Users find postal code valid but abandon at scheduling. Check time slot availability and UX.',
      'Date Selected But No Contact Info': 'Users select dates but abandon before entering contact details. Simplify contact form.',
      'Otp Requested But Not Verified': 'Users request OTP but fail verification. Check SMS delivery and simplify OTP process.',
      'Otp Verified But Booking Failed': 'Technical issues after OTP verification. Check booking creation process and error handling.',
      'Booking Started But Abandoned': 'Users engage but drop off unexpectedly. Analyze session duration and user behavior.',
      'Unknown Abandonment': 'Visitors browse but don\'t start booking. This is normal website behavior - focus on converting engaged users.'
    };
    return impacts[reason] || 'Requires further investigation to determine specific cause.';
  }

  private generateDropOffInsights(reasons: any[]): string[] {
    const insights = [];
    const topReason = reasons[0];
    
    if (topReason && topReason.count > 0) {
      if (topReason.reason.includes('Postal Code')) {
        insights.push('Consider expanding service areas or improving postal code validation messaging');
      }
      if (topReason.reason.includes('Otp')) {
        insights.push('OTP verification issues detected - check SMS delivery and user experience');
      }
      if (topReason.reason.includes('Date Selection')) {
        insights.push('Users struggle with date/time selection - review availability and UX flow');
      }
      if (parseFloat(topReason.percentage) > 30) {
        insights.push(`${topReason.reason} accounts for ${topReason.percentage}% of drop-offs - high priority fix needed`);
      }
    }

    return insights;
  }

  async getConversionFunnel(dateFrom?: Date, dateTo?: Date): Promise<any> {
    const analytics = await this.getDropOffAnalytics(dateFrom, dateTo);
    const { stepCounts, totalSessions } = analytics;
    
    const funnelSteps = [
      { step: 'Landing Page', count: stepCounts.landing, percentage: 100 },
      { step: 'Postal Code', count: stepCounts.postal_code, percentage: totalSessions > 0 ? (stepCounts.postal_code / totalSessions) * 100 : 0 },
      { step: 'Date & Time', count: stepCounts.date_time, percentage: totalSessions > 0 ? (stepCounts.date_time / totalSessions) * 100 : 0 },
      { step: 'Contact Info', count: stepCounts.contact_info, percentage: totalSessions > 0 ? (stepCounts.contact_info / totalSessions) * 100 : 0 },
      { step: 'Confirmation', count: stepCounts.confirmation, percentage: totalSessions > 0 ? (stepCounts.confirmation / totalSessions) * 100 : 0 },
      { step: 'Completed', count: stepCounts.completed, percentage: totalSessions > 0 ? (stepCounts.completed / totalSessions) * 100 : 0 }
    ];
    
    return {
      funnelSteps,
      totalSessions,
      completionRate: analytics.conversionRate
    };
  }

  async getPostalCodeAnalytics(dateFrom?: Date, dateTo?: Date): Promise<any> {
    // Get postal code events DIRECTLY from database - no dependencies on other methods
    let query = db.select().from(eventLogs);
    
    const conditions = [
      or(
        eq(eventLogs.eventType, 'postal_code_valid'),
        eq(eventLogs.eventType, 'postal_code_invalid'),
        eq(eventLogs.eventType, 'postal_code_error')
      )
    ];
    
    if (dateFrom) conditions.push(gte(eventLogs.timestamp, dateFrom));
    if (dateTo) conditions.push(lte(eventLogs.timestamp, dateTo));
    
    query = query.where(and(...conditions));
    
    const events = await query;
    
    // Count DIRECT from events - unique sessions only for consistency
    const uniqueSessionsWithPostal = new Set<string>();
    const uniqueSessionsWithValid = new Set<string>();
    const uniqueSessionsWithInvalid = new Set<string>();
    
    // Calculate from actual event data - not dependent on other methods
    const postalCodeStats = {
      totalAttempts: 0, // Will be calculated from unique sessions
      validCodes: 0,
      invalidCodes: 0,
      errors: 0,
      topInvalidCodes: [] as Array<{ code: string; count: number }>,
      dailyStats: [] as Array<{ date: string; valid: number; invalid: number; errors: number }>
    };
    
    const invalidCodeCounts: Record<string, number> = {};
    const dailyStatsMap: Record<string, { valid: number; invalid: number; errors: number }> = {};
    
    events.forEach(event => {
      const eventData = event.eventData ? JSON.parse(event.eventData) : {};
      const dateKey = event.timestamp.toISOString().split('T')[0];
      
      // Track unique sessions for each postal code type
      uniqueSessionsWithPostal.add(event.sessionId);
      
      if (!dailyStatsMap[dateKey]) {
        dailyStatsMap[dateKey] = { valid: 0, invalid: 0, errors: 0 };
      }
      
      switch (event.eventType) {
        case 'postal_code_valid':
          uniqueSessionsWithValid.add(event.sessionId);
          dailyStatsMap[dateKey].valid++;
          break;
        case 'postal_code_invalid':
          uniqueSessionsWithInvalid.add(event.sessionId);
          dailyStatsMap[dateKey].invalid++;
          if (eventData.postalCode) {
            invalidCodeCounts[eventData.postalCode] = (invalidCodeCounts[eventData.postalCode] || 0) + 1;
          }
          break;
        case 'postal_code_error':
          postalCodeStats.errors++;
          dailyStatsMap[dateKey].errors++;
          break;
      }
    });
    
    // Use unique session counts for consistency with conversion funnel
    postalCodeStats.totalAttempts = uniqueSessionsWithPostal.size;
    postalCodeStats.validCodes = uniqueSessionsWithValid.size;
    postalCodeStats.invalidCodes = uniqueSessionsWithInvalid.size;
    
    // Sort invalid codes by count and get top 10
    postalCodeStats.topInvalidCodes = Object.entries(invalidCodeCounts)
      .map(([code, count]) => ({ code, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);
    
    // Convert daily stats to array
    postalCodeStats.dailyStats = Object.entries(dailyStatsMap)
      .map(([date, stats]) => ({ date, ...stats }))
      .sort((a, b) => a.date.localeCompare(b.date));
    
    const successRate = postalCodeStats.totalAttempts > 0 
      ? Math.round((postalCodeStats.validCodes / postalCodeStats.totalAttempts) * 1000) / 10 
      : 0;
    
    return {
      ...postalCodeStats,
      successRate,
      insights: this.generatePostalCodeInsights(postalCodeStats)
    };
  }

  private generatePostalCodeInsights(stats: any): string[] {
    const insights: string[] = [];
    
    if (stats.totalAttempts === 0) {
      insights.push("No postal code attempts recorded in this period");
      return insights;
    }
    
    const invalidRate = (stats.invalidCodes / stats.totalAttempts) * 100;
    const errorRate = (stats.errors / stats.totalAttempts) * 100;
    
    if (invalidRate > 30) {
      insights.push(`High invalid postal code rate (${invalidRate.toFixed(1)}%) - consider expanding service areas`);
    } else if (invalidRate > 15) {
      insights.push(`Moderate invalid postal code rate (${invalidRate.toFixed(1)}%) - monitor service area demand`);
    } else {
      insights.push(`Good postal code validation rate (${(100 - invalidRate).toFixed(1)}% valid)`);
    }
    
    if (errorRate > 5) {
      insights.push(`Technical issues detected (${errorRate.toFixed(1)}% errors) - check postal code validation system`);
    }
    
    if (stats.topInvalidCodes.length > 0) {
      const topCode = stats.topInvalidCodes[0];
      insights.push(`Most attempted invalid code: ${topCode.code} (${topCode.count} attempts)`);
    }
    
    return insights;
  }

  // Blocked time slots
  async createBlockedTimeSlot(blockedSlot: InsertBlockedTimeSlot): Promise<BlockedTimeSlot> {
    const [newBlockedSlot] = await db.insert(blockedTimeSlots).values(blockedSlot).returning();
    return newBlockedSlot;
  }

  async getBlockedTimeSlots(startDate?: Date, endDate?: Date): Promise<BlockedTimeSlot[]> {
    let query = db.select().from(blockedTimeSlots);
    
    const conditions = [];
    if (startDate) conditions.push(gte(blockedTimeSlots.date, startDate));
    if (endDate) conditions.push(lte(blockedTimeSlots.date, endDate));
    
    if (conditions.length > 0) {
      query = query.where(and(...conditions));
    }
    
    return await query.orderBy(asc(blockedTimeSlots.date));
  }

  async deleteBlockedTimeSlot(id: number): Promise<boolean> {
    const result = await db.delete(blockedTimeSlots).where(eq(blockedTimeSlots.id, id));
    return (result.rowCount ?? 0) > 0;
  }

  // Booking History Implementation
  async createBookingHistoryEntry(historyEntry: InsertBookingHistory): Promise<BookingHistory> {
    const [newHistoryEntry] = await db.insert(bookingHistory).values(historyEntry).returning();
    return newHistoryEntry;
  }

  async getBookingHistoryByPhone(phoneNumber: string): Promise<BookingHistory[]> {
    return await db.select().from(bookingHistory)
      .where(eq(bookingHistory.phoneNumber, phoneNumber))
      .orderBy(desc(bookingHistory.timestamp));
  }

  async getBookingHistoryById(bookingId: number): Promise<BookingHistory[]> {
    return await db.select().from(bookingHistory)
      .where(eq(bookingHistory.bookingId, bookingId))
      .orderBy(desc(bookingHistory.timestamp));
  }

  // Helper function to track booking history
  private async trackBookingHistory({
    bookingId,
    phoneNumber,
    action,
    actionBy,
    adminUserId,
    adminUserName,
    previousValues,
    newValues,
    reason,
    ipAddress,
    userAgent
  }: {
    bookingId?: number;
    phoneNumber: string;
    action: 'created' | 'rescheduled' | 'cancelled' | 'completed';
    actionBy: 'customer' | 'admin' | 'system';
    adminUserId?: number;
    adminUserName?: string;
    previousValues?: any;
    newValues?: any;
    reason?: string;
    ipAddress?: string;
    userAgent?: string;
  }): Promise<void> {
    try {
      await this.createBookingHistoryEntry({
        bookingId,
        phoneNumber,
        action,
        actionBy,
        adminUserId,
        adminUserName,
        previousValues: previousValues ? JSON.stringify(previousValues) : null,
        newValues: newValues ? JSON.stringify(newValues) : null,
        reason,
        ipAddress,
        userAgent,
      });
    } catch (error) {
      console.error('Error tracking booking history:', error);
      // Don't throw error to avoid breaking main booking operations
    }
  }

  // Customer Support - Interactions
  async createCustomerInteraction(interaction: InsertCustomerInteraction): Promise<CustomerInteraction> {
    const [newInteraction] = await db.insert(customerInteractions).values(interaction).returning();
    return newInteraction;
  }

  async getCustomerInteractions(bookingId?: number, customerPhone?: string): Promise<CustomerInteraction[]> {
    let query = db.select().from(customerInteractions);
    
    const conditions = [];
    if (bookingId) conditions.push(eq(customerInteractions.bookingId, bookingId));
    if (customerPhone) conditions.push(eq(customerInteractions.customerPhone, customerPhone));
    
    if (conditions.length > 0) {
      query = query.where(and(...conditions));
    }
    
    return await query.orderBy(desc(customerInteractions.createdAt));
  }

  async updateCustomerInteraction(id: number, updates: Partial<CustomerInteraction>): Promise<CustomerInteraction | undefined> {
    const [updatedInteraction] = await db.update(customerInteractions)
      .set(updates)
      .where(eq(customerInteractions.id, id))
      .returning();
    return updatedInteraction;
  }

  // Customer Support - Tickets
  async createSupportTicket(ticket: InsertSupportTicket): Promise<SupportTicket> {
    const [newTicket] = await db.insert(supportTickets).values({
      ...ticket,
      updatedAt: new Date()
    }).returning();
    return newTicket;
  }

  async getSupportTickets(filters?: { status?: string; assignedAgent?: string; priority?: string; }): Promise<SupportTicket[]> {
    let query = db.select().from(supportTickets);
    
    const conditions = [];
    if (filters?.status) conditions.push(eq(supportTickets.status, filters.status));
    if (filters?.assignedAgent) conditions.push(eq(supportTickets.assignedAgent, filters.assignedAgent));
    if (filters?.priority) conditions.push(eq(supportTickets.priority, filters.priority));
    
    if (conditions.length > 0) {
      query = query.where(and(...conditions));
    }
    
    return await query.orderBy(desc(supportTickets.createdAt));
  }

  async getSupportTicket(ticketId: string): Promise<SupportTicket | undefined> {
    const [ticket] = await db.select().from(supportTickets)
      .where(eq(supportTickets.ticketId, ticketId));
    return ticket;
  }

  async updateSupportTicket(ticketId: string, updates: Partial<SupportTicket>): Promise<SupportTicket | undefined> {
    const [updatedTicket] = await db.update(supportTickets)
      .set({ ...updates, updatedAt: new Date() })
      .where(eq(supportTickets.ticketId, ticketId))
      .returning();
    return updatedTicket;
  }

  // Business Metrics
  async createBusinessMetrics(metrics: InsertBusinessMetrics): Promise<BusinessMetrics> {
    const [newMetrics] = await db.insert(businessMetrics).values(metrics).returning();
    return newMetrics;
  }

  async getBusinessMetrics(dateFrom?: Date, dateTo?: Date): Promise<BusinessMetrics[]> {
    let query = db.select().from(businessMetrics);
    
    const conditions = [];
    if (dateFrom) conditions.push(gte(businessMetrics.date, dateFrom));
    if (dateTo) conditions.push(lte(businessMetrics.date, dateTo));
    
    if (conditions.length > 0) {
      query = query.where(and(...conditions));
    }
    
    return await query.orderBy(desc(businessMetrics.date));
  }

  async getDailyBusinessSummary(date: Date): Promise<any> {
    const startOfDay = new Date(date);
    startOfDay.setHours(0, 0, 0, 0);
    
    const endOfDay = new Date(date);
    endOfDay.setHours(23, 59, 59, 999);

    // Get bookings for the day
    const dayBookings = await db.select().from(bookings)
      .where(and(
        gte(bookings.date, startOfDay),
        lte(bookings.date, endOfDay)
      ));

    // Get support tickets for the day
    const dayTickets = await db.select().from(supportTickets)
      .where(and(
        gte(supportTickets.createdAt, startOfDay),
        lte(supportTickets.createdAt, endOfDay)
      ));

    // Calculate metrics
    const totalBookings = dayBookings.length;
    const completedAppointments = dayBookings.filter(b => b.appointmentCompleted).length;
    const totalRevenue = dayBookings.reduce((sum, b) => sum + (b.purchaseAmount || 0), 0);
    const averageOrderValue = completedAppointments > 0 ? totalRevenue / completedAppointments : 0;
    const conversionRate = totalBookings > 0 ? (completedAppointments / totalBookings) * 100 : 0;
    
    const satisfactionRatings = dayBookings
      .filter(b => b.customerSatisfaction)
      .map(b => b.customerSatisfaction!);
    const averageSatisfaction = satisfactionRatings.length > 0 
      ? satisfactionRatings.reduce((sum, rating) => sum + rating, 0) / satisfactionRatings.length 
      : 0;

    const ticketsCreated = dayTickets.length;
    const ticketsResolved = dayTickets.filter(t => t.status === 'resolved' || t.status === 'closed').length;

    return {
      date,
      totalBookings,
      completedAppointments,
      totalRevenue,
      averageOrderValue,
      conversionRate,
      averageSatisfaction,
      ticketsCreated,
      ticketsResolved,
      bookings: dayBookings,
      tickets: dayTickets
    };
  }

  // Enhanced Booking Support
  async markAppointmentCompleted(bookingId: number, satisfaction?: number, notes?: string): Promise<boolean> {
    const updates: any = { 
      appointmentCompleted: true, 
      status: 'completed',
      updatedAt: new Date()
    };
    
    if (satisfaction) updates.customerSatisfaction = satisfaction;
    if (notes) updates.feedbackNotes = notes;

    const result = await db.update(bookings)
      .set(updates)
      .where(eq(bookings.id, bookingId));
    
    return (result.rowCount ?? 0) > 0;
  }

  async addTransaction(bookingId: number, amount: number, orderId: string, paymentMethod: string): Promise<boolean> {
    const result = await db.update(bookings)
      .set({ 
        purchaseAmount: amount,
        orderId,
        paymentMethod,
        paymentStatus: 'completed',
        updatedAt: new Date()
      })
      .where(eq(bookings.id, bookingId));
    
    return (result.rowCount ?? 0) > 0;
  }

  async recordCommunication(bookingId: number, method: string, notes?: string): Promise<boolean> {
    const updates: any = { 
      lastContactMethod: method,
      updatedAt: new Date()
    };

    if (method === 'call') {
      const booking = await this.getBooking(bookingId);
      if (booking) {
        updates.callAttempts = (booking.callAttempts || 0) + 1;
        updates.lastCallDate = new Date();
      }
    } else if (method === 'whatsapp') {
      const booking = await this.getBooking(bookingId);
      if (booking) {
        updates.whatsappSent = (booking.whatsappSent || 0) + 1;
      }
    } else if (method === 'email') {
      const booking = await this.getBooking(bookingId);
      if (booking) {
        updates.emailsSent = (booking.emailsSent || 0) + 1;
      }
    }

    const result = await db.update(bookings)
      .set(updates)
      .where(eq(bookings.id, bookingId));
    
    return (result.rowCount ?? 0) > 0;
  }

  async getDetailedInvalidPostalCodes(dateFrom?: Date, dateTo?: Date): Promise<any> {
    let query = db.select().from(eventLogs);
    
    const conditions = [eq(eventLogs.eventType, 'postal_code_invalid')];
    
    if (dateFrom) conditions.push(gte(eventLogs.timestamp, dateFrom));
    if (dateTo) conditions.push(lte(eventLogs.timestamp, dateTo));
    
    query = query.where(and(...conditions))
      .orderBy(desc(eventLogs.timestamp));
    
    const events = await query;
    
    // Get city information for postal codes using a basic lookup
    const getCityInfo = async (postalCode: string) => {
      // For Indian postal codes, use first 3 digits for region mapping
      const regionMap: Record<string, string> = {
        '110': 'New Delhi, Delhi',
        '400': 'Mumbai, Maharashtra', 
        '560': 'Bangalore, Karnataka',
        '600': 'Chennai, Tamil Nadu',
        '700': 'Kolkata, West Bengal',
        '500': 'Hyderabad, Telangana',
        '411': 'Pune, Maharashtra',
        '380': 'Ahmedabad, Gujarat',
        '302': 'Jaipur, Rajasthan',
        '201': 'Ghaziabad, Uttar Pradesh',
        '122': 'Gurgaon, Haryana',
        '160': 'Chandigarh, Punjab',
        '282': 'Agra, Uttar Pradesh',
        '226': 'Lucknow, Uttar Pradesh',
        '800': 'Patna, Bihar',
        '751': 'Bhubaneswar, Odisha',
        '682': 'Kochi, Kerala',
        '395': 'Surat, Gujarat',
        '484': 'Indore, Madhya Pradesh',
        '440': 'Nagpur, Maharashtra'
      };
      
      const region = postalCode.substring(0, 3);
      return regionMap[region] || 'Unknown City, Unknown State';
    };
    
    // Parse and organize invalid postal code data
    const invalidCodes: Array<{
      code: string;
      city: string;
      timestamp: string;
      sessionId: string;
      ipAddress: string | null;
      userAgent: string | null;
      attempts: number;
    }> = [];
    
    const codeStats: Record<string, { 
      count: number; 
      firstSeen: string; 
      lastSeen: string; 
      sessions: Set<string>;
      city: string;
    }> = {};
    
    for (const event of events) {
      const eventData = event.eventData ? JSON.parse(event.eventData) : {};
      const code = eventData.postalCode || 'Unknown';
      const city = await getCityInfo(code);
      
      // Track individual attempts
      invalidCodes.push({
        code,
        city,
        timestamp: event.timestamp.toISOString(),
        sessionId: event.sessionId,
        ipAddress: event.ipAddress,
        userAgent: event.userAgent,
        attempts: 1
      });
      
      // Track stats per code
      if (!codeStats[code]) {
        codeStats[code] = {
          count: 0,
          firstSeen: event.timestamp.toISOString(),
          lastSeen: event.timestamp.toISOString(),
          sessions: new Set(),
          city
        };
      }
      
      codeStats[code].count++;
      codeStats[code].sessions.add(event.sessionId);
      
      const eventTime = event.timestamp.toISOString();
      if (eventTime < codeStats[code].firstSeen) {
        codeStats[code].firstSeen = eventTime;
      }
      if (eventTime > codeStats[code].lastSeen) {
        codeStats[code].lastSeen = eventTime;
      }
    }
    
    // Convert to summary format
    const summary = Object.entries(codeStats).map(([code, stats]) => ({
      code,
      city: stats.city,
      totalAttempts: stats.count,
      uniqueSessions: stats.sessions.size,
      firstSeen: stats.firstSeen,
      lastSeen: stats.lastSeen,
      conversionRate: 0 // Invalid codes have 0% conversion
    })).sort((a, b) => b.totalAttempts - a.totalAttempts);
    
    return {
      totalInvalidAttempts: events.length,
      uniqueCodes: Object.keys(codeStats).length,
      topInvalidCodes: summary.slice(0, 20),
      allAttempts: invalidCodes,
      summary
    };
  }

  // Admin Sessions
  async createAdminSession(userId: number, sessionId: string, ipAddress?: string, userAgent?: string): Promise<void> {
    await db.insert(adminSessions).values({
      userId,
      sessionId,
      ipAddress,
      userAgent
    });
  }

  async getUserById(userId: number): Promise<any> {
    const result = await db.select()
      .from(users)
      .where(eq(users.id, userId))
      .limit(1);
    
    return result[0] || null;
  }

  async getAdminSessions(userId: number): Promise<any[]> {
    const sessions = await db.select()
      .from(adminSessions)
      .where(eq(adminSessions.userId, userId))
      .orderBy(desc(adminSessions.loginTime))
      .limit(50);
    
    return sessions;
  }

  async getAllAdminSessions(): Promise<any[]> {
    const sessions = await db.select({
      id: adminSessions.id,
      userId: adminSessions.userId,
      sessionId: adminSessions.sessionId,
      loginTime: adminSessions.loginTime,
      logoutTime: adminSessions.logoutTime,
      ipAddress: adminSessions.ipAddress,
      userAgent: adminSessions.userAgent,
      isActive: adminSessions.isActive,
      username: users.username,
      name: users.name,
      role: users.role
    })
      .from(adminSessions)
      .leftJoin(users, eq(adminSessions.userId, users.id))
      .orderBy(desc(adminSessions.loginTime))
      .limit(100);
    
    // Calculate duration and proper status for each session
    return sessions.map(session => {
      const loginTime = new Date(session.loginTime);
      const logoutTime = session.logoutTime ? new Date(session.logoutTime) : new Date();
      const timeSpent = Math.round((logoutTime.getTime() - loginTime.getTime()) / (1000 * 60)); // in minutes
      
      // Determine status based on actual activity
      const isActive = session.isActive && !session.logoutTime;
      const status = isActive ? 'active' : 'expired';
      
      return {
        ...session,
        timeSpent,
        status,
        lastActivity: session.logoutTime || new Date().toISOString()
      };
    });
  }

  async endAdminSession(sessionId: string): Promise<void> {
    await db.update(adminSessions)
      .set({ logoutTime: new Date(), isActive: false })
      .where(eq(adminSessions.sessionId, sessionId));
  }

  // Long-term user tracking methods
  async createOrUpdateUserTracking(trackingData: any): Promise<UserTracking> {
    const existingUser = await db
      .select()
      .from(userTracking)
      .where(eq(userTracking.userId, trackingData.userId))
      .limit(1);

    if (existingUser.length > 0) {
      // Update existing user
      const [updated] = await db
        .update(userTracking)
        .set({
          lastVisit: new Date(trackingData.lastVisit),
          visitCount: trackingData.visitCount,
          deviceFingerprint: trackingData.deviceFingerprint,
          
          // Update last-touch attribution
          lastUtmSource: trackingData.utmSource,
          lastUtmMedium: trackingData.utmMedium,
          lastUtmCampaign: trackingData.utmCampaign,
          lastUtmTerm: trackingData.utmTerm,
          lastUtmContent: trackingData.utmContent,
          lastReferrer: trackingData.referrer,
          
          totalPageViews: (existingUser[0].totalPageViews || 0) + 1,
          updatedAt: new Date(),
        })
        .where(eq(userTracking.userId, trackingData.userId))
        .returning();
      return updated;
    } else {
      // Create new user
      const [created] = await db.insert(userTracking).values({
        userId: trackingData.userId,
        deviceFingerprint: trackingData.deviceFingerprint,
        firstVisit: new Date(trackingData.firstVisit),
        lastVisit: new Date(trackingData.lastVisit),
        visitCount: 1,
        
        // First-touch attribution
        firstUtmSource: trackingData.utmSource,
        firstUtmMedium: trackingData.utmMedium,
        firstUtmCampaign: trackingData.utmCampaign,
        firstUtmTerm: trackingData.utmTerm,
        firstUtmContent: trackingData.utmContent,
        firstReferrer: trackingData.referrer,
        
        // Last-touch attribution (same as first for new users)
        lastUtmSource: trackingData.utmSource,
        lastUtmMedium: trackingData.utmMedium,
        lastUtmCampaign: trackingData.utmCampaign,
        lastUtmTerm: trackingData.utmTerm,
        lastUtmContent: trackingData.utmContent,
        lastReferrer: trackingData.referrer,
        
        totalPageViews: 1,
      }).returning();
      return created;
    }
  }

  async getUserTracking(userId: string): Promise<UserTracking | undefined> {
    const [user] = await db
      .select()
      .from(userTracking)
      .where(eq(userTracking.userId, userId));
    return user || undefined;
  }

  async createOrUpdateLongTermSession(sessionData: any): Promise<LongTermUserSession> {
    const existingSession = await db
      .select()
      .from(longTermUserSessions)
      .where(eq(longTermUserSessions.sessionId, sessionData.sessionId))
      .limit(1);

    if (existingSession.length > 0) {
      // Update existing session
      const [updated] = await db
        .update(longTermUserSessions)
        .set({
          pageViews: (existingSession[0].pageViews || 0) + 1,
          sessionDuration: sessionData.sessionDuration || 0,
          bounced: sessionData.bounced !== undefined ? sessionData.bounced : true,
          converted: sessionData.converted || false,
          completedSteps: sessionData.completedSteps || [],
          funnelProgress: sessionData.funnelProgress || 0,
          endedAt: sessionData.endedAt ? new Date(sessionData.endedAt) : null,
          updatedAt: new Date(),
        })
        .where(eq(longTermUserSessions.sessionId, sessionData.sessionId))
        .returning();
      return updated;
    } else {
      // Create new session
      const [created] = await db.insert(longTermUserSessions).values({
        userId: sessionData.userId,
        sessionId: sessionData.sessionId,
        deviceFingerprint: sessionData.deviceFingerprint,
        
        utmSource: sessionData.utmSource,
        utmMedium: sessionData.utmMedium,
        utmCampaign: sessionData.utmCampaign,
        utmTerm: sessionData.utmTerm,
        utmContent: sessionData.utmContent,
        referrer: sessionData.referrer,
        
        pageViews: 1,
        sessionDuration: sessionData.sessionDuration || 0,
        bounced: sessionData.bounced !== undefined ? sessionData.bounced : true,
        converted: sessionData.converted || false,
        completedSteps: sessionData.completedSteps || [],
        funnelProgress: sessionData.funnelProgress || 0,
      }).returning();
      return created;
    }
  }

  async getLongTermUserSessions(userId: string): Promise<LongTermUserSession[]> {
    return await db
      .select()
      .from(longTermUserSessions)
      .where(eq(longTermUserSessions.userId, userId))
      .orderBy(longTermUserSessions.startedAt);
  }
}

export const storage = new DatabaseStorage();
