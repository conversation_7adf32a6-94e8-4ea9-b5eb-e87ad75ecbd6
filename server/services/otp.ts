import { sendWhatsAppOTP } from './twilio-whatsapp';
import { storage } from '../storage';

// Format phone number for consistent storage
function formatPhoneNumber(phone: string): string {
  // Remove any spaces, dashes, or other formatting
  const cleanPhone = phone.replace(/\D/g, '');
  
  // If it starts with +91, use as is
  if (cleanPhone.startsWith('91') && cleanPhone.length === 12) {
    return `+${cleanPhone}`;
  }
  
  // If it's a 10-digit number starting with 6-9, assume it's Indian
  if (cleanPhone.length === 10 && /^[6-9]/.test(cleanPhone)) {
    return `+91${cleanPhone}`;
  }
  
  // For other formats, try to extract and format correctly
  const last10 = cleanPhone.slice(-10);
  return `+91${last10}`;
}

// Send OTP via Twilio WhatsApp
export async function sendOTP(phone: string, userAgent?: string, ipAddress?: string): Promise<{ success: boolean; message: string }> {
  try {
    const formattedPhone = formatPhoneNumber(phone);
    
    // Try to send WhatsApp OTP using Twilio
    const result = await sendWhatsAppOTP(phone, userAgent, ipAddress);
    
    if (result.success) {
      // Store opt-in information for Meta compliance
      try {
        const existingOptIn = await storage.getWhatsappOptIn(formattedPhone);
        if (!existingOptIn) {
          await storage.createWhatsappOptIn({
            phoneNumber: formattedPhone,
            ipAddress,
            userAgent,
            source: 'booking_flow',
            isActive: true
          });
          console.log('WhatsApp opt-in recorded for phone:', formattedPhone);
        }
      } catch (optInError) {
        console.error('Failed to record WhatsApp opt-in:', optInError);
        // Continue even if opt-in recording fails
      }
    }
    
    return result;
  } catch (error) {
    console.error('OTP sending error:', error);
    return {
      success: false,
      message: 'Failed to send OTP'
    };
  }
}

// Verify OTP - delegate to Twilio WhatsApp service
export async function verifyOTP(phone: string, userOtp: string): Promise<{ success: boolean; message: string }> {
  const { verifyWhatsAppOTP } = await import('./twilio-whatsapp');
  return verifyWhatsAppOTP(phone, userOtp);
}

// Clean up expired OTPs - delegate to Twilio WhatsApp service
export function cleanupExpiredOTPs() {
  const { cleanupExpiredOTPs: cleanup } = require('./twilio-whatsapp');
  cleanup();
}