import type { InsertBooking, Settings, Booking } from "@shared/schema";

// Helper function to format phone number for Message91
function formatPhoneNumber(phone: string): string {
  // Remove any spaces, dashes, or other formatting
  const cleanPhone = phone.replace(/\D/g, '');
  
  // If it starts with +91, use as is
  if (cleanPhone.startsWith('91') && cleanPhone.length === 12) {
    return `+${cleanPhone}`;
  }
  
  // If it's a 10-digit number starting with 6-9, assume it's Indian
  if (cleanPhone.length === 10 && /^[6-9]/.test(cleanPhone)) {
    return `+91${cleanPhone}`;
  }
  
  // For other formats, try to extract and format correctly
  const last10 = cleanPhone.slice(-10);
  return `+91${last10}`;
}

// Function to send WhatsApp notifications via Twilio API
async function sendWhatsAppNotification(to: string, templateType: 'confirmation' | 'cancellation' | 'reschedule' | 'reminder', variables: Record<string, string>): Promise<{success: boolean; message?: string}> {
  try {
    const { sendWhatsAppNotification: twilioSend } = await import('./twilio-whatsapp');
    const result = await twilioSend(to, templateType, variables);
    return result;
  } catch (error) {
    console.error('WhatsApp notification error:', error);
    return { success: false, message: 'Failed to send WhatsApp notification' };
  }
}

// Renamed from sendSMS to sendWhatsAppMessage to reflect the actual functionality
export async function sendSMS(
  bookingData: InsertBooking, 
  settings: Settings, 
  booking: Booking,
  templateType: 'confirmation' | 'cancellation' | 'reschedule' | 'reminder' | 'cx-followup' = 'confirmation'
): Promise<boolean> {
  try {
    const formattedPhone = formatPhoneNumber(booking.phone);
    
    // Send path-only format for WhatsApp template variables using secure token
    // Add UTM parameters to track WhatsApp notification traffic
    const rescheduleUrl = `booking/reschedule/${booking.secureToken}?utm_source=WhatsApp&utm_medium=notification&utm_campaign=booking_${templateType}&utm_content=reschedule_button`;
    const cancelUrl = `booking/cancel/${booking.secureToken}?utm_source=WhatsApp&utm_medium=notification&utm_campaign=booking_${templateType}&utm_content=cancel_button`;

    // Format date for WhatsApp template (e.g., "Friday 27 June, 2025")
    const formattedDate = new Date(booking.date).toLocaleDateString('en-GB', {
      weekday: 'long',
      day: 'numeric', 
      month: 'long',
      year: 'numeric'
    });

    // Prepare variables for WhatsApp notification with proper URLs
    const variables = {
      name: booking.name,
      date: formattedDate,
      time: booking.timeSlot,
      rescheduleLink: rescheduleUrl,
      cancelLink: cancelUrl,
      address: booking.address
    };

    let result;
    
    // Handle cx-followup separately as it uses different function
    if (templateType === 'cx-followup') {
      const { sendCxFollowupMessage } = await import('./twilio-whatsapp');
      result = await sendCxFollowupMessage(formattedPhone, booking.name, variables.date, booking.timeSlot);
    } else {
      // For regular templates, use the standard notification function
      result = await sendWhatsAppNotification(formattedPhone, templateType as 'confirmation' | 'cancellation' | 'reschedule' | 'reminder', variables);
    }
    
    if (result && result.success) {
      console.log(`WhatsApp ${templateType} sent successfully to ${formattedPhone}`);
      return true;
    } else {
      console.error(`Failed to send WhatsApp ${templateType} to ${formattedPhone}:`, result?.message || 'Unknown error');
      return false;
    }
    
  } catch (error) {
    console.error(`Error sending WhatsApp ${templateType}:`, error);
    return false;
  }
}

export async function sendReminderSMS(booking: Booking): Promise<boolean> {
  try {
    if (!booking.phone) {
      console.error('Missing phone number for reminder SMS');
      return false;
    }

    const date = new Date(booking.date).toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

    // Send path-only format for WhatsApp template variables using secure token
    const cancelUrl = `booking/cancel/${booking.secureToken}`;
    const rescheduleUrl = `booking/reschedule/${booking.secureToken}`;

    const reminderText = `Hi ${booking.name}! Reminder: Perfume trial tomorrow ${date} at ${booking.timeSlot}. Manage: ${rescheduleUrl}`;

    // Format phone number for Message91
    const formattedPhone = formatPhoneNumber(booking.phone);
    console.log(`Sending reminder SMS to formatted number: ${formattedPhone}`);

    const variables = {
      name: booking.name,
      date: date,
      time: booking.timeSlot,
      address: booking.address,
      rescheduleLink: rescheduleUrl,
      cancelLink: cancelUrl
    };

    const result = await sendWhatsAppNotification(formattedPhone, 'reminder', variables);
    return result.success;
  } catch (error) {
    console.error('Reminder SMS sending failed:', error);
    return false;
  }
}
