import twilio from 'twilio';

// In-memory OTP storage (in production, use Redis or database)
const otpStore = new Map<string, { otp: string; expires: number }>();

// Initialize Twilio client with proper error handling
const accountSid = process.env.TWILIO_ACCOUNT_SID;
const authToken = process.env.TWILIO_AUTH_TOKEN;
const whatsappNumber = 'whatsapp:+***********'; // Your WhatsApp Business number

if (!accountSid || !authToken) {
  console.error('Missing Twilio credentials. Please set TWILIO_ACCOUNT_SID and TWILIO_AUTH_TOKEN');
}

const client = twilio(accountSid, authToken);

// WhatsApp Business API requires pre-approved templates for all messages
// According to Meta and Twilio documentation, we need approved Content Template SIDs
// These must be created and approved in Twilio Console -> Messaging -> Content Template Builder
const WHATSAPP_TEMPLATES = {
  // Authentication template for OTP (must be approved by Meta)
  OTP_VERIFICATION: {
    contentSid: 'HX2ac9a333f19d4ecec2a59b9b7e09c8f8', // Corrected template SID from your fix
    language: 'en'
  },
  // Booking confirmation template
  BOOKING_CONFIRMATION: {
    contentSid: 'HX85b957e5285e13b37e8948b0294c3b94', // kult_booking_confirmation
    language: 'en'
  },
  // Booking cancellation template
  BOOKING_CANCELLATION: {
    contentSid: 'HX300db6bbc12129393900f43c018ec94b', // kult_booking_cancelled (updated)
    language: 'en'
  },
  // Booking reminder template
  BOOKING_REMINDER: {
    contentSid: 'HX3e3ccda5dd4e83b133c8d92f2ced792e', // kult_booking_reminder (updated)
    language: 'en'
  },
  // Booking reschedule template
  BOOKING_RESCHEDULE: {
    contentSid: 'HX7c994e4ebbc89bbd119ea905cdee388b', // kult_booking_reschedule
    language: 'en'
  },
  // Customer service follow-up template
  CX_FOLLOWUP: {
    contentSid: 'HXf8cd3f43ce006421c747f10ff3f7a78f', // Updated CX followup template
    language: 'en'
  }
};

// Generate 6-digit OTP
function generateOTP(): string {
  return Math.floor(100000 + Math.random() * 900000).toString();
}

// Format phone number for WhatsApp according to E.164 standard
// WhatsApp Business API requires E.164 format with whatsapp: prefix
function formatWhatsAppNumber(phone: string): string {
  // Remove any spaces, dashes, or other formatting
  const cleanPhone = phone.replace(/\D/g, '');
  
  // Validate Indian phone numbers (must start with 6-9 for mobile)
  let formattedNumber = '';
  
  if (cleanPhone.startsWith('91') && cleanPhone.length === 12) {
    // Already has country code
    const mobileNumber = cleanPhone.substring(2);
    if (/^[6-9]\d{9}$/.test(mobileNumber)) {
      formattedNumber = `+${cleanPhone}`;
    }
  } else if (cleanPhone.length === 10 && /^[6-9]\d{9}$/.test(cleanPhone)) {
    // Indian mobile number without country code
    formattedNumber = `+91${cleanPhone}`;
  } else {
    // Try to extract last 10 digits and validate
    const last10 = cleanPhone.slice(-10);
    if (/^[6-9]\d{9}$/.test(last10)) {
      formattedNumber = `+91${last10}`;
    }
  }
  
  if (!formattedNumber) {
    throw new Error('Invalid Indian mobile number format');
  }
  
  return `whatsapp:${formattedNumber}`;
}

// Send OTP via Twilio WhatsApp with Meta-approved template
// Following WhatsApp Business API best practices for authentication flows
export async function sendWhatsAppOTP(phone: string, userAgent?: string, ipAddress?: string): Promise<{ success: boolean; message: string; optInRequired?: boolean }> {
  try {
    if (!accountSid || !authToken) {
      return {
        success: false,
        message: 'WhatsApp service not configured properly'
      };
    }

    const otp = generateOTP();
    const formattedPhone = formatWhatsAppNumber(phone);
    
    // Store OTP with 5-minute expiry (WhatsApp guideline: short expiry for security)
    otpStore.set(phone, {
      otp,
      expires: Date.now() + 5 * 60 * 1000
    });

    console.log(`Sending WhatsApp OTP to ${formattedPhone} using Template SID ${WHATSAPP_TEMPLATES.OTP_VERIFICATION.contentSid}`);

    // According to Meta WhatsApp Business Policy, ONLY use approved templates
    // Session messages are not allowed for authentication/OTP purposes
    // Reference: https://developers.facebook.com/docs/whatsapp/cloud-api/guides/send-messages
    
    if (!WHATSAPP_TEMPLATES.OTP_VERIFICATION.contentSid) {
      throw new Error('OTP template not configured. Please create and approve an authentication template in Twilio Console.');
    }

    const message = await client.messages.create({
      from: whatsappNumber, // whatsapp:+***********
      to: formattedPhone,   // whatsapp:+************
      contentSid: WHATSAPP_TEMPLATES.OTP_VERIFICATION.contentSid,
      contentVariables: JSON.stringify({
        '1': otp // OTP code variable as defined in the template
      })
    });

    console.log('WhatsApp OTP sent successfully:', message.sid);
    console.log('Message status:', message.status);

    return {
      success: true,
      message: 'OTP sent via WhatsApp successfully',
      optInRequired: false
    };

  } catch (error: any) {
    console.error('WhatsApp OTP sending error:', error);
    console.error('Error code:', error.code);
    console.error('Error status:', error.status);

    // Handle specific Twilio/WhatsApp errors according to documentation
    if (error.code === 63016 || error.code === 63015) {
      // User has not opted in to receive messages
      return {
        success: false,
        message: 'Please initiate a conversation by sending a message to our WhatsApp Business number first.',
        optInRequired: true
      };
    } else if (error.code === 21211 || error.code === 21614) {
      // Invalid phone number
      return {
        success: false,
        message: 'Invalid phone number format. Please check and try again.'
      };
    } else if (error.code === 21408 || error.code === 63003) {
      // Service unavailable or rate limit
      return {
        success: false,
        message: 'WhatsApp service temporarily unavailable. Please try again in a few minutes.'
      };
    } else if (error.code === 21610) {
      // Message failed to send
      return {
        success: false,
        message: 'Failed to deliver message. Please check your WhatsApp is active.'
      };
    } else if (error.code === 63005) {
      // Template content submission pending review or rejected
      return {
        success: false,
        message: 'Template is pending approval or was rejected by Meta. Please check Twilio Console.'
      };
    }

    return {
      success: false,
      message: 'Failed to send WhatsApp OTP. Please try again.'
    };
  }
}

// Verify OTP
export async function verifyWhatsAppOTP(phone: string, userOtp: string): Promise<{ success: boolean; message: string }> {
  try {
    const stored = otpStore.get(phone);
    
    if (!stored) {
      return {
        success: false,
        message: 'OTP not found or expired'
      };
    }
    
    if (Date.now() > stored.expires) {
      otpStore.delete(phone);
      return {
        success: false,
        message: 'OTP has expired'
      };
    }
    
    if (stored.otp !== userOtp) {
      return {
        success: false,
        message: 'Invalid OTP'
      };
    }
    
    // OTP verified successfully, remove from store
    otpStore.delete(phone);
    
    return {
      success: true,
      message: 'Phone number verified successfully'
    };
  } catch (error) {
    console.error('OTP verification error:', error);
    return {
      success: false,
      message: 'Failed to verify OTP'
    };
  }
}

// Send WhatsApp notification for booking confirmations/updates
// Following WhatsApp Business API guidelines for session messaging vs template messaging
export async function sendWhatsAppNotification(
  phone: string, 
  templateType: 'confirmation' | 'cancellation' | 'reschedule' | 'reminder',
  variables: Record<string, string>
): Promise<{ success: boolean; message: string }> {
  try {
    if (!accountSid || !authToken) {
      return {
        success: false,
        message: 'WhatsApp service not configured properly'
      };
    }

    const formattedPhone = formatWhatsAppNumber(phone);

    // According to Meta WhatsApp Business Policy, ALL messages must use approved templates
    // Reference: https://developers.facebook.com/docs/whatsapp/cloud-api/guides/send-messages
    // Business-initiated conversations MUST use template messages
    
    let templateConfig;
    
    switch (templateType) {
      case 'confirmation':
        if (!WHATSAPP_TEMPLATES.BOOKING_CONFIRMATION.contentSid) {
          throw new Error('Booking confirmation template not configured. Please create and approve a template in Twilio Console.');
        }
        templateConfig = WHATSAPP_TEMPLATES.BOOKING_CONFIRMATION;
        break;
      case 'cancellation':
        if (!WHATSAPP_TEMPLATES.BOOKING_CANCELLATION.contentSid) {
          throw new Error('Booking cancellation template not configured. Please create and approve a template in Twilio Console.');
        }
        templateConfig = WHATSAPP_TEMPLATES.BOOKING_CANCELLATION;
        break;
      case 'reschedule':
        if (!WHATSAPP_TEMPLATES.BOOKING_RESCHEDULE.contentSid) {
          throw new Error('Booking reschedule template not configured. Please create and approve a template in Twilio Console.');
        }
        templateConfig = WHATSAPP_TEMPLATES.BOOKING_RESCHEDULE;
        break;
      case 'reminder':
        if (!WHATSAPP_TEMPLATES.BOOKING_REMINDER.contentSid) {
          throw new Error('Booking reminder template not configured. Please create and approve a template in Twilio Console.');
        }
        templateConfig = WHATSAPP_TEMPLATES.BOOKING_REMINDER;
        break;
      default:
        throw new Error(`Unsupported template type: ${templateType}`);
    }

    // Send using approved template only - Meta requires this for business-initiated messages
    // Ensure all variables are clean and properly formatted
    const templateVariables = {
      '1': variables.name || 'Customer',
      '2': variables.date || 'TBD', 
      '3': variables.time || 'TBD',
      '4': variables.rescheduleLink || 'booking/reschedule/placeholder',
      '5': variables.cancelLink || 'booking/cancel/placeholder'
    };

    console.log(`Sending WhatsApp ${templateType} with variables:`, templateVariables);

    const message = await client.messages.create({
      contentSid: templateConfig.contentSid,
      contentVariables: JSON.stringify(templateVariables),
      from: whatsappNumber, // whatsapp:+***********
      to: formattedPhone,   // whatsapp:+************
    });

    console.log(`WhatsApp ${templateType} notification sent:`, message.sid);
    console.log('Message status:', message.status);
    console.log('Template used:', templateConfig.contentSid);

    return {
      success: true,
      message: 'WhatsApp notification sent successfully'
    };

  } catch (error: any) {
    console.error('WhatsApp notification error:', error);
    console.error('Error code:', error.code);

    // Handle WhatsApp Business API specific errors
    if (error.code === 63016 || error.code === 63015) {
      return {
        success: false,
        message: 'User has not opted in to WhatsApp messages'
      };
    } else if (error.code === 63007) {
      // Message window expired - need template message
      return {
        success: false,
        message: 'Cannot send session message. 24-hour window expired.'
      };
    } else if (error.code === 63004) {
      // Message failed due to policy violation
      return {
        success: false,
        message: 'Message content violates WhatsApp policy'
      };
    }

    return {
      success: false,
      message: 'Failed to send WhatsApp notification'
    };
  }
}

// Send customer service follow-up message (triggered manually by CX team)
export async function sendCxFollowupMessage(phone: string, customerName: string, bookingDate: string, bookingTime: string): Promise<{ success: boolean; message: string }> {
  try {
    const formattedPhone = formatWhatsAppNumber(phone);
    
    if (!client) {
      throw new Error('Twilio client not initialized');
    }

    // CX followup template likely expects only 3 basic variables
    const templateVariables = {
      '1': customerName || 'Customer',
      '2': bookingDate || 'TBD',
      '3': bookingTime || 'TBD'
    };

    console.log(`Sending CX follow-up message to ${formattedPhone} with variables:`, templateVariables);

    const message = await client.messages.create({
      contentSid: WHATSAPP_TEMPLATES.CX_FOLLOWUP.contentSid,
      contentVariables: JSON.stringify(templateVariables),
      from: whatsappNumber,
      to: formattedPhone,
    });

    console.log(`CX follow-up message sent:`, message.sid);
    console.log('Message status:', message.status);

    return {
      success: true,
      message: 'Customer follow-up message sent successfully'
    };

  } catch (error: any) {
    console.error('CX follow-up message error:', error);
    console.error('Error code:', error.code);

    if (error.code === 63016 || error.code === 63015) {
      return {
        success: false,
        message: 'Customer has not opted in to WhatsApp messages'
      };
    } else if (error.code === 63005) {
      return {
        success: false,
        message: 'Template is pending approval or was rejected by Meta'
      };
    }

    return {
      success: false,
      message: 'Failed to send follow-up message'
    };
  }
}

// Clean up expired OTPs (run periodically)
export function cleanupExpiredOTPs() {
  const now = Date.now();
  const entries = Array.from(otpStore.entries());
  for (const [phone, data] of entries) {
    if (now > data.expires) {
      otpStore.delete(phone);
    }
  }
}

// Auto cleanup every 5 minutes
setInterval(cleanupExpiredOTPs, 5 * 60 * 1000);