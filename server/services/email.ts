import { MailService } from '@sendgrid/mail';
import type { InsertBooking, Settings, Booking } from "@shared/schema";

const mailService = new MailService();
const apiKey = process.env.SENDGRID_API_KEY || process.env.SENDGRID_API_KEY_ENV_VAR;

// Only set API key if it exists and is valid
if (apiKey && apiKey.startsWith('SG.')) {
  mailService.setApiKey(apiKey);
}

export async function sendEmail(
  bookingData: InsertBooking, 
  settings: Settings, 
  booking: Booking,
  notificationType: 'confirmation' | 'cancellation' | 'reschedule' | 'reminder' = 'confirmation'
): Promise<boolean> {
  try {
    if (!bookingData.email) {
      return false;
    }

    // Skip email sending if no valid API key is configured
    if (!apiKey || !apiKey.startsWith('SG.')) {
      console.log('Email sending skipped - no valid SendGrid API key configured');
      return true; // Return true to not block the booking process
    }

    const date = new Date(bookingData.date).toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

    // Generate action buttons for cancel/reschedule
    const baseUrl = process.env.BASE_URL || 'https://replit.app';
    const cancelUrl = `${baseUrl}/booking/cancel/${booking.id}`;
    const rescheduleUrl = `${baseUrl}/booking/reschedule/${booking.id}`;

    const actionButtonsHtml = `
      <div style="text-align: center; margin: 30px 0;">
        <a href="${rescheduleUrl}" style="display: inline-block; padding: 12px 24px; margin: 0 10px; background-color: #8B5CF6; color: white; text-decoration: none; border-radius: 8px; font-weight: bold;">Reschedule</a>
        <a href="${cancelUrl}" style="display: inline-block; padding: 12px 24px; margin: 0 10px; background-color: #EF4444; color: white; text-decoration: none; border-radius: 8px; font-weight: bold;">Cancel</a>
      </div>
    `;

    // Select appropriate template based on notification type
    let template = settings.emailTemplate; // fallback to legacy template
    let subject = "Your Kult Perfume Trial is Confirmed 💜";
    
    switch (notificationType) {
      case 'confirmation':
        template = settings.confirmationEmailTemplate || settings.emailTemplate;
        subject = "Your Kult Perfume Trial is Confirmed 💜";
        break;
      case 'cancellation':
        template = settings.cancellationEmailTemplate || settings.emailTemplate;
        subject = "Your Kult Perfume Trial has been Cancelled";
        break;
      case 'reschedule':
        template = settings.rescheduleEmailTemplate || settings.emailTemplate;
        subject = "Your Kult Perfume Trial has been Rescheduled 💜";
        break;
      case 'reminder':
        template = settings.reminderEmailTemplate || settings.emailTemplate;
        subject = "Reminder: Your Kult Perfume Trial is Tomorrow 💜";
        break;
    }

    // Replace template variables
    const emailContent = template
      .replace(/{{name}}/g, bookingData.name)
      .replace(/{{date}}/g, date)
      .replace(/{{time}}/g, bookingData.timeSlot)
      .replace(/{{address}}/g, bookingData.address)
      .replace(/{{actionButtons}}/g, actionButtonsHtml);

    await mailService.send({
      to: bookingData.email,
      from: process.env.FROM_EMAIL || "<EMAIL>",
      subject: subject,
      text: emailContent,
      html: emailContent.replace(/\n/g, '<br>')
    });

    return true;
  } catch (error) {
    console.error('Email sending error:', error);
    return false;
  }
}
