import { db } from '../db';
import { settings } from '../../shared/schema';
import { eq } from 'drizzle-orm';

interface BookingData {
  id: number;
  name: string;
  phone: string;
  address: string;
  postalCode: string;
  date: Date;
  timeSlot: string;
  status: string;
  service: string;
  repAssigned: string | null;
  createdAt: Date;
  updatedAt: Date;
}

export class WebhookService {
  private async getWebhookUrl(): Promise<string> {
    const currentSettings = await db.select().from(settings).limit(1);
    const settingsData = currentSettings[0];
    
    return settingsData?.webhookUrl || "https://hooks.zapier.com/hooks/catch/18959219/ub8lfib/";
  }

  private formatBookingData(booking: BookingData) {
    // Convert UTC to IST (UTC + 5:30)
    const convertToIST = (utcDate: Date): string => {
      const istOffset = 5.5 * 60 * 60 * 1000; // 5 hours 30 minutes in milliseconds
      const istDate = new Date(utcDate.getTime() + istOffset);
      return istDate.toISOString().replace('Z', '+05:30');
    };

    return {
      id: booking.id.toString(),
      name: booking.name,
      phone: booking.phone,
      address: booking.address,
      postalCode: booking.postalCode,
      date: booking.date.toISOString().split('T')[0], // Standard API date format
      timeSlot: booking.timeSlot,
      status: booking.status,
      service: booking.service,
      repAssigned: booking.repAssigned || 'Unassigned',
      createdAt: convertToIST(booking.createdAt),
      updatedAt: convertToIST(booking.updatedAt),
    };
  }

  async sendBookingData(booking: BookingData): Promise<boolean> {
    try {
      const webhookUrl = await this.getWebhookUrl();
      
      if (!webhookUrl) {
        console.log('Webhook URL not configured');
        return false;
      }

      const payload = this.formatBookingData(booking);
      
      console.log('Sending booking data to webhook:', {
        url: webhookUrl,
        bookingId: booking.id,
      });

      const response = await fetch(webhookUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        console.error('Failed to send data to webhook:', {
          status: response.status,
          statusText: response.statusText,
          bookingId: booking.id,
        });
        return false;
      }

      console.log('Successfully sent booking data to webhook:', booking.id);
      return true;
    } catch (error) {
      console.error('Error sending booking data to webhook:', error);
      return false;
    }
  }
}

export const webhookService = new WebhookService();