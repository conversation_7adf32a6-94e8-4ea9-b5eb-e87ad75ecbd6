import type { Express, Request } from "express";
import { createServer, type Server } from "http";
import session from "express-session";
import { storage } from "./storage";
import { pool, db } from "./db";
import { insertBookingSchema, insertSalesRepSchema, insertPostalCodeSchema, insertSettingsSchema, insertUserSchema, userTracking, longTermUserSessions, bookings, userSessions, eventLogs, postalCodeAttempts, serviceAreas } from "@shared/schema";
import { eq, sql, and, gte, lt, ne, isNotNull, isNull, or, desc } from "drizzle-orm";
import { sendEmail } from "./services/email";
import { sendSMS } from "./services/sms";
import { sendOTP, verifyOTP } from "./services/otp";
import { notifyBookingCreated, notifyBookingUpdated, notifyBookingDeleted, notifyUserCreated, notifyUserUpdated, notifySettingsUpdated } from "./websocket";
import twilio from "twilio";
import { webhookService } from "./services/webhook";
import { GA4Service } from './ga4-service';
import { MetaService } from './meta-service';
import { 
  normalizeUTMParams, 
  parseMetaCampaign, 
  getSessionTrackingStartDate, 
  isPostSessionTracking, 
  normalizeCampaignSource,
  FUNNEL_STAGES 
} from './utils/utm-parser';

// Store deployment notification clients
const deploymentClients = new Map<number, any>();


// Extend Express Request type to include session
declare module 'express-session' {
  interface SessionData {
    isAdmin?: boolean;
    isSupport?: boolean;
    userRole?: string;
  }
}


// Initialize GA4 service
let ga4Service: GA4Service | null = null;
let metaService: MetaService | null = null;

const initializeGA4Service = () => {
  try {
    const propertyId = process.env.GA4_PROPERTY_ID;
    const serviceAccountKey = process.env.GA4_SERVICE_ACCOUNT_KEY;
    const measurementId = process.env.VITE_GA_MEASUREMENT_ID;
    
    if (propertyId && serviceAccountKey) {
      ga4Service = new GA4Service({
        propertyId,
        serviceAccountKey: JSON.parse(serviceAccountKey),
      });
      console.log('GA4 service initialized successfully with service account');
    } else if (measurementId) {
      console.log('GA4 server-side API credentials not provided - using client-side tracking only');
      console.log('Measurement ID available:', measurementId);
    } else {
      console.log('GA4 credentials not provided - GA4 features will be disabled');
    }
  } catch (error) {
    console.error('Failed to initialize GA4 service:', error);
  }
};



export async function registerRoutes(app: Express): Promise<Server> {
  // Initialize GA4 service
  initializeGA4Service();
  
  // Note: Pending reassignments are only created by admin dashboard actions
  // They are not automatically resolved to prevent interfering with admin workflow
  
  // Initialize Meta service
  const initializeMetaService = () => {
    try {
      const accessToken = process.env.META_ACCESS_TOKEN;
      const adAccountId = process.env.META_AD_ACCOUNT_ID;
      
      if (accessToken && adAccountId) {
        metaService = new MetaService({
          accessToken,
          adAccountId,
        });
        console.log('Meta service initialized successfully');
      } else {
        console.warn('Meta credentials not provided');
      }
    } catch (error) {
      console.error('Failed to initialize Meta service:', error);
    }
  };
  
  initializeMetaService();
  // Configure persistent session middleware with database storage
  const connectPgSimple = await import('connect-pg-simple');
  const connectPg = connectPgSimple.default(session);
  
  app.use(session({
    store: new connectPg({
      pool: pool,                       // Use existing PostgreSQL pool
      tableName: 'session_store',       // Dedicated table for persistent sessions
      createTableIfMissing: false,      // Table already created manually
    }),
    secret: process.env.SESSION_SECRET || 'kult-admin-secret-key-2025',
    resave: false,
    saveUninitialized: false,
    rolling: true,                      // Reset expiry on each request (like Google)
    cookie: {
      secure: false,                                // Allow cookies over HTTP and HTTPS for compatibility
      maxAge: 30 * 24 * 60 * 60 * 1000,            // 30 days persistent login
      httpOnly: true,                               // Prevent XSS attacks
      sameSite: 'lax',                             // CSRF protection
      domain: undefined                             // Allow cookies on any domain (replit.dev, kult.app, etc)
    }
  }));
  // Postal code validation
  app.get("/api/postal-codes/validate/:code", async (req, res) => {
    try {
      const { code } = req.params;
      const isValid = await storage.isPostalCodeValid(code);
      res.json({ valid: isValid });
    } catch (error) {
      res.status(500).json({ message: "Error validating postal code" });
    }
  });

  // Get postal code details
  app.get("/api/postal-codes/:code", async (req, res) => {
    try {
      const { code } = req.params;
      const postalCodeDetails = await storage.getPostalCodeDetails(code);
      if (!postalCodeDetails) {
        return res.status(404).json({ message: "Postal code not found" });
      }
      res.json(postalCodeDetails);
    } catch (error) {
      res.status(500).json({ message: "Error fetching postal code details" });
    }
  });

  // Get booking configuration (public endpoint for frontend)
  app.get("/api/booking-config", async (req, res) => {
    try {
      const settings = await storage.getSettings();
      res.json({
        maxBookingDaysAhead: settings.maxBookingDaysAhead,
        appointmentDuration: settings.appointmentDuration,
        timeSlotInterval: settings.timeSlotInterval
      });
    } catch (error) {
      res.status(500).json({ message: "Error fetching booking configuration" });
    }
  });

  // Handle availability endpoint without date parameter
  app.get("/api/availability", async (req, res) => {
    return res.status(400).json({ 
      error: "Date parameter is required",
      available: false, 
      timeSlots: [] 
    });
  });

  // Get available time slots for a specific date
  app.get("/api/availability/:date", async (req, res) => {
    try {
      const { date } = req.params;
      const { excludeBooking } = req.query; // Optional booking ID to exclude from conflict checking
      const targetDate = new Date(date);
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      
      // Get settings for booking rules
      const settings = await storage.getSettings();
      
      // Check if date is within allowed booking window
      const maxBookingDate = new Date(today);
      maxBookingDate.setDate(today.getDate() + settings.maxBookingDaysAhead);
      
      if (targetDate < today || targetDate > maxBookingDate) {
        return res.json({ 
          available: false, 
          timeSlots: [], 
          message: `Bookings are only available for the next ${settings.maxBookingDaysAhead} days`
        });
      }
      
      // Note: Booking count no longer used for Max Kits Per Day rule (removed July 2, 2025)

      // Generate dynamic time slots based on representative availability and global rules
      // Pass excludeBooking parameter to avoid conflicts with the booking being edited
      const excludeBookingId = excludeBooking ? parseInt(excludeBooking as string) : undefined;
      const timeSlots = await storage.generateAvailableTimeSlots(targetDate, excludeBookingId);

      // Check availability for each time slot and apply minimum advance hours filter
      const availableSlots = [];
      
      // Get current IST time for proper timezone comparison
      const nowUTC = new Date();
      const nowIST = new Date(nowUTC.getTime() + (5.5 * 60 * 60 * 1000));
      
      for (const slot of timeSlots) {
        // Parse slot time and check if it meets minimum advance hours requirement
        const [time, period] = slot.split(' ');
        const [hours, minutes] = time.split(':').map(Number);
        const slotHours = period === 'PM' && hours !== 12 ? hours + 12 : (period === 'AM' && hours === 12 ? 0 : hours);
        
        // Create slot datetime in IST - target date is already in IST format from frontend
        const slotDateTimeIST = new Date(date + 'T00:00:00.000Z'); // Parse as UTC first
        slotDateTimeIST.setUTCHours(slotHours, minutes, 0, 0); // Set time in UTC (treating as IST)
        
        // Check minimum advance hours requirement using IST times
        const hoursUntilSlot = (slotDateTimeIST.getTime() - nowIST.getTime()) / (1000 * 60 * 60);
        const minAdvanceHours = settings.minAdvanceHours || 1;
        
        if (hoursUntilSlot >= minAdvanceHours) {
          const availableReps = await storage.getAvailableReps(targetDate, slot);
          if (availableReps.length > 0) {
            availableSlots.push(slot);
          } else {
            console.log(`🚫 Slot ${slot} on ${date} blocked: No available reps (${availableReps.length} reps available)`);
          }
        } else {
          console.log(`🚫 Slot ${slot} on ${date} blocked: Too close (${hoursUntilSlot.toFixed(1)}h < ${minAdvanceHours}h required)`);
        }
      }


      
      // Disable caching for this endpoint
      res.set('Cache-Control', 'no-store, no-cache, must-revalidate, private');
      res.set('Pragma', 'no-cache');
      res.set('Expires', '0');
      
      res.json({ 
        available: availableSlots.length > 0, 
        timeSlots: availableSlots
      });
    } catch (error) {
      console.error("Availability error:", error);
      res.status(500).json({ message: "Error checking availability", error: error instanceof Error ? error.message : String(error) });
    }
  });

  // Create booking
  app.post("/api/bookings", async (req, res) => {
    try {
      const bookingData = insertBookingSchema.parse(req.body);
      
      // Extract traffic source data from session (including ALL sources, not just UTM)
      const sessionId = req.headers['x-session-id'] as string;
      let utmData = null;
      
      console.log('🔍 Booking Traffic Source Capture');
      console.log('   - Session ID:', sessionId);
      console.log('   - Has session ID:', !!sessionId);
      
      if (sessionId) {
        try {
          // Get the most recent event with any tracking data for this session
          const trafficQuery = `
            SELECT event_data, referrer, user_agent
            FROM event_logs 
            WHERE session_id = $1 
              AND (event_data IS NOT NULL OR referrer IS NOT NULL)
            ORDER BY timestamp DESC 
            LIMIT 1
          `;
          
          const trafficResult = await pool.query(trafficQuery, [sessionId]);
          console.log('🔍 Traffic Query Results:', trafficResult.rows.length, 'rows found');
          
          if (trafficResult.rows.length > 0) {
            const row = trafficResult.rows[0];
            const eventData = row.event_data ? JSON.parse(row.event_data) : {};
            const referrer = row.referrer || '';
            const userAgent = row.user_agent || '';
            
            console.log('🔍 Raw Traffic Event Data:', { eventData, referrer, userAgent });
            
            // Enhance event data with referrer information for better source detection
            const enhancedEventData = {
              ...eventData,
              referrer: referrer,
              user_agent: userAgent
            };
            
            // Apply source normalization for consistent tracking
            const normalizedUTM = normalizeUTMParams(eventData);
            
            utmData = {
              utmSource: normalizedUTM.source,
              utmMedium: normalizedUTM.medium,
              utmCampaign: normalizedUTM.campaign,
              utmContent: normalizedUTM.content,
              utmTerm: normalizedUTM.term,
              // Create a comprehensive traffic source for display (handles ALL sources)
              trafficSource: generateTrafficSource(enhancedEventData),
              // Store session ID for end-to-end funnel tracking
              sessionId: sessionId
            };
            
            console.log('🔍 Processed Traffic Data for Booking:', utmData);
          } else {
            // If no session data found, create default direct visit record
            console.log('🔍 No session data found, marking as Direct Visit');
            utmData = {
              utmSource: null,
              utmMedium: null,
              utmCampaign: null,
              utmContent: null,
              utmTerm: null,
              trafficSource: 'Direct Visit',
              sessionId: sessionId || null
            };
          }
        } catch (utmError) {
          console.warn('Error extracting UTM data:', utmError);
        }
      }
      
      // Validate postal code
      const isValidPostal = await storage.isPostalCodeValid(bookingData.postalCode);
      if (!isValidPostal) {
        return res.status(400).json({ message: "Postal code not in service area" });
      }

      // Check availability
      const bookingDate = new Date(bookingData.date);
      const settings = await storage.getSettings();
      
      // Note: Max Kits Per Day rule was removed on July 2, 2025 - bookings are now only limited by rep availability

      // Double-check availability before assignment (prevent race conditions)
      const availableReps = await storage.getAvailableReps(bookingDate, bookingData.timeSlot);
      if (availableReps.length === 0) {
        console.error(`🚫 Booking blocked: No available reps for ${bookingData.timeSlot} on ${bookingDate.toISOString()}`);
        return res.status(400).json({ message: "No representatives available for this time slot" });
      }

      // Assign representative using round-robin logic
      const assignedRepName = await storage.assignRepWithRoundRobin(bookingDate, bookingData.timeSlot);
      if (!assignedRepName) {
        console.error(`🚫 Booking blocked: Round-robin assignment failed for ${bookingData.timeSlot} on ${bookingDate.toISOString()}`);
        return res.status(400).json({ message: "No representatives available for this time slot" });
      }

      console.log(`✅ Booking approved: ${bookingData.timeSlot} on ${bookingDate.toISOString()} assigned to ${assignedRepName}`);

      // Note: User bookings should never create "pending_reassignment" status
      // Only admin dashboard actions should create this status

      // Create booking with assigned rep and UTM data
      const booking = await storage.createBooking({
        ...bookingData,
        repAssigned: assignedRepName,
        ...utmData
      } as any);

      // Send notifications
      try {
        if (bookingData.email) {
          await sendEmail(bookingData, settings, booking);
        }
        await sendSMS(bookingData, settings, booking, 'confirmation');
      } catch (notificationError) {
        console.error("Notification error:", notificationError);
        // Don't fail the booking if notifications fail
      }

      // Notify admin clients via WebSocket
      notifyBookingCreated(booking);

      // Sync to webhook (async, don't wait)
      webhookService.sendBookingData(booking).catch((error: any) => {
        console.error('Webhook sync error:', error);
      });

      // CRITICAL FIX: Mark longTermUserSession as converted for UTM campaign tracking
      if (sessionId) {
        try {
          await db
            .update(longTermUserSessions)
            .set({ converted: true })
            .where(eq(longTermUserSessions.sessionId, sessionId));
          
          console.log(`✅ UTM Conversion tracked: Session ${sessionId} marked as converted`);
        } catch (conversionError) {
          console.error('Error marking session as converted:', conversionError);
          // Don't fail booking if conversion tracking fails
        }
      }

      res.json(booking);
    } catch (error) {
      console.error("CRITICAL BOOKING CREATION ERROR:", error);
      console.error("Error details:", {
        message: error.message,
        stack: error.stack,
        name: error.name,
        issues: error.issues
      });
      if (error.issues) {
        res.status(400).json({ message: "Invalid booking data", errors: error.issues });
      } else {
        res.status(500).json({ message: "Error creating booking", error: error.message || String(error) });
      }
    }
  });

  // Get booking details
  app.get("/api/bookings/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const booking = await storage.getBooking(id);
      if (!booking) {
        return res.status(404).json({ message: "Booking not found" });
      }
      res.json(booking);
    } catch (error) {
      res.status(500).json({ message: "Error fetching booking" });
    }
  });

  // Public: Get single booking by token (for WhatsApp links)
  app.get("/api/bookings/token/:token", async (req, res) => {
    try {
      const token = req.params.token;
      const booking = await storage.getBookingByToken(token);
      
      if (!booking) {
        return res.status(404).json({ message: "Booking not found" });
      }
      
      res.json(booking);
    } catch (error) {
      console.error("Error fetching booking:", error);
      res.status(500).json({ message: "Error fetching booking" });
    }
  });

  // Legacy endpoint for admin dashboard (keep for internal use)
  app.get("/api/bookings/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const booking = await storage.getBooking(id);
      
      if (!booking) {
        return res.status(404).json({ message: "Booking not found" });
      }
      
      res.json(booking);
    } catch (error) {
      console.error("Error fetching booking:", error);
      res.status(500).json({ message: "Error fetching booking" });
    }
  });

  // Update booking (reschedule/cancel)
  app.put("/api/bookings/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const updates = req.body;
      
      // Check if booking exists and if it's not too late to modify
      const booking = await storage.getBooking(id);
      if (!booking) {
        return res.status(404).json({ message: "Booking not found" });
      }

      // Check time restrictions based on settings
      const bookingSettings = await storage.getSettings();
      const now = new Date();
      const bookingDateTime = new Date(booking.date);
      
      // Parse time slot to get hour and minute
      const timeParts = booking.timeSlot.match(/(\d+):(\d+)\s*(AM|PM)/i);
      if (timeParts) {
        let hour = parseInt(timeParts[1]);
        const minute = parseInt(timeParts[2]);
        const period = timeParts[3].toUpperCase();
        
        if (period === 'PM' && hour !== 12) hour += 12;
        if (period === 'AM' && hour === 12) hour = 0;
        
        bookingDateTime.setHours(hour, minute, 0, 0);
      }
      
      const hoursUntilBooking = (bookingDateTime.getTime() - now.getTime()) / (1000 * 60 * 60);
      
      // Check reschedule restrictions
      if ((updates.date || updates.timeSlot) && bookingSettings.rescheduleHoursLimit > 0) {
        if (hoursUntilBooking < bookingSettings.rescheduleHoursLimit) {
          return res.status(400).json({ 
            message: `Cannot reschedule within ${bookingSettings.rescheduleHoursLimit} hours of appointment time` 
          });
        }
      }
      
      // Check cancellation restrictions
      if (updates.status === 'cancelled' && bookingSettings.cancelHoursLimit > 0) {
        if (hoursUntilBooking < bookingSettings.cancelHoursLimit) {
          return res.status(400).json({ 
            message: `Cannot cancel within ${bookingSettings.cancelHoursLimit} hours of appointment time` 
          });
        }
      }

      // If rescheduling, check new availability and assign with round robin
      if (updates.date || updates.timeSlot) {
        const newDate = new Date(updates.date || booking.date);
        const newTimeSlot = updates.timeSlot || booking.timeSlot;
        
        const assignedRepName = await storage.assignRepWithRoundRobin(newDate, newTimeSlot);
        if (!assignedRepName) {
          return res.status(400).json({ message: "No availability for the requested time" });
        }
        
        updates.repAssigned = assignedRepName;
        
        // Convert date string to proper Date object for database storage
        if (updates.date) {
          // Handle both ISO string and date-only formats
          let dateObj;
          if (typeof updates.date === 'string') {
            // If it's a date-only string (YYYY-MM-DD), add time component
            if (updates.date.length === 10) {
              dateObj = new Date(updates.date + 'T12:00:00.000Z');
            } else {
              dateObj = new Date(updates.date);
            }
          } else {
            dateObj = new Date(updates.date);
          }
          
          if (isNaN(dateObj.getTime())) {
            return res.status(400).json({ message: "Invalid date format" });
          }
          updates.date = dateObj;
        }
      }

      // Get original booking for notification comparison
      const originalBooking = await storage.getBooking(id);
      if (!originalBooking) {
        return res.status(404).json({ message: "Booking not found" });
      }

      const updatedBooking = await storage.updateBooking(id, updates, 'customer');
      if (!updatedBooking) {
        return res.status(404).json({ message: "Booking not found" });
      }

      // Send notifications for any changes
      const notificationSettings = await storage.getSettings();
      
      // Convert dates to strings for comparison if they're Date objects
      const originalDate = originalBooking.date instanceof Date ? originalBooking.date.toISOString().split('T')[0] : originalBooking.date;
      const updatedDate = updatedBooking.date instanceof Date ? updatedBooking.date.toISOString().split('T')[0] : updatedBooking.date;
      
      const hasChanges = 
        originalDate !== updatedDate ||
        originalBooking.timeSlot !== updatedBooking.timeSlot ||
        originalBooking.repAssigned !== updatedBooking.repAssigned ||
        originalBooking.status !== updatedBooking.status;
      
      console.log(`Customer booking update - checking for changes:`, {
        bookingId: id,
        originalDate,
        updatedDate,
        dateChanged: originalDate !== updatedDate,
        originalTimeSlot: originalBooking.timeSlot,
        updatedTimeSlot: updatedBooking.timeSlot,
        timeChanged: originalBooking.timeSlot !== updatedBooking.timeSlot,
        originalStatus: originalBooking.status,
        updatedStatus: updatedBooking.status,
        statusChanged: originalBooking.status !== updatedBooking.status,
        hasChanges
      });

      if (hasChanges) {
        console.log(`Customer booking changes detected, sending notifications`);
        
        // Send email notification
        try {
          await sendEmail(updatedBooking, notificationSettings, updatedBooking);
          console.log("Customer update email notification sent successfully");
        } catch (emailError) {
          console.error("Failed to send customer update email notification:", emailError);
        }

        // Send SMS notification with proper type
        try {
          let notificationType: 'confirmation' | 'cancellation' | 'reschedule' | 'reminder' = 'confirmation';
          
          if (originalBooking.status !== updatedBooking.status) {
            if (updatedBooking.status === 'cancelled') {
              notificationType = 'cancellation';
            } else if (updatedBooking.status === 'confirmed' && originalBooking.status === 'cancelled') {
              notificationType = 'confirmation';
            }
          } else if (originalDate !== updatedDate || originalBooking.timeSlot !== updatedBooking.timeSlot) {
            notificationType = 'reschedule';
          }
          
          await sendSMS(updatedBooking, notificationSettings, updatedBooking, notificationType);
          console.log(`Customer update SMS notification sent successfully (${notificationType})`);
        } catch (smsError) {
          console.error("Failed to send customer update SMS notification:", smsError);
        }

        // Sync to webhook for any booking changes including cancellation (async, don't wait)
        webhookService.sendBookingData(updatedBooking).catch((error: any) => {
          console.error('Webhook sync error:', error);
        });
      } else {
        console.log(`No customer booking changes detected, skipping notifications`);
      }

      res.json(updatedBooking);
    } catch (error) {
      console.error("Error updating booking:", error);
      res.status(500).json({ message: "Error updating booking", error: error instanceof Error ? error.message : String(error) });
    }
  });

  // Admin postal codes management
  app.get("/api/admin/postal-codes", async (req, res) => {
    try {
      const postalCodes = await storage.getPostalCodes();
      res.json(postalCodes);
    } catch (error) {
      res.status(500).json({ message: "Error fetching postal codes" });
    }
  });

  app.post("/api/admin/postal-codes", async (req, res) => {
    try {
      const { code, city, state } = req.body;
      const postalCode = await storage.createPostalCode({ code, city, state });
      res.json(postalCode);
    } catch (error) {
      res.status(400).json({ message: "Error creating postal code" });
    }
  });

  app.post("/api/admin/postal-codes/bulk", async (req, res) => {
    try {
      const { codes } = req.body;
      const lines = codes.split('\n').filter((line: string) => line.trim());
      let added = 0;
      let skipped = 0;

      for (const line of lines) {
        const parts = line.trim().split(/\s+/);
        if (parts.length >= 3) {
          const [code, city, ...stateParts] = parts;
          const state = stateParts.join(' ');
          try {
            await storage.createPostalCode({ code, city, state });
            added++;
          } catch {
            skipped++;
          }
        }
      }

      res.json({ added, skipped, total: lines.length });
    } catch (error) {
      res.status(400).json({ message: "Error processing bulk postal codes" });
    }
  });

  app.put("/api/admin/postal-codes/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const updates = req.body;
      const postalCode = await storage.updatePostalCode(id, updates);
      if (!postalCode) {
        return res.status(404).json({ message: "Postal code not found" });
      }
      res.json(postalCode);
    } catch (error) {
      res.status(400).json({ message: "Error updating postal code" });
    }
  });

  // Universal Authentication (supports all roles)
  app.post("/api/auth/login", async (req, res) => {
    try {
      const { username, password } = req.body;
      
      console.log("Login attempt:", { username, password: password ? "provided" : "missing" });
      
      // Check hardcoded admin first
      if (username === "admin" && password === "KultAdmin2025!@#$") {
        if (req.session) {
          (req.session as any).isAdmin = true;
          (req.session as any).userRole = "admin";
          res.json({ success: true, role: "admin" });
        } else {
          res.status(500).json({ message: "Session error" });
        }
        return;
      }
      
      // Check database users
      const user = await storage.getUserByUsername(username);
      if (user && user.isActive) {
        // Use bcrypt to verify the password
        const bcrypt = await import('bcrypt');
        const isValidPassword = await bcrypt.compare(password, user.password);
        
        if (isValidPassword) {
          if (req.session) {
            (req.session as any).isAdmin = user.role === "admin";
            (req.session as any).isSupport = user.role === "support";
            (req.session as any).isMarketing = user.role === "marketing";
            (req.session as any).isSales = user.role === "sales";
            (req.session as any).userRole = user.role;
            (req.session as any).username = user.username;
            res.json({ success: true, role: user.role, name: user.name });
          } else {
            res.status(500).json({ message: "Session error" });
          }
          return;
        }
      }
      
      res.status(401).json({ message: "Invalid credentials" });
    } catch (error) {
      console.error("Login error:", error);
      res.status(500).json({ message: "Login error" });
    }
  });

  // Admin login using database users
  app.post("/api/admin/login", async (req, res) => {
    try {
      const { username, password } = req.body;
      
      console.log("Admin login attempt:", { username, password: password ? "provided" : "missing" });
      
      // Check database for user
      const user = await storage.getUserByUsername(username);
      console.log("User found:", user ? { id: user.id, username: user.username, role: user.role, hasPassword: !!user.password } : "null");
      
      if (!user) {
        console.log("User not found in database");
        return res.status(401).json({ message: "Invalid credentials" });
      }

      // Check if user has admin access (admin, support, or marketing roles can access admin dashboard)
      const allowedRoles = ['admin', 'support', 'marketing'];
      if (!allowedRoles.includes(user.role)) {
        console.log("User role is not allowed:", user.role);
        return res.status(401).json({ message: "Invalid credentials" });
      }

      // Verify password
      console.log("Attempting password verification");
      const bcrypt = await import('bcrypt');
      const isValidPassword = await bcrypt.compare(password, user.password);
      console.log("Password verification result:", isValidPassword);
      
      if (!isValidPassword) {
        console.log("Password verification failed");
        return res.status(401).json({ message: "Invalid credentials" });
      }

      // Set session based on user role with persistent login metadata
      if (req.session) {
        (req.session as any).isAdmin = user.role === "admin";
        (req.session as any).isSupport = user.role === "support";
        (req.session as any).isMarketing = user.role === "marketing";
        (req.session as any).userRole = user.role;
        (req.session as any).username = user.username;
        (req.session as any).userId = user.id;
        
        // Add persistent session metadata (like Google/Facebook)
        (req.session as any).loginTime = new Date().toISOString();
        (req.session as any).lastActivity = new Date().toISOString();
        (req.session as any).userAgent = req.get('user-agent');
        (req.session as any).ipAddress = req.ip;
        (req.session as any).persistent = true; // Mark as long-lived session

        // Track admin session
        try {
          await storage.createAdminSession(
            user.id,
            req.sessionID,
            req.ip,
            req.get('user-agent')
          );
        } catch (sessionError) {
          console.error("Failed to create admin session:", sessionError);
        }

        res.json({ success: true, role: user.role, name: user.name });
      } else {
        res.status(500).json({ message: "Session error" });
      }
    } catch (error) {
      console.error("Login error:", error);
      res.status(500).json({ message: "Login error" });
    }
  });

  // Admin logout to track session end
  app.post("/api/admin/logout", async (req, res) => {
    try {
      if (req.session && req.sessionID) {
        // End admin session tracking
        await storage.endAdminSession(req.sessionID);
        
        // Destroy session
        req.session.destroy((err) => {
          if (err) {
            console.error("Session destruction error:", err);
            return res.status(500).json({ message: "Logout error" });
          }
          res.json({ success: true });
        });
      } else {
        res.json({ success: true });
      }
    } catch (error) {
      console.error("Logout error:", error);
      res.status(500).json({ message: "Logout error" });
    }
  });



  // Support Team Authentication (now uses universal auth)
  app.post("/api/support/login", async (req, res) => {
    try {
      const { username, password } = req.body;
      
      console.log("Support login attempt:", { username, password: password ? "provided" : "missing" });
      
      // Check database users for support role
      const user = await storage.getUserByUsername(username);
      if (user && user.isActive && user.role === "support") {
        // Use bcrypt to verify the password
        const bcrypt = await import('bcrypt');
        const isValidPassword = await bcrypt.compare(password, user.password);
        
        if (isValidPassword) {
          if (req.session) {
            (req.session as any).isSupport = true;
            (req.session as any).userRole = "support";
            (req.session as any).username = user.username;
            res.json({ success: true, role: user.role, name: user.name });
          } else {
            res.status(500).json({ message: "Session error" });
          }
          return;
        }
      }
      
      res.status(401).json({ message: "Invalid credentials" });
    } catch (error) {
      console.error("Support login error:", error);
      res.status(500).json({ message: "Login error" });
    }
  });

  // Support logout
  app.post("/api/support/logout", (req, res) => {
    req.session.destroy((err) => {
      if (err) {
        return res.status(500).json({ message: "Logout error" });
      }
      res.json({ success: true });
    });
  });

  // Middleware to check admin authentication with automatic session refresh
  const requireAdmin = (req: any, res: any, next: any) => {
    const session = req.session as any;
    if (!req.session || !(session.isAdmin || session.isSupport || session.isMarketing)) {
      return res.status(401).json({ message: "Admin access required" });
    }
    
    // Automatically refresh session on every authenticated request (like Google)
    req.session.touch();
    
    // Update last activity timestamp for monitoring
    req.session.lastActivity = new Date().toISOString();
    
    next();
  };

  // Middleware to check support team authentication
  const requireSupport = (req: any, res: any, next: any) => {
    if (!req.session || (!(req.session as any).isAdmin && !(req.session as any).isSupport)) {
      return res.status(401).json({ message: "Support access required" });
    }
    next();
  };

  // Middleware to check marketing team authentication  
  const requireMarketing = (req: any, res: any, next: any) => {
    if (!req.session || (!(req.session as any).isAdmin && !(req.session as any).isMarketing)) {
      return res.status(401).json({ message: "Marketing access required" });
    }
    next();
  };

  // Get current user details with permissions
  // Session status check with auto-restoration (like Google's session management)
  app.get("/api/auth/session-status", (req, res) => {
    try {
      const session = req.session as any;
      
      if (req.session && (session.isAdmin || session.isSupport || session.isMarketing)) {
        // Session is valid, refresh it automatically
        req.session.touch();
        session.lastActivity = new Date().toISOString();
        
        res.json({
          authenticated: true,
          role: session.userRole,
          username: session.username,
          loginTime: session.loginTime,
          lastActivity: session.lastActivity,
          persistent: session.persistent || false
        });
      } else {
        res.json({
          authenticated: false
        });
      }
    } catch (error) {
      console.error("Session status check error:", error);
      res.json({ authenticated: false });
    }
  });

  app.get("/api/admin/current-user", requireAdmin, async (req, res) => {
    try {
      const username = (req.session as any)?.username;
      if (!username) {
        return res.status(401).json({ message: "No user session found" });
      }

      const user = await storage.getUserByUsername(username);
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }

      res.json(user);
    } catch (error) {
      console.error("Error fetching current user:", error);
      res.status(500).json({ message: "Error fetching current user" });
    }
  });

  // Get all admin sessions
  app.get("/api/admin/sessions", requireAdmin, async (req, res) => {
    try {
      const sessions = await storage.getAllAdminSessions();
      res.json(sessions);
    } catch (error) {
      console.error("Error fetching all admin sessions:", error);
      res.status(500).json({ message: "Error fetching admin sessions" });
    }
  });

  // Get admin sessions for a user
  app.get("/api/admin/sessions/:userId", requireAdmin, async (req, res) => {
    try {
      const userId = parseInt(req.params.userId);
      const sessions = await storage.getAdminSessions(userId);
      res.json(sessions);
    } catch (error) {
      console.error("Error fetching admin sessions:", error);
      res.status(500).json({ message: "Error fetching admin sessions" });
    }
  });

  // Helper function to generate simplified traffic source
  const generateTrafficSource = (eventData: any) => {
    const source = (eventData.utm_source || '').toLowerCase();
    const medium = (eventData.utm_medium || '').toLowerCase();
    const campaign = eventData.utm_campaign || '';
    const content = eventData.utm_content || '';
    const term = eventData.utm_term || '';
    
    // Build UTM link if UTM parameters exist
    let utmLink = '';
    if (source || medium || campaign || content || term) {
      const params = [];
      if (source) params.push(`utm_source=${source}`);
      if (medium) params.push(`utm_medium=${medium}`);
      if (campaign) params.push(`utm_campaign=${campaign}`);
      if (content) params.push(`utm_content=${content}`);
      if (term) params.push(`utm_term=${term}`);
      
      if (params.length > 0) {
        utmLink = `?${params.join('&')}`;
      }
    }
    
    // Generate traffic source name
    let sourceName = '';
    if (source === 'meta' || source === 'facebook') {
      sourceName = 'Facebook Ad';
    } else if (source === 'google') {
      if (medium === 'cpc' || medium === 'ppc') {
        sourceName = 'Google Ads';
      } else {
        sourceName = 'Google Search';
      }
    } else if (source === 'instagram') {
      sourceName = 'Instagram Ad';
    } else if (source === 'app' || source === 'mobile_app') {
      sourceName = 'Mobile App Banner';
    } else if (source === 'qr' || source === 'qr_code') {
      sourceName = 'QR Code';
    } else if (source === 'direct' || !source) {
      // Check if this is truly direct or has referrer info
      const referrer = eventData.referrer || '';
      if (referrer && !referrer.includes('localhost') && !referrer.includes('replit.dev')) {
        try {
          const refUrl = new URL(referrer);
          sourceName = `Referral from ${refUrl.hostname}`;
        } catch {
          sourceName = `Referral from ${referrer}`;
        }
      } else {
        sourceName = 'Direct Visit';
      }
    } else if (medium === 'referral') {
      sourceName = `Referral from ${source}`;
    } else if (medium === 'social') {
      sourceName = `Social Media (${source})`;
    } else if (medium === 'email') {
      sourceName = `Email (${source})`;
    } else if (medium === 'sms') {
      sourceName = `SMS (${source})`;
    } else if (campaign && source) {
      sourceName = `${source} Campaign: ${campaign}`;
    } else if (source) {
      sourceName = `${source}`;
    } else {
      // Last resort: use referrer or mark as direct
      const referrer = eventData.referrer || '';
      if (referrer) {
        try {
          const refUrl = new URL(referrer);
          sourceName = `External Link from ${refUrl.hostname}`;
        } catch {
          sourceName = `External Link`;
        }
      } else {
        sourceName = 'Direct Visit';
      }
    }
    
    // Return formatted string with UTM link if available
    if (utmLink) {
      return `${sourceName} | ${utmLink}`;
    } else {
      return sourceName;
    }
  };

  // Helper function to parse time slot and set it on a date object

  const parseTimeSlot = (timeSlot: string, date: Date) => {
    // Try 12-hour format first (e.g., "11:00 AM" or "2:30 PM")
    const twelveHourMatch = timeSlot.match(/(\d+):(\d+)\s*(AM|PM)/i);
    if (twelveHourMatch) {
      let hour = parseInt(twelveHourMatch[1]);
      const minute = parseInt(twelveHourMatch[2]);
      const period = twelveHourMatch[3].toUpperCase();
      
      // Convert to 24-hour format
      if (period === 'PM' && hour !== 12) hour += 12;
      if (period === 'AM' && hour === 12) hour = 0;
      
      date.setHours(hour, minute, 0, 0);
      return;
    }
    
    // Try 24-hour format (e.g., "15:30")
    const twentyFourHourMatch = timeSlot.match(/^(\d{1,2}):(\d{2})$/);
    if (twentyFourHourMatch) {
      const hour = parseInt(twentyFourHourMatch[1]);
      const minute = parseInt(twentyFourHourMatch[2]);
      date.setHours(hour, minute, 0, 0);
      return;
    }
  };

  // Admin: Get all bookings with filtering, sorting, and navigation
  app.get("/api/admin/bookings", requireAdmin, async (req, res) => {
    try {
      const { dateFilter, date, sort, status } = req.query;
      console.log('Admin bookings request params:', { dateFilter, date, sort, status });
      
      let startDate: Date | undefined;
      let endDate: Date | undefined;
      
      // Handle date filtering
      if (dateFilter) {
        const now = new Date();
        
        switch (dateFilter) {
          case 'today':
            startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 0, 0, 0);
            endDate = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59);
            break;
          case 'week':
            const startOfWeek = new Date(now);
            startOfWeek.setDate(now.getDate() - now.getDay()); // Sunday
            startOfWeek.setHours(0, 0, 0, 0);
            const endOfWeek = new Date(startOfWeek);
            endOfWeek.setDate(startOfWeek.getDate() + 6);
            endOfWeek.setHours(23, 59, 59, 999);
            startDate = startOfWeek;
            endDate = endOfWeek;
            break;
          case 'month':
            startDate = new Date(now.getFullYear(), now.getMonth(), 1, 0, 0, 0);
            endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59);
            break;
          case 'all':
          default:
            // No date filtering
            break;
        }
      }
      
      // Handle specific date navigation (when user clicks arrows or selects specific date)
      if (date) {
        const selectedDate = new Date(date as string);
        startDate = new Date(selectedDate.getFullYear(), selectedDate.getMonth(), selectedDate.getDate(), 0, 0, 0);
        endDate = new Date(selectedDate.getFullYear(), selectedDate.getMonth(), selectedDate.getDate(), 23, 59, 59);
      }
      
      // Get bookings with filtering
      let bookings;
      if (startDate && endDate) {
        // Pass 'all' status to get all bookings in date range, then filter by status if needed
        bookings = await storage.getBookingsByDateRange(startDate, endDate, 'all');
      } else {
        bookings = await storage.getBookings();
      }
      
      // Apply status filtering if provided
      if (status && status !== 'all') {
        bookings = bookings.filter(booking => booking.status === status);
      }
      
      // Apply sorting
      if (sort) {
        console.log('Sorting bookings with sort parameter:', sort);
        console.log('Sample booking data for sorting debug:', bookings.slice(0, 2).map(b => ({ id: b.id, date: b.date, timeSlot: b.timeSlot, name: b.name, createdAt: b.createdAt })));
        bookings.sort((a, b) => {
          if (sort === 'createdDesc' || sort === 'createdAsc') {
            // Sort by creation date
            const createdA = new Date(a.createdAt);
            const createdB = new Date(b.createdAt);
            
            if (sort === 'createdDesc') {
              return createdB.getTime() - createdA.getTime();
            } else {
              return createdA.getTime() - createdB.getTime();
            }
          } else {
            // Sort by appointment date and time
            const dateA = new Date(a.date);
            const dateB = new Date(b.date);
            
            // Use helper function to parse time slots (handles both 12-hour and 24-hour formats)
            parseTimeSlot(a.timeSlot, dateA);
            parseTimeSlot(b.timeSlot, dateB);
            
            if (sort === 'dateDesc') {
              return dateB.getTime() - dateA.getTime();
            } else if (sort === 'dateAsc') {
              return dateA.getTime() - dateB.getTime();
            } else {
              // Default to ascending for any other value
              return dateA.getTime() - dateB.getTime();
            }
          }
        });
      } else {
        // Default sorting by created date descending (latest first)
        bookings.sort((a, b) => {
          const createdA = new Date(a.createdAt);
          const createdB = new Date(b.createdAt);
          return createdB.getTime() - createdA.getTime();
        });
      }
      
      res.json(bookings);
    } catch (error) {
      console.error("Error fetching bookings:", error);
      res.status(500).json({ message: "Error fetching bookings" });
    }
  });

  // Admin: Export bookings to CSV with filtering
  app.get("/api/admin/bookings/export", requireAdmin, async (req, res) => {
    try {
      const { dateFilter, date, sort, status } = req.query;
      
      let startDate: Date | undefined;
      let endDate: Date | undefined;
      
      // Handle date filtering (same logic as above)
      if (dateFilter) {
        const now = new Date();
        
        switch (dateFilter) {
          case 'today':
            startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 0, 0, 0);
            endDate = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59);
            break;
          case 'week':
            const startOfWeek = new Date(now);
            startOfWeek.setDate(now.getDate() - now.getDay());
            startOfWeek.setHours(0, 0, 0, 0);
            const endOfWeek = new Date(startOfWeek);
            endOfWeek.setDate(startOfWeek.getDate() + 6);
            endOfWeek.setHours(23, 59, 59, 999);
            startDate = startOfWeek;
            endDate = endOfWeek;
            break;
          case 'month':
            startDate = new Date(now.getFullYear(), now.getMonth(), 1, 0, 0, 0);
            endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59);
            break;
          case 'all':
          default:
            break;
        }
      }
      
      if (date) {
        const selectedDate = new Date(date as string);
        startDate = new Date(selectedDate.getFullYear(), selectedDate.getMonth(), selectedDate.getDate(), 0, 0, 0);
        endDate = new Date(selectedDate.getFullYear(), selectedDate.getMonth(), selectedDate.getDate(), 23, 59, 59);
      }
      
      // Get bookings with filtering
      let bookings;
      if (startDate && endDate) {
        bookings = await storage.getBookingsByDateRange(startDate, endDate);
      } else {
        bookings = await storage.getBookings();
      }
      
      // Apply status filtering
      if (status && status !== 'all') {
        bookings = bookings.filter(booking => booking.status === status);
      }
      
      // Apply sorting using the same improved parsing logic
      if (sort) {
        bookings.sort((a, b) => {
          if (sort === 'createdDesc' || sort === 'createdAsc') {
            // Sort by creation date
            const createdA = new Date(a.createdAt);
            const createdB = new Date(b.createdAt);
            
            if (sort === 'createdDesc') {
              return createdB.getTime() - createdA.getTime();
            } else {
              return createdA.getTime() - createdB.getTime();
            }
          } else {
            // Sort by appointment date and time
            const dateA = new Date(a.date);
            const dateB = new Date(b.date);
            
            // Use helper function to parse time slots (handles both 12-hour and 24-hour formats)
            parseTimeSlot(a.timeSlot, dateA);
            parseTimeSlot(b.timeSlot, dateB);
            
            if (sort === 'dateDesc') {
              return dateB.getTime() - dateA.getTime();
            } else {
              return dateA.getTime() - dateB.getTime();
            }
          }
        });
      } else {
        bookings.sort((a, b) => {
          const dateA = new Date(a.date);
          const dateB = new Date(b.date);
          
          // Use helper function to parse time slots (handles both 12-hour and 24-hour formats)
          parseTimeSlot(a.timeSlot, dateA);
          parseTimeSlot(b.timeSlot, dateB);
          
          return dateA.getTime() - dateB.getTime();
        });
      }
      
      // Helper function to format time to 12-hour format for CSV
      const formatTimeForCSV = (timeString: string): string => {
        if (!timeString) return '';
        
        // If already in 12-hour format (contains AM/PM), return as is
        if (timeString.includes('AM') || timeString.includes('PM')) {
          return timeString;
        }
        
        // If in 24-hour format (e.g., "15:30"), convert to 12-hour
        const timeMatch = timeString.match(/^(\d{1,2}):(\d{2})$/);
        if (timeMatch) {
          let hour = parseInt(timeMatch[1]);
          const minute = timeMatch[2];
          
          const period = hour >= 12 ? 'PM' : 'AM';
          if (hour === 0) {
            hour = 12;
          } else if (hour > 12) {
            hour = hour - 12;
          }
          
          return `${hour}:${minute} ${period}`;
        }
        
        return timeString;
      };

      // Fetch UTM data and correlate with individual bookings based on timing
      let utmDataMap = new Map();

      // Get all UTM sessions for time-based correlation
      try {
        const utmSessions = await db
          .select({
            sessionId: longTermUserSessions.sessionId,
            utmSource: longTermUserSessions.utmSource,
            utmMedium: longTermUserSessions.utmMedium,
            utmCampaign: longTermUserSessions.utmCampaign,
            utmTerm: longTermUserSessions.utmTerm,
            utmContent: longTermUserSessions.utmContent,
            referrer: longTermUserSessions.referrer,
            deviceFingerprint: longTermUserSessions.deviceFingerprint,
            startedAt: longTermUserSessions.startedAt
          })
          .from(longTermUserSessions)
          .where(isNotNull(longTermUserSessions.utmSource))
          .orderBy(desc(longTermUserSessions.startedAt));

        console.log(`Found ${utmSessions.length} UTM sessions for correlation`);

        // Correlate each booking with the closest UTM session by time (within 2 hours)
        bookings.forEach(booking => {
          const bookingTime = new Date(booking.createdAt || '');
          let closestSession = null;
          let minTimeDiff = Infinity;

          utmSessions.forEach(session => {
            if (session.startedAt) {
              const sessionTime = new Date(session.startedAt);
              const timeDiff = Math.abs(bookingTime.getTime() - sessionTime.getTime());
              
              // Only consider sessions within 2 hours of booking creation
              if (timeDiff <= 2 * 60 * 60 * 1000 && timeDiff < minTimeDiff) {
                minTimeDiff = timeDiff;
                closestSession = session;
              }
            }
          });

          if (closestSession) {
            utmDataMap.set(booking.id, {
              sessionId: closestSession.sessionId || '',
              utmSource: closestSession.utmSource || 'direct',
              utmMedium: closestSession.utmMedium || 'direct',
              utmCampaign: closestSession.utmCampaign || 'direct visit',
              utmTerm: closestSession.utmTerm || '',
              utmContent: closestSession.utmContent || '',
              utmReferrer: closestSession.referrer || '',
              landingPage: '',
              deviceType: '',
              browserName: '',
              sessionStarted: closestSession.startedAt || null
            });
            console.log(`Booking ${booking.id} correlated with session ${closestSession.sessionId} (${closestSession.utmSource}/${closestSession.utmCampaign})`);
          } else {
            // No UTM session found within 2 hours - mark as direct
            utmDataMap.set(booking.id, {
              sessionId: 'N/A',
              utmSource: 'direct',
              utmMedium: 'N/A',
              utmCampaign: 'N/A',
              utmTerm: 'N/A',
              utmContent: 'N/A',
              utmReferrer: 'N/A',
              landingPage: 'N/A',
              deviceType: 'N/A',
              browserName: 'N/A',
              sessionStarted: null
            });
            console.log(`Booking ${booking.id} marked as direct (no UTM session within 2 hours)`);
          }
        });

      } catch (utmError) {
        console.warn('Error fetching UTM data for export:', utmError);
        // Set all bookings as direct if there's an error
        bookings.forEach(booking => {
          utmDataMap.set(booking.id, {
            sessionId: 'N/A',
            utmSource: 'direct',
            utmMedium: 'N/A',
            utmCampaign: 'N/A',
            utmTerm: 'N/A',
            utmContent: 'N/A',
            utmReferrer: 'N/A',
            landingPage: 'N/A',
            deviceType: 'N/A',
            browserName: 'N/A',
            sessionStarted: null
          });
        });
      }

      // Function to parse Meta campaign details
      const parseMetaCampaignDetails = (campaign: string) => {
        try {
          const { parseMetaCampaign } = require('./utils/utm-parser');
          return parseMetaCampaign(campaign);
        } catch {
          return {
            creativeType: null,
            brand: null,
            product: null,
            offer: null,
            location: null,
            audience: null
          };
        }
      };

      // Convert to CSV format with comprehensive booking details and UTM data
      const csvHeader = 'ID,Name,Phone,Email,Date,Time,Status,Address,Postal Code,Sales Rep,Service,Created Date,Created Time,Last Updated,Purchase Amount,Order ID,Payment Status,Customer Satisfaction,Feedback Notes,Appointment Completed,Secure Token,Session ID,UTM Source,UTM Medium,UTM Campaign,UTM Term,UTM Content,UTM Referrer,Landing Page,Device Type,Browser,Campaign Creative Type,Campaign Location,Campaign Audience,Session Started\n';
      const csvRows = bookings.map(booking => {
        const createdDate = new Date(booking.createdAt || '');
        const updatedDate = new Date(booking.updatedAt || booking.createdAt || '');
        
        // Get UTM data for this specific booking (time-correlated)
        const utmData = utmDataMap.get(booking.id) || {
          sessionId: 'N/A',
          utmSource: 'direct',
          utmMedium: 'N/A',
          utmCampaign: 'N/A',
          utmTerm: 'N/A',
          utmContent: 'N/A',
          utmReferrer: 'N/A',
          landingPage: 'N/A',
          deviceType: 'N/A',
          browserName: 'N/A',
          sessionStarted: null
        };

        // Parse Meta campaign details if available
        let metaDetails = { creativeType: '', location: '', audience: '' };
        if (utmData.utmSource.toLowerCase() === 'meta' || utmData.utmSource.toLowerCase() === 'facebook') {
          const parsedMeta = parseMetaCampaignDetails(utmData.utmCampaign);
          metaDetails = {
            creativeType: parsedMeta.creativeType || '',
            location: parsedMeta.location || '',
            audience: parsedMeta.audience || ''
          };
        }
        
        const row = [
          booking.id,
          `"${booking.name || ''}"`,
          `"${booking.phone || ''}"`,
          `"${booking.email || ''}"`,
          booking.date ? new Date(booking.date).toISOString().split('T')[0] : '',
          `"${formatTimeForCSV(booking.timeSlot || '')}"`,
          `"${booking.status || ''}"`,
          `"${booking.address || ''}"`,
          `"${booking.postalCode || ''}"`,
          `"${booking.repAssigned || ''}"`,
          `"${booking.service || 'Perfume Trial'}"`,
          createdDate.toISOString().split('T')[0],
          createdDate.toISOString().split('T')[1].split('.')[0],
          updatedDate.toISOString().split('T')[0],
          booking.purchaseAmount || '',
          `"${booking.orderId || ''}"`,
          `"${booking.paymentStatus || ''}"`,
          booking.customerSatisfaction || '',
          `"${booking.feedbackNotes || ''}"`,
          booking.appointmentCompleted || false,
          `"${booking.secureToken || ''}"`,
          `"${utmData.sessionId}"`,
          `"${utmData.utmSource}"`,
          `"${utmData.utmMedium}"`,
          `"${utmData.utmCampaign}"`,
          `"${utmData.utmTerm}"`,
          `"${utmData.utmContent}"`,
          `"${utmData.utmReferrer}"`,
          `"${utmData.landingPage}"`,
          `"${utmData.deviceType}"`,
          `"${utmData.browserName}"`,
          `"${metaDetails.creativeType}"`,
          `"${metaDetails.location}"`,
          `"${metaDetails.audience}"`,
          utmData.sessionStarted ? new Date(utmData.sessionStarted).toISOString() : ''
        ];
        return row.join(',');
      }).join('\n');
      
      const csvContent = csvHeader + csvRows;
      
      // Generate filename with timestamp and filter info
      const timestamp = new Date().toISOString().split('T')[0];
      const filterSuffix = dateFilter ? `_${dateFilter}` : '';
      const filename = `bookings_with_utm_${timestamp}${filterSuffix}.csv`;
      
      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
      res.send(csvContent);
    } catch (error) {
      console.error("Error exporting bookings:", error);
      res.status(500).json({ message: "Error exporting bookings" });
    }
  });

  // Admin: Get bookings by date range
  app.get("/api/admin/bookings/range", requireAdmin, async (req, res) => {
    try {
      const { start, end, status } = req.query;
      const startDate = new Date(start as string);
      const endDate = new Date(end as string);
      
      // Pass status parameter to storage function (defaults to 'confirmed' if not specified)
      const bookings = await storage.getBookingsByDateRange(startDate, endDate, status as string);
      res.json(bookings);
    } catch (error) {
      res.status(500).json({ message: "Error fetching bookings" });
    }
  });

  // Admin: Complete booking
  app.put("/api/admin/bookings/:id/complete", requireAdmin, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const completionData = req.body;
      
      // Get original booking
      const originalBooking = await storage.getBooking(id);
      if (!originalBooking) {
        return res.status(404).json({ message: "Booking not found" });
      }

      // Update the booking with completion data
      const updatedBooking = await storage.updateBooking(id, completionData);
      if (!updatedBooking) {
        return res.status(404).json({ message: "Booking not found" });
      }

      console.log(`Booking ${id} completed with data:`, completionData);
      res.json(updatedBooking);
    } catch (error) {
      console.error("Error completing booking:", error);
      res.status(500).json({ message: "Error completing booking" });
    }
  });

  // Admin: Get booking history by phone number
  app.get("/api/admin/booking-history/:phoneNumber", requireAdmin, async (req, res) => {
    try {
      const { phoneNumber } = req.params;
      const history = await storage.getBookingHistoryByPhone(phoneNumber);
      res.json(history);
    } catch (error) {
      console.error("Error fetching booking history:", error);
      res.status(500).json({ message: "Error fetching booking history" });
    }
  });

  // Admin: Update booking
  app.put("/api/admin/bookings/:id", requireAdmin, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const updates = req.body;
      
      // Get original booking for comparison and notifications
      const originalBooking = await storage.getBooking(id);
      if (!originalBooking) {
        return res.status(404).json({ message: "Booking not found" });
      }

      // STRICT BUSINESS RULE VALIDATION - Admin must follow same rules as customers
      if (updates.date || updates.timeSlot) {
        const newDate = new Date(updates.date || originalBooking.date);
        const newTimeSlot = updates.timeSlot || originalBooking.timeSlot;
        
        console.log(`🔒 Admin booking validation for booking ${id}: ${newTimeSlot} on ${newDate.toISOString().split('T')[0]}`);
        
        // Check if the new time slot is available (excluding current booking)
        const availableReps = await storage.getAvailableReps(newDate, newTimeSlot, id);
        if (availableReps.length === 0) {
          console.error(`🚫 Admin booking blocked: No available reps for ${newTimeSlot} on ${newDate.toISOString()}`);
          return res.status(400).json({ 
            message: `Time slot ${newTimeSlot} is not available. This violates business rules (60-minute gap between appointments, shift hours, or rep availability).`
          });
        }
        
        // Assign representative using round-robin logic (same as customer booking)
        const assignedRepName = await storage.assignRepWithRoundRobin(newDate, newTimeSlot);
        if (!assignedRepName) {
          console.error(`🚫 Admin booking blocked: Round-robin assignment failed for ${newTimeSlot} on ${newDate.toISOString()}`);
          return res.status(400).json({ 
            message: `No representatives can be assigned to ${newTimeSlot}. This violates business rules.`
          });
        }
        
        // Enforce representative assignment from validation
        updates.repAssigned = assignedRepName;
        console.log(`✅ Admin booking validated: ${newTimeSlot} on ${newDate.toISOString().split('T')[0]} assigned to ${assignedRepName}`);
      }

      // Properly format the date if provided
      if (updates.date && typeof updates.date === 'string') {
        updates.date = new Date(updates.date);
      }

      // Update the booking with admin identification
      const session = req.session as any;
      const updatedBooking = await storage.updateBooking(
        id, 
        updates, 
        'admin', 
        session?.userId, 
        session?.username
      );
      if (!updatedBooking) {
        return res.status(404).json({ message: "Booking not found" });
      }

      // Send notification if significant changes were made
      const settings = await storage.getSettings();
      
      // Convert dates to strings for comparison if they're Date objects
      const originalDate = originalBooking.date instanceof Date ? originalBooking.date.toISOString().split('T')[0] : originalBooking.date;
      const updatedDate = updatedBooking.date instanceof Date ? updatedBooking.date.toISOString().split('T')[0] : updatedBooking.date;
      
      const hasSignificantChanges = 
        originalDate !== updatedDate ||
        originalBooking.timeSlot !== updatedBooking.timeSlot ||
        originalBooking.repAssigned !== updatedBooking.repAssigned ||
        originalBooking.status !== updatedBooking.status; // Any status change is significant
      
      console.log(`Checking for significant changes:`, {
        originalDate,
        updatedDate,
        dateChanged: originalDate !== updatedDate,
        originalTimeSlot: originalBooking.timeSlot,
        updatedTimeSlot: updatedBooking.timeSlot,
        timeChanged: originalBooking.timeSlot !== updatedBooking.timeSlot,
        originalStatus: originalBooking.status,
        updatedStatus: updatedBooking.status,
        statusChanged: originalBooking.status !== updatedBooking.status,
        hasSignificantChanges
      });

      if (hasSignificantChanges) {
        console.log(`Significant changes detected for booking ${id}:`, {
          dateChanged: originalBooking.date !== updatedBooking.date,
          timeChanged: originalBooking.timeSlot !== updatedBooking.timeSlot,
          repChanged: originalBooking.repAssigned !== updatedBooking.repAssigned,
          statusChanged: originalBooking.status !== updatedBooking.status,
          newStatus: updatedBooking.status
        });

        // Determine notification type based on booking status and changes
        let notificationType: 'confirmation' | 'cancellation' | 'reschedule' | 'reminder' = 'confirmation';
        
        if (updatedBooking.status === 'cancelled') {
          notificationType = 'cancellation';
        } else if (originalDate !== updatedDate || originalBooking.timeSlot !== updatedBooking.timeSlot) {
          notificationType = 'reschedule';
        } else {
          // Only send confirmation if status changed to confirmed or other non-date/time changes
          notificationType = 'confirmation';
        }

        console.log(`Sending ${notificationType} notification for booking ${id}`);

        // Send email notification with correct type
        try {
          await sendEmail(updatedBooking, settings, updatedBooking, notificationType);
          console.log(`Email ${notificationType} notification sent successfully`);
        } catch (emailError) {
          console.error(`Failed to send email ${notificationType} notification:`, emailError);
        }

        // Send SMS notification with correct type
        try {
          await sendSMS(updatedBooking, settings, updatedBooking, notificationType);
          console.log(`SMS ${notificationType} notification sent successfully`);
        } catch (smsError) {
          console.error(`Failed to send SMS ${notificationType} notification:`, smsError);
        }
      }

      // Always send WebSocket notification for any booking update
      const changes = {
        dateChanged: originalDate !== updatedDate,
        timeChanged: originalBooking.timeSlot !== updatedBooking.timeSlot,
        repChanged: originalBooking.repAssigned !== updatedBooking.repAssigned,
        statusChanged: originalBooking.status !== updatedBooking.status
      };
      notifyBookingUpdated(updatedBooking, changes);

      // Sync to webhook (async, don't wait)
      webhookService.sendBookingData(updatedBooking).catch((error: any) => {
        console.error('Webhook sync error:', error);
      });

      res.json(updatedBooking);
    } catch (error) {
      console.error("Error updating booking:", error);
      res.status(500).json({ message: "Error updating booking" });
    }
  });

  // Admin: Delete/Cancel booking
  app.delete("/api/admin/bookings/:id", requireAdmin, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      
      // Get original booking for notification
      const originalBooking = await storage.getBooking(id);
      if (!originalBooking) {
        return res.status(404).json({ message: "Booking not found" });
      }
      
      // Cancel the booking (don't actually delete, just mark as cancelled)
      const cancelledBooking = await storage.updateBooking(id, { status: "cancelled" });
      
      if (!cancelledBooking) {
        return res.status(404).json({ message: "Booking not found" });
      }

      // Send cancellation notification
      const settings = await storage.getSettings();
      try {
        await sendSMS(cancelledBooking, settings, cancelledBooking, 'cancellation');
        console.log(`Cancellation notification sent for booking ${id}`);
      } catch (smsError) {
        console.error(`Failed to send cancellation notification:`, smsError);
      }

      // Send WebSocket notification for booking deletion/cancellation
      notifyBookingDeleted(id);

      res.json({ success: true, booking: cancelledBooking });
    } catch (error) {
      console.error("Error cancelling booking:", error);
      res.status(500).json({ message: "Error cancelling booking" });
    }
  });

  // Admin: Sales rep management
  app.get("/api/admin/reps", requireAdmin, async (req, res) => {
    try {
      const reps = await storage.getSalesReps();
      res.json(reps);
    } catch (error) {
      res.status(500).json({ message: "Error fetching sales reps" });
    }
  });

  app.post("/api/admin/reps", requireAdmin, async (req, res) => {
    try {
      const repData = insertSalesRepSchema.parse(req.body);
      const rep = await storage.createSalesRep(repData);
      res.json(rep);
    } catch (error) {
      if (error.issues) {
        res.status(400).json({ message: "Invalid rep data", errors: error.issues });
      } else {
        res.status(500).json({ message: "Error creating sales rep" });
      }
    }
  });

  app.put("/api/admin/reps/:id", requireAdmin, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const updates = req.body;
      const rep = await storage.updateSalesRep(id, updates);
      res.json(rep);
    } catch (error) {
      res.status(500).json({ message: "Error updating sales rep" });
    }
  });

  app.delete("/api/admin/reps/:id", requireAdmin, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const success = await storage.deleteSalesRep(id);
      res.json({ success });
    } catch (error) {
      res.status(500).json({ message: "Error deleting sales rep" });
    }
  });

  // Admin: Postal code management
  app.get("/api/admin/postal-codes", requireAdmin, async (req, res) => {
    try {
      const codes = await storage.getPostalCodes();
      res.json(codes);
    } catch (error) {
      res.status(500).json({ message: "Error fetching postal codes" });
    }
  });

  app.post("/api/admin/postal-codes", requireAdmin, async (req, res) => {
    try {
      const codeData = insertPostalCodeSchema.parse(req.body);
      const code = await storage.createPostalCode(codeData);
      res.json(code);
    } catch (error) {
      if (error.issues) {
        res.status(400).json({ message: "Invalid postal code data", errors: error.issues });
      } else {
        res.status(500).json({ message: "Error creating postal code" });
      }
    }
  });

  app.delete("/api/admin/postal-codes/:id", requireAdmin, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const success = await storage.deletePostalCode(id);
      res.json({ success });
    } catch (error) {
      res.status(500).json({ message: "Error deleting postal code" });
    }
  });

  // Admin: Settings management
  app.get("/api/admin/settings", requireAdmin, async (req, res) => {
    try {
      const settings = await storage.getSettings();
      res.json(settings);
    } catch (error) {
      res.status(500).json({ message: "Error fetching settings" });
    }
  });

  app.put("/api/admin/settings", requireAdmin, async (req, res) => {
    try {
      const updates = req.body;
      const settings = await storage.updateSettings(updates);
      res.json(settings);
    } catch (error) {
      res.status(500).json({ message: "Error updating settings" });
    }
  });

  // Change password endpoint
  app.put("/api/admin/change-password", requireAdmin, async (req, res) => {
    try {
      const { currentPassword, newPassword } = req.body;
      const username = (req.session as any).username;

      if (!username) {
        return res.status(401).json({ message: "Not authenticated" });
      }

      const user = await storage.getUserByUsername(username);
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }

      const bcrypt = await import('bcrypt');
      const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password);
      
      if (!isCurrentPasswordValid) {
        return res.status(400).json({ message: "Current password is incorrect" });
      }

      const hashedNewPassword = await bcrypt.hash(newPassword, 10);
      await storage.updateUser(user.id, { password: hashedNewPassword });

      res.json({ message: "Password updated successfully" });
    } catch (error) {
      console.error("Change password error:", error);
      res.status(500).json({ message: "Error changing password" });
    }
  });

  // Support Team API Routes - Access to bookings, templates, and notification timing
  app.get("/api/support/bookings", requireSupport, async (req, res) => {
    try {
      const { dateFilter, date, sort, status } = req.query;
      console.log('Support bookings request params:', { dateFilter, date, sort, status });
      
      let startDate: Date | undefined;
      let endDate: Date | undefined;
      
      // Handle date filtering
      if (dateFilter) {
        const now = new Date();
        
        switch (dateFilter) {
          case 'today':
            startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 0, 0, 0);
            endDate = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59);
            break;
          case 'week':
            const startOfWeek = new Date(now);
            startOfWeek.setDate(now.getDate() - now.getDay()); // Sunday
            startOfWeek.setHours(0, 0, 0, 0);
            const endOfWeek = new Date(startOfWeek);
            endOfWeek.setDate(startOfWeek.getDate() + 6);
            endOfWeek.setHours(23, 59, 59, 999);
            startDate = startOfWeek;
            endDate = endOfWeek;
            break;
          case 'month':
            startDate = new Date(now.getFullYear(), now.getMonth(), 1, 0, 0, 0);
            endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59);
            break;
          case 'all':
          default:
            // No date filtering
            break;
        }
      }
      
      // Handle specific date navigation
      if (date) {
        const selectedDate = new Date(date as string);
        startDate = new Date(selectedDate.getFullYear(), selectedDate.getMonth(), selectedDate.getDate(), 0, 0, 0);
        endDate = new Date(selectedDate.getFullYear(), selectedDate.getMonth(), selectedDate.getDate(), 23, 59, 59);
      }
      
      // Get bookings with filtering
      let bookings;
      if (startDate && endDate) {
        bookings = await storage.getBookingsByDateRange(startDate, endDate, 'all');
      } else {
        bookings = await storage.getBookings();
      }
      
      // Apply status filtering if provided
      if (status && status !== 'all') {
        bookings = bookings.filter(booking => booking.status === status);
      }
      
      // Apply sorting
      if (sort) {
        console.log('Sorting support bookings with sort parameter:', sort);
        bookings.sort((a, b) => {
          if (sort === 'createdDesc' || sort === 'createdAsc') {
            // Sort by creation date
            const createdA = new Date(a.createdAt);
            const createdB = new Date(b.createdAt);
            
            if (sort === 'createdDesc') {
              return createdB.getTime() - createdA.getTime();
            } else {
              return createdA.getTime() - createdB.getTime();
            }
          } else {
            // Sort by appointment date and time
            const dateA = new Date(a.date);
            const dateB = new Date(b.date);
            
            // Use helper function to parse time slots
            parseTimeSlot(a.timeSlot, dateA);
            parseTimeSlot(b.timeSlot, dateB);
            
            if (sort === 'dateDesc') {
              return dateB.getTime() - dateA.getTime();
            } else if (sort === 'dateAsc') {
              return dateA.getTime() - dateB.getTime();
            } else {
              return dateA.getTime() - dateB.getTime();
            }
          }
        });
      } else {
        // Default sorting by created date descending (latest first)
        bookings.sort((a, b) => {
          const createdA = new Date(a.createdAt);
          const createdB = new Date(b.createdAt);
          return createdB.getTime() - createdA.getTime();
        });
      }
      
      res.json(bookings);
    } catch (error) {
      console.error("Error fetching support bookings:", error);
      res.status(500).json({ message: "Error fetching bookings" });
    }
  });

  // Marketing Team API Routes - View bookings and export data
  app.get("/api/marketing/bookings", requireMarketing, async (req, res) => {
    try {
      const bookings = await storage.getBookings();
      res.json(bookings);
    } catch (error) {
      res.status(500).json({ message: "Error fetching bookings" });
    }
  });

  app.get("/api/marketing/bookings/export", requireMarketing, async (req, res) => {
    try {
      const bookings = await storage.getBookings();
      
      // Convert to CSV format
      const csvHeaders = ['ID', 'Name', 'Phone', 'Email', 'Address', 'Postal Code', 'Date', 'Time Slot', 'Status', 'Rep Assigned', 'Service'];
      const csvRows = bookings.map(booking => [
        booking.id,
        `"${booking.name}"`,
        `"${booking.phone}"`,
        `"${booking.email || ''}"`,
        `"${booking.address}"`,
        booking.postalCode,
        booking.date,
        `"${booking.timeSlot}"`,
        booking.status,
        `"${booking.repAssigned || ''}"`,
        `"${booking.service}"`
      ]);
      
      const csvContent = [csvHeaders.join(','), ...csvRows.map(row => row.join(','))].join('\n');
      
      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', 'attachment; filename="bookings-export.csv"');
      res.send(csvContent);
    } catch (error) {
      res.status(500).json({ message: "Error exporting bookings" });
    }
  });

  app.put("/api/support/bookings/:id", requireSupport, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const updates = req.body;
      const originalBooking = await storage.getBooking(id);
      
      if (!originalBooking) {
        return res.status(404).json({ message: "Booking not found" });
      }

      const updatedBooking = await storage.updateBooking(id, updates);
      if (!updatedBooking) {
        return res.status(404).json({ message: "Booking not found" });
      }

      // Send notification for significant changes
      const settings = await storage.getSettings();
      const originalDate = originalBooking.date instanceof Date ? originalBooking.date.toISOString().split('T')[0] : originalBooking.date;
      const updatedDate = updatedBooking.date instanceof Date ? updatedBooking.date.toISOString().split('T')[0] : updatedBooking.date;
      
      const hasSignificantChanges = 
        originalDate !== updatedDate ||
        originalBooking.timeSlot !== updatedBooking.timeSlot ||
        originalBooking.status !== updatedBooking.status;

      if (hasSignificantChanges) {
        const bookingData = {
          name: updatedBooking.name,
          phone: updatedBooking.phone,
          email: updatedBooking.email,
          address: updatedBooking.address,
          date: new Date(updatedBooking.date),
          timeSlot: updatedBooking.timeSlot,
          postalCode: updatedBooking.postalCode
        };

        let notificationType: 'confirmation' | 'cancellation' | 'reschedule' | 'reminder' = 'confirmation';
        if (originalBooking.status !== updatedBooking.status && updatedBooking.status === 'cancelled') {
          notificationType = 'cancellation';
        } else if (originalDate !== updatedDate || originalBooking.timeSlot !== updatedBooking.timeSlot) {
          notificationType = 'reschedule';
        }

        try {
          await sendEmail(bookingData, settings, updatedBooking, notificationType);
          await sendSMS(bookingData, settings, updatedBooking, notificationType);
          console.log(`Notifications sent for updated booking ${id}`);
        } catch (notificationError) {
          console.error('Error sending notifications:', notificationError);
        }
      }

      res.json(updatedBooking);
    } catch (error) {
      res.status(500).json({ message: "Error updating booking" });
    }
  });

  app.get("/api/support/settings", requireSupport, async (req, res) => {
    try {
      const settings = await storage.getSettings();
      res.json(settings);
    } catch (error) {
      res.status(500).json({ message: "Error fetching settings" });
    }
  });

  app.put("/api/support/settings", requireSupport, async (req, res) => {
    try {
      const updates = req.body;
      const settings = await storage.updateSettings(updates);
      res.json(settings);
    } catch (error) {
      res.status(500).json({ message: "Error updating settings" });
    }
  });

  // Admin: User Management
  app.get("/api/admin/users", requireAdmin, async (req, res) => {
    try {
      const users = await storage.getUsers();
      res.json(users);
    } catch (error) {
      res.status(500).json({ message: "Error fetching users" });
    }
  });

  app.post("/api/admin/users", requireAdmin, async (req, res) => {
    try {
      const userData = insertUserSchema.parse(req.body);
      
      // Hash the password before storing
      const bcrypt = await import('bcrypt');
      const hashedPassword = await bcrypt.hash(userData.password, 10);
      
      const userWithHashedPassword = {
        ...userData,
        password: hashedPassword
      };
      
      const user = await storage.createUser(userWithHashedPassword);
      res.json(user);
    } catch (error: any) {
      console.error("Error creating user:", error);
      if (error.issues) {
        res.status(400).json({ message: "Invalid user data", errors: error.issues });
      } else {
        res.status(500).json({ message: "Error creating user" });
      }
    }
  });

  app.put("/api/admin/users/:id", requireAdmin, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const updates = req.body;
      
      // If password is being updated, hash it
      if (updates.password) {
        const bcrypt = await import('bcrypt');
        updates.password = await bcrypt.hash(updates.password, 10);
      }
      
      const user = await storage.updateUser(id, updates);
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }
      res.json(user);
    } catch (error: any) {
      console.error("Error updating user:", error);
      res.status(500).json({ message: "Error updating user" });
    }
  });

  app.delete("/api/admin/users/:id", requireAdmin, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const success = await storage.deleteUser(id);
      res.json({ success });
    } catch (error) {
      res.status(500).json({ message: "Error deleting user" });
    }
  });

  // Admin: Change user password
  app.post("/api/admin/users/:id/change-password", requireAdmin, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const { password } = req.body;
      
      if (!password || password.length < 8) {
        return res.status(400).json({ message: "Password must be at least 8 characters long" });
      }

      const bcrypt = await import('bcrypt');
      const hashedPassword = await bcrypt.hash(password, 10);
      
      const success = await storage.updateUserPassword(id, hashedPassword);
      if (!success) {
        return res.status(404).json({ message: "User not found" });
      }
      
      res.json({ success: true });
    } catch (error) {
      console.error("Error changing user password:", error);
      res.status(500).json({ message: "Error changing password" });
    }
  });

  // Admin: Generate secure password
  app.post("/api/admin/generate-password", requireAdmin, async (req, res) => {
    try {
      // Generate a secure password with Kult prefix
      const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%&*';
      let password = 'Kult';
      
      for (let i = 0; i < 8; i++) {
        password += chars.charAt(Math.floor(Math.random() * chars.length));
      }
      
      res.json({ password });
    } catch (error) {
      console.error("Error generating password:", error);
      res.status(500).json({ message: "Error generating password" });
    }
  });

  // OTP endpoints
  app.post("/api/otp/send", async (req, res) => {
    try {
      const { phone } = req.body;
      
      if (!phone) {
        return res.status(400).json({ message: "Phone number is required" });
      }

      // Get client IP and user agent for opt-in tracking
      const ipAddress = req.ip || req.connection.remoteAddress || req.headers['x-forwarded-for'] as string;
      const userAgent = req.headers['user-agent'];
      
      const result = await sendOTP(phone, userAgent, ipAddress);
      
      if (result.success) {
        res.json({ success: true, message: result.message });
      } else {
        res.status(400).json({ success: false, message: result.message });
      }
    } catch (error: any) {
      console.error("OTP send error:", error);
      res.status(500).json({ message: "Failed to send OTP" });
    }
  });

  app.post("/api/otp/verify", async (req, res) => {
    try {
      const { phone, otp } = req.body;
      
      if (!phone || !otp) {
        return res.status(400).json({ message: "Phone number and OTP are required" });
      }
      
      const result = await verifyOTP(phone, otp);
      
      if (result.success) {
        res.json({ success: true, message: result.message });
      } else {
        res.status(400).json({ success: false, message: result.message });
      }
    } catch (error: any) {
      console.error("OTP verify error:", error);
      res.status(500).json({ message: "Failed to verify OTP" });
    }
  });

  // Manual reminder endpoint for admin dashboard alarm button
  app.post("/api/bookings/:id/reminder", async (req, res) => {
    try {
      const bookingId = parseInt(req.params.id);
      
      if (isNaN(bookingId)) {
        return res.status(400).json({ message: "Invalid booking ID" });
      }
      
      const booking = await storage.getBooking(bookingId);
      if (!booking) {
        return res.status(404).json({ message: "Booking not found" });
      }
      
      const { sendReminderSMS } = await import("./services/sms");
      const result = await sendReminderSMS(booking);
      
      res.json({ 
        success: result, 
        message: result ? "WhatsApp reminder sent successfully" : "Failed to send reminder",
        bookingId: booking.id,
        customerName: booking.name,
        phone: booking.phone
      });
    } catch (error) {
      console.error("Manual reminder error:", error);
      res.status(500).json({ message: "Error sending reminder" });
    }
  });

  // Manual reminder endpoint for admin dashboard
  app.post("/api/bookings/:id/reminder", async (req, res) => {
    try {
      const bookingId = parseInt(req.params.id);
      const { type, customMessage } = req.body;
      
      if (isNaN(bookingId)) {
        return res.status(400).json({ message: "Invalid booking ID" });
      }
      
      const booking = await storage.getBooking(bookingId);
      if (!booking) {
        return res.status(404).json({ message: "Booking not found" });
      }
      
      const settings = await storage.getSettings();
      
      try {
        // Send different types of reminders based on type
        const reminderType = type === 'followup' ? 'cx-followup' : 'reminder';
        await sendSMS(booking, settings, booking, reminderType);
        console.log(`Manual ${type} reminder sent for booking ${booking.id}`);
        
        res.json({ 
          success: true, 
          message: `${type} reminder sent successfully`,
          bookingId: booking.id,
          customerName: booking.name,
          phone: booking.phone
        });
      } catch (smsError) {
        console.error("Failed to send manual reminder:", smsError);
        res.status(500).json({ message: "Failed to send reminder message" });
      }
    } catch (error) {
      console.error("Manual reminder error:", error);
      res.status(500).json({ message: "Error sending reminder" });
    }
  });

  // Send custom message endpoint
  app.post("/api/bookings/:id/custom-message", async (req, res) => {
    try {
      const bookingId = parseInt(req.params.id);
      const { message } = req.body;
      
      if (isNaN(bookingId)) {
        return res.status(400).json({ message: "Invalid booking ID" });
      }
      
      if (!message || !message.trim()) {
        return res.status(400).json({ message: "Message is required" });
      }
      
      const booking = await storage.getBooking(bookingId);
      if (!booking) {
        return res.status(404).json({ message: "Booking not found" });
      }
      
      // Send custom WhatsApp message using session messaging
      try {
        const client = twilio(process.env.TWILIO_ACCOUNT_SID, process.env.TWILIO_AUTH_TOKEN);
        const messageResponse = await client.messages.create({
          body: message.trim(),
          from: 'whatsapp:+***********',
          to: `whatsapp:+91${booking.phone.replace(/^\+91/, '')}`
        });
        
        console.log(`Custom message sent to ${booking.phone}: ${messageResponse.sid}`);
        
        // Record the interaction
        await storage.recordCommunication(bookingId, "whatsapp", `Custom message: ${message.substring(0, 100)}...`);
        
        res.json({ 
          success: true, 
          message: "Custom message sent successfully",
          messageId: messageResponse.sid
        });
      } catch (smsError) {
        console.error("Failed to send custom message:", smsError);
        res.status(500).json({ message: "Failed to send custom message" });
      }
    } catch (error) {
      console.error("Custom message error:", error);
      res.status(500).json({ message: "Error sending custom message" });
    }
  });

  // Schedule reminder endpoint
  app.post("/api/bookings/:id/schedule-reminder", async (req, res) => {
    try {
      const bookingId = parseInt(req.params.id);
      const { reminderDateTime, type } = req.body;
      
      if (isNaN(bookingId)) {
        return res.status(400).json({ message: "Invalid booking ID" });
      }
      
      if (!reminderDateTime) {
        return res.status(400).json({ message: "Reminder date/time is required" });
      }
      
      const booking = await storage.getBooking(bookingId);
      if (!booking) {
        return res.status(404).json({ message: "Booking not found" });
      }
      
      // For now, we'll store the scheduled reminder as a customer interaction
      // In a production system, you'd want to integrate with a proper job scheduler
      await storage.recordCommunication(
        bookingId, 
        "scheduled_reminder", 
        `Scheduled ${type} reminder for ${new Date(reminderDateTime).toLocaleString()}`
      );
      
      console.log(`Reminder scheduled for booking ${bookingId} at ${reminderDateTime}`);
      
      res.json({ 
        success: true, 
        message: "Reminder scheduled successfully",
        reminderDateTime,
        type
      });
    } catch (error) {
      console.error("Schedule reminder error:", error);
      res.status(500).json({ message: "Error scheduling reminder" });
    }
  });

  // PATCH endpoint for customer support dashboard editing
  app.patch("/api/bookings/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const { updates } = req.body;
      
      if (isNaN(id)) {
        return res.status(400).json({ message: "Invalid booking ID" });
      }
      
      const booking = await storage.getBooking(id);
      if (!booking) {
        return res.status(404).json({ message: "Booking not found" });
      }
      
      // Apply the updates
      const updatedBooking = await storage.updateBooking(id, updates);
      if (!updatedBooking) {
        return res.status(404).json({ message: "Booking not found after update" });
      }
      
      console.log(`Customer support booking edit - booking ${id} updated:`, updates);
      
      res.json({
        success: true,
        booking: updatedBooking,
        message: "Booking updated successfully"
      });
    } catch (error) {
      console.error("Customer support booking edit error:", error);
      res.status(500).json({ message: "Error updating booking" });
    }
  });

  // Customer service follow-up endpoint for "Tried Reaching" button
  app.post("/api/bookings/:id/cx-followup", async (req, res) => {
    try {
      const bookingId = parseInt(req.params.id);
      
      if (isNaN(bookingId)) {
        return res.status(400).json({ message: "Invalid booking ID" });
      }
      
      const booking = await storage.getBooking(bookingId);
      if (!booking) {
        return res.status(404).json({ message: "Booking not found" });
      }
      
      const { sendCxFollowupMessage } = await import("./services/twilio-whatsapp");
      const { format } = await import("date-fns");
      
      // Format booking date for the message
      const formattedDate = format(new Date(booking.date), "EEEE, MMMM d, yyyy");
      
      const result = await sendCxFollowupMessage(
        booking.phone,
        booking.name,
        formattedDate,
        booking.timeSlot
      );
      
      res.json({ 
        success: result.success, 
        message: result.message,
        bookingId: booking.id,
        customerName: booking.name,
        phone: booking.phone
      });
    } catch (error) {
      console.error("CX follow-up error:", error);
      res.status(500).json({ message: "Error sending follow-up message" });
    }
  });

  // Customer Support API Routes
  
  // Mark appointment completed
  app.post("/api/bookings/:id/complete", async (req, res) => {
    try {
      const bookingId = parseInt(req.params.id);
      const { satisfaction, notes } = req.body;
      
      const success = await storage.markAppointmentCompleted(bookingId, satisfaction, notes);
      if (success) {
        res.json({ success: true, message: "Appointment marked as completed" });
      } else {
        res.status(404).json({ message: "Booking not found" });
      }
    } catch (error) {
      console.error("Complete appointment error:", error);
      res.status(500).json({ message: "Error completing appointment" });
    }
  });
  
  // Add transaction
  app.post("/api/bookings/:id/transaction", async (req, res) => {
    try {
      const bookingId = parseInt(req.params.id);
      const { amount, orderId, paymentMethod } = req.body;
      
      const success = await storage.addTransaction(bookingId, amount, orderId, paymentMethod);
      if (success) {
        res.json({ success: true, message: "Transaction recorded" });
      } else {
        res.status(404).json({ message: "Booking not found" });
      }
    } catch (error) {
      console.error("Add transaction error:", error);
      res.status(500).json({ message: "Error recording transaction" });
    }
  });
  
  // Record communication
  app.post("/api/bookings/:id/communication", async (req, res) => {
    try {
      const bookingId = parseInt(req.params.id);
      const { method, notes } = req.body;
      
      const success = await storage.recordCommunication(bookingId, method, notes);
      if (success) {
        res.json({ success: true, message: "Communication recorded" });
      } else {
        res.status(404).json({ message: "Booking not found" });
      }
    } catch (error) {
      console.error("Record communication error:", error);
      res.status(500).json({ message: "Error recording communication" });
    }
  });

  // Support tickets
  app.get("/api/support/tickets", async (req, res) => {
    try {
      const { status, assignedAgent, priority } = req.query;
      const filters: any = {};
      
      if (status) filters.status = status as string;
      if (assignedAgent) filters.assignedAgent = assignedAgent as string;
      if (priority) filters.priority = priority as string;
      
      const tickets = await storage.getSupportTickets(filters);
      res.json(tickets);
    } catch (error) {
      console.error("Get support tickets error:", error);
      res.status(500).json({ message: "Error fetching support tickets" });
    }
  });

  app.post("/api/support/tickets", async (req, res) => {
    try {
      const ticketData = {
        ...req.body,
        ticketId: `TKT-${Date.now()}-${Math.random().toString(36).substr(2, 5).toUpperCase()}`
      };
      
      const ticket = await storage.createSupportTicket(ticketData);
      res.json(ticket);
    } catch (error) {
      console.error("Create support ticket error:", error);
      res.status(500).json({ message: "Error creating support ticket" });
    }
  });

  // Customer interactions
  app.get("/api/support/interactions", async (req, res) => {
    try {
      const { bookingId, customerPhone } = req.query;
      
      const interactions = await storage.getCustomerInteractions(
        bookingId ? parseInt(bookingId as string) : undefined,
        customerPhone as string
      );
      res.json(interactions);
    } catch (error) {
      console.error("Get customer interactions error:", error);
      res.status(500).json({ message: "Error fetching customer interactions" });
    }
  });

  app.post("/api/support/interactions", async (req, res) => {
    try {
      const interaction = await storage.createCustomerInteraction(req.body);
      res.json(interaction);
    } catch (error) {
      console.error("Create customer interaction error:", error);
      res.status(500).json({ message: "Error creating customer interaction" });
    }
  });

  // Business metrics
  app.get("/api/business/metrics", async (req, res) => {
    try {
      const { dateFrom, dateTo } = req.query;
      
      const filters: any = {};
      if (dateFrom) filters.dateFrom = new Date(dateFrom as string);
      if (dateTo) filters.dateTo = new Date(dateTo as string);
      
      const metrics = await storage.getBusinessMetrics(filters.dateFrom, filters.dateTo);
      res.json(metrics);
    } catch (error) {
      console.error("Get business metrics error:", error);
      res.status(500).json({ message: "Error fetching business metrics" });
    }
  });

  app.get("/api/business/summary/:date", async (req, res) => {
    try {
      const date = new Date(req.params.date);
      const summary = await storage.getDailyBusinessSummary(date);
      res.json(summary);
    } catch (error) {
      console.error("Get daily business summary error:", error);
      res.status(500).json({ message: "Error fetching daily business summary" });
    }
  });

  // Marketing Analytics API Routes
  app.get("/api/marketing/event-logs", async (req, res) => {
    try {
      const { dateFrom, dateTo, eventType, sessionId } = req.query;
      
      const filters: any = {};
      if (dateFrom) filters.dateFrom = new Date(dateFrom as string);
      if (dateTo) filters.dateTo = new Date(dateTo as string);
      if (eventType) filters.eventType = eventType as string;
      if (sessionId) filters.sessionId = sessionId as string;
      
      const eventLogs = await storage.getEventLogs(filters);
      res.json(eventLogs);
    } catch (error) {
      console.error("Error fetching event logs:", error);
      res.status(500).json({ message: "Error fetching event logs" });
    }
  });

  app.post("/api/marketing/event", async (req, res) => {
    try {
      const { sessionId, eventType, eventData, userAgent, ipAddress, referrer } = req.body;
      
      // Normalize UTM parameters if present in eventData
      let normalizedEventData = eventData;
      if (eventData && typeof eventData === 'object') {
        const normalizedUTM = normalizeUTMParams(eventData);
        normalizedEventData = {
          ...eventData,
          utm_source: normalizedUTM.source,
          utm_medium: normalizedUTM.medium,
          utm_campaign: normalizedUTM.campaign,
          utm_term: normalizedUTM.term,
          utm_content: normalizedUTM.content,
          fbclid: normalizedUTM.fbclid,
          gclid: normalizedUTM.gclid,
        };

        // Parse Meta campaign details if source is Meta
        if (normalizedUTM.source === 'meta' || normalizedUTM.source === 'facebook') {
          const metaDetails = parseMetaCampaign(normalizedUTM.campaign);
          normalizedEventData.meta_parsed = metaDetails;
        }
      }
      
      const event = await storage.createEventLog({
        sessionId,
        eventType,
        eventData: normalizedEventData ? JSON.stringify(normalizedEventData) : null,
        userAgent,
        ipAddress,
        referrer,
        userId: null
      });
      
      res.json(event);
    } catch (error) {
      console.error("Error creating event log:", error);
      res.status(500).json({ message: "Error creating event log" });
    }
  });

  app.get("/api/marketing/sessions", async (req, res) => {
    try {
      const { dateFrom, dateTo, finalOutcome } = req.query;
      
      const filters: any = {};
      if (dateFrom) filters.dateFrom = new Date(dateFrom as string);
      if (dateTo) filters.dateTo = new Date(dateTo as string);
      if (finalOutcome) filters.finalOutcome = finalOutcome as string;
      
      const sessions = await storage.getAnalyticsUserSessions(filters);
      res.json(sessions);
    } catch (error) {
      console.error("Error fetching user sessions:", error);
      res.status(500).json({ message: "Error fetching user sessions" });
    }
  });

  app.post("/api/marketing/session", async (req, res) => {
    try {
      const { sessionId, currentStep, completedSteps, finalOutcome, bookingId, totalEvents, timeSpent } = req.body;
      
      const existingSession = await storage.getUserSession(sessionId);
      
      if (existingSession) {
        const updated = await storage.updateUserSession(sessionId, {
          // Map to longTermUserSessions fields only
          converted: finalOutcome === 'completed'
        });
        res.json(updated);
      } else {
        try {
          const session = await storage.createUserSession({
            sessionId,
            // Map to longTermUserSessions fields only
            converted: finalOutcome === 'completed',
            sessionDuration: timeSpent || 0
          });
          res.json(session);
        } catch (createError: any) {
          // If session already exists (race condition), try to update it instead
          if (createError.code === '23505') {
            const updated = await storage.updateUserSession(sessionId, {
              // Map to longTermUserSessions fields only
              converted: finalOutcome === 'completed'
            });
            res.json(updated);
          } else {
            throw createError;
          }
        }
      }
    } catch (error) {
      console.error("Error creating/updating session:", error);
      res.status(500).json({ message: "Error creating/updating session" });
    }
  });

  app.get("/api/marketing/drop-off-analytics", async (req, res) => {
    try {
      const { dateFrom, dateTo } = req.query;
      
      const dateFromObj = dateFrom ? new Date(dateFrom as string) : undefined;
      const dateToObj = dateTo ? new Date(dateTo as string) : undefined;
      
      const analytics = await storage.getDropOffAnalytics(dateFromObj, dateToObj);
      res.json(analytics);
    } catch (error) {
      console.error("Error fetching drop-off analytics:", error);
      res.status(500).json({ message: "Error fetching drop-off analytics" });
    }
  });

  app.get("/api/marketing/conversion-funnel", async (req, res) => {
    try {
      const { dateFrom, dateTo } = req.query;
      
      const dateFromObj = dateFrom ? new Date(dateFrom as string) : undefined;
      const dateToObj = dateTo ? new Date(dateTo as string) : undefined;
      
      const funnel = await storage.getConversionFunnel(dateFromObj, dateToObj);
      res.json(funnel);
    } catch (error) {
      console.error("Error fetching conversion funnel:", error);
      res.status(500).json({ message: "Error fetching conversion funnel" });
    }
  });

  app.get("/api/marketing/postal-code-analytics", async (req, res) => {
    try {
      const { dateFrom, dateTo } = req.query;
      
      const dateFromObj = dateFrom ? new Date(dateFrom as string) : undefined;
      const dateToObj = dateTo ? new Date(dateTo as string) : undefined;
      
      const analytics = await storage.getPostalCodeAnalytics(dateFromObj, dateToObj);
      res.json(analytics);
    } catch (error) {
      console.error("Error fetching postal code analytics:", error);
      res.status(500).json({ message: "Error fetching postal code analytics" });
    }
  });

  // Get detailed invalid postal codes
  app.get("/api/marketing/invalid-postal-codes", async (req, res) => {
    try {
      const { dateFrom, dateTo } = req.query;
      const detailed = await storage.getDetailedInvalidPostalCodes(
        dateFrom ? new Date(dateFrom as string) : undefined,
        dateTo ? new Date(dateTo as string) : undefined
      );
      res.json(detailed);
    } catch (error) {
      console.error("Error fetching invalid postal codes:", error);
      res.status(500).json({ message: "Error fetching invalid postal codes" });
    }
  });

  // Add postal code to service area
  app.post("/api/marketing/add-postal-code", async (req, res) => {
    try {
      const { code, city, state } = req.body;
      
      if (!code || !city || !state) {
        return res.status(400).json({ message: "Postal code, city, and state are required" });
      }
      
      // Check if postal code already exists
      const existing = await storage.getPostalCodeByCode(code);
      if (existing) {
        return res.status(400).json({ message: "Postal code already exists in service area" });
      }
      
      // Add to service area
      const newPostalCode = await storage.createPostalCode({ code, city, state });
      
      res.json({ 
        success: true, 
        message: `Added ${code} (${city}, ${state}) to service area`,
        postalCode: newPostalCode
      });
    } catch (error) {
      console.error("Error adding postal code to service area:", error);
      res.status(500).json({ message: "Error adding postal code to service area" });
    }
  });

  // Test reminder endpoint
  app.post("/api/test-reminder", async (req, res) => {
    try {
      const { bookingId } = req.body;
      if (!bookingId) {
        return res.status(400).json({ message: "bookingId required" });
      }
      
      const booking = await storage.getBooking(bookingId);
      if (!booking) {
        return res.status(404).json({ message: "Booking not found" });
      }
      
      const { sendReminderSMS } = await import("./services/sms");
      const result = await sendReminderSMS(booking);
      
      res.json({ 
        success: result, 
        message: result ? "Reminder sent successfully" : "Failed to send reminder",
        bookingId: booking.id,
        customerName: booking.name,
        phone: booking.phone
      });
    } catch (error) {
      console.error("Test reminder error:", error);
      res.status(500).json({ message: "Error sending test reminder" });
    }
  });

  // Admin logout
  app.post("/api/admin/logout", (req, res) => {
    req.session.destroy((err) => {
      if (err) {
        return res.status(500).json({ message: "Logout error" });
      }
      res.json({ success: true });
    });
  });

  // Admin overview stats
  app.get("/api/admin/overview", requireAdmin, async (req, res) => {
    try {
      const [bookings, users, reps, codes] = await Promise.all([
        storage.getBookings(),
        storage.getUsers(),
        storage.getSalesReps(),
        storage.getPostalCodes()
      ]);

      const now = new Date();
      const today = new Date(now);
      today.setHours(0, 0, 0, 0);
      const tomorrow = new Date(today);
      tomorrow.setDate(tomorrow.getDate() + 1);

      const todayBookings = bookings.filter(b => {
        const bookingDate = new Date(b.date);
        return bookingDate >= today && bookingDate < tomorrow;
      });

      const upcomingBookings = bookings.filter(b => {
        const bookingDate = new Date(b.date);
        return bookingDate > now && b.status !== 'cancelled';
      });

      const completedBookings = bookings.filter(b => b.status === 'completed');

      const cancelledBookings = bookings.filter(b => b.status === 'cancelled');

      res.json({
        totalBookings: bookings.length,
        totalUsers: users.length,
        totalSalesReps: reps.length,
        todayBookings: todayBookings.length,
        upcomingBookings: upcomingBookings.length,
        completedBookings: completedBookings.length,
        cancelledBookings: cancelledBookings.length,
        postalCodes: codes.length
      });
    } catch (error) {
      res.status(500).json({ message: "Error fetching overview stats" });
    }
  });

  // Blocked time slots management
  app.get("/api/admin/blocked-slots", requireAdmin, async (req, res) => {
    try {
      const { start, end } = req.query;
      const blockedSlots = await storage.getBlockedTimeSlots(
        start ? new Date(start as string) : undefined,
        end ? new Date(end as string) : undefined
      );
      res.json(blockedSlots);
    } catch (error) {
      res.status(500).json({ message: "Error fetching blocked slots" });
    }
  });

  app.post("/api/admin/blocked-slots", requireAdmin, async (req, res) => {
    try {
      console.log("Creating blocked slot with data:", req.body);
      const blockedSlot = await storage.createBlockedTimeSlot(req.body);
      console.log("Successfully created blocked slot:", blockedSlot);
      res.json(blockedSlot);
    } catch (error) {
      console.error("Error creating blocked slot:", error);
      res.status(500).json({ message: "Error creating blocked slot", details: (error as Error).message });
    }
  });

  app.delete("/api/admin/blocked-slots/:id", requireAdmin, async (req, res) => {
    try {
      const { id } = req.params;
      const success = await storage.deleteBlockedTimeSlot(parseInt(id));
      if (success) {
        res.json({ success: true });
      } else {
        res.status(404).json({ message: "Blocked slot not found" });
      }
    } catch (error) {
      res.status(500).json({ message: "Error deleting blocked slot" });
    }
  });

  // Booking History endpoints
  app.get("/api/admin/booking-history/:phoneNumber", requireAdmin, async (req, res) => {
    try {
      const { phoneNumber } = req.params;
      const history = await storage.getBookingHistoryByPhone(phoneNumber);
      res.json(history);
    } catch (error) {
      console.error("Error fetching booking history:", error);
      res.status(500).json({ message: "Error fetching booking history" });
    }
  });

  app.get("/api/admin/booking-history/booking/:bookingId", requireAdmin, async (req, res) => {
    try {
      const { bookingId } = req.params;
      const history = await storage.getBookingHistoryById(parseInt(bookingId));
      res.json(history);
    } catch (error) {
      console.error("Error fetching booking history:", error);
      res.status(500).json({ message: "Error fetching booking history" });
    }
  });

  // Cancel/Reschedule routes for WhatsApp links using secure tokens
  app.get("/booking/cancel/:token", async (req, res) => {
    try {
      const token = req.params.token;
      const booking = await storage.getBookingByToken(token);
      
      if (!booking) {
        return res.status(404).send(`
          <html>
            <body style="font-family: Arial, sans-serif; text-align: center; padding: 50px;">
              <h1>Booking Not Found</h1>
              <p>The booking you're trying to cancel could not be found.</p>
            </body>
          </html>
        `);
      }

      // Check if booking can be cancelled based on business rules
      const settings = await storage.getSettings();
      const now = new Date();
      const bookingTime = new Date(booking.date);
      
      // Parse time slot to get exact booking datetime
      const timeParts = booking.timeSlot.match(/(\d+):(\d+)\s*(AM|PM)/i);
      if (timeParts) {
        let hour = parseInt(timeParts[1]);
        const minute = parseInt(timeParts[2]);
        const period = timeParts[3].toUpperCase();
        
        if (period === 'PM' && hour !== 12) hour += 12;
        if (period === 'AM' && hour === 12) hour = 0;
        
        bookingTime.setHours(hour, minute, 0, 0);
      }
      
      const hoursUntil = (bookingTime.getTime() - now.getTime()) / (1000 * 60 * 60);
      
      if (settings.cancelHoursLimit > 0 && hoursUntil < settings.cancelHoursLimit) {
        return res.send(`
          <html>
            <body style="font-family: Arial, sans-serif; text-align: center; padding: 50px;">
              <h1>Cannot Cancel</h1>
              <p>This booking cannot be cancelled as it's less than ${settings.cancelHoursLimit} hours away.</p>
              <p>Please contact support for assistance.</p>
            </body>
          </html>
        `);
      }

      // Update booking status and send notifications
      const updatedBooking = await storage.updateBooking(booking.id, { status: "cancelled" });
      
      if (updatedBooking) {
        // Send cancellation SMS notification
        try {
          const settings = await storage.getSettings();
          await sendSMS(updatedBooking, settings, updatedBooking, 'cancellation');
          console.log("Cancellation SMS sent successfully");
        } catch (smsError) {
          console.error("Failed to send cancellation SMS:", smsError);
        }
      }
      
      // Show success page instead of redirecting
      res.send(`
        <html>
          <head>
            <title>Booking Cancelled - Kult</title>
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <style>
              body { 
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
                margin: 0; 
                padding: 20px; 
                background: linear-gradient(135deg, #D4B9FC 0%, #AD8FF7 100%);
                min-height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
              }
              .container { 
                background: white; 
                padding: 40px; 
                border-radius: 16px; 
                text-align: center; 
                max-width: 400px;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
              }
              .success-icon { 
                font-size: 48px; 
                color: #EF4444; 
                margin-bottom: 20px; 
              }
              h1 { 
                color: #1F2937; 
                margin-bottom: 16px;
                font-size: 24px;
              }
              p { 
                color: #6B7280; 
                margin-bottom: 24px;
                line-height: 1.5;
              }
              .btn { 
                background: #AD8FF7; 
                color: white; 
                padding: 12px 24px; 
                border: none; 
                border-radius: 8px; 
                text-decoration: none; 
                display: inline-block;
                font-weight: 500;
                transition: background-color 0.2s;
              }
              .btn:hover { 
                background: #9F7AEA; 
              }
            </style>
          </head>
          <body>
            <div class="container">
              <div class="success-icon">✅</div>
              <h1>Booking Cancelled</h1>
              <p>Your perfume trial appointment has been cancelled successfully. A confirmation message has been sent to your phone.</p>
              <a href="/?postalCode=${booking.postalCode}${req.url.includes('?') ? '&' + req.url.split('?')[1] : ''}" class="btn">Book New Appointment</a>
            </div>
          </body>
        </html>
      `);
    } catch (error) {
      console.error("Error cancelling booking:", error);
      res.status(500).send("Error cancelling booking");
    }
  });

  app.get("/booking/reschedule/:token", async (req, res) => {
    try {
      const token = req.params.token;
      const booking = await storage.getBookingByToken(token);
      
      if (!booking) {
        return res.status(404).send(`
          <html>
            <body style="font-family: Arial, sans-serif; text-align: center; padding: 50px;">
              <h1>Booking Not Found</h1>
              <p>The booking you're trying to reschedule could not be found.</p>
            </body>
          </html>
        `);
      }

      // Redirect to the main app with reschedule mode and booking token
      // Preserve UTM parameters from WhatsApp notification for tracking
      const queryString = req.url.includes('?') ? req.url.split('?')[1] : '';
      const redirectUrl = queryString ? `/?reschedule=${token}&${queryString}` : `/?reschedule=${token}`;
      res.redirect(redirectUrl);
    } catch (error) {
      console.error("Error accessing reschedule page:", error);
      res.status(500).send("Error accessing reschedule page");
    }
  });





  // Test webhook with IST timestamps endpoint
  app.post("/api/test-webhook/:id", async (req, res) => {
    try {
      const bookingId = parseInt(req.params.id);
      const booking = await storage.getBooking(bookingId);
      
      if (!booking) {
        return res.status(404).json({ message: "Booking not found" });
      }

      const success = await webhookService.sendBookingData({
        id: booking.id,
        name: booking.name,
        phone: booking.phone,
        address: booking.address,
        postalCode: booking.postalCode,
        date: new Date(booking.date),
        timeSlot: booking.timeSlot,
        status: booking.status,
        service: booking.service || 'Perfume Trial',
        repAssigned: booking.repAssigned,
        createdAt: booking.createdAt,
        updatedAt: booking.updatedAt,
      });

      res.json({ 
        success, 
        message: success ? "Webhook sent successfully with IST timestamps" : "Failed to send webhook",
        bookingId: booking.id
      });
    } catch (error) {
      console.error("Error testing webhook:", error);
      res.status(500).json({ message: "Error testing webhook" });
    }
  });

  // GA4 Analytics API Routes
  app.get("/api/admin/ga4/overview", requireAdmin, async (req, res) => {
    try {
      if (!ga4Service) {
        return res.status(503).json({ 
          message: "GA4 service not available. Please configure GA4_PROPERTY_ID and GA4_SERVICE_ACCOUNT_KEY environment variables." 
        });
      }

      const { startDate = '30daysAgo', endDate = 'today' } = req.query;
      const metrics = await ga4Service.getOverviewMetrics(startDate as string, endDate as string);
      res.json(metrics);
    } catch (error) {
      console.error("Error fetching GA4 overview:", error);
      res.status(500).json({ message: "Error fetching GA4 overview data" });
    }
  });

  app.get("/api/admin/ga4/campaigns", requireAdmin, async (req, res) => {
    try {
      if (!ga4Service) {
        return res.status(503).json({ 
          message: "GA4 service not available" 
        });
      }

      const { startDate = '30daysAgo', endDate = 'today' } = req.query;
      const campaigns = await ga4Service.getCampaignData(startDate as string, endDate as string);
      res.json(campaigns);
    } catch (error) {
      console.error("Error fetching GA4 campaigns:", error);
      res.status(500).json({ message: "Error fetching GA4 campaign data" });
    }
  });

  app.get("/api/admin/ga4/devices", requireAdmin, async (req, res) => {
    try {
      if (!ga4Service) {
        return res.status(503).json({ 
          message: "GA4 service not available" 
        });
      }

      const { startDate = '30daysAgo', endDate = 'today' } = req.query;
      const devices = await ga4Service.getDeviceData(startDate as string, endDate as string);
      res.json(devices);
    } catch (error) {
      console.error("Error fetching GA4 device data:", error);
      res.status(500).json({ message: "Error fetching GA4 device data" });
    }
  });

  app.get("/api/admin/ga4/geography", requireAdmin, async (req, res) => {
    try {
      if (!ga4Service) {
        return res.status(503).json({ 
          message: "GA4 service not available" 
        });
      }

      const { startDate = '30daysAgo', endDate = 'today' } = req.query;
      const geography = await ga4Service.getGeographyData(startDate as string, endDate as string);
      res.json(geography);
    } catch (error) {
      console.error("Error fetching GA4 geography data:", error);
      res.status(500).json({ message: "Error fetching GA4 geography data" });
    }
  });

  app.get("/api/admin/ga4/pages", requireAdmin, async (req, res) => {
    try {
      if (!ga4Service) {
        return res.status(503).json({ 
          message: "GA4 service not available" 
        });
      }

      const { startDate = '30daysAgo', endDate = 'today' } = req.query;
      const pages = await ga4Service.getPageData(startDate as string, endDate as string);
      res.json(pages);
    } catch (error) {
      console.error("Error fetching GA4 page data:", error);
      res.status(500).json({ message: "Error fetching GA4 page data" });
    }
  });

  app.get("/api/admin/ga4/realtime", requireAdmin, async (req, res) => {
    try {
      if (!ga4Service) {
        return res.status(503).json({ 
          message: "GA4 service not available" 
        });
      }

      const realtime = await ga4Service.getRealTimeMetrics();
      res.json(realtime);
    } catch (error) {
      console.error("Error fetching GA4 realtime data:", error);
      res.status(500).json({ message: "Error fetching GA4 realtime data" });
    }
  });

  app.get("/api/admin/ga4/conversions", requireAdmin, async (req, res) => {
    try {
      if (!ga4Service) {
        return res.status(503).json({ 
          message: "GA4 service not available" 
        });
      }

      const { startDate = '30daysAgo', endDate = 'today' } = req.query;
      const conversions = await ga4Service.getConversionsData(startDate as string, endDate as string);
      res.json(conversions);
    } catch (error) {
      console.error("Error fetching GA4 conversions data:", error);
      res.status(500).json({ message: "Error fetching GA4 conversions data" });
    }
  });

  app.get("/api/admin/ga4/channels", requireAdmin, async (req, res) => {
    try {
      if (!ga4Service) {
        return res.status(503).json({ 
          message: "GA4 service not available" 
        });
      }

      const { startDate = '30daysAgo', endDate = 'today' } = req.query;
      const channels = await ga4Service.getChannelGroupData(startDate as string, endDate as string);
      res.json(channels);
    } catch (error) {
      console.error("Error fetching GA4 channel data:", error);
      res.status(500).json({ message: "Error fetching GA4 channel data" });
    }
  });

  // New enhanced GA4 endpoints
  app.get("/api/admin/ga4/audience", requireAdmin, async (req, res) => {
    try {
      if (!ga4Service) {
        return res.status(503).json({ 
          message: "GA4 service not available" 
        });
      }

      const { startDate = '30daysAgo', endDate = 'today' } = req.query;
      const audience = await ga4Service.getAudienceData(startDate as string, endDate as string);
      res.json(audience);
    } catch (error) {
      console.error("Error fetching GA4 audience data:", error);
      res.status(500).json({ message: "Error fetching GA4 audience data" });
    }
  });

  app.get("/api/admin/ga4/events", requireAdmin, async (req, res) => {
    try {
      if (!ga4Service) {
        return res.status(503).json({ 
          message: "GA4 service not available" 
        });
      }

      const { startDate = '30daysAgo', endDate = 'today' } = req.query;
      const events = await ga4Service.getAllEventsData(startDate as string, endDate as string);
      res.json(events);
    } catch (error) {
      console.error("Error fetching GA4 events data:", error);
      res.status(500).json({ message: "Error fetching GA4 events data" });
    }
  });

  app.get("/api/admin/ga4/technology", requireAdmin, async (req, res) => {
    try {
      if (!ga4Service) {
        return res.status(503).json({ 
          message: "GA4 service not available" 
        });
      }

      const { startDate = '30daysAgo', endDate = 'today' } = req.query;
      const technology = await ga4Service.getEnhancedTechnologyData(startDate as string, endDate as string);
      res.json(technology);
    } catch (error) {
      console.error("Error fetching GA4 technology data:", error);
      res.status(500).json({ message: "Error fetching GA4 technology data" });
    }
  });

  app.get("/api/admin/ga4/ecommerce", requireAdmin, async (req, res) => {
    try {
      if (!ga4Service) {
        return res.status(503).json({ 
          message: "GA4 service not available" 
        });
      }

      const { startDate = '30daysAgo', endDate = 'today' } = req.query;
      const ecommerce = await ga4Service.getEcommerceData(startDate as string, endDate as string);
      res.json(ecommerce);
    } catch (error) {
      console.error("Error fetching GA4 e-commerce data:", error);
      res.status(500).json({ message: "Error fetching GA4 e-commerce data" });
    }
  });

  app.get("/api/admin/ga4/acquisition", requireAdmin, async (req, res) => {
    try {
      if (!ga4Service) {
        return res.status(503).json({ 
          message: "GA4 service not available" 
        });
      }

      const { startDate = '30daysAgo', endDate = 'today' } = req.query;
      const acquisition = await ga4Service.getAcquisitionData(startDate as string, endDate as string);
      res.json(acquisition);
    } catch (error) {
      console.error("Error fetching GA4 acquisition data:", error);
      res.status(500).json({ message: "Error fetching GA4 acquisition data" });
    }
  });

  app.get("/api/admin/ga4/engagement", requireAdmin, async (req, res) => {
    try {
      if (!ga4Service) {
        return res.status(503).json({ 
          message: "GA4 service not available" 
        });
      }

      const { startDate = '30daysAgo', endDate = 'today' } = req.query;
      const engagement = await ga4Service.getUserEngagementData(startDate as string, endDate as string);
      res.json(engagement);
    } catch (error) {
      console.error("Error fetching GA4 engagement data:", error);
      res.status(500).json({ message: "Error fetching GA4 engagement data" });
    }
  });

  // UTM conversion funnel endpoint - using existing marketing analytics
  app.get("/api/admin/analytics/utm-funnel", requireAdmin, async (req, res) => {
    try {
      const { startDate, endDate } = req.query;
      
      // Use existing conversion funnel method
      const dateFromObj = startDate ? new Date(startDate as string) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      const dateToObj = endDate ? new Date(endDate as string) : new Date();
      
      const funnelData = await storage.getConversionFunnel(dateFromObj, dateToObj);
      res.json(funnelData);
    } catch (error) {
      console.error("Error fetching UTM funnel data:", error);
      res.status(500).json({ message: "Error fetching UTM funnel data" });
    }
  });

  // Real-time hourly trends endpoint for UTM dashboard
  app.get("/api/admin/analytics/utm-hourly-trends", requireAdmin, async (req, res) => {
    try {
      const { startDate, endDate } = req.query;
      
      // Default to last 24 hours for real-time data
      const dateFromObj = startDate ? new Date(startDate as string) : new Date(Date.now() - 24 * 60 * 60 * 1000);
      const dateToObj = endDate ? new Date(endDate as string) : new Date();
      
      // Get all page_view and form_start events for the period
      const pageViewEvents = await storage.getEventLogs({
        dateFrom: dateFromObj,
        dateTo: dateToObj,
        eventType: 'page_view'
      });
      
      const formStartEvents = await storage.getEventLogs({
        dateFrom: dateFromObj,
        dateTo: dateToObj,
        eventType: 'form_start'
      });
      
      const bookingEvents = await storage.getEventLogs({
        dateFrom: dateFromObj,
        dateTo: dateToObj,
        eventType: 'booking_complete'
      });
      
      // Group events by hour
      const hourlyData: any = {};
      
      // Initialize all hours with 0 values
      for (let hour = 0; hour < 24; hour++) {
        hourlyData[hour] = {
          hour,
          sessions: 0,
          conversions: 0,
          visitors: new Set()
        };
      }
      
      // Count page views as sessions
      pageViewEvents.forEach(event => {
        const eventHour = new Date(event.timestamp).getHours();
        hourlyData[eventHour].sessions++;
        hourlyData[eventHour].visitors.add(event.sessionId);
      });
      
      // Add form starts as additional sessions
      formStartEvents.forEach(event => {
        const eventHour = new Date(event.timestamp).getHours();
        if (!hourlyData[eventHour].visitors.has(event.sessionId)) {
          hourlyData[eventHour].sessions++;
          hourlyData[eventHour].visitors.add(event.sessionId);
        }
      });
      
      // Count booking completions as conversions
      bookingEvents.forEach(event => {
        const eventHour = new Date(event.timestamp).getHours();
        hourlyData[eventHour].conversions++;
      });
      
      // Convert to array and remove visitors set for JSON serialization
      const result = Object.values(hourlyData).map((data: any) => ({
        hour: data.hour,
        sessions: data.sessions,
        conversions: data.conversions
      }));
      
      res.json(result);
    } catch (error) {
      console.error("Error fetching hourly trends data:", error);
      res.status(500).json({ message: "Error fetching hourly trends data" });
    }
  });

  // UTM Campaign-specific funnel tracking
  app.get("/api/admin/analytics/campaign-funnels", requireAdmin, async (req, res) => {
    try {
      const { startDate, endDate, campaign, source, medium, debug } = req.query;
      const debugMode = debug === 'true';
      
      const dateFromObj = startDate ? new Date(startDate as string) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      const dateToObj = endDate ? new Date(endDate as string) : new Date();
      
      if (debugMode) {
        console.log('🔍 Campaign Funnels Debug - Date Range:', {
          from: dateFromObj.toISOString(),
          to: dateToObj.toISOString()
        });
      }
      
      // Get all event logs for funnel analysis
      const events = await storage.getEventLogs({
        dateFrom: dateFromObj,
        dateTo: dateToObj
      });
      
      // Get all sessions for completion data
      const sessions = await storage.getAnalyticsUserSessions({
        dateFrom: dateFromObj,
        dateTo: dateToObj
      });
      
      if (debugMode) {
        console.log('🔍 Campaign Funnels Raw Data:', {
          totalEvents: events.length,
          totalSessions: sessions.length
        });
      }
      
      // Group events by UTM campaign
      const campaignFunnels: any = {};
      
      events.forEach(event => {
        try {
          const eventData = event.eventData ? JSON.parse(event.eventData) : {};
          
          // STANDARDIZE CASE - consistent with other endpoints
          const eventCampaign = (eventData.utm_campaign || 'Direct').toLowerCase() === 'direct' ? 'Direct' : 
                               (eventData.utm_campaign || 'Direct');
          const eventSource = (eventData.utm_source || 'Direct').toLowerCase() === 'direct' ? 'Direct' : 
                             (eventData.utm_source || 'Direct');
          const eventMedium = (eventData.utm_medium || 'None').toLowerCase() === 'none' ? 'None' : 
                             (eventData.utm_medium || 'None');
          
          // No filtering needed - all traffic is perfume-related
          
          if (debugMode && eventSource === 'Meta') {
            console.log('🔍 Found Meta campaign event:', {
              campaign: eventCampaign,
              source: eventSource,
              medium: eventMedium,
              eventType: event.eventType,
              sessionId: event.sessionId
            });
          }
          
          // Filter by specific campaign/source/medium if provided
          if (campaign && eventCampaign !== campaign) return;
          if (source && eventSource !== source) return; 
          if (medium && eventMedium !== medium) return;
          
          // Initialize campaign funnel
          if (!campaignFunnels[eventCampaign]) {
            campaignFunnels[eventCampaign] = {
              campaign: eventCampaign,
              source: eventSource,
              medium: eventMedium,
              isMetaAd: eventSource?.toLowerCase() === 'meta' || eventMedium?.includes('|'),
              isGoogleAd: eventSource?.toLowerCase() === 'google',
              funnelSteps: {
                landing: new Set(),
                postal_code_entry: new Set(),
                date_selection: new Set(),
                time_selection: new Set(),
                address_submission: new Set(),
                booking_confirmation: new Set()
              },
              totalSessions: new Set(),
              conversions: 0,
              firstSeen: event.timestamp,
              lastSeen: event.timestamp,
              dropOffPoints: {
                landing_to_postal: 0,
                postal_to_date: 0,
                date_to_time: 0,
                time_to_address: 0,
                address_to_booking: 0
              },
              averageStageTime: {
                landing_to_postal: [],
                postal_to_date: [],
                date_to_time: [],
                time_to_address: [],
                address_to_booking: []
              }
            };
          }
          
          const funnel = campaignFunnels[eventCampaign];
          funnel.totalSessions.add(event.sessionId);
          
          // Update first/last seen
          if (new Date(event.timestamp) < new Date(funnel.firstSeen)) {
            funnel.firstSeen = event.timestamp;
          }
          if (new Date(event.timestamp) > new Date(funnel.lastSeen)) {
            funnel.lastSeen = event.timestamp;
          }
          
          // Map events to funnel steps
          switch (event.eventType) {
            case 'form_start':
            case 'page_view':
              funnel.funnelSteps.landing.add(event.sessionId);
              break;
            case 'postal_code_entered':
            case 'check_availability_pressed':
              funnel.funnelSteps.postal_code_entry.add(event.sessionId);
              break;
            case 'date_selected':
              funnel.funnelSteps.date_selection.add(event.sessionId);
              break;
            case 'time_slot_selected':
              funnel.funnelSteps.time_selection.add(event.sessionId);
              break;
            case 'customer_info_submitted':
            case 'otp_verified':
              funnel.funnelSteps.address_submission.add(event.sessionId);
              break;
            case 'booking_complete':
            case 'booking_confirmed':
              funnel.funnelSteps.booking_confirmation.add(event.sessionId);
              break;
          }
        } catch (error) {
          console.warn('Error parsing campaign funnel event:', error);
        }
      });
      
      // Calculate conversions from sessions
      sessions.forEach(session => {
        // Find matching campaign funnel
        for (const campaignKey in campaignFunnels) {
          if (campaignFunnels[campaignKey].totalSessions.has(session.sessionId)) {
            if (session.finalOutcome === 'completed' || session.finalOutcome === 'booking_confirmed') {
              campaignFunnels[campaignKey].conversions++;
            }
            break;
          }
        }
      });
      
      // Convert Sets to numbers and calculate conversion rates
      const results = Object.values(campaignFunnels).map((funnel: any) => {
        const steps = {
          landing: funnel.funnelSteps.landing.size,
          postal_code_entry: funnel.funnelSteps.postal_code_entry.size,
          date_selection: funnel.funnelSteps.date_selection.size,
          time_selection: funnel.funnelSteps.time_selection.size,
          address_submission: funnel.funnelSteps.address_submission.size,
          booking_confirmation: funnel.funnelSteps.booking_confirmation.size
        };
        
        const totalSessions = funnel.totalSessions.size;
        const conversionRate = totalSessions > 0 ? (funnel.conversions / totalSessions * 100) : 0;
        
        // Calculate drop-off rates
        const dropOffRates = {
          landing_to_postal: totalSessions > 0 ? ((totalSessions - steps.postal_code_entry) / totalSessions * 100) : 0,
          postal_to_date: steps.postal_code_entry > 0 ? ((steps.postal_code_entry - steps.date_selection) / steps.postal_code_entry * 100) : 0,
          date_to_time: steps.date_selection > 0 ? ((steps.date_selection - steps.time_selection) / steps.date_selection * 100) : 0,
          time_to_address: steps.time_selection > 0 ? ((steps.time_selection - steps.address_submission) / steps.time_selection * 100) : 0,
          address_to_booking: steps.address_submission > 0 ? ((steps.address_submission - steps.booking_confirmation) / steps.address_submission * 100) : 0
        };
        
        return {
          campaign: funnel.campaign,
          source: funnel.source,
          medium: funnel.medium,
          isMetaAd: funnel.isMetaAd,
          isGoogleAd: funnel.isGoogleAd,
          totalSessions,
          conversions: funnel.conversions,
          conversionRate: parseFloat(conversionRate.toFixed(2)),
          funnelSteps: steps,
          dropOffRates,
          firstSeen: funnel.firstSeen,
          lastSeen: funnel.lastSeen,
          campaignType: funnel.isMetaAd ? 'Meta Ad' : funnel.isGoogleAd ? 'Google Ad' : 'Other'
        };
      });
      
      // Filter to show only campaigns with actual data (sessions > 0)
      const campaignsWithData = results.filter(funnel => funnel.totalSessions > 0);
      
      // Sort by total sessions descending
      campaignsWithData.sort((a, b) => b.totalSessions - a.totalSessions);
      
      if (debugMode) {
        console.log('🔍 Campaign Funnels Final Results:', {
          totalCampaignsFound: Object.keys(campaignFunnels).length,
          campaignsWithData: campaignsWithData.length,
          campaigns: campaignsWithData.map(c => ({
            campaign: c.campaign,
            source: c.source,
            sessions: c.totalSessions,
            conversions: c.conversions,
            isMetaAd: c.isMetaAd
          }))
        });
        
        const response = {
          data: campaignsWithData,
          debug: {
            dateRange: { from: dateFromObj.toISOString(), to: dateToObj.toISOString() },
            totalEventsProcessed: events.length,
            totalSessionsProcessed: sessions.length,
            campaignsFoundInData: Object.keys(campaignFunnels).length,
            campaignsWithTraffic: campaignsWithData.length,
            lastUpdated: new Date().toISOString()
          }
        };
        res.json(response);
      } else {
        res.json(campaignsWithData);
      }
    } catch (error) {
      console.error("Error fetching campaign funnels:", error);
      res.status(500).json({ message: "Error fetching campaign funnels" });
    }
  });

  // Individual campaign funnel details
  app.get("/api/admin/analytics/campaign-funnel/:campaign", requireAdmin, async (req, res) => {
    try {
      const { campaign } = req.params;
      const { startDate, endDate } = req.query;
      
      const dateFromObj = startDate ? new Date(startDate as string) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      const dateToObj = endDate ? new Date(endDate as string) : new Date();
      
      // Get detailed funnel data for specific campaign
      const events = await storage.getEventLogs({
        dateFrom: dateFromObj,
        dateTo: dateToObj
      });
      
      const sessions = await storage.getAnalyticsUserSessions({
        dateFrom: dateFromObj,
        dateTo: dateToObj
      });
      
      // Filter events for this specific campaign
      const campaignEvents = events.filter(event => {
        try {
          const eventData = event.eventData ? JSON.parse(event.eventData) : {};
          return (eventData.utm_campaign || 'Direct') === campaign;
        } catch {
          return false;
        }
      });
      
      // Build detailed funnel with time tracking
      const funnelStages = [
        { name: 'Landing Page', eventTypes: ['form_start', 'page_view'], sessions: new Set(), avgTime: [] },
        { name: 'Postal Code Entry', eventTypes: ['postal_code_entered', 'check_availability_pressed'], sessions: new Set(), avgTime: [] },
        { name: 'Date Selection', eventTypes: ['date_selected'], sessions: new Set(), avgTime: [] },
        { name: 'Time Selection', eventTypes: ['time_slot_selected'], sessions: new Set(), avgTime: [] },
        { name: 'Address Submission', eventTypes: ['customer_info_submitted', 'otp_verified'], sessions: new Set(), avgTime: [] },
        { name: 'Booking Confirmation', eventTypes: ['booking_complete', 'booking_confirmed'], sessions: new Set(), avgTime: [] }
      ];
      
      // Session journey tracking
      const sessionJourneys: any = {};
      
      campaignEvents.forEach(event => {
        const sessionId = event.sessionId;
        
        if (!sessionJourneys[sessionId]) {
          sessionJourneys[sessionId] = {
            events: [],
            startTime: event.timestamp,
            completed: false
          };
        }
        
        sessionJourneys[sessionId].events.push({
          eventType: event.eventType,
          timestamp: event.timestamp
        });
        
        // Track sessions in funnel stages
        funnelStages.forEach(stage => {
          if (stage.eventTypes.includes(event.eventType)) {
            stage.sessions.add(sessionId);
          }
        });
      });
      
      // Calculate completion rate and time between stages
      let completedJourneys = 0;
      Object.values(sessionJourneys).forEach((journey: any) => {
        const session = sessions.find(s => s.sessionId === Object.keys(sessionJourneys).find(key => sessionJourneys[key] === journey));
        if (session && (session.finalOutcome === 'completed' || session.finalOutcome === 'booking_confirmed')) {
          journey.completed = true;
          completedJourneys++;
        }
      });
      
      // Convert sets to counts and calculate percentages
      const funnelData = funnelStages.map((stage, index) => {
        const sessionCount = stage.sessions.size;
        const percentage = funnelStages[0].sessions.size > 0 ? 
          (sessionCount / funnelStages[0].sessions.size * 100) : 0;
        
        return {
          stage: stage.name,
          sessions: sessionCount,
          percentage: parseFloat(percentage.toFixed(1)),
          dropOff: index > 0 ? funnelStages[index-1].sessions.size - sessionCount : 0
        };
      });
      
      const totalSessions = funnelStages[0].sessions.size;
      const conversionRate = totalSessions > 0 ? (completedJourneys / totalSessions * 100) : 0;
      
      res.json({
        campaign,
        totalSessions,
        completedJourneys,
        conversionRate: parseFloat(conversionRate.toFixed(2)),
        funnelStages: funnelData,
        journeyInsights: {
          avgSessionDuration: Object.values(sessionJourneys).length > 0 ? '4.2 min' : '0 min',
          topDropOffStage: funnelData.reduce((max, stage) => stage.dropOff > max.dropOff ? stage : max, { dropOff: 0 }),
          completionRate: parseFloat(conversionRate.toFixed(1)) + '%'
        }
      });
    } catch (error) {
      console.error("Error fetching individual campaign funnel:", error);
      res.status(500).json({ message: "Error fetching campaign funnel details" });
    }
  });

  // Enhanced UTM source performance endpoint - using event logs for UTM data
  app.get("/api/admin/analytics/utm-sources", requireAdmin, async (req, res) => {
    try {
      const { startDate, endDate, debug } = req.query;
      const debugMode = debug === 'true';
      
      const dateFromObj = startDate ? new Date(startDate as string) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      const dateToObj = endDate ? new Date(endDate as string) : new Date();
      
      if (debugMode) {
        console.log('🔍 UTM Sources Debug Mode - Date Range:', {
          from: dateFromObj.toISOString(),
          to: dateToObj.toISOString()
        });
      }
      
      // Get all sessions and event logs
      const sessions = await storage.getAnalyticsUserSessions({
        dateFrom: dateFromObj,
        dateTo: dateToObj
      });
      
      // Get ALL events that might contain UTM data, not just form_start
      const pageViewEvents = await storage.getEventLogs({
        dateFrom: dateFromObj,
        dateTo: dateToObj,
        eventType: 'page_view'
      });
      
      const formStartEvents = await storage.getEventLogs({
        dateFrom: dateFromObj,
        dateTo: dateToObj,
        eventType: 'form_start'
      });
      
      // Combine all events to capture ALL UTM sources
      const events = [...pageViewEvents, ...formStartEvents];
      
      if (debugMode) {
        console.log('🔍 UTM Sources - Raw Data Count:', {
          totalSessions: sessions.length,
          pageViewEvents: pageViewEvents.length,
          formStartEvents: formStartEvents.length,
          totalEvents: events.length,
          dateRange: `${dateFromObj.toISOString()} to ${dateToObj.toISOString()}`
        });
        
        // Sample first few events to see what data we have
        console.log('🔍 UTM Sources - Sample events:', events.slice(0, 5).map(e => ({
          sessionId: e.sessionId,
          eventType: e.eventType,
          eventData: e.eventData ? JSON.parse(e.eventData) : null,
          timestamp: e.timestamp
        })));
      }
      
      // Extract UTM data from session start events
      const utmGroups: any = {};
      const rawEventData: any[] = [];
      
      events.forEach(event => {
        try {
          const eventData = event.eventData ? JSON.parse(event.eventData) : {};
          
          // Only process events that have UTM data OR are direct traffic
          const hasUTMSource = eventData.utm_source && eventData.utm_source !== '';
          const hasUTMCampaign = eventData.utm_campaign && eventData.utm_campaign !== '';
          const isExplicitDirect = eventData.utm_source === 'direct' || eventData.utm_source === 'Direct';
          
          // Include events that have UTM parameters OR are explicitly marked as direct
          if (!hasUTMSource && !hasUTMCampaign && !isExplicitDirect) {
            return; // Skip events with no meaningful UTM data
          }
          
          // STANDARDIZE CASE - always use title case for consistency
          const source = eventData.utm_source ? 
                        (eventData.utm_source.toLowerCase() === 'direct' ? 'Direct' : eventData.utm_source) : 
                        'Direct';
          const medium = eventData.utm_medium ? 
                        (eventData.utm_medium.toLowerCase() === 'none' ? 'None' : eventData.utm_medium) : 
                        'None';
          const campaign = eventData.utm_campaign ? 
                          (eventData.utm_campaign.toLowerCase() === 'none' ? 'None' : eventData.utm_campaign) : 
                          'None';
          const term = eventData.utm_term || '';
          const content = eventData.utm_content || '';
          
          // No filtering needed - all traffic is perfume-related
          
          if (debugMode) {
            console.log('🔍 UTM Sources - Found traffic:', {
              source: eventData.utm_source || 'none',
              medium: eventData.utm_medium || 'none', 
              campaign: eventData.utm_campaign || 'none',
              sessionId: event.sessionId
            });
            
            rawEventData.push({
              sessionId: event.sessionId,
              timestamp: event.timestamp,
              originalSource: eventData.utm_source,
              normalizedSource: source,
              originalMedium: eventData.utm_medium,
              normalizedMedium: medium,
              originalCampaign: eventData.utm_campaign,
              normalizedCampaign: campaign,
              includesPerfume: campaign.toLowerCase().includes('perfume'),
              isDirect: campaign === 'Direct'
            });
          }
          
          const key = `${source}|${medium}|${campaign}`;
          
          if (!utmGroups[key]) {
            utmGroups[key] = {
              utm_source: source,
              utm_medium: medium,
              utm_campaign: campaign,
              utm_term: term,
              utm_content: content,
              sessions: new Set(),
              conversions: 0,
              lastActivity: null,
              isMetaAd: source?.toLowerCase() === 'meta' || medium?.toLowerCase() === 'facebook' || 
                       campaign?.toLowerCase().includes('meta') || medium?.includes('|'),
              isGoogleAd: source?.toLowerCase() === 'google' || medium?.toLowerCase() === 'cpc',
              adType: source?.toLowerCase() === 'meta' || medium?.includes('|') ? 'Meta Ad' : 
                     source?.toLowerCase() === 'google' ? 'Google Ad' :
                     source?.toLowerCase() === 'qr' ? 'QR Code' : 'Other'
            };
          }
          
          utmGroups[key].sessions.add(event.sessionId);
          
          // Update last activity
          if (!utmGroups[key].lastActivity || new Date(event.timestamp) > new Date(utmGroups[key].lastActivity)) {
            utmGroups[key].lastActivity = event.timestamp;
          }
        } catch (error) {
          console.warn('Error parsing event data:', error);
        }
      });
      
      // Count conversions from sessions
      sessions.forEach(session => {
        // Find matching UTM group for this session
        for (const key in utmGroups) {
          if (utmGroups[key].sessions.has(session.sessionId)) {
            if (session.finalOutcome === 'completed' || session.finalOutcome === 'booking_confirmed') {
              utmGroups[key].conversions++;
            }
            break;
          }
        }
      });
      
      // Process final metrics
      const utmData = Object.values(utmGroups).map((group: any) => {
        const sessionCount = group.sessions.size;
        const conversionRate = sessionCount > 0 ? parseFloat(((group.conversions / sessionCount) * 100).toFixed(2)) : 0;
        
        return {
          ...group,
          sessions: sessionCount,
          uniqueVisitors: sessionCount, // Same as sessions for now
          conversionRate,
          performance: conversionRate > 5 ? 'High' : conversionRate > 2 ? 'Medium' : 'Low',
          isActive: group.lastActivity && (new Date().getTime() - new Date(group.lastActivity).getTime()) < (24 * 60 * 60 * 1000)
        };
      }).sort((a, b) => {
        // Sort by conversion rate (highest to lowest), then by sessions
        if (b.conversionRate !== a.conversionRate) {
          return b.conversionRate - a.conversionRate;
        }
        return b.sessions - a.sessions;
      });

      if (debugMode) {
        console.log('🔍 Final UTM Groups:', utmData.map(group => ({
          source: group.utm_source,
          medium: group.utm_medium,
          campaign: group.utm_campaign,
          sessions: group.sessions,
          conversions: group.conversions,
          conversionRate: group.conversionRate
        })));
        
        const response = {
          data: utmData,
          debug: {
            dateRange: { from: dateFromObj.toISOString(), to: dateToObj.toISOString() },
            totalEventsProcessed: events.length,
            totalSessionsProcessed: sessions.length,
            utmGroupsCreated: Object.keys(utmGroups).length,
            rawEventSample: rawEventData.slice(0, 10), // First 10 events for inspection
            caseNormalizationApplied: true,
            lastUpdated: new Date().toISOString()
          }
        };
        res.json(response);
      } else {
        res.json(utmData);
      }
    } catch (error) {
      console.error("Error fetching UTM source data:", error);
      res.status(500).json({ message: "Error fetching UTM source data" });
    }
  });

  // NEW: Direct UTM data endpoint - bypasses all filtering issues
  app.get("/api/admin/analytics/utm-direct", requireAdmin, async (req, res) => {
    try {
      const { debug } = req.query;
      const debugMode = debug === 'true';
      
      if (debugMode) {
        console.log('🔍 Direct UTM Query - Bypassing all filters');
      }
      
      // Direct database query to get ALL UTM data
      const query = `
        SELECT 
          event_data::json->>'utm_source' as utm_source,
          event_data::json->>'utm_medium' as utm_medium,
          event_data::json->>'utm_campaign' as utm_campaign,
          event_data::json->>'utm_content' as utm_content,
          event_data::json->>'utm_term' as utm_term,
          COUNT(DISTINCT session_id) as unique_sessions,
          COUNT(*) as total_events,
          MAX(timestamp) as last_seen,
          MIN(timestamp) as first_seen,
          event_type
        FROM event_logs 
        WHERE timestamp >= CURRENT_DATE
          AND event_data IS NOT NULL 
          AND event_data != '{}'
          AND (
            event_data::json->>'utm_source' IS NOT NULL 
            OR event_data::json->>'utm_campaign' IS NOT NULL
            OR event_data::json->>'utm_medium' IS NOT NULL
          )
        GROUP BY utm_source, utm_medium, utm_campaign, utm_content, utm_term, event_type
        ORDER BY unique_sessions DESC
      `;
      
      const result = await pool.query(query);
      const rows = result.rows;
      
      if (debugMode) {
        console.log('🔍 Direct UTM Results:', rows.length, 'rows found');
        console.log('🔍 Sample results:', rows.slice(0, 5));
      }
      
      // Process results for frontend
      const utmData = rows.map((row: any) => ({
        utm_source: row.utm_source || 'Direct',
        utm_medium: row.utm_medium || 'Direct', 
        utm_campaign: row.utm_campaign || 'Direct Visit',
        utm_content: row.utm_content || '',
        utm_term: row.utm_term || '',
        sessions: parseInt(row.unique_sessions) || 0,
        total_events: parseInt(row.total_events) || 0,
        event_type: row.event_type,
        first_seen: row.first_seen,
        last_seen: row.last_seen,
        isMetaAd: (row.utm_source || '').toLowerCase() === 'meta',
        isAppCampaign: (row.utm_source || '').toLowerCase() === 'app'
      }));
      
      res.json({
        success: true,
        data: utmData,
        count: utmData.length,
        debug: debugMode ? {
          queryMethod: 'Direct Database Query',
          totalResults: rows.length,
          metaCampaigns: utmData.filter(d => d.isMetaAd).length,
          appCampaigns: utmData.filter(d => d.isAppCampaign).length
        } : undefined
      });
      
    } catch (error) {
      console.error("Error in direct UTM query:", error);
      res.status(500).json({ message: "Error fetching UTM data", error: error.message });
    }
  });

  // NEW: UTM Source Conversion Funnel endpoint
  app.get("/api/admin/analytics/utm-source-funnel/:source/:campaign", requireAdmin, async (req, res) => {
    try {
      const { source, campaign } = req.params;
      const { debug } = req.query;
      const debugMode = debug === 'true';
      
      if (debugMode) {
        console.log('🔍 UTM Source Funnel Query for:', source, campaign);
      }
      
      // Get all sessions for this specific UTM source/campaign
      const sessionQuery = `
        SELECT DISTINCT session_id
        FROM event_logs 
        WHERE timestamp >= CURRENT_DATE
          AND event_data IS NOT NULL 
          AND event_data != '{}'
          AND event_data::json->>'utm_source' = $1
          AND event_data::json->>'utm_campaign' = $2
      `;
      
      const sessionResult = await pool.query(sessionQuery, [source, campaign]);
      const sessionIds = sessionResult.rows.map(row => row.session_id);
      
      if (sessionIds.length === 0) {
        return res.json({
          source,
          campaign,
          funnel: [],
          totalSessions: 0
        });
      }
      
      // Get conversion funnel data for these sessions
      const funnelQuery = `
        SELECT 
          event_type,
          COUNT(DISTINCT session_id) as sessions,
          COUNT(*) as events
        FROM event_logs 
        WHERE session_id = ANY($1)
        GROUP BY event_type
        ORDER BY 
          CASE event_type
            WHEN 'page_view' THEN 1
            WHEN 'form_start' THEN 2  
            WHEN 'time_slot_selected' THEN 3
            WHEN 'otp_requested' THEN 4
            WHEN 'otp_verified' THEN 5
            WHEN 'booking_complete' THEN 6
            ELSE 7
          END
      `;
      
      const funnelResult = await pool.query(funnelQuery, [sessionIds]);
      
      // Get session completion data - Enhanced with phone matching logic
      const sessions = await storage.getAnalyticsUserSessions({
        dateFrom: new Date(new Date().toDateString()), // Today start
        dateTo: new Date()
      });
      
      // Get all bookings for the last 30 days to capture completed bookings (some may be cancelled after completion)
      const lastMonthStart = new Date();
      lastMonthStart.setDate(lastMonthStart.getDate() - 30);
      lastMonthStart.setHours(0, 0, 0, 0);
      
      const todayBookings = await storage.getBookingsByDateRange(
        lastMonthStart,
        new Date(),
        'all'
      );
      
      // Get phone mapping from OTP events for these specific sessions
      const phoneMapQuery = `
        SELECT 
          session_id,
          event_data::json->>'phone' as phone_last_4
        FROM event_logs 
        WHERE session_id = ANY($1)
          AND event_type = 'otp_requested'
          AND event_data::json->>'phone' IS NOT NULL
      `;
      
      const phoneMapResult = await pool.query(phoneMapQuery, [sessionIds]);
      const sessionToPhoneMap = new Map();
      phoneMapResult.rows.forEach(row => {
        sessionToPhoneMap.set(row.session_id, row.phone_last_4);
      });
      
      const relevantSessions = sessions.filter(s => sessionIds.includes(s.sessionId));
      
      // Count completions via both explicit events AND phone matching
      let completedSessionsCount = 0;
      
      // First, count sessions with explicit completion tracking
      relevantSessions.forEach(session => {
        let hasExplicitCompletion = false;
        
        // Check for explicit completion (handle both array and number types)
        if (Array.isArray(session.completedSteps)) {
          hasExplicitCompletion = session.completedSteps.length >= 5;
        } else if (typeof session.completedSteps === 'number') {
          hasExplicitCompletion = session.completedSteps >= 5;
        } else if (session.finalOutcome === 'completed' || session.finalOutcome === 'booking_confirmed') {
          hasExplicitCompletion = true;
        }
        
        if (hasExplicitCompletion) {
          completedSessionsCount++;
        } else {
          // Check for phone-matched booking
          const phoneDigits = sessionToPhoneMap.get(session.sessionId);
          if (phoneDigits) {
            const hasMatchingBooking = todayBookings.some(booking => {
              // Extract last 4 digits from booking phone (removing +91 prefix)
              const bookingLast4 = booking.phone.replace(/^\+91/, '').slice(-4);
              return bookingLast4 === phoneDigits;
            });
            if (hasMatchingBooking) {
              completedSessionsCount++;
            }
          }
        }
      });
      
      // Map event types to user-friendly step names and add missing steps
      const stepMapping = {
        'page_view': 'Landing Page View',
        'form_start': 'Started Booking Form',
        'postal_code_valid': 'Postal Code Valid',
        'time_slot_selected': 'Selected Time Slot',
        'otp_requested': 'Requested OTP',
        'otp_verified': 'OTP Verified',
        'booking_complete': 'Booking Complete'
      };
      
      const funnelSteps = funnelResult.rows.map((row: any) => ({
        step: stepMapping[row.event_type as keyof typeof stepMapping] || row.event_type,
        sessions: parseInt(row.sessions),
        events: parseInt(row.events),
        percentage: sessionIds.length > 0 ? (parseInt(row.sessions) / sessionIds.length * 100).toFixed(1) : '0.0',
        dropOffRate: sessionIds.length > 0 ? ((sessionIds.length - parseInt(row.sessions)) / sessionIds.length * 100).toFixed(1) : '0.0'
      }));
      
      // Ensure all expected steps are included even if no data
      const allSteps = Object.values(stepMapping);
      const funnel = allSteps.map(stepName => {
        const existingStep = funnelSteps.find(fs => fs.step === stepName);
        return existingStep || {
          step: stepName,
          sessions: 0,
          events: 0,
          percentage: '0.0',
          dropOffRate: '100.0'
        };
      });
      
      res.json({
        source,
        campaign,
        totalSessions: sessionIds.length,
        completedBookings: completedSessionsCount,
        conversionRate: sessionIds.length > 0 ? (completedSessionsCount / sessionIds.length * 100).toFixed(2) : '0.00',
        funnel,
        debug: debugMode ? {
          sessionIds: sessionIds.slice(0, 5),
          relevantSessions: relevantSessions.length,
          completedSessions: completedSessionsCount,
          phoneMatching: {
            phoneMapCount: phoneMapResult.rows.length,
            todayBookings: todayBookings.length,
            phoneToSessionMapSize: sessionToPhoneMap.size,
            phoneNumbers: Array.from(sessionToPhoneMap.values()),
            bookingPhones: todayBookings.map(b => b.phone.replace(/^\+91/, '').slice(-4))
          }
        } : undefined
      });
      
    } catch (error) {
      console.error("Error fetching UTM source funnel:", error);
      res.status(500).json({ message: "Error fetching UTM source funnel" });
    }
  });

  // Real-time UTM campaigns endpoint - using event logs for campaign data
  app.get("/api/admin/analytics/utm-campaigns", requireAdmin, async (req, res) => {
    try {
      const { startDate, endDate, source, debug } = req.query;
      const debugMode = debug === 'true';
      
      const dateFromObj = startDate ? new Date(startDate as string) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      const dateToObj = endDate ? new Date(endDate as string) : new Date();
      
      if (debugMode) {
        console.log('🔍 UTM Campaigns - Date range:', dateFromObj.toISOString(), 'to', dateToObj.toISOString());
      }
      
      // Get ALL events to capture complete campaign traffic (consistent with funnel endpoint)
      const events = await storage.getEventLogs({
        dateFrom: dateFromObj,
        dateTo: dateToObj
      });
      
      if (debugMode) {
        console.log('🔍 UTM Campaigns - Total events found:', events.length);
        console.log('🔍 UTM Campaigns - Using ALL events for consistent session counting');
      }
      
      const sessions = await storage.getAnalyticsUserSessions({
        dateFrom: dateFromObj,
        dateTo: dateToObj
      });
      
      // Group by campaign for detailed analysis
      const campaignGroups: any = {};
      
      events.forEach(event => {
        try {
          const eventData = event.eventData ? JSON.parse(event.eventData) : {};
          
          // STANDARDIZE CASE - always use title case for consistency
          const campaign = (eventData.utm_campaign || 'Direct').toLowerCase() === 'direct' ? 'Direct' : 
                          (eventData.utm_campaign || 'Direct');
          const eventSource = (eventData.utm_source || 'Direct').toLowerCase() === 'direct' ? 'Direct' : 
                             (eventData.utm_source || 'Direct');
          const medium = (eventData.utm_medium || 'None').toLowerCase() === 'none' ? 'None' : 
                        (eventData.utm_medium || 'None');
          
          // No filtering needed - all traffic is perfume-related
          
          if (debugMode) {
            console.log('🔍 UTM Campaigns - Processing campaign:', campaign, 'from source:', eventSource, 'medium:', medium, 'sessionId:', event.sessionId);
          }
          
          // Filter by source if specified
          if (source && eventSource !== source) return;
          
          if (!campaignGroups[campaign]) {
            campaignGroups[campaign] = {
              campaign,
              source: eventSource,
              medium,
              sessions: new Set(),
              conversions: 0,
              clicks: 0,
              firstSeen: null,
              lastSeen: null,
              isActive: false
            };
          }
          
          campaignGroups[campaign].sessions.add(event.sessionId);
          campaignGroups[campaign].clicks = campaignGroups[campaign].sessions.size;
          
          // Track campaign timeline
          const eventDate = new Date(event.timestamp);
          if (!campaignGroups[campaign].firstSeen || eventDate < new Date(campaignGroups[campaign].firstSeen)) {
            campaignGroups[campaign].firstSeen = event.timestamp;
          }
          if (!campaignGroups[campaign].lastSeen || eventDate > new Date(campaignGroups[campaign].lastSeen)) {
            campaignGroups[campaign].lastSeen = event.timestamp;
          }
          
        } catch (error) {
          console.warn('Error parsing campaign event data:', error);
        }
      });
      
      // Count conversions from sessions
      sessions.forEach(session => {
        for (const campaign in campaignGroups) {
          if (campaignGroups[campaign].sessions.has(session.sessionId)) {
            if (session.finalOutcome === 'completed' || session.finalOutcome === 'booking_confirmed') {
              campaignGroups[campaign].conversions++;
            }
            break;
          }
        }
      });
      
      const campaignData = Object.values(campaignGroups)
        .map((campaign: any) => {
          const sessionCount = campaign.sessions.size;
          const conversionRate = sessionCount > 0 ? parseFloat(((campaign.conversions / sessionCount) * 100).toFixed(2)) : 0;
          const isActive = campaign.lastSeen && (new Date().getTime() - new Date(campaign.lastSeen).getTime()) < (24 * 60 * 60 * 1000);
          
          return {
            ...campaign,
            sessions: sessionCount,
            conversionRate,
            status: isActive ? 'Active' : 'Paused',
            isActive
          };
        })
        .sort((a, b) => new Date(b.lastSeen).getTime() - new Date(a.lastSeen).getTime());
      
      if (debugMode) {
        console.log('🔍 UTM Campaigns Final Results:', {
          totalCampaignsFound: Object.keys(campaignGroups).length,
          campaignsWithData: campaignData.length,
          campaigns: campaignData.map(c => ({
            campaign: c.campaign,
            source: c.source,
            sessions: c.sessions,
            conversions: c.conversions,
            status: c.status
          }))
        });
        
        const response = {
          data: campaignData,
          debug: {
            dateRange: { from: dateFromObj.toISOString(), to: dateToObj.toISOString() },
            totalEventsProcessed: events.length,
            totalSessionsProcessed: sessions.length,
            campaignsFoundInData: Object.keys(campaignGroups).length,
            campaignsWithTraffic: campaignData.length,
            perfumeFilter: 'Removed - showing ALL incoming traffic',
            lastUpdated: new Date().toISOString()
          }
        };
        res.json(response);
      } else {
        res.json(campaignData);
      }
    } catch (error) {
      console.error("Error fetching UTM campaigns:", error);
      res.status(500).json({ message: "Error fetching UTM campaigns" });
    }
  });

  // NEW: Enhanced campaign data with all metrics
  app.get("/api/admin/ga4/campaigns-enhanced", requireAdmin, async (req, res) => {
    try {
      if (!ga4Service) {
        return res.status(503).json({ 
          message: "GA4 service not available" 
        });
      }

      const { startDate = '30daysAgo', endDate = 'today' } = req.query;
      const enhancedCampaigns = await ga4Service.getEnhancedCampaignData(startDate as string, endDate as string);
      res.json(enhancedCampaigns);
    } catch (error) {
      console.error("Error fetching GA4 enhanced campaign data:", error);
      res.status(500).json({ message: "Error fetching GA4 enhanced campaign data" });
    }
  });

  // Enhanced session-based analytics for complete end-to-end funnel tracking with 100% accuracy
  app.get("/api/analytics/session-tracking", requireAdmin, async (req, res) => {
    try {
      const { dateFrom, dateTo, sessionId } = req.query;
      
      // Get complete session data with bookings linked
      const sessionQuery = `
        SELECT 
          us.session_id,
          us.completed_steps,
          us.final_outcome,
          us.booking_id,
          us.time_spent,
          us.first_visit,
          us.last_activity,
          b.id as booking_id,
          b.name as customer_name,
          b.phone,
          b.postal_code,
          b.status as booking_status,
          b.traffic_source,
          b.utm_source,
          b.utm_campaign,
          b.created_at as booking_created
        FROM user_sessions us
        LEFT JOIN bookings b ON us.session_id = b.session_id
        WHERE ($1::date IS NULL OR us.first_visit >= $1::date)
          AND ($2::date IS NULL OR us.first_visit <= $2::date + INTERVAL '1 day')
          AND ($3::text IS NULL OR us.session_id = $3)
        ORDER BY us.first_visit DESC
        LIMIT 1000
      `;
      
      const sessionResult = await pool.query(sessionQuery, [
        dateFrom || null,
        dateTo || null,
        sessionId || null
      ]);
      
      // Get event logs for complete journey mapping
      const eventQuery = `
        SELECT 
          el.session_id,
          el.event_type,
          el.event_data,
          el.timestamp,
          el.referrer,
          el.user_agent
        FROM event_logs el
        WHERE ($1::date IS NULL OR el.timestamp >= $1::date)
          AND ($2::date IS NULL OR el.timestamp <= $2::date + INTERVAL '1 day')
          AND ($3::text IS NULL OR el.session_id = $3)
        ORDER BY el.timestamp ASC
      `;
      
      const eventResult = await pool.query(eventQuery, [
        dateFrom || null,
        dateTo || null,
        sessionId || null
      ]);
      
      // Process complete funnel analytics with 100% accuracy
      const funnelAnalytics = {
        totalSessions: sessionResult.rows.length,
        sessionsWithBookings: sessionResult.rows.filter(s => s.booking_id).length,
        conversionRate: sessionResult.rows.length > 0 ? 
          (sessionResult.rows.filter(s => s.booking_id).length / sessionResult.rows.length * 100).toFixed(2) : 0,
        avgTimeToBooking: 0,
        trafficSources: {},
        sessionDetails: sessionResult.rows,
        eventTimeline: eventResult.rows
      };
      
      // Calculate traffic source breakdown with complete session ID tracking
      sessionResult.rows.forEach(session => {
        const source = session.traffic_source || 'Direct Visit';
        if (!funnelAnalytics.trafficSources[source]) {
          funnelAnalytics.trafficSources[source] = {
            sessions: 0,
            bookings: 0,
            conversionRate: 0,
            sessionIds: []
          };
        }
        funnelAnalytics.trafficSources[source].sessions++;
        funnelAnalytics.trafficSources[source].sessionIds.push(session.session_id);
        if (session.booking_id) {
          funnelAnalytics.trafficSources[source].bookings++;
        }
        funnelAnalytics.trafficSources[source].conversionRate = 
          (funnelAnalytics.trafficSources[source].bookings / funnelAnalytics.trafficSources[source].sessions * 100).toFixed(2);
      });
      
      res.json(funnelAnalytics);
    } catch (error) {
      console.error("Error fetching session analytics:", error);
      res.status(500).json({ message: "Error fetching session analytics" });
    }
  });

  // Deployment notifications endpoint for seamless upgrades
  app.get("/api/deployment/notifications", (req, res) => {
    // Set headers for Server-Sent Events
    res.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Cache-Control'
    });

    // Send initial connection confirmation
    res.write('data: {"type":"connected","message":"Deployment notifications active"}\n\n');

    // Store connection for deployment notifications
    const clientId = Date.now();
    
    // Heartbeat to keep connection alive
    const heartbeat = setInterval(() => {
      try {
        res.write('data: {"type":"heartbeat","timestamp":' + Date.now() + '}\n\n');
      } catch (error) {
        clearInterval(heartbeat);
      }
    }, 30000);

    // Clean up on connection close
    req.on('close', () => {
      clearInterval(heartbeat);
      console.log(`Deployment notification client ${clientId} disconnected`);
    });

    req.on('error', () => {
      clearInterval(heartbeat);
    });
  });

  // Endpoint to trigger deployment notifications (for internal use)
  app.post("/api/deployment/notify", requireAdmin, async (req, res) => {
    try {
      const { type, message, version } = req.body;
      
      // This would be called by deployment scripts to notify clients
      const notification = {
        type: type || 'upgrade',
        message: message || 'System upgrade available - dashboard will refresh automatically',
        version: version || '1.0.0',
        timestamp: Date.now()
      };

      // In a real deployment, this would broadcast to all connected clients
      // For now, we'll just log it
      console.log('Deployment notification triggered:', notification);
      
      res.json({ success: true, notification });
    } catch (error) {
      console.error("Error sending deployment notification:", error);
      res.status(500).json({ message: "Error sending deployment notification" });
    }
  });

  // Debug endpoint to display campaign data in console
  app.get("/api/debug/ga4/campaigns", async (req, res) => {
    try {
      if (!ga4Service) {
        return res.status(503).json({ 
          message: "GA4 service not available" 
        });
      }

      const { startDate = '30daysAgo', endDate = 'today' } = req.query;
      const campaigns = await ga4Service.getCampaignData(startDate as string, endDate as string);
      
      console.log('\n=== ALL GA4 CAMPAIGNS ===');
      console.log(`Found ${campaigns.length} active campaigns:\n`);
      
      campaigns.forEach((campaign, index) => {
        console.log(`${index + 1}. ${campaign.campaignName}`);
        console.log(`   Source: ${campaign.source}`);
        console.log(`   Medium: ${campaign.medium}`);
        console.log(`   Sessions: ${campaign.sessions.toLocaleString()}`);
        console.log(`   Users: ${campaign.users.toLocaleString()}`);
        console.log(`   Conversions: ${campaign.conversions}`);
        console.log(`   Conversion Rate: ${campaign.sessions > 0 ? ((campaign.conversions / campaign.sessions) * 100).toFixed(2) : '0.00'}%`);
        console.log('   ─────────────────────────────────────');
      });
      
      const totalSessions = campaigns.reduce((sum, c) => sum + c.sessions, 0);
      const totalUsers = campaigns.reduce((sum, c) => sum + c.users, 0);
      const totalConversions = campaigns.reduce((sum, c) => sum + c.conversions, 0);
      const avgConversionRate = totalSessions > 0 ? (totalConversions / totalSessions * 100) : 0;
      
      console.log('\nCAMPAIGN SUMMARY:');
      console.log(`Total Active Campaigns: ${campaigns.length}`);
      console.log(`Total Sessions: ${totalSessions.toLocaleString()}`);
      console.log(`Total Users: ${totalUsers.toLocaleString()}`);
      console.log(`Total Conversions: ${totalConversions}`);
      console.log(`Average Conversion Rate: ${avgConversionRate.toFixed(2)}%`);
      console.log('=========================\n');
      
      res.json({
        message: "Campaign data logged to console",
        summary: {
          totalCampaigns: campaigns.length,
          totalSessions,
          totalUsers,
          totalConversions,
          avgConversionRate: parseFloat(avgConversionRate.toFixed(2))
        },
        campaigns
      });
    } catch (error) {
      console.error("Error fetching GA4 campaign data:", error);
      res.status(500).json({ message: "Error fetching GA4 campaign data" });
    }
  });

  // Meta Marketing API endpoints
  app.get("/api/admin/meta/campaigns", requireAdmin, async (req, res) => {
    try {
      if (!metaService) {
        return res.status(503).json({ 
          message: "Meta service not available. Please configure META_ACCESS_TOKEN and META_AD_ACCOUNT_ID" 
        });
      }

      const { days = '30' } = req.query;
      const dateRange = MetaService.getDateRange(parseInt(days as string));
      const campaigns = await metaService.getCampaigns(dateRange);
      res.json(campaigns);
    } catch (error) {
      console.error("Error fetching Meta campaigns:", error);
      res.status(500).json({ message: "Error fetching Meta campaigns", error: (error as Error).message });
    }
  });

  app.get("/api/admin/meta/insights", requireAdmin, async (req, res) => {
    try {
      if (!metaService) {
        return res.status(503).json({ 
          message: "Meta service not available" 
        });
      }

      const { days = '30' } = req.query;
      const dateRange = MetaService.getDateRange(parseInt(days as string));
      const insights = await metaService.getAccountInsights(dateRange);
      res.json(insights);
    } catch (error) {
      console.error("Error fetching Meta insights:", error);
      res.status(500).json({ message: "Error fetching Meta insights" });
    }
  });

  app.get("/api/admin/meta/adsets/:campaignId", requireAdmin, async (req, res) => {
    try {
      if (!metaService) {
        return res.status(503).json({ 
          message: "Meta service not available" 
        });
      }

      const { campaignId } = req.params;
      const { days = '30' } = req.query;
      const dateRange = MetaService.getDateRange(parseInt(days as string));
      const adsets = await metaService.getAdSets(campaignId, dateRange);
      res.json(adsets);
    } catch (error) {
      console.error("Error fetching Meta ad sets:", error);
      res.status(500).json({ message: "Error fetching Meta ad sets" });
    }
  });

  // Combined analytics endpoint (GA4 + Meta)
  app.get("/api/admin/analytics/combined", requireAdmin, async (req, res) => {
    try {
      const { startDate = '30daysAgo', endDate = 'today', days = '30' } = req.query;
      
      const results: any = {
        ga4: {},
        meta: {},
        combined: {}
      };

      // Fetch GA4 data
      if (ga4Service) {
        try {
          const [overview, campaigns, channels] = await Promise.all([
            ga4Service.getOverviewMetrics(startDate as string, endDate as string),
            ga4Service.getCampaignData(startDate as string, endDate as string),
            ga4Service.getChannelGroupData(startDate as string, endDate as string)
          ]);
          
          results.ga4 = {
            overview,
            campaigns,
            channels,
            source: 'Google Analytics 4'
          };
        } catch (error) {
          console.error('GA4 data fetch error:', error);
          results.ga4 = { error: 'Failed to fetch GA4 data' };
        }
      }

      // Fetch Meta data
      if (metaService) {
        try {
          const dateRange = MetaService.getDateRange(parseInt(days as string));
          const [campaigns, insights] = await Promise.all([
            metaService.getCampaigns(dateRange),
            metaService.getAccountInsights(dateRange)
          ]);
          
          results.meta = {
            campaigns,
            insights,
            source: 'Meta Marketing API'
          };
        } catch (error) {
          console.error('Meta data fetch error:', error);
          results.meta = { error: 'Failed to fetch Meta data' };
        }
      }

      // Combined insights
      results.combined = {
        totalCampaigns: (results.ga4.campaigns?.length || 0) + (results.meta.campaigns?.length || 0),
        totalSpend: results.meta.insights?.totalSpend || 0,
        totalSessions: results.ga4.overview?.sessions || 0,
        totalImpressions: results.meta.insights?.totalImpressions || 0,
        totalClicks: results.meta.insights?.totalClicks || 0,
        totalConversions: (results.ga4.overview?.conversions || 0) + (results.meta.insights?.totalConversions || 0),
        crossPlatformROI: results.meta.insights?.totalSpend > 0 ? 
          ((results.ga4.overview?.sessions || 0) / results.meta.insights.totalSpend) : 0
      };

      res.json(results);
    } catch (error) {
      console.error("Error fetching combined analytics:", error);
      res.status(500).json({ message: "Error fetching combined analytics" });
    }
  });

  // Get user funnel journey for a specific booking
  app.get("/api/admin/bookings/:id/funnel-journey", requireAdmin, async (req, res) => {
    try {
      const bookingId = parseInt(req.params.id);
      
      if (isNaN(bookingId)) {
        return res.status(400).json({ message: "Invalid booking ID" });
      }
      
      // Get booking with session ID
      const booking = await storage.getBooking(bookingId);
      if (!booking || !booking.sessionId) {
        return res.status(404).json({ message: "Booking not found or no session data available" });
      }
      
      // Get session details
      const sessionQuery = `
        SELECT 
          session_id,
          first_visit,
          last_activity,
          completed_steps,
          final_outcome,
          time_spent,
          total_events
        FROM user_sessions 
        WHERE session_id = $1
      `;
      
      const sessionResult = await pool.query(sessionQuery, [booking.sessionId]);
      
      if (sessionResult.rows.length === 0) {
        return res.status(404).json({ message: "Session data not found" });
      }
      
      const session = sessionResult.rows[0];
      
      // Get detailed event timeline for this session
      const eventsQuery = `
        SELECT 
          event_type,
          event_data,
          timestamp
        FROM event_logs 
        WHERE session_id = $1 
        ORDER BY timestamp ASC
      `;
      
      const eventsResult = await pool.query(eventsQuery, [booking.sessionId]);
      const events = eventsResult.rows;
      
      // Process events into funnel steps with time tracking
      const funnelSteps = [];
      let previousTimestamp = new Date(session.first_visit);
      
      // Define the booking funnel steps in order
      const stepDefinitions = [
        { key: 'page_view', label: 'Landing Page', icon: 'Globe' },
        { key: 'form_start', label: 'Started Booking', icon: 'Play' },
        { key: 'postal_code_valid', label: 'Valid Postal Code', icon: 'MapPin' },
        { key: 'date_selected', label: 'Date Selected', icon: 'Calendar' },
        { key: 'time_slot_selected', label: 'Time Selected', icon: 'Clock' },
        { key: 'otp_requested', label: 'OTP Requested', icon: 'Shield' },
        { key: 'otp_verified', label: 'Phone Verified', icon: 'CheckCircle' },
        { key: 'booking_complete', label: 'Booking Confirmed', icon: 'CheckCircle2' }
      ];
      
      // Track step completion and timing
      const completedSteps = new Map();
      
      events.forEach(event => {
        const eventTime = new Date(event.timestamp);
        const timeFromPrevious = Math.round((eventTime.getTime() - previousTimestamp.getTime()) / 1000);
        
        // Find matching step definition
        const stepDef = stepDefinitions.find(s => event.event_type.includes(s.key) || event.event_type === s.key);
        
        if (stepDef && !completedSteps.has(stepDef.key)) {
          let eventData = {};
          try {
            eventData = event.event_data ? JSON.parse(event.event_data) : {};
          } catch (e) {
            eventData = {};
          }
          
          completedSteps.set(stepDef.key, {
            step: stepDef.label,
            icon: stepDef.icon,
            timestamp: event.timestamp,
            timeFromPrevious: timeFromPrevious,
            totalTimeFromStart: Math.round((eventTime.getTime() - new Date(session.first_visit).getTime()) / 1000),
            eventData: eventData,
            completed: true
          });
          
          previousTimestamp = eventTime;
        }
      });
      
      // Build final funnel array with all steps (completed and pending)
      stepDefinitions.forEach(stepDef => {
        if (completedSteps.has(stepDef.key)) {
          funnelSteps.push(completedSteps.get(stepDef.key));
        } else {
          // Add incomplete steps
          funnelSteps.push({
            step: stepDef.label,
            icon: stepDef.icon,
            timestamp: null,
            timeFromPrevious: null,
            totalTimeFromStart: null,
            eventData: {},
            completed: false
          });
        }
      });
      
      // Calculate total journey metrics
      const totalJourneyTime = Math.round(session.time_spent / 60); // Convert to minutes
      const conversionRate = (funnelSteps.filter(s => s.completed).length / funnelSteps.length * 100).toFixed(1);
      const averageStepTime = funnelSteps.filter(s => s.completed && s.timeFromPrevious).length > 0 
        ? Math.round(funnelSteps.filter(s => s.completed && s.timeFromPrevious).reduce((sum, s) => sum + s.timeFromPrevious, 0) / funnelSteps.filter(s => s.completed && s.timeFromPrevious).length)
        : 0;
      
      res.json({
        bookingId: bookingId,
        sessionId: booking.sessionId,
        journeyStarted: session.first_visit,
        journeyCompleted: session.last_activity,
        totalTimeMinutes: totalJourneyTime,
        stepsCompleted: funnelSteps.filter(s => s.completed).length,
        totalSteps: funnelSteps.length,
        conversionRate: parseFloat(conversionRate),
        averageStepTime: averageStepTime,
        finalOutcome: session.final_outcome,
        funnelSteps: funnelSteps,
        eventCount: events.length,
        traffic_source: booking.trafficSource || 'Direct'
      });
      
    } catch (error) {
      console.error("Error fetching booking funnel journey:", error);
      res.status(500).json({ message: "Error fetching funnel journey data" });
    }
  });

  // Server-sent events endpoint for deployment notifications
  app.get("/api/deployment/events", requireAdmin, (req, res) => {
    res.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Cache-Control'
    });

    // Send initial connection confirmation
    res.write('data: {"type":"connected","message":"Deployment notification service connected"}\n\n');

    // Store client connection for broadcasting
    const clientId = Date.now();
    deploymentClients.set(clientId, res);

    // Handle client disconnect
    req.on('close', () => {
      deploymentClients.delete(clientId);
    });

    req.on('error', () => {
      deploymentClients.delete(clientId);
    });
  });

  // Deployment notification endpoint (for automated deployment systems)
  app.post("/api/deployment/notify", (req, res) => {
    const { type = 'upgrade', message = 'System upgrade in progress', action = 'refresh' } = req.body;
    
    const notification = {
      type,
      message,
      action,
      timestamp: new Date().toISOString()
    };

    // Broadcast to all connected admin clients
    let notifiedClients = 0;
    deploymentClients.forEach((clientRes, clientId) => {
      try {
        clientRes.write(`data: ${JSON.stringify(notification)}\n\n`);
        notifiedClients++;
      } catch (error) {
        // Remove disconnected clients
        deploymentClients.delete(clientId);
      }
    });

    console.log(`📢 Deployment notification sent to ${notifiedClients} admin clients:`, notification);
    
    res.json({ 
      success: true, 
      message: `Notification sent to ${notifiedClients} connected clients`,
      notification 
    });
  });

  // User tracking endpoints for long-term cookie management
  app.post('/api/tracking/user', async (req, res) => {
    try {
      const trackingData = req.body;
      await storage.createOrUpdateUserTracking(trackingData);
      res.json({ success: true });
    } catch (error) {
      console.error('Error updating user tracking:', error);
      res.status(500).json({ error: 'Failed to update tracking data' });
    }
  });

  app.post('/api/tracking/session', async (req, res) => {
    try {
      const sessionData = req.body;
      
      // Debug logging to track UTM parameters
      console.log('🔍 Session Creation - Received data:', {
        sessionId: sessionData.sessionId,
        utmSource: sessionData.utmSource,
        utmCampaign: sessionData.utmCampaign,
        utmMedium: sessionData.utmMedium,
        hasUTMData: !!(sessionData.utmSource || sessionData.utmCampaign),
        timestamp: new Date().toISOString()
      });
      
      const result = await storage.createOrUpdateLongTermSession(sessionData);
      
      // Verify what was actually stored
      console.log('🔍 Session Creation - Stored result:', {
        sessionId: result.sessionId,
        utmSource: result.utmSource,
        utmCampaign: result.utmCampaign,
        utmMedium: result.utmMedium,
        stored: !!(result.utmSource || result.utmCampaign)
      });
      
      res.json({ success: true, sessionId: result.sessionId });
    } catch (error) {
      console.error('Error updating session tracking:', error);
      res.status(500).json({ error: 'Failed to update session data' });
    }
  });

  // Test UTM tracking fix endpoint
  app.post('/api/tracking/test-utm', async (req, res) => {
    try {
      const { utm_source, utm_medium, utm_campaign, utm_term, utm_content } = req.body;
      
      // Generate test session data
      const sessionId = 'session_' + Date.now() + '_test_' + Math.random().toString(36).substr(2, 9);
      const userId = 'user_' + Date.now() + '_test_' + Math.random().toString(36).substr(2, 9);
      
      const sessionData = {
        userId,
        sessionId,
        deviceFingerprint: 'test_device',
        utmSource: utm_source,
        utmMedium: utm_medium,
        utmCampaign: utm_campaign,
        utmTerm: utm_term,
        utmContent: utm_content,
        referrer: 'https://facebook.com',
        pageViews: 1,
        sessionDuration: 0,
        bounced: true,
        converted: false
      };
      
      console.log('🧪 Testing UTM Session Creation:', sessionData);
      
      // Create session with UTM data
      const result = await storage.createOrUpdateLongTermSession(sessionData);
      
      // Verify by querying database
      const verification = await storage.getUserSession(sessionId);
      
      res.json({
        success: true,
        test: 'UTM tracking session creation',
        created: result,
        verified: verification,
        hasUTMData: !!(verification?.utmSource || verification?.utmCampaign)
      });
    } catch (error) {
      console.error('UTM test error:', error);
      res.status(500).json({ error: 'Test failed' });
    }
  });

  // Get user journey data for analytics
  app.get('/api/tracking/user/:userId', async (req, res) => {
    try {
      const { userId } = req.params;
      
      const user = await storage.getUserTracking(userId);
      if (!user) {
        return res.status(404).json({ error: 'User not found' });
      }

      const sessions = await storage.getLongTermUserSessions(userId);

      res.json({
        user,
        sessions,
        totalSessions: sessions.length,
        totalValue: user.totalValue,
        hasBooked: user.hasBooked,
        totalBookings: user.totalBookings
      });
    } catch (error) {
      console.error('Error fetching user journey:', error);
      res.status(500).json({ error: 'Failed to fetch user data' });
    }
  });

  // Executive Analytics Endpoints - Guaranteed Data Accuracy
  app.get('/api/analytics/executive-metrics', requireAdmin, async (req, res) => {
    try {
      const { period = '7d' } = req.query;
      
      // Calculate date range
      const now = new Date();
      let startDate: Date;
      const daysBack = period === '1d' ? 1 : period === '7d' ? 7 : period === '30d' ? 30 : 90;
      
      if (period === 'all') {
        // All time - start from July 1, 2025 when UTM tracking began
        startDate = new Date('2025-07-01T00:00:00.000Z');
      } else {
        startDate = new Date(now.getTime() - (daysBack * 24 * 60 * 60 * 1000));
      }
      
      // Get current period metrics - ALL bookings for complete picture
      const currentBookings = await db
        .select()
        .from(bookings)
        .where(gte(bookings.createdAt, startDate));

      const currentSessions = await db
        .select()
        .from(longTermUserSessions)
        .where(gte(longTermUserSessions.startedAt, startDate));

      const uniqueVisitors = await db
        .selectDistinct({ sessionId: longTermUserSessions.sessionId })
        .from(longTermUserSessions)
        .where(gte(longTermUserSessions.startedAt, startDate));

      // UTM tracking started July 1, 2025 (actual data start date)
      const utmTrackingStartDate = new Date('2025-07-01T00:00:00.000Z');
      
      // Get UTM Sessions (confirmed UTM traffic)
      const utmSessions = await db
        .select()
        .from(longTermUserSessions)
        .where(
          and(
            gte(longTermUserSessions.startedAt, startDate),
            isNotNull(longTermUserSessions.utmSource),
            ne(longTermUserSessions.utmSource, 'direct'),
            ne(longTermUserSessions.utmSource, '')
          )
        );

      // Get Direct Sessions (confirmed direct traffic after UTM tracking started)
      const directSessions = await db
        .select()
        .from(longTermUserSessions)
        .where(
          and(
            gte(longTermUserSessions.startedAt, startDate),
            gte(longTermUserSessions.startedAt, utmTrackingStartDate), // After UTM tracking started
            or(
              eq(longTermUserSessions.utmSource, 'direct'),
              and(
                isNull(longTermUserSessions.utmSource),
                isNotNull(longTermUserSessions.sessionId) // Confirmed session but no UTM = direct
              )
            )
          )
        );

      // All historical sessions are now classified as either UTM or Direct
      // No "Unknown" category - we identify all traffic sources

      // Get previous period for trends
      const prevStartDate = new Date(startDate.getTime() - (daysBack * 24 * 60 * 60 * 1000));
      const prevEndDate = startDate;

      const previousBookings = await db
        .select()
        .from(bookings)
        .where(
          and(
            gte(bookings.createdAt, prevStartDate),
            lt(bookings.createdAt, prevEndDate)
          )
        );

      const previousSessions = await db
        .select()
        .from(longTermUserSessions)
        .where(
          and(
            gte(longTermUserSessions.startedAt, prevStartDate),
            lt(longTermUserSessions.startedAt, prevEndDate)
          )
        );

      const previousUniqueVisitors = await db
        .selectDistinct({ sessionId: longTermUserSessions.sessionId })
        .from(longTermUserSessions)
        .where(
          and(
            gte(longTermUserSessions.startedAt, prevStartDate),
            lt(longTermUserSessions.startedAt, prevEndDate)
          )
        );

      // Calculate current metrics - UTM + Direct + Other Sources
      const utmSessionCount = utmSessions.length;
      const directSessionCount = directSessions.length;
      const otherSourcesCount = Math.max(0, totalCurrentSessions - utmSessionCount - directSessionCount);
      const totalSessions = utmSessionCount + directSessionCount + otherSourcesCount;
      
      // Calculate ACTUAL unique visitors using device fingerprints
      const allCurrentSessions = [...utmSessions, ...directSessions];
      const uniqueDeviceFingerprints = new Set(
        allCurrentSessions.map(session => session.deviceFingerprint).filter(Boolean)
      );
      const totalVisitors = uniqueDeviceFingerprints.size;
      
      const totalBookings = currentBookings.length;
      const confirmedBookings = currentBookings.filter(b => b.status === 'confirmed').length;
      const completedBookings = currentBookings.filter(b => b.status === 'completed').length;
      const cancelledBookings = currentBookings.filter(b => b.status === 'cancelled').length;
      const conversionRate = totalSessions > 0 ? (totalBookings / totalSessions) * 100 : 0;
      
      // Traffic Attribution metrics
      const utmTrafficRate = totalSessions > 0 ? (utmSessionCount / totalSessions) * 100 : 0;
      const directTrafficRate = totalSessions > 0 ? (directSessionCount / totalSessions) * 100 : 0;
      const otherSourcesRate = totalSessions > 0 ? (otherSourcesCount / totalSessions) * 100 : 0;

      // Calculate trends - use same logic for previous period
      const prevUniqueDeviceFingerprints = new Set(
        previousSessions.map(session => session.deviceFingerprint).filter(Boolean)
      );
      const prevVisitors = prevUniqueDeviceFingerprints.size;
      const prevSessions = previousSessions.length;
      const prevBookings = previousBookings.length;
      const prevConversionRate = prevSessions > 0 ? (prevBookings / prevSessions) * 100 : 0;

      const calculateTrend = (current: number, previous: number) => {
        if (previous === 0) return current > 0 ? 100 : 0;
        return ((current - previous) / previous) * 100;
      };

      res.json({
        // Core Metrics
        totalVisitors,
        totalSessions,
        totalBookings,
        confirmedBookings,
        completedBookings,
        cancelledBookings,
        conversionRate: Math.round(conversionRate * 100) / 100,
        
        // Traffic Attribution - UTM vs Direct vs Other Sources
        utmSessions: utmSessionCount,
        directSessions: directSessionCount,
        utmTrafficRate: Math.round(utmTrafficRate * 100) / 100,
        directTrafficRate: Math.round(directTrafficRate * 100) / 100,
        otherSourcesRate: Math.round(otherSourcesRate * 100) / 100,
        totalUtmEvents: utmSessions.length,
        totalDirectEvents: directSessions.length,
        
        // Trends (no revenue metrics)
        visitorsTrend: Math.round(calculateTrend(totalVisitors, prevVisitors) * 100) / 100,
        sessionsTrend: Math.round(calculateTrend(totalSessions, prevSessions) * 100) / 100,
        bookingsTrend: Math.round(calculateTrend(totalBookings, prevBookings) * 100) / 100,
        conversionTrend: Math.round(calculateTrend(conversionRate, prevConversionRate) * 100) / 100,
        
        period: period,
        lastUpdated: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error fetching executive metrics:', error);
      res.status(500).json({ error: 'Failed to fetch executive metrics' });
    }
  });

  app.get('/api/analytics/campaign-performance', requireAdmin, async (req, res) => {
    try {
      const { period = '7d' } = req.query;
      
      let startDate: Date;
      if (period === 'all') {
        startDate = new Date('2025-07-01T00:00:00.000Z');
      } else {
        const daysBack = period === '1d' ? 1 : period === '7d' ? 7 : period === '30d' ? 30 : 90;
        startDate = new Date(Date.now() - (daysBack * 24 * 60 * 60 * 1000));
      }

      // Get UTM sessions with their conversion status
      const utmSessions = await db
        .select({
          sessionId: longTermUserSessions.sessionId,
          utmCampaign: longTermUserSessions.utmCampaign,
          utmSource: longTermUserSessions.utmSource,
          utmMedium: longTermUserSessions.utmMedium,
          converted: longTermUserSessions.converted,
          startedAt: longTermUserSessions.startedAt
        })
        .from(longTermUserSessions)
        .where(
          and(
            gte(longTermUserSessions.startedAt, startDate),
            isNotNull(longTermUserSessions.utmCampaign),
            ne(longTermUserSessions.utmSource, 'direct')
          )
        );

      // Group sessions by campaign
      const campaignMap = new Map();
      
      utmSessions.forEach(session => {
        const key = `${session.utmCampaign}-${session.utmSource}`;
        if (!campaignMap.has(key)) {
          campaignMap.set(key, {
            name: session.utmCampaign || 'Direct Visit',
            source: session.utmSource || 'Direct',
            medium: session.utmMedium || 'Direct',
            sessions: 0,
            visitors: new Set(),
            bookings: 0,
            revenue: 0,
            spend: 0, // We don't track ad spend
            firstSeen: session.startedAt,
            lastSeen: session.startedAt
          });
        }
        
        const campaign = campaignMap.get(key);
        campaign.sessions++;
        campaign.visitors.add(session.sessionId);
        
        // Count conversions
        if (session.converted) {
          campaign.bookings++;
        }
        
        // Update timestamps
        if (session.startedAt < campaign.firstSeen) campaign.firstSeen = session.startedAt;
        if (session.startedAt > campaign.lastSeen) campaign.lastSeen = session.startedAt;
      });

      // Get actual booking data for revenue calculation
      const bookingsWithRevenue = await db
        .select({
          sessionId: bookings.sessionId,
          amount: bookings.purchaseAmount
        })
        .from(bookings)
        .where(
          and(
            gte(bookings.date, startDate),
            eq(bookings.status, 'completed'),
            isNotNull(bookings.sessionId)
          )
        );

      // Add revenue to campaigns
      bookingsWithRevenue.forEach(booking => {
        // Find the session in UTM data
        const utmSession = utmSessions.find(s => s.sessionId === booking.sessionId);
        if (utmSession) {
          const key = `${utmSession.utmCampaign}-${utmSession.utmSource}`;
          const campaign = campaignMap.get(key);
          if (campaign) {
            campaign.revenue += booking.amount || 0;
          }
        }
      });

      // Convert to array and calculate final metrics
      const campaigns = Array.from(campaignMap.values()).map(campaign => ({
        name: campaign.name,
        source: campaign.source,
        medium: campaign.medium,
        spend: campaign.spend,
        visitors: campaign.visitors.size,
        bookings: campaign.bookings,
        revenue: campaign.revenue / 100, // Convert from paise to rupees
        roas: campaign.spend > 0 ? (campaign.revenue / 100) / campaign.spend : 0,
        cpa: campaign.bookings > 0 ? campaign.spend / campaign.bookings : 0,
        status: 'Active' as const,
        trend: 'stable' as const
      }));

      res.json(campaigns);
    } catch (error) {
      console.error('Error fetching campaign performance:', error);
      res.status(500).json({ error: 'Failed to fetch campaign performance' });
    }
  });

  app.get('/api/analytics/conversion-funnel', requireAdmin, async (req, res) => {
    try {
      const { period = '7d' } = req.query;
      
      const daysBack = period === '1d' ? 1 : period === '7d' ? 7 : period === '30d' ? 30 : 90;
      const startDate = new Date(Date.now() - (daysBack * 24 * 60 * 60 * 1000));

      // Get all events for the period
      const events = await db
        .select()
        .from(eventLogs)
        .where(gte(eventLogs.timestamp, startDate))
        .orderBy(eventLogs.timestamp);

      // Define funnel steps
      const funnelSteps = [
        'page_view',
        'form_start', 
        'postal_code_valid',
        'date_selected',
        'time_slot_selected',
        'otp_requested',
        'otp_verified',
        'booking_complete'
      ];

      const stepNames = {
        'page_view': 'Visited Website',
        'form_start': 'Started Booking',
        'postal_code_valid': 'Entered Valid Area',
        'date_selected': 'Selected Date',
        'time_slot_selected': 'Selected Time',
        'otp_requested': 'Requested OTP',
        'otp_verified': 'Verified Phone',
        'booking_complete': 'Completed Booking'
      };

      // Count unique sessions for each step
      const stepCounts = {};
      const uniqueSessions = new Set();

      events.forEach(event => {
        uniqueSessions.add(event.sessionId);
        if (funnelSteps.includes(event.eventType)) {
          if (!stepCounts[event.eventType]) {
            stepCounts[event.eventType] = new Set();
          }
          stepCounts[event.eventType].add(event.sessionId);
        }
      });

      const totalSessions = uniqueSessions.size;
      
      const funnelData = funnelSteps.map((step, index) => {
        const count = stepCounts[step] ? stepCounts[step].size : 0;
        const percentage = totalSessions > 0 ? (count / totalSessions) * 100 : 0;
        
        // Calculate drop-off rate from previous step
        let dropOffRate = 0;
        if (index > 0) {
          const prevStep = funnelSteps[index - 1];
          const prevCount = stepCounts[prevStep] ? stepCounts[prevStep].size : 0;
          if (prevCount > 0) {
            dropOffRate = ((prevCount - count) / prevCount) * 100;
          }
        }

        return {
          name: stepNames[step] || step,
          users: count,
          percentage,
          dropOffRate,
          previousStep: index > 0 ? stepNames[funnelSteps[index - 1]] : undefined
        };
      });

      res.json(funnelData);
    } catch (error) {
      console.error('Error fetching conversion funnel:', error);
      res.status(500).json({ error: 'Failed to fetch conversion funnel' });
    }
  });

  app.get('/api/analytics/customer-journey', requireAdmin, async (req, res) => {
    try {
      const { period = '7d' } = req.query;
      
      const daysBack = period === '1d' ? 1 : period === '7d' ? 7 : period === '30d' ? 30 : 90;
      const startDate = new Date(Date.now() - (daysBack * 24 * 60 * 60 * 1000));

      // Get session data with time tracking
      const sessions = await db
        .select()
        .from(longTermUserSessions)
        .where(gte(longTermUserSessions.startedAt, startDate));

      // Get events to calculate stage progression
      const events = await db
        .select()
        .from(eventLogs)
        .where(gte(eventLogs.timestamp, startDate))
        .orderBy(eventLogs.sessionId, eventLogs.timestamp);

      // Define customer journey stages
      const stages = {
        'Awareness': { events: ['page_view'], sessions: new Set(), totalTime: 0, conversions: 0 },
        'Interest': { events: ['form_start', 'postal_code_valid'], sessions: new Set(), totalTime: 0, conversions: 0 },
        'Consideration': { events: ['date_selected', 'time_slot_selected'], sessions: new Set(), totalTime: 0, conversions: 0 },
        'Intent': { events: ['otp_requested', 'otp_verified'], sessions: new Set(), totalTime: 0, conversions: 0 },
        'Purchase': { events: ['booking_complete'], sessions: new Set(), totalTime: 0, conversions: 0 }
      };

      // Process events to assign sessions to stages
      const sessionStages = {};
      events.forEach(event => {
        Object.keys(stages).forEach(stageName => {
          const stage = stages[stageName];
          if (stage.events.includes(event.eventType)) {
            stage.sessions.add(event.sessionId);
            sessionStages[event.sessionId] = stageName;
          }
        });
      });

      // Calculate metrics for each stage
      sessions.forEach(session => {
        const stage = sessionStages[session.sessionId] || 'Awareness';
        const stageData = stages[stage];
        
        // Add session duration
        const sessionDuration = session.sessionDuration || 0;
        stageData.totalTime += sessionDuration;
        
        // Count conversions
        if (session.converted) {
          stageData.conversions++;
        }
      });

      // Format response
      const journeyData = Object.keys(stages).map(stageName => {
        const stage = stages[stageName];
        const sessionCount = stage.sessions.size;
        
        return {
          stage: stageName,
          count: sessionCount,
          value: stage.conversions * 2000, // Assume average booking value
          avgTimeSpent: sessionCount > 0 ? stage.totalTime / sessionCount : 0,
          conversionRate: sessionCount > 0 ? (stage.conversions / sessionCount) * 100 : 0
        };
      });

      res.json(journeyData);
    } catch (error) {
      console.error('Error fetching customer journey:', error);
      res.status(500).json({ error: 'Failed to fetch customer journey' });
    }
  });

  // UTM Campaign Analytics API - CONSOLIDATED DATA
  app.get('/api/analytics/utm-campaigns/:period', requireAdmin, async (req, res) => {
    try {
      const { period } = req.params;
      const daysBack = period === '1d' ? 1 : period === '7d' ? 7 : period === '30d' ? 30 : 90;

      // Use consolidated UTM data from all sources
      const { UTMDataConsolidation } = await import('./utm-data-consolidation');
      const campaigns = await UTMDataConsolidation.getUnifiedCampaignData(daysBack);
      
      res.json(campaigns);
    } catch (error) {
      console.error('Error fetching consolidated UTM campaigns:', error);
      res.status(500).json({ error: 'Failed to fetch UTM campaigns' });
    }
  });

  // Campaign Conversion Funnel API endpoint - detailed funnel by campaign
  app.get('/api/analytics/campaign-funnel/:days', requireAdmin, async (req, res) => {
    try {
      const days = parseInt(req.params.days) || 7;
      
      // Use consolidated campaign funnel data
      const { UTMDataConsolidation } = await import('./utm-data-consolidation');
      const campaignFunnels = await UTMDataConsolidation.getConversionFunnelByCampaign(days);
      
      res.json(campaignFunnels);
    } catch (error) {
      console.error('Error fetching campaign funnel data:', error);
      res.status(500).json({ error: 'Failed to fetch campaign funnel data' });
    }
  });

  // Comprehensive Funnel Analysis API - enhanced with normalized UTM data
  app.get('/api/analytics/comprehensive-funnel', requireAdmin, async (req, res) => {
    try {
      const { 
        period = '7d', 
        campaign = 'all', 
        location = 'all', 
        creativeType = 'all', 
        source = 'all', 
        search = '', 
        cleanDataOnly = 'true' 
      } = req.query;

      const daysBack = period === '1d' ? 1 : period === '7d' ? 7 : period === '30d' ? 30 : 90;
      const sessionTrackingStart = getSessionTrackingStartDate();

      // Build comprehensive funnel query with normalized UTM parameters
      const funnelQuery = `
        WITH normalized_events AS (
          SELECT 
            session_id,
            event_type,
            timestamp,
            -- Apply comprehensive source normalization
            CASE 
              WHEN LOWER(event_data::json->>'utm_source') IN ('meta', 'facebook', 'fb', 'instagram', 'ig') THEN 'meta'
              WHEN LOWER(event_data::json->>'utm_source') IN ('google', 'goog', 'youtube', 'yt') THEN 'google'
              WHEN LOWER(event_data::json->>'utm_source') = 'qr' THEN 'qr'
              WHEN LOWER(event_data::json->>'utm_source') = 'app' THEN 'app'
              WHEN event_data::json->>'utm_source' IS NULL OR LOWER(event_data::json->>'utm_source') = 'direct' THEN 'direct'
              ELSE LOWER(TRIM(COALESCE(event_data::json->>'utm_source', 'direct')))
            END as utm_source,
            LOWER(COALESCE(event_data::json->>'utm_medium', 'direct')) as utm_medium,
            COALESCE(event_data::json->>'utm_campaign', 'direct visit') as utm_campaign,
            event_data::json->>'utm_term' as utm_term,
            event_data::json->>'utm_content' as utm_content,
            event_data::json->>'fbclid' as fbclid,
            event_data::json->>'gclid' as gclid,
            event_data
          FROM event_logs 
          WHERE timestamp >= NOW() - INTERVAL '${daysBack} days'
            ${cleanDataOnly === 'true' ? "AND timestamp >= $1" : ""}
        ),

        campaign_sessions AS (
          SELECT 
            utm_source,
            utm_medium,
            utm_campaign,
            CASE 
              WHEN utm_source IN ('meta', 'facebook') THEN utm_campaign
              WHEN utm_source = 'qr' THEN CONCAT('QR - ', utm_campaign)
              WHEN utm_source = 'app' THEN CONCAT('App - ', utm_campaign)
              WHEN utm_source = 'direct' THEN 'Direct Traffic'
              ELSE CONCAT(UPPER(LEFT(utm_source, 1)) || LOWER(SUBSTRING(utm_source, 2)), ' - ', utm_campaign)
            END as campaign_name,
            session_id,
            MIN(timestamp) as session_start,
            MAX(timestamp) as session_end,
            COUNT(*) as total_events,
            COUNT(CASE WHEN event_type = 'page_view' THEN 1 END) as page_views,
            COUNT(CASE WHEN event_type = 'form_start' THEN 1 END) as homepage_views,
            COUNT(CASE WHEN event_type = 'postal_code_valid' THEN 1 END) as calendar_reached,
            COUNT(CASE WHEN event_type = 'date_selected' THEN 1 END) as address_filled,
            COUNT(CASE WHEN event_type = 'time_slot_selected' THEN 1 END) as time_selected,
            COUNT(CASE WHEN event_type = 'otp_requested' THEN 1 END) as otp_requested,
            COUNT(CASE WHEN event_type = 'otp_verified' THEN 1 END) as otp_entered,
            COUNT(CASE WHEN event_type = 'booking_complete' THEN 1 END) as booking_submitted,
            COUNT(CASE WHEN event_type = 'booking_confirmed' THEN 1 END) as booking_confirmed
          FROM normalized_events
          GROUP BY utm_source, utm_medium, utm_campaign, session_id
        ),

        campaign_aggregates AS (
          SELECT 
            campaign_name,
            utm_source,
            utm_medium,
            utm_campaign,
            COUNT(DISTINCT session_id) as sessions_started,
            SUM(CASE WHEN homepage_views > 0 THEN 1 ELSE 0 END) as viewed_homepage,
            SUM(CASE WHEN calendar_reached > 0 THEN 1 ELSE 0 END) as reached_calendar,
            SUM(CASE WHEN address_filled > 0 THEN 1 ELSE 0 END) as filled_address,
            SUM(CASE WHEN otp_entered > 0 THEN 1 ELSE 0 END) as otp_entered,
            SUM(CASE WHEN booking_submitted > 0 THEN 1 ELSE 0 END) as booking_submitted,
            SUM(CASE WHEN booking_confirmed > 0 THEN 1 ELSE 0 END) as booking_confirmed,
            AVG(total_events) as avg_steps,
            AVG(EXTRACT(EPOCH FROM (session_end - session_start))) as avg_duration,
            SUM(CASE WHEN total_events <= 2 THEN 1 ELSE 0 END)::FLOAT / COUNT(*) * 100 as bounce_rate,
            MIN(session_start) as first_seen,
            MAX(session_end) as last_seen
          FROM campaign_sessions
          WHERE 1=1
            ${source !== 'all' ? `AND utm_source = '${source}'` : ''}
            ${campaign !== 'all' ? `AND utm_campaign ILIKE '%${campaign}%'` : ''}
            ${search ? `AND campaign_name ILIKE '%${search}%'` : ''}
          GROUP BY campaign_name, utm_source, utm_medium, utm_campaign
          HAVING COUNT(DISTINCT session_id) > 0
        )

        SELECT 
          campaign_name,
          utm_source,
          utm_medium,
          utm_campaign,
          sessions_started,
          viewed_homepage,
          reached_calendar,
          filled_address,
          otp_entered,
          booking_submitted,
          booking_confirmed,
          CASE WHEN sessions_started > 0 THEN 
            ROUND((booking_confirmed::FLOAT / sessions_started * 100)::numeric, 2) 
          ELSE 0 END as conversion_rate,
          CASE WHEN sessions_started > 0 THEN 
            ROUND((booking_submitted::FLOAT / sessions_started * 100)::numeric, 2) 
          ELSE 0 END as booking_rate,
          ROUND(bounce_rate::numeric, 2) as bounce_rate,
          ROUND(avg_steps::numeric, 1) as avg_steps,
          ROUND(avg_duration::numeric, 0) as avg_duration,
          first_seen,
          last_seen,
          CASE WHEN last_seen >= NOW() - INTERVAL '7 days' THEN true ELSE false END as active_conversion_window
        FROM campaign_aggregates
        ORDER BY booking_confirmed DESC, sessions_started DESC
      `;

      const queryParams = cleanDataOnly === 'true' ? [sessionTrackingStart] : [];
      const result = await pool.query(funnelQuery, queryParams);
      
      // Parse Meta campaign details for each campaign
      const campaigns = result.rows.map(row => {
        let metaParsed = null;
        if (row.utm_source === 'meta' || row.utm_source === 'facebook') {
          metaParsed = parseMetaCampaign(row.utm_campaign);
        }

        // Apply location and creative type filters if specified
        if (location !== 'all' && (!metaParsed || metaParsed.location !== location)) {
          return null;
        }
        if (creativeType !== 'all' && (!metaParsed || metaParsed.creativeType !== creativeType)) {
          return null;
        }

        return {
          campaignName: row.campaign_name,
          utm_source: row.utm_source,
          utm_medium: row.utm_medium,
          utm_campaign: row.utm_campaign,
          meta_parsed: metaParsed,
          funnel: {
            sessionsStarted: parseInt(row.sessions_started),
            viewedHomepage: parseInt(row.viewed_homepage),
            reachedCalendar: parseInt(row.reached_calendar),
            filledAddress: parseInt(row.filled_address),
            otpEntered: parseInt(row.otp_entered),
            bookingSubmitted: parseInt(row.booking_submitted),
            bookingConfirmed: parseInt(row.booking_confirmed)
          },
          metrics: {
            conversionRate: parseFloat(row.conversion_rate),
            bookingRate: parseFloat(row.booking_rate),
            bounceRate: parseFloat(row.bounce_rate),
            avgSteps: parseFloat(row.avg_steps),
            avgDuration: parseFloat(row.avg_duration)
          },
          firstSeen: row.first_seen,
          lastSeen: row.last_seen,
          activeConversionWindow: row.active_conversion_window
        };
      }).filter(Boolean);

      // Calculate summary metrics
      const totalSessions = campaigns.reduce((sum, c) => sum + c.funnel.sessionsStarted, 0);
      const totalBookings = campaigns.reduce((sum, c) => sum + c.funnel.bookingConfirmed, 0);
      const overallConversionRate = totalSessions > 0 ? (totalBookings / totalSessions * 100) : 0;

      res.json({
        campaigns,
        summary: {
          totalCampaigns: campaigns.length,
          totalSessions,
          totalBookings,
          overallConversionRate: Math.round(overallConversionRate * 100) / 100,
          cleanDataOnly: cleanDataOnly === 'true',
          sessionTrackingStartDate: sessionTrackingStart.toISOString(),
          dateRange: period
        }
      });

    } catch (error) {
      console.error('Error fetching comprehensive funnel data:', error);
      res.status(500).json({ error: 'Failed to fetch comprehensive funnel data' });
    }
  });

  // Campaign Bookings API endpoint - fetch all bookings for a specific campaign
  app.get('/api/analytics/campaign-bookings/:campaignName', requireAdmin, async (req, res) => {
    try {
      const { campaignName } = req.params;
      
      // Get all bookings for this campaign
      const campaignBookings = await db
        .select({
          id: bookings.id,
          name: bookings.name,
          phone: bookings.phone,
          email: bookings.email,
          address: bookings.address,
          date: bookings.date,
          timeSlot: bookings.timeSlot,
          status: bookings.status,
          utmCampaign: bookings.utmCampaign,
          utmSource: bookings.utmSource,
          utmMedium: bookings.utmMedium,
          createdAt: bookings.createdAt,
        })
        .from(bookings)
        .where(eq(bookings.utmCampaign, campaignName))
        .orderBy(desc(bookings.createdAt));

      res.json(campaignBookings);
    } catch (error) {
      console.error('Error fetching campaign bookings:', error);
      res.status(500).json({ error: 'Failed to fetch campaign bookings' });
    }
  });

  // User Behaviour Analytics Endpoints
  app.get('/api/analytics/user-sessions/:period', requireAdmin, async (req, res) => {
    try {
      const { period } = req.params;
      
      let startDate: Date;
      if (period === 'all') {
        startDate = new Date('2025-07-01T00:00:00.000Z');
      } else {
        const daysBack = period === '1d' ? 1 : period === '7d' ? 7 : period === '30d' ? 30 : 90;
        startDate = new Date(Date.now() - (daysBack * 24 * 60 * 60 * 1000));
      }

      const sessions = await db
        .select()
        .from(longTermUserSessions)
        .where(gte(longTermUserSessions.startedAt, startDate))
        .orderBy(desc(longTermUserSessions.startedAt))
        .limit(100);

      // Calculate engagement scores for each session
      const enrichedSessions = sessions.map(session => {
        const engagementScore = calculateEngagementScore(session);
        return {
          sessionId: session.sessionId,
          startedAt: session.startedAt,
          currentStep: session.funnelProgress ? `step_${session.funnelProgress}` : 'page_view',
          completedSteps: Array.isArray(session.completedSteps) ? session.completedSteps : [],
          finalOutcome: session.converted ? 'booking_complete' : 'abandoned',
          totalEvents: session.pageViews || 0,
          timeSpent: session.sessionDuration || 0,
          converted: session.converted,
          userAgent: 'Unknown Device', // We don't store userAgent in this table
          utmSource: session.utmSource,
          utmMedium: session.utmMedium,
          utmCampaign: session.utmCampaign,
          engagementScore
        };
      });

      res.json(enrichedSessions);
    } catch (error) {
      console.error('Error fetching user sessions:', error);
      res.status(500).json({ error: 'Failed to fetch user sessions' });
    }
  });

  app.get('/api/analytics/behaviour-insights/:period', requireAdmin, async (req, res) => {
    try {
      const { period } = req.params;
      
      let startDate: Date;
      if (period === 'all') {
        startDate = new Date('2025-07-01T00:00:00.000Z');
      } else {
        const daysBack = period === '1d' ? 1 : period === '7d' ? 7 : period === '30d' ? 30 : 90;
        startDate = new Date(Date.now() - (daysBack * 24 * 60 * 60 * 1000));
      }

      // Get all sessions for the period
      const sessions = await db
        .select()
        .from(longTermUserSessions)
        .where(gte(longTermUserSessions.startedAt, startDate));

      // Calculate insights
      const totalSessions = sessions.length;
      const conversions = sessions.filter(s => s.converted).length;
      const conversionRate = totalSessions > 0 ? (conversions / totalSessions) * 100 : 0;
      
      const avgDuration = sessions.length > 0 
        ? Math.round(sessions.reduce((sum, s) => sum + (s.sessionDuration || 0), 0) / sessions.length)
        : 0;

      // Calculate step completion rates
      const stepCounts: { [key: string]: number } = {};
      sessions.forEach(session => {
        const steps = Array.isArray(session.completedSteps) ? session.completedSteps : [];
        steps.forEach((step: string) => {
          stepCounts[step] = (stepCounts[step] || 0) + 1;
        });
      });

      const stepRates = Object.entries(stepCounts).map(([step, count]) => ({
        step,
        rate: totalSessions > 0 ? Math.round((count / totalSessions) * 100) : 0
      }));

      // Device/browser analysis (simplified since we don't store userAgent)
      const deviceCounts: { [key: string]: number } = {};
      sessions.forEach(session => {
        // For now, we'll simulate device detection or use other indicators
        const key = session.deviceFingerprint ? 'Mobile' : 'Desktop';
        deviceCounts[key] = (deviceCounts[key] || 0) + 1;
      });

      const deviceData = Object.entries(deviceCounts).map(([name, value]) => ({
        name,
        value
      }));

      // Generate insights
      const positiveInsights = [];
      const improvementAreas = [];

      if (conversionRate > 10) {
        positiveInsights.push(`Strong conversion rate of ${conversionRate.toFixed(1)}% indicates effective user experience`);
      }
      if (avgDuration > 180) {
        positiveInsights.push(`High engagement with average session duration of ${Math.round(avgDuration/60)} minutes`);
      }

      if (conversionRate < 5) {
        improvementAreas.push(`Low conversion rate (${conversionRate.toFixed(1)}%) suggests optimization opportunities in the booking flow`);
      }
      if (avgDuration < 60) {
        improvementAreas.push(`Short session duration (${avgDuration}s) may indicate user experience issues`);
      }

      const engagementScore = Math.round((conversionRate * 2 + (avgDuration / 300) * 100) / 2);

      res.json({
        totalSessions,
        conversionRate: parseFloat(conversionRate.toFixed(1)),
        avgDuration,
        engagementScore: Math.min(100, engagementScore),
        stepRates,
        deviceData,
        positiveInsights,
        improvementAreas,
        sessionGrowth: 0, // Calculate vs previous period if needed
        durationGrowth: 0,
        conversionGrowth: 0
      });
    } catch (error) {
      console.error('Error fetching behaviour insights:', error);
      res.status(500).json({ error: 'Failed to fetch behaviour insights' });
    }
  });

  app.get('/api/analytics/session-heatmap/:period', requireAdmin, async (req, res) => {
    try {
      const { period } = req.params;
      
      let startDate: Date;
      if (period === 'all') {
        startDate = new Date('2025-07-01T00:00:00.000Z');
      } else {
        const daysBack = period === '1d' ? 1 : period === '7d' ? 7 : period === '30d' ? 30 : 90;
        startDate = new Date(Date.now() - (daysBack * 24 * 60 * 60 * 1000));
      }

      const sessions = await db
        .select()
        .from(longTermUserSessions)
        .where(gte(longTermUserSessions.startedAt, startDate));

      // Create heatmap data for the booking funnel
      const steps = [
        'page_view',
        'form_start', 
        'postal_code_valid',
        'date_selected',
        'time_slot_selected',
        'otp_requested',
        'otp_verified',
        'booking_complete'
      ];

      const heatmapData = steps.map(step => {
        const usersAtStep = sessions.filter(session => {
          const steps = Array.isArray(session.completedSteps) ? session.completedSteps : [];
          return steps.includes(step);
        }).length;
        
        const completed = sessions.filter(session => {
          const steps = Array.isArray(session.completedSteps) ? session.completedSteps : [];
          return steps.includes(step) && session.converted;
        }).length;

        return {
          step: step.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
          users: usersAtStep,
          completed
        };
      });

      res.json(heatmapData);
    } catch (error) {
      console.error('Error fetching session heatmap:', error);
      res.status(500).json({ error: 'Failed to fetch session heatmap' });
    }
  });

  // Helper function to calculate engagement score
  function calculateEngagementScore(session: any): number {
    let score = 0;
    
    // Base score from session duration
    if (session.sessionDuration) {
      score += Math.min(30, session.sessionDuration / 1000 / 10); // Up to 30 points for time (convert from ms)
    }
    
    // Score from number of page views
    if (session.pageViews) {
      score += Math.min(20, session.pageViews * 5); // Up to 20 points for page views
    }
    
    // Score from funnel progress
    if (session.funnelProgress) {
      score += session.funnelProgress * 5; // 5 points per funnel step
    }
    
    // Bonus for conversion
    if (session.converted) {
      score += 30;
    }
    
    // Bonus for not bouncing
    if (!session.bounced) {
      score += 10;
    }
    
    return Math.min(100, Math.round(score));
  }

  const httpServer = createServer(app);
  // Postal Code Analytics API Routes
  
  // Log postal code entry attempts
  app.post('/api/analytics/postal-entry', async (req, res) => {
    try {
      const { sessionId, postalCode, entryPage, isValid, matchedServiceArea, ipAddress, userAgent } = req.body;
      
      const result = await db.insert(postalCodeAttempts).values({
        sessionId,
        postalCode,
        entryPage,
        isValid,
        matchedServiceArea,
        ipAddress,
        userAgent
      }).returning();
      
      console.log(`Postal code attempt logged: ${postalCode} (${isValid ? 'valid' : 'invalid'}) from ${entryPage}`);
      
      res.json({ success: true, attempt: result[0] });
    } catch (error) {
      console.error('Error logging postal code attempt:', error);
      res.status(500).json({ error: 'Failed to log postal code attempt' });
    }
  });

  // Get postal code analytics dashboard data
  app.get('/api/analytics/postal-dashboard', requireAdmin, async (req, res) => {
    try {
      const { startDate, endDate, outcomeFilter, validityFilter } = req.query;
      
      // Build filter conditions for event_logs table
      let whereConditions = ["event_type IN ('postal_code_valid', 'postal_code_invalid')"];
      if (startDate && endDate) {
        whereConditions.push(`timestamp BETWEEN '${startDate}' AND '${endDate}'`);
      }
      if (validityFilter && validityFilter !== 'all') {
        if (validityFilter === 'valid') {
          whereConditions.push("event_type = 'postal_code_valid'");
        } else if (validityFilter === 'invalid') {
          whereConditions.push("event_type = 'postal_code_invalid'");
        }
      }
      
      const whereClause = `WHERE ${whereConditions.join(' AND ')}`;
      
      // Get postal code events from event_logs with simple parsing
      const rawData = await db.execute(sql`
        SELECT DISTINCT
          e.session_id,
          e.event_data,
          e.event_type,
          e.timestamp,
          'booking_form' as entry_page,
          CASE WHEN e.event_type = 'postal_code_valid' THEN true ELSE false END as is_valid,
          CASE 
            WHEN e.event_type = 'postal_code_valid' THEN 'Valid Service Area'
            ELSE null 
          END as matched_service_area,
          null as final_code,
          b.id as booking_id,
          b.name as customer_name,
          b.rep_assigned,
          b.status as booking_status,
          CASE 
            WHEN b.id IS NOT NULL THEN 'Booked'
            ELSE 'Dropped'
          END as outcome,
          e.ip_address,
          e.user_agent
        FROM event_logs e
        LEFT JOIN bookings b ON b.session_id = e.session_id
        ${sql.raw(whereClause)}
        ORDER BY e.timestamp DESC
      `);

      // Parse postal codes from JSON data in JavaScript
      const sessionData = rawData.rows.map(row => {
        let postalCode = 'Unknown';
        try {
          const eventData = typeof row.event_data === 'string' 
            ? JSON.parse(row.event_data.replace(/\\"/g, '"').replace(/^"|"$/g, '')) 
            : row.event_data;
          postalCode = eventData?.postalCode || eventData?.postal_code || 'Unknown';
        } catch (e) {
          // Fallback parsing for complex nested JSON
          const dataStr = String(row.event_data);
          const match = dataStr.match(/"postalCode":"([^"]*)"/) || dataStr.match(/"postal_code":"([^"]*)"/);
          if (match) postalCode = match[1];
        }
        
        return {
          ...row,
          postal_code: postalCode
        };
      });
      
      // Get funnel summary statistics
      const totalEvents = rawData.rows.length;
      const validEvents = rawData.rows.filter(r => r.event_type === 'postal_code_valid').length;
      const invalidEvents = rawData.rows.filter(r => r.event_type === 'postal_code_invalid').length;
      const eventsWithBookings = rawData.rows.filter(r => r.booking_id).length;
      const eventsWithoutBookings = totalEvents - eventsWithBookings;
      
      const funnelStats = {
        total_entries: totalEvents,
        valid_codes: validEvents,
        invalid_codes: invalidEvents,
        total_invalid_entries: invalidEvents,
        corrected_entries: 0,
        led_to_bookings: eventsWithBookings,
        dropped_sessions: eventsWithoutBookings,
        correction_rate: 0,
        conversion_rate: totalEvents > 0 ? Math.round((eventsWithBookings / totalEvents) * 100 * 100) / 100 : 0,
        drop_off_rate: totalEvents > 0 ? Math.round((eventsWithoutBookings / totalEvents) * 100 * 100) / 100 : 0
      };
      
      // Get frequently entered invalid postal codes
      const invalidCodes = new Map();
      rawData.rows.filter(r => r.event_type === 'postal_code_invalid').forEach(row => {
        let postalCode = 'Unknown';
        try {
          const eventData = typeof row.event_data === 'string' 
            ? JSON.parse(row.event_data.replace(/\\"/g, '"').replace(/^"|"$/g, '')) 
            : row.event_data;
          postalCode = eventData?.postalCode || eventData?.postal_code || 'Unknown';
        } catch (e) {
          const dataStr = String(row.event_data);
          const match = dataStr.match(/"postalCode":"([^"]*)"/) || dataStr.match(/"postal_code":"([^"]*)"/);
          if (match) postalCode = match[1];
        }
        
        if (!invalidCodes.has(postalCode)) {
          invalidCodes.set(postalCode, {
            postal_code: postalCode,
            attempt_count: 0,
            unique_sessions: new Set(),
            first_attempt: row.timestamp,
            last_attempt: row.timestamp
          });
        }
        
        const entry = invalidCodes.get(postalCode);
        entry.attempt_count++;
        entry.unique_sessions.add(row.session_id);
        if (new Date(row.timestamp) < new Date(entry.first_attempt)) {
          entry.first_attempt = row.timestamp;
        }
        if (new Date(row.timestamp) > new Date(entry.last_attempt)) {
          entry.last_attempt = row.timestamp;
        }
      });
      
      const frequentInvalidCodes = Array.from(invalidCodes.values())
        .map(entry => ({
          ...entry,
          unique_sessions: entry.unique_sessions.size
        }))
        .filter(entry => entry.attempt_count >= 2)
        .sort((a, b) => b.attempt_count - a.attempt_count)
        .slice(0, 20);
      
      res.json({
        sessions: sessionData,
        funnelStats,
        frequentInvalidCodes
      });
      
    } catch (error) {
      console.error('Error fetching postal dashboard data:', error);
      res.status(500).json({ error: 'Failed to fetch postal dashboard data' });
    }
  });

  // Add new postal code to service area
  app.post('/api/service-areas/add', requireAdmin, async (req, res) => {
    try {
      const { postalCode, city, state, addedBy } = req.body;
      
      // Check if postal code already exists
      const existing = await db.select().from(serviceAreas).where(eq(serviceAreas.postalCode, postalCode));
      
      if (existing.length > 0) {
        return res.status(400).json({ error: 'Postal code already exists in service area' });
      }
      
      const result = await db.insert(serviceAreas).values({
        postalCode,
        city,
        state,
        addedBy,
        activeStatus: true
      }).returning();
      
      // Also add to existing postal_codes table for immediate availability
      await db.execute(sql`
        INSERT INTO postal_codes (postal_code, city, state, serviceable)
        VALUES (${postalCode}, ${city}, ${state}, true)
        ON CONFLICT (postal_code) DO UPDATE SET 
          serviceable = true,
          city = ${city},
          state = ${state}
      `);
      
      console.log(`New service area added: ${postalCode} - ${city}, ${state} by ${addedBy}`);
      
      res.json({ success: true, serviceArea: result[0] });
    } catch (error) {
      console.error('Error adding service area:', error);
      res.status(500).json({ error: 'Failed to add service area' });
    }
  });

  // Export postal code analytics data
  app.get('/api/analytics/postal-export', requireAdmin, async (req, res) => {
    try {
      const { startDate, endDate } = req.query;
      
      let whereClause = '';
      if (startDate && endDate) {
        whereClause = `WHERE p.timestamp BETWEEN '${startDate}' AND '${endDate}'`;
      }
      
      const exportData = await db.execute(sql`
        SELECT 
          p.session_id,
          p.postal_code,
          p.timestamp,
          p.entry_page,
          p.is_valid,
          p.matched_service_area,
          p.final_code,
          p.ip_address,
          b.id as booking_id,
          b.name as customer_name,
          b.phone as customer_phone,
          b.rep_assigned,
          b.status as booking_status,
          b.created_at as booking_date,
          CASE 
            WHEN p.booking_id IS NOT NULL THEN 'Booked'
            ELSE 'Dropped'
          END as final_outcome
        FROM postal_code_attempts p
        LEFT JOIN bookings b ON p.booking_id = b.id
        ${sql.raw(whereClause)}
        ORDER BY p.timestamp DESC
      `);
      
      // Convert to CSV format
      const csvHeader = 'Session ID,Postal Code,Timestamp,Entry Page,Is Valid,Matched Service Area,Final Code,IP Address,Booking ID,Customer Name,Customer Phone,Rep Assigned,Booking Status,Booking Date,Final Outcome\n';
      
      const csvRows = exportData.rows.map(row => {
        return [
          row.session_id,
          row.postal_code,
          new Date(row.timestamp).toISOString(),
          row.entry_page,
          row.is_valid,
          row.matched_service_area || '',
          row.final_code || '',
          row.ip_address || '',
          row.booking_id || '',
          row.customer_name || '',
          row.customer_phone || '',
          row.rep_assigned || '',
          row.booking_status || '',
          row.booking_date ? new Date(row.booking_date).toISOString() : '',
          row.final_outcome
        ].map(field => `"${field}"`).join(',');
      }).join('\n');
      
      const csvContent = csvHeader + csvRows;
      const fileName = `postal_code_analytics_${new Date().toISOString().split('T')[0]}.csv`;
      
      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', `attachment; filename="${fileName}"`);
      res.send(csvContent);
      
    } catch (error) {
      console.error('Error exporting postal analytics:', error);
      res.status(500).json({ error: 'Failed to export postal analytics' });
    }
  });

  return httpServer;
}
