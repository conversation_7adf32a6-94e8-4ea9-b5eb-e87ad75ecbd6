/**
 * UTM Campaign Parsing Utilities
 * Provides normalized parsing for Meta campaigns and other UTM parameters
 */

export interface ParsedUTMParams {
  source: string;
  medium: string;
  campaign: string;
  term: string | null;
  content: string | null;
  fbclid: string | null;
  gclid: string | null;
}

export interface ParsedMetaCampaign {
  creativeType: string | null;  // Video, Static, Image
  brand: string | null;         // Kult
  product: string | null;       // Perfume
  offer: string | null;         // Discount, Calendar_Old
  location: string | null;      // Gurugram, Delhi
  audience: string | null;      // Men&Women, extracted from campaign
}

/**
 * Normalize UTM parameters with consistent casing and fallbacks
 */
export function normalizeUTMParams(utmParams: any): ParsedUTMParams {
  // Apply source normalization to fix inconsistencies
  const normalizedSource = normalizeCampaignSource(utmParams.utm_source);
  
  return {
    source: normalizedSource,
    medium: (utmParams.utm_medium || 'unknown').toLowerCase(),
    campaign: utmParams.utm_campaign || 'unknown campaign',
    term: utmParams.utm_term || null,
    content: utmParams.utm_content || null,
    fbclid: utmParams.fbclid || null,
    gclid: utmParams.gclid || null,
  };
}

/**
 * Parse Meta campaign structure: "CreativeType| Brand | Product | Offer | Location"
 * Example: "Video| Kult | Perfume | Discount | Gurugram"
 */
export function parseMetaCampaign(campaignName: string): ParsedMetaCampaign {
  const fallback: ParsedMetaCampaign = {
    creativeType: null,
    brand: null,
    product: null,
    offer: null,
    location: null,
    audience: null,
  };

  if (!campaignName || campaignName === 'unknown campaign') {
    return fallback;
  }

  try {
    // Split by pipe and clean whitespace
    const parts = campaignName.split('|').map(part => part.trim());
    
    if (parts.length >= 5) {
      const parsed: ParsedMetaCampaign = {
        creativeType: parts[0] || null,
        brand: parts[1] || null,
        product: parts[2] || null,
        offer: parts[3] || null,
        location: parts[4] || null,
        audience: extractAudience(campaignName),
      };

      // Log for QA purposes
      console.log('🎯 Meta Campaign Parsed:', {
        original: campaignName,
        parsed,
      });

      return parsed;
    }

    return fallback;
  } catch (error) {
    console.warn('Failed to parse Meta campaign:', campaignName, error);
    return fallback;
  }
}

/**
 * Extract audience information from campaign name
 * Look for audience indicators in the campaign string
 */
function extractAudience(campaignName: string): string | null {
  const audiencePatterns = [
    /men[&\s]*women/i,
    /women[&\s]*men/i,
    /male[&\s]*female/i,
    /female[&\s]*male/i,
    /all[_\s]*audience/i,
    /broad/i,
  ];

  for (const pattern of audiencePatterns) {
    const match = campaignName.match(pattern);
    if (match) {
      return match[0];
    }
  }

  return null;
}

/**
 * Determine session tracking start date
 * This is when long-term session tracking was implemented
 */
export function getSessionTrackingStartDate(): Date {
  // Session tracking was implemented on July 1, 2025
  return new Date('2025-07-01T00:00:00.000Z');
}

/**
 * Check if an event occurred after session tracking was enabled
 */
export function isPostSessionTracking(timestamp: Date | string): boolean {
  const eventDate = new Date(timestamp);
  const trackingStart = getSessionTrackingStartDate();
  return eventDate >= trackingStart;
}

/**
 * Normalize campaign source for consistent grouping
 */
export function normalizeCampaignSource(source: string | null | undefined): string {
  if (!source) return 'direct';
  
  const normalized = source.toLowerCase().trim();
  
  // Map variations to standard sources
  const sourceMapping: Record<string, string> = {
    'facebook': 'meta',
    'fb': 'meta',
    'instagram': 'meta',
    'ig': 'meta',
    'google': 'google',
    'goog': 'google',
    'youtube': 'google',
    'yt': 'google',
    'whatsapp': 'whatsapp',
    'wa': 'whatsapp',
  };

  return sourceMapping[normalized] || normalized;
}

/**
 * Generate funnel stage mapping from event names
 */
export const FUNNEL_STAGES = {
  'page_view': 'Sessions Started',
  'form_start': 'Viewed Homepage',
  'postal_code_valid': 'Reached Calendar Page',
  'date_selected': 'Filled Address',
  'time_slot_selected': 'Time Selected',
  'otp_requested': 'OTP Requested',
  'otp_verified': 'OTP Entered',
  'booking_complete': 'Booking Submitted',
  'booking_confirmed': 'Booking Confirmed',
} as const;

export type FunnelStage = typeof FUNNEL_STAGES[keyof typeof FUNNEL_STAGES];