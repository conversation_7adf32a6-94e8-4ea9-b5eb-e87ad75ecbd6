import { WebSocketServer } from "ws";

export function broadcastToAdmins(data: any) {
  const wss = (global as any).adminWss as WebSocketServer;
  if (!wss) return;

  const message = JSON.stringify({
    type: 'admin_update',
    timestamp: new Date().toISOString(),
    ...data
  });

  wss.clients.forEach((client) => {
    if (client.readyState === 1) { // WebSocket.OPEN = 1
      try {
        client.send(message);
      } catch (error) {
        console.error('Error sending WebSocket message:', error);
      }
    }
  });
}

// Specific notification types for different admin updates
export function notifyBookingCreated(booking: any) {
  broadcastToAdmins({
    event: 'booking_created',
    data: booking
  });
}

export function notifyBookingUpdated(booking: any, changes: any) {
  broadcastToAdmins({
    event: 'booking_updated',
    data: booking,
    changes: changes
  });
}

export function notifyBookingDeleted(bookingId: number) {
  broadcastToAdmins({
    event: 'booking_deleted',
    data: { id: bookingId }
  });
}

export function notifyUserCreated(user: any) {
  broadcastToAdmins({
    event: 'user_created',
    data: user
  });
}

export function notifyUserUpdated(user: any) {
  broadcastToAdmins({
    event: 'user_updated',
    data: user
  });
}

export function notifySettingsUpdated(settings: any) {
  broadcastToAdmins({
    event: 'settings_updated',
    data: settings
  });
}