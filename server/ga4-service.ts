import { BetaAnalyticsDataClient } from '@google-analytics/data';
import { google } from 'googleapis';

interface GA4Config {
  propertyId: string;
  serviceAccountKey: any;
}

interface GA4Metrics {
  sessions: number;
  users: number;
  pageviews: number;
  bounceRate: number;
  sessionDuration: number;
  newUsers: number;
}

interface GA4CampaignData {
  campaignName: string;
  medium: string;
  source: string;
  sessions: number;
  users: number;
  conversions: number;
  engagedSessions: number;
  engagementRate: number;
  averageEngagementTime: number;
  eventsPerSession: number;
  eventCount: number;
  bounceRate: number;
  revenue: number;
}

interface GA4SessionSourceData {
  sessionSourceMedium: string;
  sessionDefaultChannelGroup: string;
  engagedSessions: number;
  sessions: number;
  engagementRate: number;
  averageEngagementTime: number;
  eventsPerSession: number;
  eventCount: number;
  allEvents: number;
  revenue: number;
}

interface GA4DeviceData {
  deviceCategory: string;
  operatingSystem: string;
  browser: string;
  sessions: number;
  users: number;
}

interface GA4GeographyData {
  city: string;
  region: string;
  country: string;
  sessions: number;
  users: number;
}

interface GA4PageData {
  pageTitle: string;
  pagePath: string;
  pageviews: number;
  uniquePageviews: number;
  avgTimeOnPage: number;
  bounceRate: number;
}

export class GA4Service {
  private analyticsDataClient: BetaAnalyticsDataClient;
  private propertyId: string;

  constructor(config: GA4Config) {
    this.propertyId = config.propertyId;
    
    // Initialize the GA4 client with service account credentials
    this.analyticsDataClient = new BetaAnalyticsDataClient({
      credentials: config.serviceAccountKey,
      projectId: config.serviceAccountKey.project_id,
    });
  }

  // Get overview metrics for a date range
  async getOverviewMetrics(startDate: string, endDate: string): Promise<GA4Metrics> {
    try {
      const [response] = await this.analyticsDataClient.runReport({
        property: `properties/${this.propertyId}`,
        dateRanges: [{ startDate, endDate }],
        metrics: [
          { name: 'sessions' },
          { name: 'totalUsers' },
          { name: 'screenPageViews' },
          { name: 'bounceRate' },
          { name: 'averageSessionDuration' },
          { name: 'newUsers' },
        ],
      });

      const row = response.rows?.[0];
      if (!row) throw new Error('No data returned from GA4');

      return {
        sessions: parseInt(row.metricValues?.[0]?.value || '0'),
        users: parseInt(row.metricValues?.[1]?.value || '0'),
        pageviews: parseInt(row.metricValues?.[2]?.value || '0'),
        bounceRate: parseFloat(row.metricValues?.[3]?.value || '0'),
        sessionDuration: parseFloat(row.metricValues?.[4]?.value || '0'),
        newUsers: parseInt(row.metricValues?.[5]?.value || '0'),
      };
    } catch (error) {
      console.error('Error fetching GA4 overview metrics:', error);
      throw error;
    }
  }

  // Get campaign performance data
  async getCampaignData(startDate: string, endDate: string): Promise<GA4CampaignData[]> {
    try {
      const [response] = await this.analyticsDataClient.runReport({
        property: `properties/${this.propertyId}`,
        dateRanges: [{ startDate, endDate }],
        dimensions: [
          { name: 'campaignName' },
          { name: 'medium' },
          { name: 'source' },
        ],
        metrics: [
          { name: 'sessions' },
          { name: 'totalUsers' },
          { name: 'conversions' },
        ],
        orderBys: [{ metric: { metricName: 'sessions' }, desc: true }],
        limit: 50,
      });

      return response.rows?.map(row => ({
        campaignName: row.dimensionValues?.[0]?.value || 'Unknown',
        medium: row.dimensionValues?.[1]?.value || 'Unknown',
        source: row.dimensionValues?.[2]?.value || 'Unknown',
        sessions: parseInt(row.metricValues?.[0]?.value || '0'),
        users: parseInt(row.metricValues?.[1]?.value || '0'),
        conversions: parseInt(row.metricValues?.[2]?.value || '0'),
      })) || [];
    } catch (error) {
      console.error('Error fetching GA4 campaign data:', error);
      throw error;
    }
  }

  // Get device and browser data
  async getDeviceData(startDate: string, endDate: string): Promise<GA4DeviceData[]> {
    try {
      const [response] = await this.analyticsDataClient.runReport({
        property: `properties/${this.propertyId}`,
        dateRanges: [{ startDate, endDate }],
        dimensions: [
          { name: 'deviceCategory' },
          { name: 'operatingSystem' },
          { name: 'browser' },
        ],
        metrics: [
          { name: 'sessions' },
          { name: 'totalUsers' },
        ],
        orderBys: [{ metric: { metricName: 'sessions' }, desc: true }],
        limit: 50,
      });

      return response.rows?.map(row => ({
        deviceCategory: row.dimensionValues?.[0]?.value || 'Unknown',
        operatingSystem: row.dimensionValues?.[1]?.value || 'Unknown',
        browser: row.dimensionValues?.[2]?.value || 'Unknown',
        sessions: parseInt(row.metricValues?.[0]?.value || '0'),
        users: parseInt(row.metricValues?.[1]?.value || '0'),
      })) || [];
    } catch (error) {
      console.error('Error fetching GA4 device data:', error);
      throw error;
    }
  }

  // Get geography data (focused on Indian cities)
  async getGeographyData(startDate: string, endDate: string): Promise<GA4GeographyData[]> {
    try {
      const [response] = await this.analyticsDataClient.runReport({
        property: `properties/${this.propertyId}`,
        dateRanges: [{ startDate, endDate }],
        dimensions: [
          { name: 'city' },
          { name: 'region' },
          { name: 'country' },
        ],
        metrics: [
          { name: 'sessions' },
          { name: 'totalUsers' },
        ],
        dimensionFilter: {
          filter: {
            fieldName: 'country',
            stringFilter: { value: 'India' }
          }
        },
        orderBys: [{ metric: { metricName: 'sessions' }, desc: true }],
        limit: 100,
      });

      return response.rows?.map(row => ({
        city: row.dimensionValues?.[0]?.value || 'Unknown',
        region: row.dimensionValues?.[1]?.value || 'Unknown',
        country: row.dimensionValues?.[2]?.value || 'India',
        sessions: parseInt(row.metricValues?.[0]?.value || '0'),
        users: parseInt(row.metricValues?.[1]?.value || '0'),
      })) || [];
    } catch (error) {
      console.error('Error fetching GA4 geography data:', error);
      throw error;
    }
  }

  // Get page performance data
  async getPageData(startDate: string, endDate: string): Promise<GA4PageData[]> {
    try {
      const [response] = await this.analyticsDataClient.runReport({
        property: `properties/${this.propertyId}`,
        dateRanges: [{ startDate, endDate }],
        dimensions: [
          { name: 'pageTitle' },
          { name: 'pagePath' },
        ],
        metrics: [
          { name: 'screenPageViews' },
          { name: 'screenPageViewsPerSession' },
          { name: 'averageSessionDuration' },
          { name: 'bounceRate' },
        ],
        orderBys: [{ metric: { metricName: 'screenPageViews' }, desc: true }],
        limit: 50,
      });

      return response.rows?.map(row => ({
        pageTitle: row.dimensionValues?.[0]?.value || 'Unknown',
        pagePath: row.dimensionValues?.[1]?.value || 'Unknown',
        pageviews: parseInt(row.metricValues?.[0]?.value || '0'),
        uniquePageviews: parseInt(row.metricValues?.[1]?.value || '0'),
        avgTimeOnPage: parseFloat(row.metricValues?.[2]?.value || '0'),
        bounceRate: parseFloat(row.metricValues?.[3]?.value || '0'),
      })) || [];
    } catch (error) {
      console.error('Error fetching GA4 page data:', error);
      throw error;
    }
  }

  // Get real-time metrics
  async getRealTimeMetrics(): Promise<{ activeUsers: number; activeUsersByCountry: any[] }> {
    try {
      const [response] = await this.analyticsDataClient.runRealtimeReport({
        property: `properties/${this.propertyId}`,
        metrics: [{ name: 'activeUsers' }],
        dimensions: [{ name: 'country' }],
        limit: 10,
      });

      const totalActiveUsers = response.rows?.reduce((sum, row) => 
        sum + parseInt(row.metricValues?.[0]?.value || '0'), 0) || 0;

      const activeUsersByCountry = response.rows?.map(row => ({
        country: row.dimensionValues?.[0]?.value || 'Unknown',
        activeUsers: parseInt(row.metricValues?.[0]?.value || '0'),
      })) || [];

      return {
        activeUsers: totalActiveUsers,
        activeUsersByCountry,
      };
    } catch (error) {
      console.error('Error fetching GA4 real-time metrics:', error);
      throw error;
    }
  }

  // Get conversion events data
  async getConversionsData(startDate: string, endDate: string): Promise<any[]> {
    try {
      const [response] = await this.analyticsDataClient.runReport({
        property: `properties/${this.propertyId}`,
        dateRanges: [{ startDate, endDate }],
        dimensions: [
          { name: 'eventName' },
          { name: 'source' },
          { name: 'medium' },
        ],
        metrics: [
          { name: 'conversions' },
        ],
        dimensionFilter: {
          filter: {
            fieldName: 'eventName',
            stringFilter: { matchType: 'CONTAINS', value: 'booking' }
          }
        },
        orderBys: [{ metric: { metricName: 'conversions' }, desc: true }],
        limit: 50,
      });

      return response.rows?.map(row => ({
        eventName: row.dimensionValues?.[0]?.value || 'Unknown',
        source: row.dimensionValues?.[1]?.value || 'Unknown',
        medium: row.dimensionValues?.[2]?.value || 'Unknown',
        conversions: parseInt(row.metricValues?.[0]?.value || '0'),
      })) || [];
    } catch (error) {
      console.error('Error fetching GA4 conversions data:', error);
      throw error;
    }
  }

  // Get channel group data
  async getChannelGroupData(startDate: string, endDate: string): Promise<any[]> {
    try {
      const [response] = await this.analyticsDataClient.runReport({
        property: `properties/${this.propertyId}`,
        dateRanges: [{ startDate, endDate }],
        dimensions: [
          { name: 'sessionPrimaryChannelGroup' },
        ],
        metrics: [
          { name: 'sessions' },
          { name: 'engagedSessions' },
          { name: 'engagementRate' },
          { name: 'averageSessionDuration' },
          { name: 'eventsPerSession' },
          { name: 'eventCount' },
          { name: 'keyEvents' },
          { name: 'totalRevenue' },
        ],
        orderBys: [{ metric: { metricName: 'sessions' }, desc: true }],
        limit: 20,
      });

      return response.rows?.map(row => ({
        channelGroup: row.dimensionValues?.[0]?.value || 'Unknown',
        sessions: parseInt(row.metricValues?.[0]?.value || '0'),
        engagedSessions: parseInt(row.metricValues?.[1]?.value || '0'),
        engagementRate: parseFloat(row.metricValues?.[2]?.value || '0'),
        averageSessionDuration: parseFloat(row.metricValues?.[3]?.value || '0'),
        eventsPerSession: parseFloat(row.metricValues?.[4]?.value || '0'),
        eventCount: parseInt(row.metricValues?.[5]?.value || '0'),
        keyEvents: parseInt(row.metricValues?.[6]?.value || '0'),
        totalRevenue: parseFloat(row.metricValues?.[7]?.value || '0'),
      })) || [];
    } catch (error) {
      console.error('Error fetching GA4 channel group data:', error);
      throw error;
    }
  }

  // Get comprehensive audience demographics data
  async getAudienceData(startDate: string, endDate: string): Promise<any[]> {
    try {
      const [response] = await this.analyticsDataClient.runReport({
        property: `properties/${this.propertyId}`,
        dateRanges: [{ startDate, endDate }],
        dimensions: [
          { name: 'userAgeBracket' },
          { name: 'userGender' },
        ],
        metrics: [
          { name: 'totalUsers' },
          { name: 'sessions' },
          { name: 'engagementRate' },
          { name: 'averageSessionDuration' },
        ],
        orderBys: [{ metric: { metricName: 'totalUsers' }, desc: true }],
      });

      return response.rows?.map(row => ({
        ageBracket: row.dimensionValues?.[0]?.value || 'Unknown',
        gender: row.dimensionValues?.[1]?.value || 'Unknown',
        users: parseInt(row.metricValues?.[0]?.value || '0'),
        sessions: parseInt(row.metricValues?.[1]?.value || '0'),
        engagementRate: parseFloat(row.metricValues?.[2]?.value || '0'),
        averageSessionDuration: parseFloat(row.metricValues?.[3]?.value || '0'),
      })) || [];
    } catch (error) {
      console.error('Error fetching GA4 audience data:', error);
      return [];
    }
  }

  // Get all custom events data
  async getAllEventsData(startDate: string, endDate: string): Promise<any[]> {
    try {
      const [response] = await this.analyticsDataClient.runReport({
        property: `properties/${this.propertyId}`,
        dateRanges: [{ startDate, endDate }],
        dimensions: [
          { name: 'eventName' },
          { name: 'source' },
          { name: 'medium' },
          { name: 'campaignName' },
        ],
        metrics: [
          { name: 'eventCount' },
          { name: 'eventCountPerUser' },
          { name: 'eventValue' },
        ],
        orderBys: [{ metric: { metricName: 'eventCount' }, desc: true }],
        limit: 100,
      });

      return response.rows?.map(row => ({
        eventName: row.dimensionValues?.[0]?.value || 'Unknown',
        source: row.dimensionValues?.[1]?.value || 'Unknown',
        medium: row.dimensionValues?.[2]?.value || 'Unknown',
        campaign: row.dimensionValues?.[3]?.value || 'Unknown',
        eventCount: parseInt(row.metricValues?.[0]?.value || '0'),
        eventCountPerUser: parseFloat(row.metricValues?.[1]?.value || '0'),
        eventValue: parseFloat(row.metricValues?.[2]?.value || '0'),
      })) || [];
    } catch (error) {
      console.error('Error fetching GA4 events data:', error);
      return [];
    }
  }

  // Get enhanced technology data (browsers, OS, devices with versions)
  async getEnhancedTechnologyData(startDate: string, endDate: string): Promise<any[]> {
    try {
      const [response] = await this.analyticsDataClient.runReport({
        property: `properties/${this.propertyId}`,
        dateRanges: [{ startDate, endDate }],
        dimensions: [
          { name: 'deviceCategory' },
          { name: 'operatingSystem' },
          { name: 'operatingSystemVersion' },
          { name: 'browser' },
          { name: 'browserVersion' },
          { name: 'screenResolution' },
        ],
        metrics: [
          { name: 'sessions' },
          { name: 'totalUsers' },
          { name: 'engagementRate' },
          { name: 'bounceRate' },
        ],
        orderBys: [{ metric: { metricName: 'sessions' }, desc: true }],
        limit: 100,
      });

      return response.rows?.map(row => ({
        deviceCategory: row.dimensionValues?.[0]?.value || 'Unknown',
        operatingSystem: row.dimensionValues?.[1]?.value || 'Unknown',
        osVersion: row.dimensionValues?.[2]?.value || 'Unknown',
        browser: row.dimensionValues?.[3]?.value || 'Unknown',
        browserVersion: row.dimensionValues?.[4]?.value || 'Unknown',
        screenResolution: row.dimensionValues?.[5]?.value || 'Unknown',
        sessions: parseInt(row.metricValues?.[0]?.value || '0'),
        users: parseInt(row.metricValues?.[1]?.value || '0'),
        engagementRate: parseFloat(row.metricValues?.[2]?.value || '0'),
        bounceRate: parseFloat(row.metricValues?.[3]?.value || '0'),
      })) || [];
    } catch (error) {
      console.error('Error fetching GA4 enhanced technology data:', error);
      return [];
    }
  }

  // Get enhanced e-commerce data
  async getEcommerceData(startDate: string, endDate: string): Promise<any[]> {
    try {
      const [response] = await this.analyticsDataClient.runReport({
        property: `properties/${this.propertyId}`,
        dateRanges: [{ startDate, endDate }],
        dimensions: [
          { name: 'itemName' },
          { name: 'itemCategory' },
          { name: 'source' },
          { name: 'medium' },
        ],
        metrics: [
          { name: 'itemsPurchased' },
          { name: 'itemRevenue' },
          { name: 'purchaseRevenue' },
          { name: 'itemsAddedToCart' },
          { name: 'itemsCheckedOut' },
        ],
        orderBys: [{ metric: { metricName: 'itemRevenue' }, desc: true }],
        limit: 50,
      });

      return response.rows?.map(row => ({
        itemName: row.dimensionValues?.[0]?.value || 'Unknown',
        itemCategory: row.dimensionValues?.[1]?.value || 'Unknown',
        source: row.dimensionValues?.[2]?.value || 'Unknown',
        medium: row.dimensionValues?.[3]?.value || 'Unknown',
        itemsPurchased: parseInt(row.metricValues?.[0]?.value || '0'),
        itemRevenue: parseFloat(row.metricValues?.[1]?.value || '0'),
        purchaseRevenue: parseFloat(row.metricValues?.[2]?.value || '0'),
        itemsAddedToCart: parseInt(row.metricValues?.[3]?.value || '0'),
        itemsCheckedOut: parseInt(row.metricValues?.[4]?.value || '0'),
      })) || [];
    } catch (error) {
      console.error('Error fetching GA4 e-commerce data:', error);
      return [];
    }
  }

  // Get enhanced acquisition data with attribution
  async getAcquisitionData(startDate: string, endDate: string): Promise<any[]> {
    try {
      const [response] = await this.analyticsDataClient.runReport({
        property: `properties/${this.propertyId}`,
        dateRanges: [{ startDate, endDate }],
        dimensions: [
          { name: 'firstUserSource' },
          { name: 'firstUserMedium' },
          { name: 'firstUserCampaignName' },
          { name: 'sessionSource' },
          { name: 'sessionMedium' },
          { name: 'sessionCampaignName' },
        ],
        metrics: [
          { name: 'newUsers' },
          { name: 'sessions' },
          { name: 'engagedSessions' },
          { name: 'conversions' },
          { name: 'totalRevenue' },
        ],
        orderBys: [{ metric: { metricName: 'newUsers' }, desc: true }],
        limit: 100,
      });

      return response.rows?.map(row => ({
        firstUserSource: row.dimensionValues?.[0]?.value || 'Unknown',
        firstUserMedium: row.dimensionValues?.[1]?.value || 'Unknown',
        firstUserCampaign: row.dimensionValues?.[2]?.value || 'Unknown',
        sessionSource: row.dimensionValues?.[3]?.value || 'Unknown',
        sessionMedium: row.dimensionValues?.[4]?.value || 'Unknown',
        sessionCampaign: row.dimensionValues?.[5]?.value || 'Unknown',
        newUsers: parseInt(row.metricValues?.[0]?.value || '0'),
        sessions: parseInt(row.metricValues?.[1]?.value || '0'),
        engagedSessions: parseInt(row.metricValues?.[2]?.value || '0'),
        conversions: parseInt(row.metricValues?.[3]?.value || '0'),
        totalRevenue: parseFloat(row.metricValues?.[4]?.value || '0'),
      })) || [];
    } catch (error) {
      console.error('Error fetching GA4 acquisition data:', error);
      return [];
    }
  }

  // Get user engagement and retention data
  async getUserEngagementData(startDate: string, endDate: string): Promise<any[]> {
    try {
      const [response] = await this.analyticsDataClient.runReport({
        property: `properties/${this.propertyId}`,
        dateRanges: [{ startDate, endDate }],
        dimensions: [
          { name: 'date' },
          { name: 'userType' },
        ],
        metrics: [
          { name: 'totalUsers' },
          { name: 'newUsers' },
          { name: 'returningUsers' },
          { name: 'engagedSessions' },
          { name: 'engagementRate' },
          { name: 'averageSessionDuration' },
          { name: 'sessionsPerUser' },
        ],
        orderBys: [{ dimension: { dimensionName: 'date' } }],
      });

      return response.rows?.map(row => ({
        date: row.dimensionValues?.[0]?.value || 'Unknown',
        userType: row.dimensionValues?.[1]?.value || 'Unknown',
        totalUsers: parseInt(row.metricValues?.[0]?.value || '0'),
        newUsers: parseInt(row.metricValues?.[1]?.value || '0'),
        returningUsers: parseInt(row.metricValues?.[2]?.value || '0'),
        engagedSessions: parseInt(row.metricValues?.[3]?.value || '0'),
        engagementRate: parseFloat(row.metricValues?.[4]?.value || '0'),
        averageSessionDuration: parseFloat(row.metricValues?.[5]?.value || '0'),
        sessionsPerUser: parseFloat(row.metricValues?.[6]?.value || '0'),
      })) || [];
    } catch (error) {
      console.error('Error fetching GA4 user engagement data:', error);
      return [];
    }
  }

  // Get detailed session source/medium data (matches GA4 screenshot)
  async getSessionSourceMediumData(startDate: string, endDate: string): Promise<GA4SessionSourceData[]> {
    try {
      const [response] = await this.analyticsDataClient.runReport({
        property: `properties/${this.propertyId}`,
        dateRanges: [{ startDate, endDate }],
        dimensions: [
          { name: 'sessionSourceMedium' },
          { name: 'sessionDefaultChannelGroup' },
        ],
        metrics: [
          { name: 'sessions' },
          { name: 'engagedSessions' },
          { name: 'engagementRate' },
          { name: 'userEngagementDuration' },
          { name: 'eventsPerSession' },
          { name: 'eventCount' },
          { name: 'bounceRate' },
          { name: 'totalRevenue' },
        ],
        orderBys: [{ metric: { metricName: 'sessions' }, desc: true }],
        limit: 100,
      });

      return response.rows?.map(row => ({
        sessionSourceMedium: row.dimensionValues?.[0]?.value || 'Unknown',
        sessionDefaultChannelGroup: row.dimensionValues?.[1]?.value || 'Unknown',
        sessions: parseInt(row.metricValues?.[0]?.value || '0'),
        engagedSessions: parseInt(row.metricValues?.[1]?.value || '0'),
        engagementRate: parseFloat(row.metricValues?.[2]?.value || '0'),
        averageEngagementTime: parseFloat(row.metricValues?.[3]?.value || '0'),
        eventsPerSession: parseFloat(row.metricValues?.[4]?.value || '0'),
        eventCount: parseInt(row.metricValues?.[5]?.value || '0'),
        allEvents: parseInt(row.metricValues?.[5]?.value || '0'), // Same as eventCount
        revenue: parseFloat(row.metricValues?.[7]?.value || '0'),
      })) || [];
    } catch (error) {
      console.error('Error fetching GA4 session source/medium data:', error);
      return [];
    }
  }

  // Get enhanced campaign data with all metrics
  async getEnhancedCampaignData(startDate: string, endDate: string): Promise<GA4CampaignData[]> {
    try {
      const [response] = await this.analyticsDataClient.runReport({
        property: `properties/${this.propertyId}`,
        dateRanges: [{ startDate, endDate }],
        dimensions: [
          { name: 'campaignName' },
          { name: 'source' },
          { name: 'medium' },
        ],
        metrics: [
          { name: 'sessions' },
          { name: 'totalUsers' },
          { name: 'conversions' },
          { name: 'engagedSessions' },
          { name: 'engagementRate' },
          { name: 'userEngagementDuration' },
          { name: 'eventsPerSession' },
          { name: 'eventCount' },
          { name: 'bounceRate' },
          { name: 'totalRevenue' },
        ],
        orderBys: [{ metric: { metricName: 'sessions' }, desc: true }],
        limit: 100,
      });

      return response.rows?.map(row => ({
        campaignName: row.dimensionValues?.[0]?.value || 'Unknown',
        source: row.dimensionValues?.[1]?.value || 'Unknown',
        medium: row.dimensionValues?.[2]?.value || 'Unknown',
        sessions: parseInt(row.metricValues?.[0]?.value || '0'),
        users: parseInt(row.metricValues?.[1]?.value || '0'),
        conversions: parseInt(row.metricValues?.[2]?.value || '0'),
        engagedSessions: parseInt(row.metricValues?.[3]?.value || '0'),
        engagementRate: parseFloat(row.metricValues?.[4]?.value || '0'),
        averageEngagementTime: parseFloat(row.metricValues?.[5]?.value || '0'),
        eventsPerSession: parseFloat(row.metricValues?.[6]?.value || '0'),
        eventCount: parseInt(row.metricValues?.[7]?.value || '0'),
        bounceRate: parseFloat(row.metricValues?.[8]?.value || '0'),
        revenue: parseFloat(row.metricValues?.[9]?.value || '0'),
      })) || [];
    } catch (error) {
      console.error('Error fetching GA4 enhanced campaign data:', error);
      return [];
    }
  }
}