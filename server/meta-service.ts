import axios from 'axios';

interface MetaConfig {
  accessToken: string;
  adAccountId: string;
}

interface MetaCampaignData {
  campaignId: string;
  campaignName: string;
  status: string;
  objective: string;
  spend: number;
  impressions: number;
  clicks: number;
  reach: number;
  frequency: number;
  cpm: number;
  cpc: number;
  ctr: number;
  conversions: number;
  costPerConversion: number;
  roas: number;
  dateStart: string;
  dateStop: string;
}

interface MetaAdSetData {
  adsetId: string;
  adsetName: string;
  campaignId: string;
  campaignName: string;
  targeting: any;
  spend: number;
  impressions: number;
  clicks: number;
  conversions: number;
  ctr: number;
  cpc: number;
}

interface MetaInsights {
  totalSpend: number;
  totalImpressions: number;
  totalClicks: number;
  totalConversions: number;
  averageCTR: number;
  averageCPC: number;
  averageROAS: number;
  activeCampaigns: number;
}

export class MetaService {
  private accessToken: string;
  private adAccountId: string;
  private baseUrl = 'https://graph.facebook.com/v18.0';

  constructor(config: MetaConfig) {
    this.accessToken = config.accessToken;
    this.adAccountId = config.adAccountId;
  }

  private async makeRequest(endpoint: string, params: any = {}) {
    try {
      const response = await axios.get(`${this.baseUrl}/${endpoint}`, {
        params: {
          access_token: this.accessToken,
          ...params
        }
      });
      return response.data;
    } catch (error: any) {
      console.error('Meta API Error:', error.response?.data || error.message);
      throw new Error(`Meta API Error: ${error.response?.data?.error?.message || error.message}`);
    }
  }

  async getCampaigns(dateRange: { since: string; until: string }): Promise<MetaCampaignData[]> {
    try {
      const params = {
        fields: 'id,name,status,objective',
        limit: 100
      };

      const response = await this.makeRequest(`act_${this.adAccountId}/campaigns`, params);
      
      const campaignInsights = await Promise.all(
        response.data.map(async (campaign: any) => {
          return this.getCampaignInsights(campaign.id, dateRange);
        })
      );

      return campaignInsights.filter(insight => insight !== null);
    } catch (error) {
      console.error('Error fetching Meta campaigns:', error);
      throw error;
    }
  }

  async getCampaignInsights(campaignId: string, dateRange: { since: string; until: string }): Promise<MetaCampaignData | null> {
    try {
      const params = {
        fields: 'campaign_id,campaign_name,spend,impressions,clicks,reach,frequency,cpm,cpc,ctr,conversions,cost_per_conversion,actions,date_start,date_stop',
        time_range: `{"since":"${dateRange.since}","until":"${dateRange.until}"}`,
        level: 'campaign'
      };

      const response = await this.makeRequest(`${campaignId}/insights`, params);
      
      if (!response.data || response.data.length === 0) {
        return null;
      }

      const insight = response.data[0];
      const conversions = this.extractConversions(insight.actions);
      
      return {
        campaignId: insight.campaign_id,
        campaignName: insight.campaign_name,
        status: 'ACTIVE', // We'll get this from campaign data
        objective: 'CONVERSIONS', // Default, can be enhanced
        spend: parseFloat(insight.spend || '0'),
        impressions: parseInt(insight.impressions || '0'),
        clicks: parseInt(insight.clicks || '0'),
        reach: parseInt(insight.reach || '0'),
        frequency: parseFloat(insight.frequency || '0'),
        cpm: parseFloat(insight.cpm || '0'),
        cpc: parseFloat(insight.cpc || '0'),
        ctr: parseFloat(insight.ctr || '0'),
        conversions: conversions,
        costPerConversion: parseFloat(insight.cost_per_conversion || '0'),
        roas: conversions > 0 ? parseFloat(insight.spend || '0') / conversions : 0,
        dateStart: insight.date_start,
        dateStop: insight.date_stop,
      };
    } catch (error) {
      console.error(`Error fetching insights for campaign ${campaignId}:`, error);
      return null;
    }
  }

  async getAdSets(campaignId: string, dateRange: { since: string; until: string }): Promise<MetaAdSetData[]> {
    try {
      const params = {
        fields: 'id,name,targeting',
        limit: 100
      };

      const response = await this.makeRequest(`${campaignId}/adsets`, params);
      
      const adSetInsights = await Promise.all(
        response.data.map(async (adset: any) => {
          const insights = await this.getAdSetInsights(adset.id, dateRange);
          return {
            adsetId: adset.id,
            adsetName: adset.name,
            campaignId: campaignId,
            campaignName: '', // Will be populated
            targeting: adset.targeting,
            ...insights
          };
        })
      );

      return adSetInsights.filter(insight => insight !== null);
    } catch (error) {
      console.error('Error fetching Meta ad sets:', error);
      throw error;
    }
  }

  async getAdSetInsights(adsetId: string, dateRange: { since: string; until: string }): Promise<any> {
    try {
      const params = {
        fields: 'spend,impressions,clicks,ctr,cpc,conversions',
        time_range: `{"since":"${dateRange.since}","until":"${dateRange.until}"}`,
        level: 'adset'
      };

      const response = await this.makeRequest(`${adsetId}/insights`, params);
      
      if (!response.data || response.data.length === 0) {
        return {
          spend: 0,
          impressions: 0,
          clicks: 0,
          conversions: 0,
          ctr: 0,
          cpc: 0
        };
      }

      const insight = response.data[0];
      const conversions = this.extractConversions(insight.actions);
      
      return {
        spend: parseFloat(insight.spend || '0'),
        impressions: parseInt(insight.impressions || '0'),
        clicks: parseInt(insight.clicks || '0'),
        conversions: conversions,
        ctr: parseFloat(insight.ctr || '0'),
        cpc: parseFloat(insight.cpc || '0'),
      };
    } catch (error) {
      console.error(`Error fetching insights for adset ${adsetId}:`, error);
      return null;
    }
  }

  async getAccountInsights(dateRange: { since: string; until: string }): Promise<MetaInsights> {
    try {
      const params = {
        fields: 'spend,impressions,clicks,ctr,cpc,conversions,actions',
        time_range: `{"since":"${dateRange.since}","until":"${dateRange.until}"}`,
        level: 'account'
      };

      const response = await this.makeRequest(`act_${this.adAccountId}/insights`, params);
      
      if (!response.data || response.data.length === 0) {
        return this.getEmptyInsights();
      }

      const insight = response.data[0];
      const conversions = this.extractConversions(insight.actions);
      
      // Get campaign count
      const campaignsResponse = await this.makeRequest(`act_${this.adAccountId}/campaigns`, {
        fields: 'id',
        effective_status: '["ACTIVE"]'
      });

      return {
        totalSpend: parseFloat(insight.spend || '0'),
        totalImpressions: parseInt(insight.impressions || '0'),
        totalClicks: parseInt(insight.clicks || '0'),
        totalConversions: conversions,
        averageCTR: parseFloat(insight.ctr || '0'),
        averageCPC: parseFloat(insight.cpc || '0'),
        averageROAS: conversions > 0 ? parseFloat(insight.spend || '0') / conversions : 0,
        activeCampaigns: campaignsResponse.data?.length || 0,
      };
    } catch (error) {
      console.error('Error fetching Meta account insights:', error);
      return this.getEmptyInsights();
    }
  }

  private extractConversions(actions: any[]): number {
    if (!actions || !Array.isArray(actions)) return 0;
    
    // Look for conversion actions
    const conversionActions = actions.filter(action => 
      action.action_type === 'purchase' || 
      action.action_type === 'lead' || 
      action.action_type === 'complete_registration' ||
      action.action_type === 'submit_application'
    );
    
    return conversionActions.reduce((sum, action) => sum + parseInt(action.value || '0'), 0);
  }

  private getEmptyInsights(): MetaInsights {
    return {
      totalSpend: 0,
      totalImpressions: 0,
      totalClicks: 0,
      totalConversions: 0,
      averageCTR: 0,
      averageCPC: 0,
      averageROAS: 0,
      activeCampaigns: 0,
    };
  }

  // Utility method to get date range in Meta's format
  static getDateRange(days: number = 30): { since: string; until: string } {
    const until = new Date();
    const since = new Date();
    since.setDate(since.getDate() - days);
    
    return {
      since: since.toISOString().split('T')[0],
      until: until.toISOString().split('T')[0]
    };
  }
}