import { pool } from './db';
import { 
  normalizeUTMParams, 
  parseMetaCampaign, 
  getSessionTrackingStartDate, 
  isPostSessionTracking, 
  normalizeCampaignSource,
  FUNNEL_STAGES 
} from './utils/utm-parser';

// Comprehensive UTM data consolidation and cleanup service
export class UTMDataConsolidation {
  
  // Clean and normalize UTM campaign names
  static normalizeUTMCampaign(campaign: string): string {
    if (!campaign) return campaign;
    
    // URL decode and normalize
    return decodeURIComponent(campaign)
      .replace(/\+/g, ' ')
      .replace(/\s+/g, ' ')
      .trim();
  }

  // Clean and normalize UTM source for consistent grouping
  static normalizeUTMSource(source: string): string {
    return normalizeCampaignSource(source);
  }

  // Get unified campaign performance data from all sources with normalized sources
  static async getUnifiedCampaignData(days: number = 7): Promise<any[]> {
    const query = `
      WITH normalized_data AS (
        SELECT 
          session_id,
          event_type,
          timestamp,
          -- Apply source normalization at query level
          CASE 
            WHEN LOWER(event_data::json->>'utm_source') IN ('meta', 'facebook', 'fb', 'instagram', 'ig') THEN 'meta'
            WHEN LOWER(event_data::json->>'utm_source') IN ('google', 'goog', 'youtube', 'yt') THEN 'google'
            WHEN LOWER(event_data::json->>'utm_source') IN ('whatsapp', 'wa') THEN 'whatsapp'
            WHEN LOWER(event_data::json->>'utm_source') = 'qr' THEN 'qr'
            WHEN LOWER(event_data::json->>'utm_source') = 'app' THEN 'app'
            WHEN event_data::json->>'utm_source' IS NULL OR LOWER(event_data::json->>'utm_source') = 'direct' THEN 'direct'
            ELSE LOWER(TRIM(event_data::json->>'utm_source'))
          END as normalized_source,
          LOWER(COALESCE(event_data::json->>'utm_medium', 'unknown')) as utm_medium,
          COALESCE(event_data::json->>'utm_campaign', 'unknown campaign') as utm_campaign
        FROM event_logs 
        WHERE timestamp >= NOW() - INTERVAL '${days} days'
          AND timestamp >= '2025-07-01T00:00:00Z'  -- Only post-tracking data
      ),
      session_data AS (
        SELECT 
          CASE 
            WHEN normalized_source = 'meta' THEN utm_campaign
            WHEN normalized_source = 'qr' THEN CONCAT('QR - ', utm_campaign)
            WHEN normalized_source = 'app' THEN CONCAT('App - ', utm_campaign)
            WHEN normalized_source = 'direct' THEN 'Direct Traffic'
            ELSE CONCAT(UPPER(LEFT(normalized_source, 1)) || LOWER(SUBSTRING(normalized_source, 2)), ' - ', utm_campaign)
          END as campaign_name,
          normalized_source as utm_source,
          utm_medium,
          utm_campaign,
          COUNT(DISTINCT session_id) as sessions,
          COUNT(*) as events,
          COUNT(CASE WHEN event_type = 'page_view' THEN 1 END) as page_views,
          COUNT(CASE WHEN event_type = 'form_start' THEN 1 END) as form_starts,
          COUNT(CASE WHEN event_type = 'booking_complete' THEN 1 END) as booking_events,
          MIN(timestamp) as first_occurrence,
          MAX(timestamp) as last_occurrence
        FROM event_logs 
        WHERE event_data::json->>'utm_source' IS NOT NULL 
          AND event_data::json->>'utm_source' != ''
          AND timestamp >= NOW() - INTERVAL '${days} days'
          AND timestamp >= '2025-07-01T00:00:00.000Z'  -- Only post-session tracking data
        GROUP BY event_data::json->>'utm_source', event_data::json->>'utm_medium', event_data::json->>'utm_campaign'
      ),
      
      booking_data AS (
        SELECT 
          -- Apply source normalization for bookings data too
          CASE 
            WHEN LOWER(utm_source) IN ('meta', 'facebook', 'fb', 'instagram', 'ig') THEN 'meta'
            WHEN LOWER(utm_source) IN ('google', 'goog', 'youtube', 'yt') THEN 'google'
            WHEN LOWER(utm_source) = 'qr' THEN 'qr'
            WHEN LOWER(utm_source) = 'app' THEN 'app'
            WHEN utm_source IS NULL OR LOWER(utm_source) = 'direct' THEN 'direct'
            ELSE LOWER(TRIM(utm_source))
          END as normalized_source,
          CASE 
            WHEN LOWER(utm_source) IN ('meta', 'facebook', 'fb', 'instagram', 'ig') THEN utm_campaign
            WHEN LOWER(utm_source) = 'qr' THEN CONCAT('QR - ', utm_campaign)
            WHEN LOWER(utm_source) = 'app' THEN CONCAT('App - ', utm_campaign)
            WHEN utm_source IS NULL OR LOWER(utm_source) = 'direct' THEN 'Direct Traffic'
            ELSE CONCAT(COALESCE(utm_source, 'Unknown'), ' - ', COALESCE(utm_campaign, 'Unknown'))
          END as campaign_name,
          utm_medium,
          utm_campaign,
          COUNT(*) as bookings,
          COUNT(CASE WHEN status = 'confirmed' THEN 1 END) as confirmed_bookings,
          COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_bookings,
          MIN(created_at) as first_booking,
          MAX(created_at) as last_booking
        FROM bookings 
        WHERE utm_source IS NOT NULL 
          AND created_at >= NOW() - INTERVAL '${days} days'
          AND created_at >= '2025-07-01T00:00:00Z'  -- Only post-tracking data
        GROUP BY normalized_source, utm_medium, utm_campaign
      )
      
      SELECT 
        COALESCE(sd.campaign_name, bd.campaign_name) as campaign_name,
        COALESCE(sd.utm_source, bd.normalized_source) as utm_source,
        COALESCE(sd.utm_medium, bd.utm_medium) as utm_medium,
        COALESCE(sd.utm_campaign, bd.utm_campaign) as utm_campaign,
        COALESCE(sd.sessions, 0) as sessions,
        COALESCE(sd.events, 0) as events,
        COALESCE(sd.page_views, 0) as page_views,
        COALESCE(sd.form_starts, 0) as form_starts,
        COALESCE(bd.bookings, 0) as bookings,
        COALESCE(bd.confirmed_bookings, 0) as confirmed_bookings,
        COALESCE(bd.completed_bookings, 0) as completed_bookings,
        CASE 
          WHEN COALESCE(sd.sessions, 0) > 0 
          THEN ROUND((COALESCE(bd.bookings, 0)::numeric / sd.sessions * 100), 2)
          ELSE 0 
        END as conversion_rate,
        COALESCE(sd.first_occurrence, bd.first_booking) as first_activity,
        COALESCE(sd.last_occurrence, bd.last_booking) as last_activity
      FROM session_data sd
      FULL OUTER JOIN booking_data bd ON sd.utm_source = bd.normalized_source AND sd.utm_campaign = bd.utm_campaign
      WHERE COALESCE(sd.sessions, 0) > 0 OR COALESCE(bd.bookings, 0) > 0
      ORDER BY COALESCE(bd.bookings, 0) DESC, COALESCE(sd.sessions, 0) DESC;
    `;

    const result = await pool.query(query);
    return result.rows.map(row => ({
      ...row,
      utm_campaign: this.normalizeUTMCampaign(row.utm_campaign)
    }));
  }

  // Get comprehensive funnel data by campaign with detailed tracking
  static async getConversionFunnelByCampaign(days: number = 7): Promise<any[]> {
    const query = `
      WITH campaign_funnel AS (
        SELECT 
          session_id,
          -- Apply source normalization
          CASE 
            WHEN LOWER(event_data::json->>'utm_source') IN ('meta', 'facebook', 'fb', 'instagram', 'ig') THEN 'meta'
            WHEN LOWER(event_data::json->>'utm_source') IN ('google', 'goog', 'youtube', 'yt') THEN 'google'
            WHEN LOWER(event_data::json->>'utm_source') = 'qr' THEN 'qr'
            WHEN LOWER(event_data::json->>'utm_source') = 'app' THEN 'app'
            WHEN event_data::json->>'utm_source' IS NULL OR LOWER(event_data::json->>'utm_source') = 'direct' THEN 'direct'
            ELSE LOWER(TRIM(COALESCE(event_data::json->>'utm_source', 'direct')))
          END as normalized_source,
          CASE 
            WHEN LOWER(event_data::json->>'utm_source') IN ('meta', 'facebook', 'fb', 'instagram', 'ig') THEN event_data::json->>'utm_campaign'
            WHEN LOWER(event_data::json->>'utm_source') = 'qr' THEN CONCAT('QR - ', event_data::json->>'utm_campaign')
            WHEN LOWER(event_data::json->>'utm_source') = 'app' THEN CONCAT('App - ', event_data::json->>'utm_campaign')
            WHEN event_data::json->>'utm_source' IS NULL OR LOWER(event_data::json->>'utm_source') = 'direct' THEN 'Direct Traffic'
            ELSE CONCAT(COALESCE(event_data::json->>'utm_source', 'Unknown'), ' - ', COALESCE(event_data::json->>'utm_campaign', 'Unknown'))
          END as campaign_name,
          MAX(CASE WHEN event_type = 'page_view' THEN 1 ELSE 0 END) as visited_site,
          MAX(CASE WHEN event_type = 'form_start' THEN 1 ELSE 0 END) as started_form,
          MAX(CASE WHEN event_type = 'postal_code_checked' THEN 1 ELSE 0 END) as checked_postal_code,
          MAX(CASE WHEN event_type = 'date_selected' THEN 1 ELSE 0 END) as selected_date,
          MAX(CASE WHEN event_type = 'time_slot_selected' THEN 1 ELSE 0 END) as selected_time,
          MAX(CASE WHEN event_type = 'otp_requested' THEN 1 ELSE 0 END) as requested_otp,
          MAX(CASE WHEN event_type = 'otp_verified' THEN 1 ELSE 0 END) as verified_otp,
          MAX(CASE WHEN event_type = 'booking_complete' THEN 1 ELSE 0 END) as completed_booking
        FROM event_logs 
        WHERE timestamp >= NOW() - INTERVAL '${days} days'
          AND session_id IS NOT NULL
          AND event_data IS NOT NULL
          AND event_data != '{}'
          AND timestamp >= '2025-07-01T00:00:00.000Z'  -- Only post-tracking data
        GROUP BY session_id, campaign_name, normalized_source
      ),
      
      campaign_stats AS (
        SELECT 
          campaign_name,
          normalized_source,
          COUNT(*) as total_sessions,
          SUM(visited_site) as step_1_visited,
          SUM(started_form) as step_2_started_form,
          SUM(checked_postal_code) as step_3_checked_postal,
          SUM(selected_date) as step_4_selected_date,
          SUM(selected_time) as step_5_selected_time,
          SUM(requested_otp) as step_6_requested_otp,
          SUM(verified_otp) as step_7_verified_otp,
          SUM(completed_booking) as step_8_completed_booking
        FROM campaign_funnel
        WHERE campaign_name IS NOT NULL 
          AND campaign_name != 'Unknown - Unknown'
          AND normalized_source IS NOT NULL
        GROUP BY campaign_name, normalized_source
      ),
      
      booking_data AS (
        SELECT 
          -- Apply source normalization to bookings too
          CASE 
            WHEN LOWER(utm_source) IN ('meta', 'facebook', 'fb', 'instagram', 'ig') THEN 'meta'
            WHEN LOWER(utm_source) IN ('google', 'goog', 'youtube', 'yt') THEN 'google'
            WHEN LOWER(utm_source) = 'qr' THEN 'qr'
            WHEN LOWER(utm_source) = 'app' THEN 'app'
            WHEN utm_source IS NULL OR LOWER(utm_source) = 'direct' THEN 'direct'
            ELSE LOWER(TRIM(utm_source))
          END as normalized_source,
          CASE 
            WHEN LOWER(utm_source) IN ('meta', 'facebook', 'fb', 'instagram', 'ig') THEN utm_campaign
            WHEN LOWER(utm_source) = 'qr' THEN CONCAT('QR - ', utm_campaign)
            WHEN LOWER(utm_source) = 'app' THEN CONCAT('App - ', utm_campaign)
            WHEN utm_source IS NULL OR LOWER(utm_source) = 'direct' THEN 'Direct Traffic'
            ELSE CONCAT(COALESCE(utm_source, 'Unknown'), ' - ', COALESCE(utm_campaign, 'Unknown'))
          END as campaign_name,
          COUNT(*) as actual_bookings,
          COUNT(CASE WHEN status = 'confirmed' THEN 1 END) as confirmed_bookings
        FROM bookings 
        WHERE utm_source IS NOT NULL 
          AND created_at >= NOW() - INTERVAL '${days} days'
          AND created_at >= '2025-07-01T00:00:00Z'  -- Only post-tracking data
        GROUP BY normalized_source, campaign_name
      )
      
      SELECT 
        cs.campaign_name,
        cs.normalized_source as utm_source,
        cs.total_sessions,
        cs.step_1_visited,
        cs.step_2_started_form,
        cs.step_3_checked_postal,
        cs.step_4_selected_date,
        cs.step_5_selected_time,
        cs.step_6_requested_otp,
        cs.step_7_verified_otp,
        cs.step_8_completed_booking,
        COALESCE(bd.actual_bookings, 0) as actual_bookings,
        COALESCE(bd.confirmed_bookings, 0) as confirmed_bookings,
        CASE 
          WHEN cs.step_1_visited > 0 
          THEN ROUND((cs.step_2_started_form::numeric / cs.step_1_visited * 100), 2)
          ELSE 0 
        END as conversion_rate_step_2,
        CASE 
          WHEN cs.step_2_started_form > 0 
          THEN ROUND((cs.step_3_checked_postal::numeric / cs.step_2_started_form * 100), 2)
          ELSE 0 
        END as conversion_rate_step_3,
        CASE 
          WHEN cs.step_3_checked_postal > 0 
          THEN ROUND((cs.step_4_selected_date::numeric / cs.step_3_checked_postal * 100), 2)
          ELSE 0 
        END as conversion_rate_step_4,
        CASE 
          WHEN cs.step_4_selected_date > 0 
          THEN ROUND((cs.step_5_selected_time::numeric / cs.step_4_selected_date * 100), 2)
          ELSE 0 
        END as conversion_rate_step_5,
        CASE 
          WHEN cs.step_5_selected_time > 0 
          THEN ROUND((cs.step_6_requested_otp::numeric / cs.step_5_selected_time * 100), 2)
          ELSE 0 
        END as conversion_rate_step_6,
        CASE 
          WHEN cs.step_6_requested_otp > 0 
          THEN ROUND((cs.step_7_verified_otp::numeric / cs.step_6_requested_otp * 100), 2)
          ELSE 0 
        END as conversion_rate_step_7,
        CASE 
          WHEN cs.step_1_visited > 0 
          THEN ROUND((COALESCE(bd.actual_bookings, 0)::numeric / cs.step_1_visited * 100), 2)
          ELSE 0 
        END as overall_conversion_rate
      FROM campaign_stats cs
      LEFT JOIN booking_data bd ON cs.campaign_name = bd.campaign_name AND cs.normalized_source = bd.normalized_source
      WHERE cs.total_sessions >= 5  -- Only show campaigns with meaningful traffic
      ORDER BY cs.total_sessions DESC, COALESCE(bd.actual_bookings, 0) DESC;
    `;

    const result = await pool.query(query);
    return result.rows;
  }

  // Get overall funnel data (existing method for backwards compatibility)
  static async getConversionFunnel(days: number = 7): Promise<any[]> {
    const query = `
      WITH funnel_events AS (
        SELECT DISTINCT
          session_id,
          MAX(CASE WHEN event_type = 'page_view' THEN 1 ELSE 0 END) as visited_site,
          MAX(CASE WHEN event_type = 'form_start' THEN 1 ELSE 0 END) as started_form,
          MAX(CASE WHEN event_type = 'postal_code_checked' THEN 1 ELSE 0 END) as checked_postal_code,
          MAX(CASE WHEN event_type = 'date_selected' THEN 1 ELSE 0 END) as selected_date,
          MAX(CASE WHEN event_type = 'time_slot_selected' THEN 1 ELSE 0 END) as selected_time,
          MAX(CASE WHEN event_type = 'otp_requested' THEN 1 ELSE 0 END) as requested_otp,
          MAX(CASE WHEN event_type = 'otp_verified' THEN 1 ELSE 0 END) as verified_otp,
          MAX(CASE WHEN event_type = 'booking_complete' THEN 1 ELSE 0 END) as completed_booking
        FROM event_logs 
        WHERE timestamp >= NOW() - INTERVAL '${days} days'
          AND session_id IS NOT NULL
        GROUP BY session_id
      )
      
      SELECT 
        '1. Visited Website' as step,
        1 as step_number,
        COUNT(*) as count,
        100.0 as percentage,
        0 as drop_off_count,
        0.0 as drop_off_percentage
      FROM funnel_events WHERE visited_site = 1
      
      UNION ALL
      
      SELECT 
        '2. Started Booking Form' as step,
        2 as step_number,
        COUNT(*) as count,
        ROUND(COUNT(*)::numeric / (SELECT COUNT(*) FROM funnel_events WHERE visited_site = 1) * 100, 1) as percentage,
        (SELECT COUNT(*) FROM funnel_events WHERE visited_site = 1) - COUNT(*) as drop_off_count,
        ROUND(((SELECT COUNT(*) FROM funnel_events WHERE visited_site = 1) - COUNT(*))::numeric / (SELECT COUNT(*) FROM funnel_events WHERE visited_site = 1) * 100, 1) as drop_off_percentage
      FROM funnel_events WHERE started_form = 1
      
      UNION ALL
      
      SELECT 
        '3. Checked Postal Code' as step,
        3 as step_number,
        COUNT(*) as count,
        ROUND(COUNT(*)::numeric / (SELECT COUNT(*) FROM funnel_events WHERE visited_site = 1) * 100, 1) as percentage,
        (SELECT COUNT(*) FROM funnel_events WHERE started_form = 1) - COUNT(*) as drop_off_count,
        ROUND(((SELECT COUNT(*) FROM funnel_events WHERE started_form = 1) - COUNT(*))::numeric / (SELECT COUNT(*) FROM funnel_events WHERE started_form = 1) * 100, 1) as drop_off_percentage
      FROM funnel_events WHERE checked_postal_code = 1
      
      UNION ALL
      
      SELECT 
        '4. Selected Date' as step,
        4 as step_number,
        COUNT(*) as count,
        ROUND(COUNT(*)::numeric / (SELECT COUNT(*) FROM funnel_events WHERE visited_site = 1) * 100, 1) as percentage,
        (SELECT COUNT(*) FROM funnel_events WHERE checked_postal_code = 1) - COUNT(*) as drop_off_count,
        ROUND(((SELECT COUNT(*) FROM funnel_events WHERE checked_postal_code = 1) - COUNT(*))::numeric / (SELECT COUNT(*) FROM funnel_events WHERE checked_postal_code = 1) * 100, 1) as drop_off_percentage
      FROM funnel_events WHERE selected_date = 1
      
      UNION ALL
      
      SELECT 
        '5. Selected Time Slot' as step,
        5 as step_number,
        COUNT(*) as count,
        ROUND(COUNT(*)::numeric / (SELECT COUNT(*) FROM funnel_events WHERE visited_site = 1) * 100, 1) as percentage,
        (SELECT COUNT(*) FROM funnel_events WHERE selected_date = 1) - COUNT(*) as drop_off_count,
        ROUND(((SELECT COUNT(*) FROM funnel_events WHERE selected_date = 1) - COUNT(*))::numeric / (SELECT COUNT(*) FROM funnel_events WHERE selected_date = 1) * 100, 1) as drop_off_percentage
      FROM funnel_events WHERE selected_time = 1
      
      UNION ALL
      
      SELECT 
        '6. Requested OTP' as step,
        6 as step_number,
        COUNT(*) as count,
        ROUND(COUNT(*)::numeric / (SELECT COUNT(*) FROM funnel_events WHERE visited_site = 1) * 100, 1) as percentage,
        (SELECT COUNT(*) FROM funnel_events WHERE selected_time = 1) - COUNT(*) as drop_off_count,
        ROUND(((SELECT COUNT(*) FROM funnel_events WHERE selected_time = 1) - COUNT(*))::numeric / (SELECT COUNT(*) FROM funnel_events WHERE selected_time = 1) * 100, 1) as drop_off_percentage
      FROM funnel_events WHERE requested_otp = 1
      
      UNION ALL
      
      SELECT 
        '7. Verified OTP' as step,
        7 as step_number,
        COUNT(*) as count,
        ROUND(COUNT(*)::numeric / (SELECT COUNT(*) FROM funnel_events WHERE visited_site = 1) * 100, 1) as percentage,
        (SELECT COUNT(*) FROM funnel_events WHERE requested_otp = 1) - COUNT(*) as drop_off_count,
        ROUND(((SELECT COUNT(*) FROM funnel_events WHERE requested_otp = 1) - COUNT(*))::numeric / (SELECT COUNT(*) FROM funnel_events WHERE requested_otp = 1) * 100, 1) as drop_off_percentage
      FROM funnel_events WHERE verified_otp = 1
      
      UNION ALL
      
      SELECT 
        '8. Completed Booking' as step,
        8 as step_number,
        COUNT(*) as count,
        ROUND(COUNT(*)::numeric / (SELECT COUNT(*) FROM funnel_events WHERE visited_site = 1) * 100, 1) as percentage,
        (SELECT COUNT(*) FROM funnel_events WHERE verified_otp = 1) - COUNT(*) as drop_off_count,
        ROUND(((SELECT COUNT(*) FROM funnel_events WHERE verified_otp = 1) - COUNT(*))::numeric / (SELECT COUNT(*) FROM funnel_events WHERE verified_otp = 1) * 100, 1) as drop_off_percentage
      FROM funnel_events WHERE completed_booking = 1
      
      ORDER BY step_number;
    `;

    const result = await pool.query(query);
    return result.rows;
  }

  // Clean up and normalize UTM data across all tables
  static async cleanupUTMData(): Promise<void> {
    // Normalize campaign names in bookings table
    await pool.query(`
      UPDATE bookings 
      SET utm_campaign = regexp_replace(
        regexp_replace(utm_campaign, '%7C', '|', 'g'),
        '\\+', ' ', 'g'
      )
      WHERE utm_campaign IS NOT NULL;
    `);

    // Clean up event_logs data (this is read-only for historical preservation)
    console.log('UTM data cleanup completed for bookings table');
  }
}