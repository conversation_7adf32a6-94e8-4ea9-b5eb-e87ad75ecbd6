<!DOCTYPE html>
<html>
<head>
    <title>Test Facebook Purchase Event</title>
    <!-- Facebook Pixel Code -->
    <script>
    !function(f,b,e,v,n,t,s)
    {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
    n.callMethod.apply(n,arguments):n.queue.push(arguments)};
    if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
    n.queue=[];t=b.createElement(e);t.async=!0;
    t.src=v;s=b.getElementsByTagName(e)[0];
    s.parentNode.insertBefore(t,s)}(window, document,'script',
    'https://connect.facebook.net/en_US/fbevents.js');

    fbq('init', '2478007082599449');
    fbq('track', 'PageView');
    </script>
    <noscript><img height="1" width="1" style="display:none"
    src="https://www.facebook.com/tr?id=2478007082599449&ev=PageView&noscript=1"
    /></noscript>
    <!-- End Facebook Pixel Code -->
</head>
<body>
    <h1>Facebook Purchase Event Trigger</h1>
    <p>Click the button below to fire a Purchase event:</p>
    
    <button onclick="triggerPurchase()" style="
        background: #1877f2;
        color: white;
        padding: 15px 30px;
        border: none;
        border-radius: 8px;
        font-size: 16px;
        cursor: pointer;
        margin: 10px;
    ">Fire Purchase Event</button>

    <button onclick="triggerAllEvents()" style="
        background: #42b883;
        color: white;
        padding: 15px 30px;
        border: none;
        border-radius: 8px;
        font-size: 16px;
        cursor: pointer;
        margin: 10px;
    ">Fire All Funnel Events</button>

    <div id="status" style="margin-top: 20px; padding: 15px; background: #f0f0f0; border-radius: 8px;"></div>

    <script>
    function updateStatus(message) {
        document.getElementById('status').innerHTML += '<div>' + new Date().toLocaleTimeString() + ': ' + message + '</div>';
    }

    function triggerPurchase() {
        updateStatus('🔥 Firing Purchase event...');
        
        fbq('track', 'Purchase', {
            value: 0,
            currency: 'INR',
            content_type: 'product',
            content_ids: ['perfume_trial'],
            content_name: 'Perfume Trial Booking Completed',
            content_category: 'Beauty',
            postal_code: '110001',
            city: 'Delhi',
            state: 'Delhi',
            appointment_date: '2025-07-01',
            appointment_time: '14:00'
        });
        
        updateStatus('✅ Purchase event sent! Check Events Manager in 1-2 minutes.');
    }

    function triggerAllEvents() {
        updateStatus('🚀 Starting clean custom event sequence...');
        
        // 1. Page View
        fbq('trackCustom', 'PageView', {
            content_name: 'Perfume Trial Homepage',
            content_category: 'Beauty'
        });
        updateStatus('✅ PageView event sent');
        
        setTimeout(() => {
            // 2. Check Availability Pressed
            fbq('trackCustom', 'CheckAvailabilityPressed', {
                content_name: 'Check Availability Button Pressed',
                content_category: 'Beauty',
                postal_code: '110001',
                city: 'Delhi',
                state: 'Delhi'
            });
            updateStatus('✅ CheckAvailabilityPressed event sent');
        }, 1000);
        
        setTimeout(() => {
            // 3. Date Selected
            fbq('trackCustom', 'DateSelected', {
                content_name: 'Date Selected',
                content_category: 'Beauty',
                appointment_date: '2025-07-01',
                postal_code: '110001'
            });
            updateStatus('✅ DateSelected event sent');
        }, 2000);
        
        setTimeout(() => {
            // 4. Time Slot Selected
            fbq('trackCustom', 'TimeSlotSelected', {
                content_name: 'Time Slot Selected',
                content_category: 'Beauty',
                appointment_time: '14:00',
                appointment_date: '2025-07-01',
                postal_code: '110001'
            });
            updateStatus('✅ TimeSlotSelected event sent');
        }, 3000);
        
        setTimeout(() => {
            // 5. OTP Requested
            fbq('trackCustom', 'OTPRequested', {
                content_name: 'OTP Requested',
                content_category: 'Beauty',
                phone_number: '+919999999999'
            });
            updateStatus('✅ OTPRequested event sent');
        }, 4000);
        
        setTimeout(() => {
            // 6. Book My Trial Pressed
            fbq('trackCustom', 'BookMyTrialPressed', {
                content_name: 'Book My Trial Button Clicked',
                content_category: 'Beauty',
                phone_number: '+919999999999'
            });
            updateStatus('✅ BookMyTrialPressed event sent');
        }, 5000);
        
        setTimeout(() => {
            // 7. Booking Confirmed
            fbq('trackCustom', 'BookingConfirmed', {
                content_name: 'Booking Confirmed',
                content_category: 'Beauty',
                booking_id: 'test_booking_123',
                postal_code: '110001',
                city: 'Delhi',
                state: 'Delhi',
                appointment_date: '2025-07-01',
                appointment_time: '14:00',
                sales_rep: 'Test Rep',
                phone_number: '+919999999999'
            });
            updateStatus('🎉 BookingConfirmed event sent!');
            updateStatus('📊 Wait 2-3 minutes, then check Events Manager');
            updateStatus('Your clean custom events sequence:');
            updateStatus('PageView → CheckAvailabilityPressed → DateSelected → TimeSlotSelected → OTPRequested → BookMyTrialPressed → BookingConfirmed');
        }, 6000);
    }

    // Auto-fire PageView
    updateStatus('📄 PageView event sent automatically');
    </script>
</body>
</html>