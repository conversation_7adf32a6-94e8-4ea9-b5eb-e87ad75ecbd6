#!/usr/bin/env node

/**
 * Production API Fix Script
 * This script helps debug and fix API routing issues in production deployment
 */

import { config } from 'dotenv';
import express from 'express';
import { createServer } from 'http';
import path from 'path';
import fs from 'fs';

// Load environment variables
config();

const app = express();
app.use(express.json());
app.use(express.urlencoded({ extended: false }));

// Simple health check endpoint that should work in production
app.get('/api/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'unknown',
    routes_loaded: 'basic'
  });
});

// Test database connectivity
app.get('/api/db-test', async (req, res) => {
  try {
    // Import database connection
    const { pool } = await import('./server/db.js');
    const result = await pool.query('SELECT 1 as test');
    res.json({ database: 'connected', result: result.rows[0] });
  } catch (error) {
    res.status(500).json({ 
      database: 'error', 
      error: error.message,
      stack: error.stack 
    });
  }
});

// Serve static files
const distPath = path.resolve('./public');
if (fs.existsSync(distPath)) {
  app.use(express.static(distPath));
  
  // Only serve index.html for non-API routes
  app.get('*', (req, res) => {
    if (req.path.startsWith('/api/')) {
      return res.status(404).json({ error: 'API endpoint not found' });
    }
    res.sendFile(path.resolve(distPath, 'index.html'));
  });
} else {
  console.error('⚠️  Public directory not found:', distPath);
}

const port = process.env.PORT || 5000;
const server = createServer(app);

server.listen({
  port,
  host: "0.0.0.0",
}, () => {
  console.log(`🚀 Production API fix server running on port ${port}`);
  console.log(`📍 Health check: http://localhost:${port}/api/health`);
  console.log(`🗄️  Database test: http://localhost:${port}/api/db-test`);
});