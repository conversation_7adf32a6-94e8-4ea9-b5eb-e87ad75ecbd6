import { pgTable, text, serial, integer, boolean, timestamp, varchar, jsonb } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";
import { relations } from "drizzle-orm";

export const users = pgTable("users", {
  id: serial("id").primaryKey(),
  username: text("username").notNull().unique(),
  password: text("password").notNull(),
  role: text("role").notNull().default("admin"), // "admin", "support", "sales", "marketing"
  name: text("name").notNull(),
  email: text("email"),
  isActive: boolean("is_active").notNull().default(true),
  dashboardPermissions: text("dashboard_permissions"), // JSON string with permissions
  createdAt: timestamp("created_at").notNull().defaultNow(),
});

export const adminSessions = pgTable("admin_sessions", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").notNull(),
  sessionId: text("session_id").notNull(),
  loginTime: timestamp("login_time").notNull().defaultNow(),
  logoutTime: timestamp("logout_time"),
  ipAddress: text("ip_address"),
  userAgent: text("user_agent"),
  isActive: boolean("is_active").notNull().default(true),
});



export const bookings = pgTable("bookings", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  phone: text("phone").notNull(),
  email: text("email"),
  address: text("address").notNull(),
  postalCode: text("postal_code").notNull(),
  date: timestamp("date").notNull(),
  timeSlot: text("time_slot").notNull(),
  service: text("service").notNull().default("Perfume Trial at Home"),
  repAssigned: text("rep_assigned"),
  status: text("status").notNull().default("confirmed"), // confirmed, completed, cancelled, no-show
  secureToken: text("secure_token").unique().notNull(),
  
  // Customer Support Fields
  appointmentCompleted: boolean("appointment_completed").default(false),
  customerSatisfaction: integer("customer_satisfaction"), // 1-5 rating
  feedbackNotes: text("feedback_notes"),
  
  // Sales & Transaction Fields
  purchaseAmount: integer("purchase_amount"), // Amount in paise (₹1 = 100 paise)
  orderId: text("order_id"),
  paymentStatus: text("payment_status"), // pending, completed, failed, refunded
  paymentMethod: text("payment_method"), // card, upi, cash, etc.
  
  // Communication Tracking
  callAttempts: integer("call_attempts").default(0),
  lastCallDate: timestamp("last_call_date"),
  whatsappSent: integer("whatsapp_sent").default(0),
  emailsSent: integer("emails_sent").default(0),
  
  // Support Ticket Integration
  supportTicketId: text("support_ticket_id"),
  lastContactMethod: text("last_contact_method"), // call, whatsapp, email, in-person
  
  // UTM Tracking Fields
  utmSource: text("utm_source"), // Meta, Google, Direct, etc.
  utmMedium: text("utm_medium"), // social, cpc, organic, etc.
  utmCampaign: text("utm_campaign"), // campaign name
  utmContent: text("utm_content"), // ad content identifier
  utmTerm: text("utm_term"), // keyword or term
  trafficSource: text("traffic_source"), // Simplified source (e.g., "Facebook Ad", "Google Search", "Direct Visit")
  
  // Session Tracking for End-to-End Funnel Analysis
  sessionId: text("session_id"), // Links to user_sessions for complete funnel tracking
  
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
});

export const salesReps = pgTable("sales_reps", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  email: text("email").notNull(),
  phone: text("phone").notNull(),
  workingDays: text("working_days").array().notNull(), // ["Monday", "Tuesday"]
  shiftStart: text("shift_start").notNull(), // "10:00"
  shiftEnd: text("shift_end").notNull(), // "18:00"
  isActive: boolean("is_active").notNull().default(true),
});

export const postalCodes = pgTable("postal_codes", {
  id: serial("id").primaryKey(),
  code: text("code").notNull().unique(),
  city: text("city").notNull(),
  state: text("state").notNull(),
  isActive: boolean("is_active").notNull().default(true),
});

export const whatsappOptIns = pgTable("whatsapp_opt_ins", {
  id: serial("id").primaryKey(),
  phoneNumber: text("phone_number").notNull().unique(),
  optedInAt: timestamp("opted_in_at").defaultNow(),
  ipAddress: text("ip_address"),
  userAgent: text("user_agent"),
  source: text("source").default("booking_flow"), // booking_flow, admin, etc.
  isActive: boolean("is_active").default(true),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

export const settings = pgTable("settings", {
  id: serial("id").primaryKey(),
  emailTemplate: text("email_template").notNull().default(`Hi {{name}},

Thank you for booking the "Perfume Trial at Home" with Kult.  
Your appointment is confirmed for {{date}} at {{time}}.

We'll be arriving at:  
{{address}}

– Stay fragrant,  
Team Kult 💜`),
  smsTemplate: text("sms_template").notNull().default("Hi {{name}}, your Perfume Trial is confirmed for {{date}} at {{time}}. See you soon! 💜 – Team Kult"),
  
  // Confirmation templates
  confirmationEmailTemplate: text("confirmation_email_template").notNull().default(`Hi {{name}},

Thank you for booking the "Perfume Trial at Home" with Kult.  
Your appointment is confirmed for {{date}} at {{time}}.

We'll be arriving at:  
{{address}}

– Stay fragrant,  
Team Kult 💜`),
  confirmationSmsTemplate: text("confirmation_sms_template").notNull().default("Hi {{name}}, your Perfume Trial is confirmed for {{date}} at {{time}}. See you soon! 💜 – Team Kult"),
  
  // Cancellation templates
  cancellationEmailTemplate: text("cancellation_email_template").notNull().default(`Hi {{name}},

Your perfume trial for {{date}} at {{time}} has been cancelled.

You can book a new appointment anytime on our website.

– Stay fragrant,  
Team Kult 💜`),
  cancellationSmsTemplate: text("cancellation_sms_template").notNull().default("Hi {{name}}, your Perfume Trial for {{date}} at {{time}} has been cancelled. Book again anytime! 💜 – Team Kult"),
  
  // Reschedule templates
  rescheduleEmailTemplate: text("reschedule_email_template").notNull().default(`Hi {{name}},

Your perfume trial has been rescheduled to {{date}} at {{time}}.

We'll be arriving at:  
{{address}}

– Stay fragrant,  
Team Kult 💜`),
  rescheduleSmsTemplate: text("reschedule_sms_template").notNull().default("Hi {{name}}, your Perfume Trial has been rescheduled to {{date}} at {{time}}. See you then! 💜 – Team Kult"),
  
  // Reminder templates
  reminderEmailTemplate: text("reminder_email_template").notNull().default(`Hi {{name}},

Reminder: Your perfume trial is scheduled for {{date}} at {{time}}.

We'll be arriving at:  
{{address}}

Looking forward to seeing you!

– Stay fragrant,  
Team Kult 💜`),
  reminderSmsTemplate: text("reminder_sms_template").notNull().default("Hi {{name}}, reminder: Your Perfume Trial is scheduled for {{date}} at {{time}}. See you then! 💜 – Team Kult"),
  
  // Notification timing settings
  remindersEnabled: boolean("reminders_enabled").notNull().default(true),
  firstReminderEnabled: boolean("first_reminder_enabled").notNull().default(true),
  firstReminderHours: integer("first_reminder_hours").notNull().default(24),
  firstReminderEmail: boolean("first_reminder_email").notNull().default(true),
  firstReminderSms: boolean("first_reminder_sms").notNull().default(true),
  
  secondReminderEnabled: boolean("second_reminder_enabled").notNull().default(true),
  secondReminderHours: integer("second_reminder_hours").notNull().default(12),
  secondReminderEmail: boolean("second_reminder_email").notNull().default(true),
  secondReminderSms: boolean("second_reminder_sms").notNull().default(false),
  
  finalReminderEnabled: boolean("final_reminder_enabled").notNull().default(true),
  finalReminderHours: integer("final_reminder_hours").notNull().default(3),
  finalReminderEmail: boolean("final_reminder_email").notNull().default(false),
  finalReminderSms: boolean("final_reminder_sms").notNull().default(true),
  maxKits: integer("max_kits").notNull().default(20),
  
  // Theme Configuration
  backgroundGradientStart: text("background_gradient_start").notNull().default("#D4B9FC"),
  backgroundGradientEnd: text("background_gradient_end").notNull().default("#AD8FF7"),
  backgroundGradientDirection: text("background_gradient_direction").notNull().default("135deg"),
  
  buttonInactiveColor: text("button_inactive_color").notNull().default("rgb(192, 161, 240)"),
  buttonActiveColor: text("button_active_color").notNull().default("rgb(170, 138, 219)"),
  buttonDisabledColor: text("button_disabled_color").notNull().default("#E5E7EB"),
  buttonTextColor: text("button_text_color").notNull().default("#FFFFFF"),
  
  primaryTextColor: text("primary_text_color").notNull().default("#1F2937"),
  secondaryTextColor: text("secondary_text_color").notNull().default("#6B7280"),
  headingColor: text("heading_color").notNull().default("#111827"),
  accentTextColor: text("accent_text_color").notNull().default("#8B5CF6"),
  
  inputBorderColor: text("input_border_color").notNull().default("#D1D5DB"),
  inputFocusColor: text("input_focus_color").notNull().default("#D4B9FC"),
  inputBackgroundColor: text("input_background_color").notNull().default("#FFFFFF"),
  
  primaryBrandColor: text("primary_brand_color").notNull().default("#D4B9FC"),
  secondaryBrandColor: text("secondary_brand_color").notNull().default("#AD8FF7"),
  
  successColor: text("success_color").notNull().default("#10B981"),
  errorColor: text("error_color").notNull().default("#EF4444"),
  warningColor: text("warning_color").notNull().default("#F59E0B"),
  infoColor: text("info_color").notNull().default("#3B82F6"),
  // Global booking rules
  appointmentDuration: integer("appointment_duration").notNull().default(90), // minutes
  gapBetweenAppointments: integer("gap_between_appointments").notNull().default(60), // minutes
  shiftStartBuffer: integer("shift_start_buffer").notNull().default(90), // minutes
  timeSlotInterval: integer("time_slot_interval").notNull().default(30), // minutes
  maxBookingDaysAhead: integer("max_booking_days_ahead").notNull().default(7), // days
  minAdvanceHours: integer("min_advance_hours").notNull().default(1), // minimum hours before booking
  maxBookingsPerRep: integer("max_bookings_per_rep").notNull().default(8), // per day
  workingDaysStart: text("working_days_start").notNull().default("Monday"), // start of working week
  workingDaysEnd: text("working_days_end").notNull().default("Sunday"), // end of working week
  defaultShiftStart: text("default_shift_start").notNull().default("10:00"), // 24h format
  defaultShiftEnd: text("default_shift_end").notNull().default("20:00"), // 24h format
  shiftEndBuffer: integer("shift_end_buffer").notNull().default(30), // minutes before shift end
  rescheduleHoursLimit: integer("reschedule_hours_limit").notNull().default(0), // 0 = no limit
  cancelHoursLimit: integer("cancel_hours_limit").notNull().default(0), // 0 = no limit
  
  // Webhook Integration
  webhookUrl: text("webhook_url").notNull().default("https://hooks.zapier.com/hooks/catch/18959219/ub8lfib/")
});

export const blockedTimeSlots = pgTable("blocked_time_slots", {
  id: serial("id").primaryKey(),
  date: timestamp("date").notNull(),
  startTime: text("start_time").notNull(), // "09:00"
  endTime: text("end_time").notNull(), // "12:00"
  reason: text("reason").notNull().default("Blocked"),
  createdBy: text("created_by").notNull(),
  selectedReps: text("selected_reps"), // JSON array of sales rep names, null means all reps
  createdAt: timestamp("created_at").notNull().defaultNow(),
});

// Comprehensive booking history table
export const bookingHistory = pgTable("booking_history", {
  id: serial("id").primaryKey(),
  bookingId: integer("booking_id").references(() => bookings.id),
  phoneNumber: text("phone_number").notNull(),
  action: text("action").notNull(), // 'created', 'rescheduled', 'cancelled', 'completed'
  actionBy: text("action_by").notNull(), // 'customer', 'admin', 'system'
  adminUserId: integer("admin_user_id").references(() => users.id), // null if customer action
  adminUserName: text("admin_user_name"), // admin name for accountability
  previousValues: text("previous_values"), // JSON of old booking data
  newValues: text("new_values"), // JSON of new booking data  
  reason: text("reason"), // cancellation/reschedule reason
  ipAddress: text("ip_address"),
  userAgent: text("user_agent"),
  timestamp: timestamp("timestamp").notNull().defaultNow(),
});

// Event tracking for marketing analytics
export const eventLogs = pgTable("event_logs", {
  id: serial("id").primaryKey(),
  sessionId: text("session_id").notNull(),
  userId: text("user_id"), // Can be null for anonymous users
  eventType: text("event_type").notNull(), // page_view, postal_code_entered, date_selected, etc.
  eventData: text("event_data"), // JSON string with event details
  userAgent: text("user_agent"),
  ipAddress: text("ip_address"),
  referrer: text("referrer"),
  timestamp: timestamp("timestamp").notNull().defaultNow(),
});

// User sessions for drop-off analysis
export const userSessions = pgTable("user_sessions", {
  id: serial("id").primaryKey(),
  sessionId: text("session_id").notNull().unique(),
  firstVisit: timestamp("first_visit").notNull().defaultNow(),
  lastActivity: timestamp("last_activity").notNull().defaultNow(),
  currentStep: text("current_step").notNull().default("landing"), // landing, postal_code, date_time, contact_info, confirmation
  completedSteps: text("completed_steps").array().notNull().default("{}"), // Array of completed steps
  finalOutcome: text("final_outcome"), // completed, abandoned, cancelled
  bookingId: integer("booking_id"), // Reference to completed booking if any
  totalEvents: integer("total_events").notNull().default(0),
  timeSpent: integer("time_spent").notNull().default(0), // Total time in seconds
});

// Postal Code Analytics Tables
export const postalCodeAttempts = pgTable("postal_code_attempts", {
  id: serial("id").primaryKey(),
  sessionId: text("session_id").notNull(),
  postalCode: text("postal_code").notNull(),
  timestamp: timestamp("timestamp").notNull().defaultNow(),
  entryPage: text("entry_page").notNull(), // "homepage" or "booking_form"
  isValid: boolean("is_valid").notNull(),
  matchedServiceArea: text("matched_service_area"), // City name if valid
  finalCode: text("final_code"), // If user corrected it later
  bookingId: integer("booking_id").references(() => bookings.id), // If session led to booking
  ipAddress: text("ip_address"),
  userAgent: text("user_agent"),
});

export const serviceAreas = pgTable("service_areas", {
  id: serial("id").primaryKey(),
  postalCode: text("postal_code").notNull().unique(),
  city: text("city").notNull(),
  state: text("state").notNull(),
  addedBy: text("added_by").notNull(), // Admin username
  addedOn: timestamp("added_on").notNull().defaultNow(),
  activeStatus: boolean("active_status").notNull().default(true),
  lastUpdated: timestamp("last_updated").notNull().defaultNow(),
});

// Customer Support Dashboard Tables
export const customerInteractions = pgTable("customer_interactions", {
  id: serial("id").primaryKey(),
  bookingId: integer("booking_id").references(() => bookings.id),
  customerPhone: text("customer_phone").notNull(),
  interactionType: text("interaction_type").notNull(), // call, whatsapp, email, in-person
  direction: text("direction").notNull(), // inbound, outbound
  agentName: text("agent_name"),
  duration: integer("duration"), // in seconds for calls
  outcome: text("outcome"), // answered, no-answer, busy, voicemail, completed, escalated
  notes: text("notes"),
  followUpRequired: boolean("follow_up_required").default(false),
  followUpDate: timestamp("follow_up_date"),
  createdAt: timestamp("created_at").notNull().defaultNow(),
});

export const supportTickets = pgTable("support_tickets", {
  id: serial("id").primaryKey(),
  ticketId: text("ticket_id").unique().notNull(),
  bookingId: integer("booking_id").references(() => bookings.id),
  customerName: text("customer_name").notNull(),
  customerPhone: text("customer_phone").notNull(),
  issueCategory: text("issue_category").notNull(), // product, scheduling, payment, delivery, general
  priority: text("priority").notNull().default("medium"), // low, medium, high, urgent
  status: text("status").notNull().default("open"), // open, in-progress, resolved, closed
  description: text("description").notNull(),
  resolution: text("resolution"),
  assignedAgent: text("assigned_agent"),
  estimatedResolutionTime: timestamp("estimated_resolution_time"),
  actualResolutionTime: timestamp("actual_resolution_time"),
  customerSatisfactionScore: integer("customer_satisfaction_score"), // 1-5
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
});

export const businessMetrics = pgTable("business_metrics", {
  id: serial("id").primaryKey(),
  date: timestamp("date").notNull(),
  totalBookings: integer("total_bookings").default(0),
  completedAppointments: integer("completed_appointments").default(0),
  totalRevenue: integer("total_revenue").default(0), // in paise
  averageOrderValue: integer("average_order_value").default(0), // in paise
  conversionRate: integer("conversion_rate").default(0), // percentage * 100 (50.5% = 5050)
  customerSatisfactionAvg: integer("customer_satisfaction_avg").default(0), // rating * 100 (4.5 = 450)
  supportTicketsCreated: integer("support_tickets_created").default(0),
  supportTicketsResolved: integer("support_tickets_resolved").default(0),
  createdAt: timestamp("created_at").notNull().defaultNow(),
});

export const bookingRelations = relations(bookings, ({ one }) => ({
  rep: one(salesReps, {
    fields: [bookings.repAssigned],
    references: [salesReps.name],
  }),
}));

export const insertUserSchema = createInsertSchema(users).omit({
  id: true,
  createdAt: true,
});

export const insertAdminSessionSchema = createInsertSchema(adminSessions).omit({
  id: true,
  loginTime: true,
  isActive: true,
});

export const insertBookingSchema = createInsertSchema(bookings).omit({
  id: true,
  createdAt: true,
  service: true,
  status: true,
  secureToken: true,
  utmSource: true,
  utmMedium: true,
  utmCampaign: true,
  utmContent: true,
  utmTerm: true,
  trafficSource: true,
}).extend({
  date: z.string().transform((str) => new Date(str)),
});

export const insertSalesRepSchema = createInsertSchema(salesReps).omit({
  id: true,
  isActive: true,
});

export const insertPostalCodeSchema = createInsertSchema(postalCodes).omit({
  id: true,
  isActive: true,
});

export const insertWhatsappOptInSchema = createInsertSchema(whatsappOptIns).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const insertSettingsSchema = createInsertSchema(settings).omit({
  id: true,
});

export const insertEventLogSchema = createInsertSchema(eventLogs).omit({
  id: true,
  timestamp: true,
});

export const insertUserSessionSchema = createInsertSchema(userSessions).omit({
  id: true,
  firstVisit: true,
  lastActivity: true,
});

export const insertBlockedTimeSlotSchema = createInsertSchema(blockedTimeSlots).omit({
  id: true,
  createdAt: true,
});

export const insertBookingHistorySchema = createInsertSchema(bookingHistory).omit({
  id: true,
  timestamp: true,
});

export const insertCustomerInteractionSchema = createInsertSchema(customerInteractions).omit({
  id: true,
  createdAt: true,
});

export const insertSupportTicketSchema = createInsertSchema(supportTickets).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const insertBusinessMetricsSchema = createInsertSchema(businessMetrics).omit({
  id: true,
  createdAt: true,
});

export type User = typeof users.$inferSelect;
export type InsertUser = z.infer<typeof insertUserSchema>;
export type Booking = typeof bookings.$inferSelect;
export type InsertBooking = z.infer<typeof insertBookingSchema>;
export type SalesRep = typeof salesReps.$inferSelect;
export type InsertSalesRep = z.infer<typeof insertSalesRepSchema>;
export type PostalCode = typeof postalCodes.$inferSelect;
export type InsertPostalCode = z.infer<typeof insertPostalCodeSchema>;
export type WhatsappOptIn = typeof whatsappOptIns.$inferSelect;
export type InsertWhatsappOptIn = z.infer<typeof insertWhatsappOptInSchema>;
export type Settings = typeof settings.$inferSelect;
export type InsertSettings = z.infer<typeof insertSettingsSchema>;
export type EventLog = typeof eventLogs.$inferSelect;
export type InsertEventLog = z.infer<typeof insertEventLogSchema>;
export type UserSession = typeof userSessions.$inferSelect;
export type InsertUserSession = z.infer<typeof insertUserSessionSchema>;
export type BlockedTimeSlot = typeof blockedTimeSlots.$inferSelect;
export type InsertBlockedTimeSlot = z.infer<typeof insertBlockedTimeSlotSchema>;
export type BookingHistory = typeof bookingHistory.$inferSelect;
export type InsertBookingHistory = z.infer<typeof insertBookingHistorySchema>;
export type CustomerInteraction = typeof customerInteractions.$inferSelect;
export type InsertCustomerInteraction = z.infer<typeof insertCustomerInteractionSchema>;
export type SupportTicket = typeof supportTickets.$inferSelect;
export type InsertSupportTicket = z.infer<typeof insertSupportTicketSchema>;
export type BusinessMetrics = typeof businessMetrics.$inferSelect;
export type InsertBusinessMetrics = z.infer<typeof insertBusinessMetricsSchema>;

// Long-term user tracking for marketing attribution
export const userTracking = pgTable("user_tracking", {
  id: serial("id").primaryKey(),
  userId: varchar("user_id").unique().notNull(), // Persistent user ID from cookie
  deviceFingerprint: varchar("device_fingerprint").notNull(),
  firstVisit: timestamp("first_visit").notNull(),
  lastVisit: timestamp("last_visit").notNull(),
  visitCount: integer("visit_count").default(1).notNull(),
  
  // Attribution data (first-touch)
  firstUtmSource: varchar("first_utm_source"),
  firstUtmMedium: varchar("first_utm_medium"),
  firstUtmCampaign: varchar("first_utm_campaign"),
  firstUtmTerm: varchar("first_utm_term"),
  firstUtmContent: varchar("first_utm_content"),
  firstReferrer: text("first_referrer"),
  
  // Latest attribution data (last-touch)
  lastUtmSource: varchar("last_utm_source"),
  lastUtmMedium: varchar("last_utm_medium"),
  lastUtmCampaign: varchar("last_utm_campaign"),
  lastUtmTerm: varchar("last_utm_term"),
  lastUtmContent: varchar("last_utm_content"),
  lastReferrer: text("last_referrer"),
  
  // Conversion tracking
  hasBooked: boolean("has_booked").default(false),
  totalBookings: integer("total_bookings").default(0),
  totalValue: integer("total_value").default(0), // in paise
  
  // Engagement metrics
  totalPageViews: integer("total_page_views").default(0),
  totalSessionDuration: integer("total_session_duration").default(0), // in milliseconds
  
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

// User session tracking for detailed journey analysis
export const longTermUserSessions = pgTable("long_term_user_sessions", {
  id: serial("id").primaryKey(),
  userId: varchar("user_id").references(() => userTracking.userId),
  sessionId: varchar("session_id").unique().notNull(),
  deviceFingerprint: varchar("device_fingerprint"),
  
  // Session attribution
  utmSource: varchar("utm_source"),
  utmMedium: varchar("utm_medium"),
  utmCampaign: varchar("utm_campaign"),
  utmTerm: varchar("utm_term"),
  utmContent: varchar("utm_content"),
  referrer: text("referrer"),
  
  // Session metrics
  pageViews: integer("page_views").default(0),
  sessionDuration: integer("session_duration").default(0), // in milliseconds
  bounced: boolean("bounced").default(true), // false if more than 1 page or >30s
  converted: boolean("converted").default(false),
  
  // Funnel progression
  completedSteps: jsonb("completed_steps").default('[]'), // Array of completed funnel steps
  funnelProgress: integer("funnel_progress").default(0), // 0-8 (booking funnel steps)
  
  startedAt: timestamp("started_at").defaultNow(),
  endedAt: timestamp("ended_at"),
  updatedAt: timestamp("updated_at").defaultNow(),
});

// Insert schemas for user tracking
export const insertUserTrackingSchema = createInsertSchema(userTracking).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const insertLongTermUserSessionSchema = createInsertSchema(longTermUserSessions).omit({
  id: true,
  startedAt: true,
  updatedAt: true,
});

// Insert schemas for postal code analytics
export const insertPostalCodeAttemptSchema = createInsertSchema(postalCodeAttempts).omit({
  id: true,
  timestamp: true,
});

export const insertServiceAreaSchema = createInsertSchema(serviceAreas).omit({
  id: true,
  addedOn: true,
  lastUpdated: true,
});

// Types for user tracking
export type UserTracking = typeof userTracking.$inferSelect;
export type InsertUserTracking = z.infer<typeof insertUserTrackingSchema>;
export type LongTermUserSession = typeof longTermUserSessions.$inferSelect;
export type InsertLongTermUserSession = z.infer<typeof insertLongTermUserSessionSchema>;

// Types for postal code analytics
export type PostalCodeAttempt = typeof postalCodeAttempts.$inferSelect;
export type InsertPostalCodeAttempt = z.infer<typeof insertPostalCodeAttemptSchema>;
export type ServiceArea = typeof serviceAreas.$inferSelect;
export type InsertServiceArea = z.infer<typeof insertServiceAreaSchema>;
