import { format as dateFnsFormat } from "date-fns";

/**
 * Centralized date and time formatting utilities
 * This ensures consistent formatting across the entire application
 */

// Standard date formats
export const DATE_FORMATS = {
  // Display formats for users
  DISPLAY_DATE: "EEEE, MMMM d, yyyy", // "Friday, July 4, 2025"
  SHORT_DATE: "MMM d, yyyy", // "Jul 4, 2025"
  COMPACT_DATE: "MMM d", // "Jul 4"
  
  // Database/API formats
  API_DATE: "yyyy-MM-dd", // "2025-07-04" - ISO date format for APIs
  
  // Time formats
  TIME_12H: "h:mm a", // "2:30 PM"
  TIME_24H: "HH:mm", // "14:30"
  
  // Combined date-time formats
  DATETIME_DISPLAY: "PPp", // "Jul 4, 2025, 2:30 PM"
  DATETIME_COMPACT: "MMM d, h:mm a", // "Jul 4, 2:30 PM"
  DATETIME_DETAILED: "EEEE, MMMM d, yyyy 'at' h:mm a", // "Friday, July 4, 2025 at 2:30 PM"
  
  // Technical formats
  TIMESTAMP: "MMM dd, HH:mm:ss", // "Jul 04, 14:30:15"
  LOG_FORMAT: "MMM d, h:mm:ss a", // "Jul 4, 2:30:15 PM"
} as const;

/**
 * Format a date using standardized formats
 */
export function formatDate(date: Date | string, formatType: keyof typeof DATE_FORMATS): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return dateFnsFormat(dateObj, DATE_FORMATS[formatType]);
}

/**
 * Convert date to API format (YYYY-MM-DD)
 */
export function toApiDate(date: Date | string): string {
  return formatDate(date, 'API_DATE');
}

/**
 * Convert date to display format for users
 */
export function toDisplayDate(date: Date | string): string {
  return formatDate(date, 'DISPLAY_DATE');
}

/**
 * Convert date to short format
 */
export function toShortDate(date: Date | string): string {
  return formatDate(date, 'SHORT_DATE');
}

/**
 * Convert time to 12-hour format
 */
export function toTime12H(date: Date | string): string {
  return formatDate(date, 'TIME_12H');
}

/**
 * Convert time to 24-hour format
 */
export function toTime24H(date: Date | string): string {
  return formatDate(date, 'TIME_24H');
}

/**
 * Convert to combined date-time display
 */
export function toDateTimeDisplay(date: Date | string): string {
  return formatDate(date, 'DATETIME_DISPLAY');
}

/**
 * Convert to compact date-time format
 */
export function toDateTimeCompact(date: Date | string): string {
  return formatDate(date, 'DATETIME_COMPACT');
}

/**
 * Convert to detailed date-time format
 */
export function toDateTimeDetailed(date: Date | string): string {
  return formatDate(date, 'DATETIME_DETAILED');
}

/**
 * Standardized date parsing for consistent handling
 */
export function parseDate(dateString: string): Date {
  return new Date(dateString);
}

/**
 * Get current date in API format
 */
export function getCurrentApiDate(): string {
  return toApiDate(new Date());
}

/**
 * Check if a date string is in valid API format (YYYY-MM-DD)
 */
export function isValidApiDate(dateString: string): boolean {
  const regex = /^\d{4}-\d{2}-\d{2}$/;
  if (!regex.test(dateString)) return false;
  
  const date = new Date(dateString);
  return date instanceof Date && !isNaN(date.getTime()) && toApiDate(date) === dateString;
}