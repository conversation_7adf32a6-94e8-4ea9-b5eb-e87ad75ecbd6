# Kult Perfume Trial Booking System

## Overview
This is a full-stack web application for <PERSON><PERSON>'s perfume trial booking system, enabling customers to book at-home perfume trials and providing comprehensive admin management. The system aims to streamline the booking process, manage staff schedules, and provide detailed analytics for business insights. It supports core functionalities like postal code validation, dynamic date/time selection, customer information collection, and automated notifications.

## User Preferences
Preferred communication style: Simple, everyday language.

## System Architecture

### Frontend
- **Framework**: React 18 with TypeScript
- **Build Tool**: Vite
- **Routing**: Wouter
- **State Management**: TanStack React Query
- **UI Framework**: Custom components using Radix UI and styled with Tailwind CSS
- **Form Handling**: React Hook Form with Zod validation
- **Styling**: Tailwind CSS with CSS variables for theming, featuring a purple gradient background and two-state button styling.
- **UI/UX Decisions**:
    - Consistent purple gradient branding across booking and modification pages.
    - Simplified OTP verification flow with a single smart button.
    - Always-visible reschedule/cancel buttons on confirmation page with time restriction indicators.
    - Auto-advance from time selection to customer details.
    - Resend OTP functionality with countdown timer.
    - Customer support footer on all frontend booking pages.
    - Service area disclosure with visual pill-shaped design.
    - Mobile-friendly popup positioning and consistent spacing.

### Backend
- **Framework**: Express.js with TypeScript
- **Runtime**: Node.js 20
- **API Design**: RESTful API with JSON responses
- **Database ORM**: Drizzle ORM
- **Session Management**: Express sessions with PostgreSQL store
- **Notification Service**: Twilio WhatsApp Business API for all transactional messages.
- **Business Rules Management**: Configurable rules for appointment duration, gaps, buffer times, time slot intervals, advance booking limits, and sales rep availability. Automatic conflict detection and reassignment. Round-robin logic for sales rep assignment.
- **Authentication**: Simple username/password for admin users, OTP-based phone verification for customers. Role-based authentication (Admin, Support, Marketing, Sales).
- **Security**: Secure, non-sequential booking tokens for modification links, bcrypt password hashing.

### Database
- **Primary Database**: PostgreSQL 16 (Neon serverless)
- **Schema Management**: Drizzle Kit
- **Core Tables**: `users`, `bookings`, `sales_reps`, `postal_codes`, `settings`.
- **New Tables/Columns**: `secureToken` for bookings, `longTermUserSessions` for detailed user tracking, `sheets_webhook_url` for Google Sheets integration.
- **Data Preservation**: Cancelled bookings are preserved with a status field instead of deletion.

### Core Features
- **Booking Flow**: Postal code validation, dynamic date/time selection, customer information, confirmation with WhatsApp notifications.
- **Admin Dashboard**:
    - **Booking Management**: View, update, cancel, and complete appointments. Manual reminder triggers. Status-based color coding for calendar.
    - **Calendar View**: Day/week views, interactive appointment booking, visual booking blocks, time blocking functionality.
    - **Sales Rep Management**: Scheduling, availability, email/phone fields, CRUD operations.
    - **Settings Management**: Notification timing, business rules.
    - **Postal Code Management**: Service area administration with search and bulk upload.
    - **User Management**: Role-based access control, password management.
    - **Analytics**:
        - **Marketing Analytics**: Comprehensive dashboard with conversion funnel analysis, drop-off analysis, user session tracking, real-time metrics, UTM tracking, and ad campaign performance.
        - **GA4 Integration**: Real-time data from Google Analytics 4 API for overview, campaigns, devices, geography, pages, conversions, and channels.
        - **Postal Code Analytics**: Detailed insights into valid/invalid postal code attempts.
- **Notifications**: Exclusively WhatsApp Business API for all booking confirmations, cancellations, reschedules, reminders, and customer service follow-ups. Uses Meta-approved templates.

## External Dependencies
- **Database**: Neon PostgreSQL serverless
- **UI Components**: Radix UI
- **Validation**: Zod
- **Date Handling**: date-fns
- **Notifications**: Twilio WhatsApp Business API (for OTP and all other messaging)
- **Analytics**: Google Analytics 4 (GA4), Meta Pixel, Zapier (for Google Sheets integration)
- **SMS (deprecated)**: Message91 (previously used for OTP and transactional SMS)
- **Email (deprecated)**: SendGrid (previously used for transactional emails)