import { db } from './server/storage.js';
import fs from 'fs';

async function executeRestoration() {
  try {
    console.log('Starting bulk restoration...');
    
    // Read the SQL file
    const sqlContent = fs.readFileSync('./restore_missing_bookings.sql', 'utf8');
    const insertStatements = sqlContent.split(');').filter(stmt => stmt.trim().length > 0);
    
    console.log(`Found ${insertStatements.length} INSERT statements to execute`);
    
    let successCount = 0;
    let errorCount = 0;
    
    for (let i = 0; i < insertStatements.length; i++) {
      const statement = insertStatements[i].trim() + ');';
      
      try {
        await db.execute(statement);
        successCount++;
        
        if (successCount % 10 === 0) {
          console.log(`Progress: ${successCount}/${insertStatements.length} bookings restored`);
        }
      } catch (error) {
        console.error(`Error restoring booking ${i + 1}:`, error.message);
        errorCount++;
      }
    }
    
    console.log('\n=== Restoration Complete ===');
    console.log(`Successfully restored: ${successCount} bookings`);
    console.log(`Errors encountered: ${errorCount}`);
    
    // Verify final count
    const result = await db.execute('SELECT COUNT(*) as total FROM bookings');
    console.log(`Total bookings in database: ${result[0].total}`);
    
    const maxId = await db.execute('SELECT MAX(id) as max_id FROM bookings');
    console.log(`Maximum booking ID: ${maxId[0].max_id}`);
    
  } catch (error) {
    console.error('Fatal error during restoration:', error);
  }
}

executeRestoration();