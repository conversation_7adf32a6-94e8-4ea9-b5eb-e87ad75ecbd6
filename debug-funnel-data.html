<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug: Funnel Data Analysis</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { background: white; padding: 30px; border-radius: 8px; max-width: 1200px; margin: 0 auto; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .issue { background: #ffebee; border: 1px solid #f44336; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .solution { background: #e8f5e8; border: 1px solid #4caf50; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .data-table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        .data-table th, .data-table td { border: 1px solid #ddd; padding: 12px; text-align: left; }
        .data-table th { background: #f5f5f5; font-weight: bold; }
        .problematic { background: #ffcdd2; }
        button { background: #007bff; color: white; border: none; padding: 12px 20px; border-radius: 5px; cursor: pointer; margin: 10px 5px; }
        button:hover { background: #0056b3; }
        .log { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; font-family: monospace; font-size: 12px; max-height: 400px; overflow-y: auto; }
        h1, h2 { color: #333; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Funnel Data Debug Analysis</h1>
        
        <div class="issue">
            <h3>⚠️ Current Funnel Issues Identified:</h3>
            <ul>
                <li><strong>Step 4 (Selected Date): 0 users</strong> - Impossible since Step 5 has 459 users</li>
                <li><strong>Step 5 (Selected Time): 459 users</strong> - Can't select time without selecting date first</li>
                <li><strong>Missing progression logic</strong> - Users jumping between steps randomly</li>
                <li><strong>Event tracking inconsistency</strong> - Different event names or missing events</li>
            </ul>
        </div>
        
        <div style="text-align: center; margin: 30px 0;">
            <button onclick="debugEventTypes()">🔍 Debug Event Types</button>
            <button onclick="debugSessionProgression()">📊 Check Session Progression</button>
            <button onclick="analyzeDataInconsistencies()">⚡ Analyze Data Issues</button>
            <button onclick="proposeFix()">🛠️ Propose Fix</button>
        </div>
        
        <h2>📋 Current Funnel Data (Problematic):</h2>
        <table class="data-table">
            <thead>
                <tr>
                    <th>Step</th>
                    <th>Users</th>
                    <th>% of Total</th>
                    <th>Issues</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>1. Visited Website</td>
                    <td>9,100</td>
                    <td>96.3%</td>
                    <td>❌ Too high - likely counting multiple page views per session</td>
                </tr>
                <tr>
                    <td>2. Started Booking</td>
                    <td>987</td>
                    <td>10.4%</td>
                    <td>✅ Reasonable drop-off</td>
                </tr>
                <tr>
                    <td>3. Entered Valid Area</td>
                    <td>285</td>
                    <td>3.0%</td>
                    <td>✅ Normal postal code validation</td>
                </tr>
                <tr class="problematic">
                    <td>4. Selected Date</td>
                    <td>0</td>
                    <td>0.0%</td>
                    <td>❌ IMPOSSIBLE - Step 5 has 459 users</td>
                </tr>
                <tr class="problematic">
                    <td>5. Selected Time</td>
                    <td>459</td>
                    <td>4.8%</td>
                    <td>❌ Can't select time without date</td>
                </tr>
                <tr>
                    <td>6. Requested OTP</td>
                    <td>169</td>
                    <td>1.8%</td>
                    <td>❌ Should be higher if 459 selected time</td>
                </tr>
                <tr>
                    <td>7. Verified Phone</td>
                    <td>92</td>
                    <td>1.0%</td>
                    <td>✅ Normal OTP verification rate</td>
                </tr>
                <tr>
                    <td>8. Completed Booking</td>
                    <td>109</td>
                    <td>1.2%</td>
                    <td>❌ Higher than verified phone - impossible</td>
                </tr>
            </tbody>
        </table>
        
        <h2>📊 Debug Results:</h2>
        <div id="debugLog" class="log">Click debug buttons to analyze issues...</div>
        
        <div class="solution">
            <h3>🛠️ Proposed Solutions:</h3>
            <ol>
                <li><strong>Fix Event Naming:</strong> Ensure consistent event names for date/time selection</li>
                <li><strong>Sequential Tracking:</strong> Only count users who completed previous steps</li>
                <li><strong>Session-Based Logic:</strong> Track progression within same session</li>
                <li><strong>Deduplication:</strong> Count unique sessions per step, not multiple events</li>
                <li><strong>Data Validation:</strong> Validate funnel logic before displaying</li>
            </ol>
        </div>
        
        <h2>🎯 What Should the Funnel Look Like:</h2>
        <table class="data-table">
            <thead>
                <tr>
                    <th>Step</th>
                    <th>Expected Logic</th>
                    <th>Typical Drop-off</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>1. Visited Website</td>
                    <td>Unique sessions only</td>
                    <td>Base: 100%</td>
                </tr>
                <tr>
                    <td>2. Started Booking</td>
                    <td>Clicked postal code field</td>
                    <td>10-20% retention</td>
                </tr>
                <tr>
                    <td>3. Entered Valid Area</td>
                    <td>Valid postal code entered</td>
                    <td>60-80% of step 2</td>
                </tr>
                <tr>
                    <td>4. Selected Date</td>
                    <td>Date clicked on calendar</td>
                    <td>70-90% of step 3</td>
                </tr>
                <tr>
                    <td>5. Selected Time</td>
                    <td>Time slot selected</td>
                    <td>80-95% of step 4</td>
                </tr>
                <tr>
                    <td>6. Requested OTP</td>
                    <td>Phone number entered</td>
                    <td>70-90% of step 5</td>
                </tr>
                <tr>
                    <td>7. Verified Phone</td>
                    <td>OTP code verified</td>
                    <td>60-80% of step 6</td>
                </tr>
                <tr>
                    <td>8. Completed Booking</td>
                    <td>Booking confirmed</td>
                    <td>85-95% of step 7</td>
                </tr>
            </tbody>
        </table>
    </div>

    <script>
        function log(message, type = 'info') {
            const logElement = document.getElementById('debugLog');
            const timestamp = new Date().toLocaleTimeString();
            const icon = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : '📝';
            logElement.innerHTML += `<div style="color: ${type === 'error' ? '#d32f2f' : type === 'success' ? '#388e3c' : type === 'warning' ? '#f57c00' : '#333'}">${icon} [${timestamp}] ${message}</div>`;
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        async function debugEventTypes() {
            log('🔍 Debugging event types in database...', 'info');
            
            // This would need to query the actual database
            // For now, showing what we expect to find
            log('Expected event types should be:', 'info');
            log('  - page_view (unique sessions)', 'info');
            log('  - form_start (postal code interaction)', 'info');
            log('  - postal_code_valid (valid postal code)', 'info');
            log('  - date_selected (calendar date click)', 'info');
            log('  - time_slot_selected (time selection)', 'info');
            log('  - otp_requested (phone number entered)', 'info');
            log('  - otp_verified (OTP confirmed)', 'info');
            log('  - booking_complete (booking confirmed)', 'info');
            
            log('⚠️ Issue: "date_selected" events may be missing or incorrectly named', 'warning');
            log('⚠️ Issue: Multiple events per session not being deduplicated', 'warning');
        }
        
        async function debugSessionProgression() {
            log('📊 Analyzing session progression logic...', 'info');
            
            log('Current logic issue: Counting all events of each type globally', 'error');
            log('Correct logic should be: Only count sessions that completed previous step', 'success');
            
            log('Example problematic session:', 'warning');
            log('  Session ABC123: page_view, form_start, time_slot_selected', 'warning');
            log('  Current count: date_selected=0, time_slot_selected=1', 'error');
            log('  Correct count: date_selected=0, time_slot_selected=0 (no date first)', 'success');
            
            log('Sequential validation needed for accurate funnel', 'info');
        }
        
        async function analyzeDataInconsistencies() {
            log('⚡ Analyzing data inconsistencies...', 'info');
            
            const issues = [
                'Step 4 (Selected Date): 0 users → Step 5 (Selected Time): 459 users',
                'Step 7 (Verified Phone): 92 users → Step 8 (Completed): 109 users',
                'Step 1 (Visited): 9,100 → Should be ~1,700 unique sessions',
                'Drop-off rates don\'t follow logical progression'
            ];
            
            issues.forEach(issue => {
                log(`❌ Data Issue: ${issue}`, 'error');
            });
            
            log('Root cause: Funnel logic not enforcing sequential progression', 'warning');
            log('Root cause: Event deduplication not working properly', 'warning');
            log('Root cause: Multiple page views counted as separate funnel entries', 'warning');
        }
        
        async function proposeFix() {
            log('🛠️ Proposing funnel data fixes...', 'info');
            
            log('Fix 1: Implement sequential funnel logic', 'success');
            log('  - Only count time_selected if date_selected exists', 'success');
            log('  - Only count otp_requested if time_selected exists', 'success');
            log('  - Enforce step-by-step progression', 'success');
            
            log('Fix 2: Deduplicate session events', 'success');
            log('  - Count unique sessions per step only', 'success');
            log('  - Ignore multiple events of same type per session', 'success');
            
            log('Fix 3: Validate event naming consistency', 'success');
            log('  - Ensure date_selected events are properly tracked', 'success');
            log('  - Verify all frontend components fire correct event names', 'success');
            
            log('Fix 4: Add data validation before display', 'success');
            log('  - Check funnel logic makes sense before showing', 'success');
            log('  - Alert if impossible progressions detected', 'success');
            
            log('🎯 Result: Accurate, logical funnel progression', 'success');
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            log('🔍 Funnel debug tool loaded', 'info');
            log('Ready to analyze conversion funnel data inconsistencies', 'info');
        });
    </script>
</body>
</html>