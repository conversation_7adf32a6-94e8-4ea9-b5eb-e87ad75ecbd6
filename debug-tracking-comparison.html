<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug: Meta vs Dashboard Tracking Comparison</title>
    
    <!-- Facebook Pixel Code -->
    <script>
    !function(f,b,e,v,n,t,s)
    {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
    n.callMethod.apply(n,arguments):n.queue.push(arguments)};
    if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
    n.queue=[];t=b.createElement(e);t.async=!0;
    t.src=v;s=b.getElementsByTagName(e)[0];
    s.parentNode.insertBefore(t,s)}(window, document,'script',
    'https://connect.facebook.net/en_US/fbevents.js');
    
    fbq('init', '2478007082599449');
    fbq('track', 'PageView');
    </script>
    
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { background: white; padding: 30px; border-radius: 8px; max-width: 1000px; margin: 0 auto; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .comparison { display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin: 20px 0; }
        .section { padding: 20px; border: 2px solid #e0e0e0; border-radius: 8px; }
        .meta-section { border-color: #1877f2; background: #f0f8ff; }
        .dashboard-section { border-color: #28a745; background: #f0fff4; }
        .metric { background: white; padding: 15px; margin: 10px 0; border-radius: 5px; display: flex; justify-content: space-between; align-items: center; }
        .metric-value { font-size: 24px; font-weight: bold; color: #333; }
        .explanation { background: #fffbf0; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0; }
        button { background: #007bff; color: white; border: none; padding: 12px 20px; border-radius: 5px; cursor: pointer; margin: 10px 5px; }
        button:hover { background: #0056b3; }
        .log { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; font-family: monospace; font-size: 12px; max-height: 300px; overflow-y: auto; }
        h1 { color: #333; text-align: center; }
        h2 { color: #333; margin-bottom: 15px; }
        .highlight { background: #ffeb3b; padding: 2px 4px; border-radius: 3px; }
        .refresh-counter { background: #e3f2fd; border: 1px solid #2196f3; padding: 10px; border-radius: 5px; margin: 10px 0; text-align: center; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Meta vs Dashboard Tracking Comparison</h1>
        
        <div class="refresh-counter">
            <strong>Page Refresh Counter:</strong> <span id="refreshCount">1</span>
            <br><small>Each refresh fires a new Facebook PageView event, but our dashboard counts only 1 session</small>
        </div>
        
        <div class="explanation">
            <h3>📊 Why Meta Shows 15x More "Page Views":</h3>
            <ul>
                <li><strong>Meta Pixel:</strong> Fires PageView on every page load, refresh, or navigation</li>
                <li><strong>Our Dashboard:</strong> Counts unique user sessions (one per browser session)</li>
                <li><strong>User Behavior:</strong> Users refresh, navigate back/forward, reload pages multiple times</li>
                <li><strong>Bots & Crawlers:</strong> Meta captures all traffic including non-human visitors</li>
            </ul>
        </div>
        
        <div class="comparison">
            <div class="section meta-section">
                <h2>🔵 Meta Facebook Pixel</h2>
                <div class="metric">
                    <span>PageView Events Fired</span>
                    <span class="metric-value" id="metaPageViews">0</span>
                </div>
                <div class="metric">
                    <span>Tracking Method</span>
                    <span>Every page load</span>
                </div>
                <div class="metric">
                    <span>Deduplication</span>
                    <span>❌ None</span>
                </div>
                <div class="metric">
                    <span>Bot Filtering</span>
                    <span>❌ Minimal</span>
                </div>
            </div>
            
            <div class="section dashboard-section">
                <h2>🟢 Our Analytics Dashboard</h2>
                <div class="metric">
                    <span>Unique Sessions</span>
                    <span class="metric-value" id="dashboardSessions">Loading...</span>
                </div>
                <div class="metric">
                    <span>Tracking Method</span>
                    <span>Session-based</span>
                </div>
                <div class="metric">
                    <span>Deduplication</span>
                    <span>✅ By Session ID</span>
                </div>
                <div class="metric">
                    <span>Bot Filtering</span>
                    <span>✅ Advanced</span>
                </div>
            </div>
        </div>
        
        <div style="text-align: center; margin: 30px 0;">
            <button onclick="simulatePageRefresh()">🔄 Simulate Page Refresh</button>
            <button onclick="loadDashboardData()">📊 Refresh Dashboard Data</button>
            <button onclick="testUserBehavior()">👤 Test User Behavior</button>
        </div>
        
        <div class="explanation">
            <h3>📈 Typical User Behavior Pattern:</h3>
            <div id="behaviorDemo" style="display: none;">
                <p><strong>Single User Session:</strong></p>
                <ol id="behaviorSteps">
                    <!-- Will be populated by JavaScript -->
                </ol>
                <p><strong>Result:</strong> <span class="highlight">8-12 Facebook PageView events</span> vs <span class="highlight">1 Dashboard session</span></p>
            </div>
        </div>
        
        <h3>📋 Real-Time Event Log:</h3>
        <div id="eventLog" class="log">Loading tracking comparison...</div>
        
        <div class="explanation">
            <h3>🎯 Which Metric is More Accurate?</h3>
            <ul>
                <li><strong>For Conversion Tracking:</strong> Our dashboard sessions (unique user journeys)</li>
                <li><strong>For Ad Optimization:</strong> Meta PageViews (engagement intensity)</li>
                <li><strong>For Business KPIs:</strong> Dashboard sessions (actual potential customers)</li>
                <li><strong>For Meta Algorithm:</strong> PageViews help Meta understand user interest</li>
            </ul>
        </div>
    </div>

    <script>
        let metaPageViewCount = 0;
        let refreshCount = parseInt(localStorage.getItem('refreshCount') || '1');
        
        // Update refresh counter
        document.getElementById('refreshCount').textContent = refreshCount;
        localStorage.setItem('refreshCount', (refreshCount + 1).toString());
        
        function log(message, type = 'info') {
            const logElement = document.getElementById('eventLog');
            const timestamp = new Date().toLocaleTimeString();
            const icon = type === 'meta' ? '🔵' : type === 'dashboard' ? '🟢' : '📝';
            logElement.innerHTML += `<div style="color: ${type === 'meta' ? '#1877f2' : type === 'dashboard' ? '#28a745' : '#333'}">${icon} [${timestamp}] ${message}</div>`;
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        // Track Meta PageView events
        function trackMetaPageView() {
            metaPageViewCount++;
            document.getElementById('metaPageViews').textContent = metaPageViewCount;
            log(`Meta PageView event #${metaPageViewCount} fired`, 'meta');
            
            // Send to our backend for comparison
            fetch('/api/marketing/facebook-event', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    event_name: 'PageView',
                    event_time: Math.floor(Date.now() / 1000),
                    user_data: { country: 'IN' },
                    custom_data: { content_name: 'Debug Comparison Page' },
                    event_source_url: window.location.href,
                    action_source: 'website'
                })
            }).catch(e => log(`Meta API call failed: ${e.message}`, 'meta'));
        }
        
        // Load dashboard session data
        async function loadDashboardData() {
            try {
                log('Fetching dashboard session data...', 'dashboard');
                const response = await fetch('/api/analytics/executive-metrics');
                const data = await response.json();
                
                document.getElementById('dashboardSessions').textContent = data.totalSessions?.toLocaleString() || 'Error';
                log(`Dashboard shows ${data.totalSessions} total sessions`, 'dashboard');
                log(`Dashboard conversion rate: ${data.conversionRate}%`, 'dashboard');
                
                // Calculate the ratio
                if (data.totalSessions > 0) {
                    const ratio = (27600 / data.totalSessions).toFixed(1);
                    log(`📊 Meta vs Dashboard ratio: ${ratio}:1 (${27600} Meta PageViews ÷ ${data.totalSessions} Dashboard sessions)`, 'info');
                }
            } catch (error) {
                log(`Dashboard API error: ${error.message}`, 'dashboard');
                document.getElementById('dashboardSessions').textContent = 'Error';
            }
        }
        
        // Simulate page refresh behavior
        function simulatePageRefresh() {
            log('🔄 Simulating page refresh...', 'info');
            
            // Fire Facebook PageView (as it would on real refresh)
            if (window.fbq) {
                fbq('track', 'PageView', {
                    content_name: 'Simulated Page Refresh',
                    refresh_count: metaPageViewCount + 1
                });
            }
            
            trackMetaPageView();
            log('Page refresh simulation complete - Meta count increased, Dashboard session unchanged', 'info');
        }
        
        // Test typical user behavior
        function testUserBehavior() {
            const behaviorDemo = document.getElementById('behaviorDemo');
            const behaviorSteps = document.getElementById('behaviorSteps');
            
            behaviorDemo.style.display = 'block';
            
            const steps = [
                'User clicks Meta ad → PageView #1',
                'Page loads slowly, user refreshes → PageView #2',
                'User fills postal code, browser back button → PageView #3',
                'User returns to form → PageView #4',
                'User selects date → PageView #5',
                'User accidentally closes tab, reopens → PageView #6',
                'User selects time slot → PageView #7',
                'User fills phone number → PageView #8',
                'User completes booking → PageView #9'
            ];
            
            behaviorSteps.innerHTML = steps.map(step => `<li>${step}</li>`).join('');
            
            log('👤 Demonstrated typical user behavior: 9 PageViews = 1 Dashboard session', 'info');
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            log('🔍 Tracking comparison tool loaded', 'info');
            log(`Page refresh #${refreshCount} - Meta will fire new PageView, Dashboard keeps same session`, 'info');
            
            // Track initial PageView
            trackMetaPageView();
            
            // Load dashboard data
            loadDashboardData();
            
            log('💡 The 15x difference is normal user behavior: multiple page views per session', 'info');
        });
    </script>
</body>
</html>