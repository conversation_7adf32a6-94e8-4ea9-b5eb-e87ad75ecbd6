// Facebook Conversion Tracking Test Script
// Run this in browser console to test all Facebook events

console.log('🚀 Testing Facebook Conversion Funnel...');

// Test 1: Page View Tracking
console.log('1. Testing PageView event...');
if (typeof fbq !== 'undefined') {
  fbq('track', 'PageView', {
    content_name: 'Test Page View',
    content_category: 'Beauty'
  });
  console.log('✅ PageView event sent');
} else {
  console.log('❌ Facebook Pixel not loaded');
}

// Test 2: ViewContent (Postal Code Entry)
console.log('2. Testing ViewContent event...');
fbq('track', 'ViewContent', {
  content_name: 'Postal Code Entry',
  content_category: 'Beauty',
  postal_code: '110001',
  city: 'Delhi',
  state: 'Delhi'
});
console.log('✅ ViewContent event sent');

// Test 3: InitiateCheckout (Booking Started)
console.log('3. Testing InitiateCheckout event...');
fbq('track', 'InitiateCheckout', {
  content_name: 'Perfume Trial Booking Started',
  content_category: 'Beauty',
  postal_code: '110001',
  value: 0,
  currency: 'INR'
});
console.log('✅ InitiateCheckout event sent');

// Test 4: AddToCart (Date Selected)
console.log('4. Testing AddToCart event...');
fbq('track', 'AddToCart', {
  content_name: 'Date Selected',
  content_category: 'Beauty',
  appointment_date: '2025-07-01',
  postal_code: '110001'
});
console.log('✅ AddToCart event sent');

// Test 5: AddPaymentInfo (Time Selected)
console.log('5. Testing AddPaymentInfo event...');
fbq('track', 'AddPaymentInfo', {
  content_name: 'Time Slot Selected',
  content_category: 'Beauty',
  appointment_time: '14:00',
  appointment_date: '2025-07-01',
  postal_code: '110001'
});
console.log('✅ AddPaymentInfo event sent');

// Test 6: BeginCheckout (OTP Requested)
console.log('6. Testing BeginCheckout event...');
fbq('track', 'BeginCheckout', {
  content_name: 'OTP Requested',
  content_category: 'Beauty',
  phone_number: '+919999999999'
});
console.log('✅ BeginCheckout event sent');

// Test 7: CompleteRegistration (OTP Verified)
console.log('7. Testing CompleteRegistration event...');
fbq('track', 'CompleteRegistration', {
  content_name: 'OTP Verified',
  content_category: 'Beauty',
  phone_number: '+919999999999',
  registration_method: 'whatsapp_otp'
});
console.log('✅ CompleteRegistration event sent');

// Test 8: Purchase (Booking Completed)
console.log('8. Testing Purchase event...');
fbq('track', 'Purchase', {
  value: 0,
  currency: 'INR',
  content_type: 'product',
  content_ids: ['perfume_trial', 'test_booking_123'],
  content_name: 'Perfume Trial Booking Completed',
  content_category: 'Beauty',
  postal_code: '110001',
  city: 'Delhi',
  state: 'Delhi',
  appointment_date: '2025-07-01',
  appointment_time: '14:00',
  sales_rep: 'Test Rep',
  phone_number: '+919999999999'
});
console.log('✅ Purchase event sent');

// Test 9: Custom Events
console.log('9. Testing Custom Events...');

// Reschedule event
fbq('track', 'EditCart', {
  content_name: 'Booking Rescheduled',
  content_category: 'Beauty',
  content_ids: ['test_booking_123'],
  appointment_date: '2025-07-02',
  appointment_time: '15:00'
});
console.log('✅ Reschedule event sent');

// Cancel event
fbq('track', 'RemoveFromCart', {
  content_name: 'Booking Cancelled',
  content_category: 'Beauty',
  content_ids: ['test_booking_123'],
  cancellation_reason: 'user_requested'
});
console.log('✅ Cancel event sent');

console.log('🎉 All Facebook conversion events tested!');
console.log('');
console.log('📊 Check Facebook Events Manager to verify events are appearing:');
console.log('1. Go to Events Manager in Facebook Business');
console.log('2. Select your pixel (2478007082599449)');
console.log('3. Look for recent events in the last few minutes');
console.log('');
console.log('Expected events in order:');
console.log('PageView → ViewContent → InitiateCheckout → AddToCart → AddPaymentInfo → BeginCheckout → CompleteRegistration → Purchase');
console.log('');
console.log('🔄 To test the actual booking flow:');
console.log('1. Go to your website');
console.log('2. Enter a valid postal code (110001)');
console.log('3. Select date and time');
console.log('4. Enter contact details and verify OTP');
console.log('5. Complete booking');
console.log('6. Check Events Manager for real conversion data');

// Helper function to check if pixel is working
function checkPixelStatus() {
  if (typeof fbq === 'undefined') {
    console.log('❌ Facebook Pixel not loaded');
    return false;
  }
  
  console.log('✅ Facebook Pixel loaded successfully');
  console.log('📊 Pixel ID: 2478007082599449');
  
  // Check if events are being tracked
  const originalFbq = window.fbq;
  let eventCount = 0;
  
  window.fbq = function() {
    eventCount++;
    console.log(`📈 Event ${eventCount}: ${arguments[0]} - ${arguments[1]}`);
    return originalFbq.apply(this, arguments);
  };
  
  return true;
}

checkPixelStatus();