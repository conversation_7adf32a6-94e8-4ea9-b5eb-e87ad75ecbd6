# Google Sheets Integration via Zapier - Setup Guide

## Step 1: Create a Zapier Account
1. Go to [zapier.com](https://zapier.com)
2. Sign up for a free account (free plan supports 100 tasks/month)

## Step 2: Create a New Zap
1. Click "Create Zap" in your Zapier dashboard
2. Name your Zap: "Kult Booking to Google Sheets"

## Step 3: Set Up the Trigger (Webhook)
1. **Choose Trigger App**: Search and select "Webhooks by Zapier"
2. **Choose Trigger Event**: Select "Catch Hook"
3. **Set up Trigger**: 
   - Zapier will provide a webhook URL like: `https://hooks.zapier.com/hooks/catch/123456/abcdef/`
   - **Copy this URL** - you'll need it for your admin dashboard
4. **Test Trigger**: Skip for now (we'll test after setup)

## Step 4: Set Up the Action (Google Sheets)
1. **Choose Action App**: Search and select "Google Sheets"
2. **Choose Action Event**: Select "Create Spreadsheet Row"
3. **Connect Account**: Sign in to your Google account
4. **Set up Action**:
   - **Spreadsheet**: Choose existing or create new
   - **Worksheet**: Usually "Sheet1" 
   - **Map Fields**: Zapier will show you the booking data structure

## Step 5: Field Mapping (Important!)
Map these booking fields to your Google Sheets columns:

| Google Sheets Column | Zapier Field | Description |
|---------------------|--------------|-------------|
| Booking ID | `id` | Unique booking identifier |
| Customer Name | `name` | Customer full name |
| Phone Number | `phone` | Customer phone (+91 format) |
| Address | `address` | Full customer address |
| Postal Code | `postalCode` | Area postal code |
| Appointment Date | `date` | Booking date (YYYY-MM-DD) |
| Time Slot | `timeSlot` | Appointment time (e.g., "2:00 PM") |
| Status | `status` | confirmed/completed/cancelled |
| Service | `service` | Usually "Perfume Trial at Home" |
| Assigned Rep | `repAssigned` | Sales representative name |
| Created At | `createdAt` | When booking was made |
| Updated At | `updatedAt` | Last modification time |

## Step 6: Configure Your Admin Dashboard
1. Log into your admin dashboard
2. Go to Settings section
3. Find "Google Sheets Integration" card
4. Paste your Zapier webhook URL
5. (Optional) Add your Google Sheets URL for easy access
6. Click "Save Config"
7. Click "Test Webhook" to verify connection

## Step 7: Test the Integration
1. In Zapier, click "Test & Continue" 
2. In your admin dashboard, click "Sync All Bookings" 
3. Check your Google Sheet - you should see existing bookings appear
4. Create a test booking through your website to verify real-time sync

## Real-Time Updates Include:
- ✅ New booking creation
- ✅ Booking status changes (confirmed → completed → cancelled)
- ✅ Appointment rescheduling 
- ✅ Customer information updates
- ✅ Sales rep assignments
- ✅ Completion tracking with purchase amounts

## Data Sent to Google Sheets:
```json
{
  "id": "123",
  "name": "John Doe",
  "phone": "+************", 
  "address": "123 Main St, Delhi",
  "postalCode": "110001",
  "date": "2025-06-28",
  "timeSlot": "2:00 PM",
  "status": "confirmed",
  "service": "Perfume Trial at Home",
  "repAssigned": "Sales Rep Name",
  "createdAt": "2025-06-28T09:00:00Z",
  "updatedAt": "2025-06-28T09:00:00Z"
}
```

## Troubleshooting:
- If webhook test fails, check the URL is copied correctly
- Free Zapier accounts have a 100 task/month limit
- Upgrade to paid plan for unlimited bookings
- Check Zapier task history if data isn't appearing

## Cost:
- **Free Plan**: 100 bookings/month
- **Starter Plan**: $19.99/month for unlimited bookings
- Each booking update counts as 1 task