<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Facebook Events</title>
    
    <!-- Facebook Pixel Code -->
    <script>
    !function(f,b,e,v,n,t,s)
    {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
    n.callMethod.apply(n,arguments):n.queue.push(arguments)};
    if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
    n.queue=[];t=b.createElement(e);t.async=!0;
    t.src=v;s=b.getElementsByTagName(e)[0];
    s.parentNode.insertBefore(t,s)}(window, document,'script',
    'https://connect.facebook.net/en_US/fbevents.js');
    
    fbq('init', '2478007082599449');
    fbq('track', 'PageView');
    </script>
    <noscript><img height="1" width="1" style="display:none"
    src="https://www.facebook.com/tr?id=2478007082599449&ev=PageView&noscript=1"
    /></noscript>
    <!-- End Facebook Pixel Code -->
    
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .test-section { background: white; padding: 20px; margin: 20px 0; border-radius: 8px; border: 1px solid #ddd; }
        button { background: #1877f2; color: white; border: none; padding: 10px 20px; margin: 5px; border-radius: 5px; cursor: pointer; }
        button:hover { background: #166fe5; }
        .log { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; font-family: monospace; font-size: 12px; max-height: 300px; overflow-y: auto; border: 1px solid #e9ecef; }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #007bff; }
    </style>
</head>
<body>
    <h1>🔍 Facebook Pixel Events Debug Tool</h1>
    <p>This tool helps debug Facebook pixel events that should be firing in your booking system.</p>
    
    <div class="test-section">
        <h2>Pixel Status Check</h2>
        <button onclick="checkPixelStatus()">Check Facebook Pixel Status</button>
        <div id="pixelStatus" class="log">Click button to check pixel status...</div>
    </div>
    
    <div class="test-section">
        <h2>Standard Facebook Events Test</h2>
        <button onclick="testStandardEvents()">Test All Standard Events</button>
        <button onclick="testCustomEvents()">Test All Custom Events</button>
        <button onclick="clearLog()">Clear Log</button>
        <div id="eventLog" class="log">Event testing log will appear here...</div>
    </div>
    
    <div class="test-section">
        <h2>Browser Console Monitor</h2>
        <p>Check your browser's console (F12) for any Facebook pixel errors or warnings.</p>
        <button onclick="showConsoleInstructions()">Show Console Instructions</button>
        <div id="consoleInstructions" class="log" style="display: none;">
            <strong>Browser Console Instructions:</strong><br>
            1. Press F12 to open Developer Tools<br>
            2. Go to Console tab<br>
            3. Look for any errors related to 'fbq' or Facebook<br>
            4. Check Network tab for requests to facebook.com<br>
            5. Look for failed requests or 404 errors
        </div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logElement = document.getElementById('eventLog');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'success' ? 'success' : type === 'error' ? 'error' : 'info';
            logElement.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        function checkPixelStatus() {
            const statusDiv = document.getElementById('pixelStatus');
            let status = '';
            
            // Check if fbq function exists
            if (typeof window.fbq === 'undefined') {
                status += '<div class="error">❌ Facebook Pixel not loaded (fbq function missing)</div>';
            } else {
                status += '<div class="success">✅ Facebook Pixel loaded successfully</div>';
                
                // Check if pixel is initialized
                try {
                    window.fbq('track', 'PageView', {test: true});
                    status += '<div class="success">✅ PageView test event sent</div>';
                } catch (e) {
                    status += `<div class="error">❌ Error sending test event: ${e.message}</div>`;
                }
            }
            
            // Check for script element
            const fbScript = document.querySelector('script[src*="fbevents.js"]');
            if (fbScript) {
                status += '<div class="success">✅ Facebook script tag found</div>';
            } else {
                status += '<div class="error">❌ Facebook script tag missing</div>';
            }
            
            // Check network requests (if possible)
            status += '<div class="info">ℹ️ Check Network tab for facebook.com requests</div>';
            
            statusDiv.innerHTML = status;
        }
        
        function testStandardEvents() {
            log('🚀 Testing Standard Facebook Events...');
            
            // Test PageView
            try {
                fbq('track', 'PageView', {
                    content_name: 'Debug Test Page'
                });
                log('✅ PageView event fired', 'success');
            } catch (e) {
                log(`❌ PageView failed: ${e.message}`, 'error');
            }
            
            // Test ViewContent
            setTimeout(() => {
                try {
                    fbq('track', 'ViewContent', {
                        content_name: 'Postal Code Availability Check',
                        content_type: 'service',
                        content_ids: ['110001'],
                        postal_code: '110001'
                    });
                    log('✅ ViewContent event fired', 'success');
                } catch (e) {
                    log(`❌ ViewContent failed: ${e.message}`, 'error');
                }
            }, 500);
            
            // Test InitiateCheckout
            setTimeout(() => {
                try {
                    fbq('track', 'InitiateCheckout', {
                        content_name: 'Perfume Trial Booking Started',
                        content_type: 'appointment',
                        value: 0,
                        currency: 'INR'
                    });
                    log('✅ InitiateCheckout event fired', 'success');
                } catch (e) {
                    log(`❌ InitiateCheckout failed: ${e.message}`, 'error');
                }
            }, 1000);
            
            // Test AddToCart
            setTimeout(() => {
                try {
                    fbq('track', 'AddToCart', {
                        content_name: 'Date Selected for Appointment',
                        content_type: 'appointment',
                        content_ids: ['2025-07-05']
                    });
                    log('✅ AddToCart event fired', 'success');
                } catch (e) {
                    log(`❌ AddToCart failed: ${e.message}`, 'error');
                }
            }, 1500);
            
            // Test AddPaymentInfo
            setTimeout(() => {
                try {
                    fbq('track', 'AddPaymentInfo', {
                        content_name: 'Time Slot Selected',
                        content_type: 'appointment'
                    });
                    log('✅ AddPaymentInfo event fired', 'success');
                } catch (e) {
                    log(`❌ AddPaymentInfo failed: ${e.message}`, 'error');
                }
            }, 2000);
            
            // Test CompleteRegistration
            setTimeout(() => {
                try {
                    fbq('track', 'CompleteRegistration', {
                        content_name: 'Customer Registration',
                        registration_method: 'whatsapp_otp'
                    });
                    log('✅ CompleteRegistration event fired', 'success');
                } catch (e) {
                    log(`❌ CompleteRegistration failed: ${e.message}`, 'error');
                }
            }, 2500);
            
            // Test BeginCheckout
            setTimeout(() => {
                try {
                    fbq('track', 'BeginCheckout', {
                        content_name: 'Begin Booking Checkout',
                        content_type: 'appointment'
                    });
                    log('✅ BeginCheckout event fired', 'success');
                } catch (e) {
                    log(`❌ BeginCheckout failed: ${e.message}`, 'error');
                }
            }, 3000);
            
            // Test Purchase
            setTimeout(() => {
                try {
                    fbq('track', 'Purchase', {
                        content_name: 'Perfume Trial Appointment Booked',
                        content_type: 'appointment',
                        value: 0,
                        currency: 'INR'
                    });
                    log('✅ Purchase event fired', 'success');
                } catch (e) {
                    log(`❌ Purchase failed: ${e.message}`, 'error');
                }
            }, 3500);
            
            setTimeout(() => {
                log('🎉 Standard events test completed! Check Facebook Events Manager for results.', 'success');
            }, 4000);
        }
        
        function testCustomEvents() {
            log('🎯 Testing Custom Events...');
            
            const customEvents = [
                'CheckAvailabilityPressed',
                'DateSelected', 
                'TimeSlotSelected',
                'OTPRequested',
                'BookMyTrialPressed',
                'BookingConfirmed'
            ];
            
            customEvents.forEach((eventName, index) => {
                setTimeout(() => {
                    try {
                        fbq('trackCustom', eventName, {
                            content_name: `Test ${eventName}`,
                            test_mode: true
                        });
                        log(`✅ Custom event ${eventName} fired`, 'success');
                    } catch (e) {
                        log(`❌ Custom event ${eventName} failed: ${e.message}`, 'error');
                    }
                }, index * 500);
            });
            
            setTimeout(() => {
                log('🎉 Custom events test completed!', 'success');
            }, customEvents.length * 500 + 500);
        }
        
        function showConsoleInstructions() {
            const instructions = document.getElementById('consoleInstructions');
            instructions.style.display = instructions.style.display === 'none' ? 'block' : 'none';
        }
        
        function clearLog() {
            document.getElementById('eventLog').innerHTML = 'Event log cleared...';
        }
        
        // Auto-check pixel status on load
        window.onload = function() {
            setTimeout(checkPixelStatus, 1000);
            log('🔍 Debug tool loaded. Facebook Pixel should be initialized.', 'info');
        };
    </script>
</body>
</html>