<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kult - Book Your Perfume Trial</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Butler:wght@300;400;700&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/heroicons@2.0.18/24/outline/index.js" type="module"></script>
    <script>
    tailwind.config = {
        theme: {
            extend: {
                colors: {
                    'primary-lilac': '#D4B9FC',
                    'deep-lilac': '#AD8FF7',
                    'soft-black': '#333132',
                    'coral-sorbet': '#EFC1B7',
                    'mint': '#BDE7DC',
                },
                fontFamily: {
                    'butler': ['Butler', 'serif'],
                    'averta': ['Inter', 'sans-serif'],
                }
            }
        }
    };
    </script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #D4B9FC 0%, #AD8FF7 50%, #BDE7DC 100%);
        }
        .glass-effect {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.8);
        }
        .step-indicator {
            transition: all 0.3s ease;
        }
        .step-indicator.active {
            background: #AD8FF7;
            color: white;
        }
        .step-indicator.completed {
            background: #BDE7DC;
            color: #333132;
        }
        .floating-card {
            animation: float 6s ease-in-out infinite;
        }
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
        .booking-card {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        .booking-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        .time-slot {
            transition: all 0.2s ease;
        }
        .time-slot:hover {
            background: #D4B9FC;
            transform: scale(1.02);
        }
        .time-slot.selected {
            background: #AD8FF7;
            color: white;
        }
        .input-field {
            transition: all 0.3s ease;
        }
        .input-field:focus {
            border-color: #AD8FF7;
            box-shadow: 0 0 0 3px rgba(173, 143, 247, 0.1);
        }
    </style>
</head>
<body class="font-averta bg-gray-50">
    <!-- @COMPONENT: Header -->
    <header class="fixed top-0 w-full z-50 glass-effect border-b border-white/20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center space-x-2">
                    <!-- Kult brand logo as text since image isn't loading properly -->
                    <h1 class="font-butler text-2xl font-bold text-soft-black">kult</h1>
                </div>
                <nav class="hidden md:flex space-x-8">
                    <a href="#" class="text-soft-black hover:text-deep-lilac transition-colors">About</a>
                    <a href="#" class="text-soft-black hover:text-deep-lilac transition-colors">Services</a>
                    <a href="#" class="text-soft-black hover:text-deep-lilac transition-colors">Contact</a>
                </nav>
            </div>
        </div>
    </header>
    <!-- @END_COMPONENT: Header -->

    <!-- @COMPONENT: Hero Section -->
    <section class="gradient-bg min-h-screen flex items-center relative overflow-hidden pt-16">
        <!-- Floating decorative elements -->
        <div class="absolute top-1/4 left-10 w-20 h-20 bg-white/20 rounded-full floating-card"></div>
        <div class="absolute bottom-1/3 right-10 w-16 h-16 bg-coral-sorbet/30 rounded-full floating-card" style="animation-delay: -2s;"></div>
        <div class="absolute top-1/2 right-1/4 w-12 h-12 bg-mint/40 rounded-full floating-card" style="animation-delay: -4s;"></div>
        
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <div class="grid lg:grid-cols-2 gap-12 items-center">
                <div class="text-center lg:text-left">
                    <h1 class="font-butler text-5xl lg:text-7xl font-bold text-soft-black mb-6 leading-tight">
                        We work 
                        <span class="italic text-coral-sorbet">hard</span> to make it 
                        <span class="italic text-mint">easy</span>
                    </h1>
                    <p class="text-lg text-soft-black/80 mb-8 max-w-lg">
                        Kult makes finding the right skincare and beauty regimen easier than ever. We do behind the scenes heavy lifting, so that you can focus on putting your best face forward.
                    </p>
                    <button onclick="startBooking()" class="bg-deep-lilac hover:bg-soft-black text-white font-semibold py-4 px-8 rounded-full transition-all duration-300 transform hover:scale-105 shadow-lg">
                        Book Your Perfume Trial
                    </button>
                    <div class="flex items-center justify-center lg:justify-start space-x-4 mt-8">
                        <!-- Apple App Store badge placeholder -->
                        <div class="bg-soft-black text-white px-4 py-2 rounded-lg text-sm">📱 App Store</div>
                        <!-- Google Play badge placeholder -->
                        <div class="bg-soft-black text-white px-4 py-2 rounded-lg text-sm">🤖 Google Play</div>
                    </div>
                </div>
                <div class="relative lg:h-96">
                    <!-- Hero image representing beauty/cosmetics with models -->
                    <!-- Two diverse women showcasing makeup and beauty in purple tones -->
                    <img src="https://images.unsplash.com/photo-1596462502278-27bfdc403348?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600" 
                         alt="Two diverse women showcasing makeup and beauty" 
                         class="w-full h-full object-cover rounded-3xl shadow-2xl" />
                </div>
            </div>
        </div>
    </section>
    <!-- @END_COMPONENT: Hero Section -->

    <!-- @COMPONENT: Booking Flow -->
    <section id="booking-section" class="py-20 bg-white" style="display: none;">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Step Progress Indicator -->
            <div class="flex justify-center mb-12">
                <div class="flex space-x-4">
                    <div class="step-indicator w-10 h-10 rounded-full flex items-center justify-center font-semibold text-sm active" data-step="1">1</div>
                    <div class="step-indicator w-10 h-10 rounded-full flex items-center justify-center font-semibold text-sm bg-gray-200 text-gray-500" data-step="2">2</div>
                    <div class="step-indicator w-10 h-10 rounded-full flex items-center justify-center font-semibold text-sm bg-gray-200 text-gray-500" data-step="3">3</div>
                    <div class="step-indicator w-10 h-10 rounded-full flex items-center justify-center font-semibold text-sm bg-gray-200 text-gray-500" data-step="4">4</div>
                </div>
            </div>

            <!-- Step 1: Postal Code Entry -->
            <div id="step-1" class="booking-card bg-white rounded-3xl shadow-xl p-8 border border-gray-100">
                <div class="text-center mb-8">
                    <h2 class="font-butler text-3xl font-bold text-soft-black mb-4">Enter Your Postal Code</h2>
                    <p class="text-gray-600">Let's check if we service your area</p>
                </div>
                <div class="max-w-md mx-auto">
                    <input 
                        type="text" 
                        id="postal-code"
                        placeholder="Enter 6-digit PIN code"
                        maxlength="6"
                        class="input-field w-full px-6 py-4 border-2 border-mint rounded-2xl text-center text-lg font-semibold focus:outline-none"
                        data-mock="true"
                    />
                    <button onclick="validatePostalCode()" class="w-full mt-6 bg-primary-lilac hover:bg-deep-lilac text-soft-black font-semibold py-4 rounded-2xl transition-all duration-300">
                        Check Availability
                    </button>
                </div>
            </div>

            <!-- Step 2: Date & Time Selection -->
            <div id="step-2" class="booking-card bg-white rounded-3xl shadow-xl p-8 border border-gray-100" style="display: none;">
                <div class="text-center mb-8">
                    <h2 class="font-butler text-3xl font-bold text-soft-black mb-4">Choose Date & Time</h2>
                    <p class="text-gray-600">Select your preferred appointment slot</p>
                </div>
                
                <!-- Date Selection -->
                <div class="mb-8">
                    <label class="block text-sm font-semibold text-soft-black mb-4">Select Date</label>
                    <div class="grid grid-cols-3 md:grid-cols-7 gap-3">
                        <!-- @MAP: availableDates.map(date => ( -->
                        <button class="date-option p-3 border-2 border-gray-200 rounded-xl hover:border-primary-lilac hover:bg-primary-lilac/10 transition-all" data-mock="true">
                            <div class="text-xs text-gray-500">Mon</div>
                            <div class="font-bold text-soft-black">15</div>
                        </button>
                        <button class="date-option p-3 border-2 border-gray-200 rounded-xl hover:border-primary-lilac hover:bg-primary-lilac/10 transition-all" data-mock="true">
                            <div class="text-xs text-gray-500">Tue</div>
                            <div class="font-bold text-soft-black">16</div>
                        </button>
                        <button class="date-option p-3 border-2 border-gray-200 rounded-xl hover:border-primary-lilac hover:bg-primary-lilac/10 transition-all" data-mock="true">
                            <div class="text-xs text-gray-500">Wed</div>
                            <div class="font-bold text-soft-black">17</div>
                        </button>
                        <button class="date-option p-3 border-2 border-gray-200 rounded-xl hover:border-primary-lilac hover:bg-primary-lilac/10 transition-all" data-mock="true">
                            <div class="text-xs text-gray-500">Thu</div>
                            <div class="font-bold text-soft-black">18</div>
                        </button>
                        <button class="date-option p-3 border-2 border-gray-200 rounded-xl hover:border-primary-lilac hover:bg-primary-lilac/10 transition-all" data-mock="true">
                            <div class="text-xs text-gray-500">Fri</div>
                            <div class="font-bold text-soft-black">19</div>
                        </button>
                        <!-- @END_MAP )) -->
                    </div>
                </div>

                <!-- Time Selection -->
                <div class="mb-8">
                    <label class="block text-sm font-semibold text-soft-black mb-4">Available Time Slots</label>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
                        <!-- @MAP: availableTimeSlots.map(slot => ( -->
                        <button class="time-slot p-3 border-2 border-gray-200 rounded-xl text-center font-semibold" data-mock="true">10:00 AM</button>
                        <button class="time-slot p-3 border-2 border-gray-200 rounded-xl text-center font-semibold" data-mock="true">11:30 AM</button>
                        <button class="time-slot p-3 border-2 border-gray-200 rounded-xl text-center font-semibold" data-mock="true">2:00 PM</button>
                        <button class="time-slot p-3 border-2 border-gray-200 rounded-xl text-center font-semibold" data-mock="true">4:30 PM</button>
                        <button class="time-slot p-3 border-2 border-gray-200 rounded-xl text-center font-semibold" data-mock="true">6:00 PM</button>
                        <!-- @END_COMPONENT: Time Slot -->
                    </div>
                </div>

                <button onclick="nextStep(3)" class="w-full bg-primary-lilac hover:bg-deep-lilac text-soft-black font-semibold py-4 rounded-2xl transition-all duration-300">
                    Continue
                </button>
            </div>

            <!-- Step 3: Customer Information -->
            <div id="step-3" class="booking-card bg-white rounded-3xl shadow-xl p-8 border border-gray-100" style="display: none;">
                <div class="text-center mb-8">
                    <h2 class="font-butler text-3xl font-bold text-soft-black mb-4">Your Information</h2>
                    <p class="text-gray-600">Tell us a bit about yourself</p>
                </div>
                
                <form class="space-y-6">
                    <div class="grid md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-semibold text-soft-black mb-2">Full Name *</label>
                            <input 
                                type="text" 
                                required
                                placeholder="Enter your full name"
                                class="input-field w-full px-4 py-3 border-2 border-mint rounded-xl focus:outline-none"
                                data-bind="customer.name"
                                data-mock="true"
                            />
                        </div>
                        <div>
                            <label class="block text-sm font-semibold text-soft-black mb-2">Phone Number *</label>
                            <input 
                                type="tel" 
                                required
                                placeholder="+91 98765 43210"
                                class="input-field w-full px-4 py-3 border-2 border-mint rounded-xl focus:outline-none"
                                data-bind="customer.phone"
                                data-mock="true"
                            />
                        </div>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-semibold text-soft-black mb-2">Email (Optional)</label>
                        <input 
                            type="email" 
                            placeholder="<EMAIL>"
                            class="input-field w-full px-4 py-3 border-2 border-mint rounded-xl focus:outline-none"
                            data-bind="customer.email"
                            data-mock="true"
                        />
                    </div>
                    
                    <div>
                        <label class="block text-sm font-semibold text-soft-black mb-2">Complete Address *</label>
                        <textarea 
                            required
                            rows="3"
                            placeholder="House/Flat number, Street, Landmark, City"
                            class="input-field w-full px-4 py-3 border-2 border-mint rounded-xl focus:outline-none resize-none"
                            data-bind="customer.address"
                            data-mock="true"
                        ></textarea>
                    </div>
                </form>

                <button onclick="nextStep(4)" class="w-full mt-8 bg-primary-lilac hover:bg-deep-lilac text-soft-black font-semibold py-4 rounded-2xl transition-all duration-300">
                    Confirm Booking
                </button>
            </div>

            <!-- Step 4: Confirmation -->
            <div id="step-4" class="booking-card bg-white rounded-3xl shadow-xl p-8 border border-gray-100" style="display: none;">
                <div class="text-center">
                    <div class="w-20 h-20 bg-coral-sorbet rounded-full flex items-center justify-center mx-auto mb-6">
                        <svg class="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <h2 class="font-butler text-3xl font-bold text-soft-black mb-4">Your Perfume Trial is Booked! 🎉</h2>
                    <p class="text-gray-600 mb-8">We're excited to meet you and help you find your perfect scent</p>
                    
                    <!-- Booking Details -->
                    <div class="bg-gray-50 rounded-2xl p-6 mb-8 text-left">
                        <h3 class="font-semibold text-lg text-soft-black mb-4">Booking Details</h3>
                        <div class="space-y-3">
                            <div class="flex justify-between">
                                <span class="text-gray-600">Service:</span>
                                <span class="font-semibold" data-mock="true">Perfume Trial at Home</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Date & Time:</span>
                                <span class="font-semibold" data-bind="booking.dateTime" data-mock="true">Wed, March 17 at 2:00 PM</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Representative:</span>
                                <span class="font-semibold" data-bind="booking.repAssigned" data-mock="true">Sarah M.</span>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="space-y-4">
                        <button class="w-full bg-mint hover:bg-mint/80 text-soft-black font-semibold py-4 rounded-2xl transition-all duration-300" data-event="click:handleReschedule">
                            Reschedule Appointment
                        </button>
                        <button class="w-full border-2 border-coral-sorbet text-coral-sorbet hover:bg-coral-sorbet hover:text-white font-semibold py-4 rounded-2xl transition-all duration-300" data-event="click:handleCancel">
                            Cancel Booking
                        </button>
                    </div>
                    
                    <p class="text-sm text-gray-500 mt-6">
                        You can reschedule or cancel up to 3 hours before your appointment
                    </p>
                </div>
            </div>
        </div>
    </section>
    <!-- @END_COMPONENT: Booking Flow -->

    <!-- @COMPONENT: Features Section -->
    <section class="py-20 bg-gradient-to-br from-mint/20 to-primary-lilac/20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="font-butler text-4xl font-bold text-soft-black mb-4">Why Choose Kult?</h2>
                <p class="text-lg text-gray-600 max-w-2xl mx-auto">Experience personalized fragrance consultation in the comfort of your home</p>
            </div>
            
            <div class="grid md:grid-cols-3 gap-8">
                <div class="text-center p-6">
                    <div class="w-16 h-16 bg-primary-lilac rounded-2xl flex items-center justify-center mx-auto mb-4">
                        <!-- Home icon representing at-home service -->
                        <svg class="w-8 h-8 text-soft-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                        </svg>
                    </div>
                    <h3 class="font-butler text-xl font-semibold text-soft-black mb-2">Home Service</h3>
                    <p class="text-gray-600">Our experts come to you for a personalized fragrance experience</p>
                </div>
                
                <div class="text-center p-6">
                    <div class="w-16 h-16 bg-coral-sorbet rounded-2xl flex items-center justify-center mx-auto mb-4">
                        <!-- Expert consultation icon -->
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                        </svg>
                    </div>
                    <h3 class="font-butler text-xl font-semibold text-soft-black mb-2">Expert Guidance</h3>
                    <p class="text-gray-600">Professional consultants help you discover your signature scent</p>
                </div>
                
                <div class="text-center p-6">
                    <div class="w-16 h-16 bg-mint rounded-2xl flex items-center justify-center mx-auto mb-4">
                        <!-- Flexible scheduling icon -->
                        <svg class="w-8 h-8 text-soft-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                    <h3 class="font-butler text-xl font-semibold text-soft-black mb-2">Flexible Scheduling</h3>
                    <p class="text-gray-600">Book at your convenience with easy rescheduling options</p>
                </div>
            </div>
        </div>
    </section>
    <!-- @END_COMPONENT: Features Section -->

    <!-- @COMPONENT: Footer -->
    <footer class="bg-soft-black text-white py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid md:grid-cols-4 gap-8">
                <div class="col-span-2">
                    <h3 class="font-butler text-2xl font-bold mb-4">kult</h3>
                    <p class="text-gray-300 mb-4">Making finding the right skincare and beauty regimen easier than ever.</p>
                    <p class="text-sm text-gray-400">
                        OFFICE: 901, FL 17, Soli 10 CHS BELAPUR, Thane 400614, Maharashtra<br>
                        © KULT E-COMMERCE PRIVATE LIMITED
                    </p>
                </div>
                <div>
                    <h4 class="font-semibold text-lg mb-4">Quick Links</h4>
                    <ul class="space-y-2 text-gray-300">
                        <li><a href="#" class="hover:text-primary-lilac transition-colors">Privacy Policy</a></li>
                        <li><a href="#" class="hover:text-primary-lilac transition-colors">Terms of Use</a></li>
                        <li><a href="#" class="hover:text-primary-lilac transition-colors">Cancellation Policy</a></li>
                        <li><a href="#" class="hover:text-primary-lilac transition-colors">Return and Refund Policy</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold text-lg mb-4">Support</h4>
                    <ul class="space-y-2 text-gray-300">
                        <li><a href="#" class="hover:text-primary-lilac transition-colors">FAQs</a></li>
                        <li><a href="#" class="hover:text-primary-lilac transition-colors">Contact Us</a></li>
                        <li><a href="#" class="hover:text-primary-lilac transition-colors">Help Center</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </footer>
    <!-- @END_COMPONENT: Footer -->

    <script>
        // TODO: Implement business logic, API calls, and state management
        
        // Booking flow state management
        (function() {
            let currentStep = 1;
            let bookingData = {
                postalCode: '',
                date: '',
                time: '',
                customer: {
                    name: '',
                    phone: '',
                    email: '',
                    address: ''
                }
            };

            // Start booking flow
            window.startBooking = function() {
                document.getElementById('booking-section').style.display = 'block';
                document.getElementById('booking-section').scrollIntoView({ behavior: 'smooth' });
            };

            // Validate postal code
            window.validatePostalCode = function() {
                const postalCode = document.getElementById('postal-code').value;
                
                // TODO: Implement actual postal code validation API call
                if (postalCode.length === 6) {
                    // Mock validation - in real app, check against database
                    const allowedCodes = ['400001', '400002', '400003', '400014', '400614'];
                    
                    if (allowedCodes.includes(postalCode)) {
                        bookingData.postalCode = postalCode;
                        nextStep(2);
                    } else {
                        showMessage("We're coming to your area soon 💜", 'info');
                    }
                } else {
                    showMessage("Please enter a valid 6-digit PIN code", 'error');
                }
            };

            // Navigate to next step
            window.nextStep = function(step) {
                // Update step indicators
                updateStepIndicators(step);
                
                // Hide current step
                document.getElementById(`step-${currentStep}`).style.display = 'none';
                
                // Show next step
                document.getElementById(`step-${step}`).style.display = 'block';
                
                currentStep = step;
                
                // If final step, simulate booking confirmation
                if (step === 4) {
                    // TODO: Submit booking data to backend
                    console.log('Booking submitted:', bookingData);
                    // TODO: Send SMS and email confirmations
                }
            };

            // Update step progress indicators
            function updateStepIndicators(activeStep) {
                for (let i = 1; i <= 4; i++) {
                    const indicator = document.querySelector(`[data-step="${i}"]`);
                    indicator.classList.remove('active', 'completed');
                    
                    if (i < activeStep) {
                        indicator.classList.add('completed');
                    } else if (i === activeStep) {
                        indicator.classList.add('active');
                    } else {
                        indicator.classList.add('bg-gray-200', 'text-gray-500');
                    }
                }
            }

            // Show messages to user
            function showMessage(message, type = 'info') {
                // TODO: Implement proper toast/modal message system
                alert(message);
            }

            // Time slot selection
            document.addEventListener('click', function(e) {
                if (e.target.classList.contains('time-slot')) {
                    // Remove selection from other time slots
                    document.querySelectorAll('.time-slot').forEach(slot => {
                        slot.classList.remove('selected');
                    });
                    
                    // Select clicked time slot
                    e.target.classList.add('selected');
                    bookingData.time = e.target.textContent;
                }
                
                if (e.target.classList.contains('date-option')) {
                    // Remove selection from other dates
                    document.querySelectorAll('.date-option').forEach(date => {
                        date.classList.remove('border-deep-lilac', 'bg-primary-lilac/20');
                    });
                    
                    // Select clicked date
                    e.target.classList.add('border-deep-lilac', 'bg-primary-lilac/20');
                    bookingData.date = e.target.textContent;
                }
            });

            // Handle rescheduling
            window.handleReschedule = function() {
                // TODO: Implement rescheduling logic
                // Check if appointment is more than 3 hours away
                // Show date/time selection again
                console.log('Reschedule booking:', bookingData);
            };

            // Handle cancellation
            window.handleCancel = function() {
                // TODO: Implement cancellation logic
                // Check if appointment is more than 3 hours away
                // Send cancellation confirmations
                if (confirm('Are you sure you want to cancel your appointment?')) {
                    console.log('Cancel booking:', bookingData);
                    // TODO: Update booking status in database
                }
            };

        })();

        // Smooth scroll behavior for internal links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({ behavior: 'smooth' });
                }
            });
        });

        // Form validation helpers
        function validateForm(formElement) {
            // TODO: Implement comprehensive form validation
            const requiredFields = formElement.querySelectorAll('[required]');
            let isValid = true;
            
            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    field.classList.add('border-red-500');
                    isValid = false;
                } else {
                    field.classList.remove('border-red-500');
                }
            });
            
            return isValid;
        }
    </script>
</body>
</html>