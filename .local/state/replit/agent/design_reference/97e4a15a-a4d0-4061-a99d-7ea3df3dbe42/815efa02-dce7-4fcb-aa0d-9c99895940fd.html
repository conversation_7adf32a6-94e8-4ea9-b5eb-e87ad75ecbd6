<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kult - Book Your Perfume Trial</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'primary-lilac': '#D4B9FC',
                        'deep-lilac': '#AD8FF7',
                        'soft-black': '#333132',
                        'coral-sorbet': '#EFC1B7',
                        'mint': '#BDE7DC'
                    },
                    fontFamily: {
                        'butler': ['Inter', 'serif'], // Using Inter as fallback for Butler
                        'averta': ['Inter', 'sans-serif'] // Using Inter as fallback for Averta
                    },
                    animation: {
                        'float': 'float 3s ease-in-out infinite',
                        'slide-up': 'slideUp 0.5s ease-out',
                        'fade-in': 'fadeIn 0.6s ease-out',
                        'scale-in': 'scaleIn 0.3s ease-out'
                    }
                }
            }
        };
    </script>
    <style>
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
        @keyframes slideUp {
            from { transform: translateY(30px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        @keyframes scaleIn {
            from { transform: scale(0.9); opacity: 0; }
            to { transform: scale(1); opacity: 1; }
        }
        .gradient-bg {
            background: linear-gradient(135deg, #D4B9FC 0%, #AD8FF7 50%, #EFC1B7 100%);
        }
        .glass-effect {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.9);
        }
        .step-inactive {
            background: #E5E7EB;
            color: #9CA3AF;
        }
        .step-active {
            background: linear-gradient(135deg, #D4B9FC, #AD8FF7);
            color: white;
        }
        .step-completed {
            background: #10B981;
            color: white;
        }
    </style>
</head>
<body class="min-h-screen font-averta">
    <!-- Background Pattern -->
    <div class="fixed inset-0 gradient-bg"></div>
    <div class="fixed inset-0 opacity-10" style="background-image: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"100\" height=\"100\" viewBox=\"0 0 100 100\"><circle cx=\"50\" cy=\"50\" r=\"2\" fill=\"white\"/></svg>'); background-size: 50px 50px;"></div>

    <!-- @COMPONENT: BookingApp [main application container] -->
    <div class="relative min-h-screen flex items-center justify-center p-4">
        <div class="w-full max-w-md">
            
            <!-- Logo Section -->
            <div class="text-center mb-8 animate-fade-in">
                <div class="inline-flex items-center justify-center w-16 h-16 rounded-full glass-effect mb-4 animate-float">
                    <span class="text-2xl font-butler font-bold text-deep-lilac">&</span>
                </div>
                <h1 class="text-3xl font-butler font-bold text-white mb-2">kult</h1>
                <p class="text-white/80 text-sm">Discover your signature scent at home</p>
            </div>

            <!-- Progress Steps -->
            <!-- @STATE: currentStep:number = 1 -->
            <div class="flex justify-center mb-8" data-mock="true">
                <div class="flex items-center space-x-2">
                    <div class="w-8 h-8 rounded-full step-active flex items-center justify-center text-xs font-semibold">1</div>
                    <div class="w-8 h-1 step-inactive rounded"></div>
                    <div class="w-8 h-8 rounded-full step-inactive flex items-center justify-center text-xs font-semibold">2</div>
                    <div class="w-8 h-1 step-inactive rounded"></div>
                    <div class="w-8 h-8 rounded-full step-inactive flex items-center justify-center text-xs font-semibold">3</div>
                    <div class="w-8 h-1 step-inactive rounded"></div>
                    <div class="w-8 h-8 rounded-full step-inactive flex items-center justify-center text-xs font-semibold">4</div>
                </div>
            </div>

            <!-- Main Booking Form Card -->
            <div class="glass-effect rounded-3xl p-8 shadow-2xl animate-slide-up">
                
                <!-- Step 1: Postal Code Entry -->
                <!-- @COMPONENT: PostalCodeStep [postal code validation] -->
                <div id="step-postal" class="animate-fade-in">
                    <div class="text-center mb-6">
                        <h2 class="text-2xl font-butler font-bold text-soft-black mb-2">Where are you located?</h2>
                        <p class="text-gray-600 text-sm">Enter your postal code to check if we serve your area</p>
                    </div>

                    <form class="space-y-6">
                        <div class="relative">
                            <input 
                                type="text" 
                                placeholder="Enter 6-digit postal code"
                                class="w-full px-4 py-4 rounded-2xl border-2 border-gray-200 focus:border-primary-lilac focus:outline-none transition-all duration-300 text-center text-lg font-semibold tracking-widest"
                                maxlength="6"
                                data-bind="postalCode"
                                data-mock="true"
                            />
                            <div class="absolute right-4 top-1/2 transform -translate-y-1/2">
                                <i class="fas fa-map-marker-alt text-primary-lilac"></i>
                            </div>
                        </div>

                        <button 
                            type="submit"
                            class="w-full py-4 rounded-2xl font-semibold text-white transition-all duration-300 transform hover:scale-105 active:scale-95"
                            style="background: linear-gradient(135deg, #D4B9FC, #AD8FF7);"
                            data-event="click:validatePostalCode"
                            data-implementation="Should validate postal code against database"
                        >
                            <span class="flex items-center justify-center">
                                Check Availability
                                <i class="fas fa-arrow-right ml-2"></i>
                            </span>
                        </button>
                    </form>

                    <!-- Service Area Message (Hidden by default) -->
                    <div class="hidden mt-4 p-4 rounded-2xl bg-coral-sorbet/20 border border-coral-sorbet">
                        <div class="flex items-center text-coral-sorbet">
                            <i class="fas fa-heart mr-2"></i>
                            <span class="text-sm">We're coming to your area soon 💜</span>
                        </div>
                    </div>
                </div>
                <!-- @END_COMPONENT: PostalCodeStep -->

                <!-- Step 2: Date & Time Selection (Hidden) -->
                <!-- @COMPONENT: DateTimeStep [availability selection] -->
                <div id="step-datetime" class="hidden">
                    <div class="text-center mb-6">
                        <h2 class="text-2xl font-butler font-bold text-soft-black mb-2">Pick your perfect time</h2>
                        <p class="text-gray-600 text-sm">Choose when you'd like your perfume trial</p>
                    </div>

                    <!-- Calendar Section -->
                    <div class="mb-6">
                        <h3 class="font-semibold text-soft-black mb-3">Select Date</h3>
                        <div class="grid grid-cols-7 gap-2 text-center text-sm">
                            <!-- Calendar Header -->
                            <div class="py-2 text-gray-500 font-medium">Su</div>
                            <div class="py-2 text-gray-500 font-medium">Mo</div>
                            <div class="py-2 text-gray-500 font-medium">Tu</div>
                            <div class="py-2 text-gray-500 font-medium">We</div>
                            <div class="py-2 text-gray-500 font-medium">Th</div>
                            <div class="py-2 text-gray-500 font-medium">Fr</div>
                            <div class="py-2 text-gray-500 font-medium">Sa</div>
                            
                            <!-- Calendar Days -->
                            <!-- @MAP: availableDates.map(date => ( -->
                            <button class="py-3 rounded-xl text-gray-400">1</button>
                            <button class="py-3 rounded-xl text-gray-400">2</button>
                            <button class="py-3 rounded-xl text-gray-400">3</button>
                            <button class="py-3 rounded-xl hover:bg-primary-lilac hover:text-white transition-colors">4</button>
                            <button class="py-3 rounded-xl hover:bg-primary-lilac hover:text-white transition-colors">5</button>
                            <button class="py-3 rounded-xl bg-primary-lilac text-white">6</button>
                            <button class="py-3 rounded-xl hover:bg-primary-lilac hover:text-white transition-colors">7</button>
                            <!-- @END_MAP )) -->
                        </div>
                    </div>

                    <!-- Time Slots -->
                    <div class="mb-6">
                        <h3 class="font-semibold text-soft-black mb-3">Select Time</h3>
                        <div class="grid grid-cols-2 gap-3">
                            <!-- @MAP: availableTimeSlots.map(slot => ( -->
                            <button class="py-3 px-4 rounded-xl border-2 border-gray-200 hover:border-primary-lilac hover:bg-primary-lilac hover:text-white transition-all" data-bind="timeSlot">10:00 AM</button>
                            <button class="py-3 px-4 rounded-xl border-2 border-primary-lilac bg-primary-lilac text-white">2:00 PM</button>
                            <button class="py-3 px-4 rounded-xl border-2 border-gray-200 hover:border-primary-lilac hover:bg-primary-lilac hover:text-white transition-all">4:00 PM</button>
                            <button class="py-3 px-4 rounded-xl border-2 border-gray-200 hover:border-primary-lilac hover:bg-primary-lilac hover:text-white transition-all">6:00 PM</button>
                            <!-- @END_MAP )) -->
                        </div>
                    </div>

                    <button 
                        type="button"
                        class="w-full py-4 rounded-2xl font-semibold text-white transition-all duration-300 transform hover:scale-105"
                        style="background: linear-gradient(135deg, #D4B9FC, #AD8FF7);"
                        data-event="click:proceedToCustomerInfo"
                    >
                        Continue to Details
                    </button>
                </div>
                <!-- @END_COMPONENT: DateTimeStep -->

                <!-- Step 3: Customer Information -->
                <!-- @COMPONENT: CustomerInfoStep [customer details form] -->
                <div id="step-customer" class="hidden">
                    <div class="text-center mb-6">
                        <h2 class="text-2xl font-butler font-bold text-soft-black mb-2">Tell us about you</h2>
                        <p class="text-gray-600 text-sm">We need a few details to confirm your appointment</p>
                    </div>

                    <form class="space-y-4">
                        <div class="relative">
                            <input 
                                type="text" 
                                placeholder="Full Name"
                                class="w-full px-4 py-4 rounded-2xl border-2 border-gray-200 focus:border-primary-lilac focus:outline-none transition-all duration-300"
                                required
                                data-bind="customerName"
                                data-mock="true"
                            />
                            <i class="fas fa-user absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                        </div>

                        <div class="relative">
                            <input 
                                type="tel" 
                                placeholder="Phone Number"
                                class="w-full px-4 py-4 rounded-2xl border-2 border-gray-200 focus:border-primary-lilac focus:outline-none transition-all duration-300"
                                required
                                data-bind="customerPhone"
                                data-mock="true"
                            />
                            <i class="fas fa-phone absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                        </div>

                        <div class="relative">
                            <input 
                                type="email" 
                                placeholder="Email (Optional)"
                                class="w-full px-4 py-4 rounded-2xl border-2 border-gray-200 focus:border-primary-lilac focus:outline-none transition-all duration-300"
                                data-bind="customerEmail"
                                data-mock="true"
                            />
                            <i class="fas fa-envelope absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                        </div>

                        <div class="relative">
                            <textarea 
                                placeholder="Full Address"
                                rows="3"
                                class="w-full px-4 py-4 rounded-2xl border-2 border-gray-200 focus:border-primary-lilac focus:outline-none transition-all duration-300 resize-none"
                                required
                                data-bind="customerAddress"
                                data-mock="true"
                            ></textarea>
                            <i class="fas fa-map-marker-alt absolute right-4 top-4 text-gray-400"></i>
                        </div>

                        <button 
                            type="submit"
                            class="w-full py-4 rounded-2xl font-semibold text-white transition-all duration-300 transform hover:scale-105"
                            style="background: linear-gradient(135deg, #D4B9FC, #AD8FF7);"
                            data-event="click:submitBooking"
                            data-implementation="Should validate form and create booking"
                        >
                            Book My Trial
                        </button>
                    </form>
                </div>
                <!-- @END_COMPONENT: CustomerInfoStep -->

                <!-- Step 4: Confirmation -->
                <!-- @COMPONENT: ConfirmationStep [booking confirmation] -->
                <div id="step-confirmation" class="hidden text-center">
                    <div class="mb-6">
                        <div class="w-20 h-20 mx-auto mb-4 rounded-full bg-gradient-to-r from-mint to-primary-lilac flex items-center justify-center animate-scale-in">
                            <i class="fas fa-check text-3xl text-white"></i>
                        </div>
                        <h2 class="text-2xl font-butler font-bold text-soft-black mb-2">You're all set! 🎉</h2>
                        <p class="text-gray-600">Your Perfume Trial is booked. See you soon!</p>
                    </div>

                    <!-- Booking Details -->
                    <div class="bg-gray-50 rounded-2xl p-6 mb-6 text-left">
                        <h3 class="font-semibold text-soft-black mb-4">Booking Details</h3>
                        <div class="space-y-3 text-sm">
                            <div class="flex justify-between">
                                <span class="text-gray-600">Service:</span>
                                <span class="font-medium">Perfume Trial at Home</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Date:</span>
                                <span class="font-medium" data-bind="selectedDate">Dec 6, 2024</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Time:</span>
                                <span class="font-medium" data-bind="selectedTime">2:00 PM</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Address:</span>
                                <span class="font-medium" data-bind="customerAddress">123 Main St, City</span>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="space-y-3">
                        <button 
                            class="w-full py-3 rounded-2xl border-2 border-primary-lilac text-primary-lilac font-semibold hover:bg-primary-lilac hover:text-white transition-all"
                            data-event="click:rescheduleBooking"
                            data-implementation="Allow rescheduling up to 3 hours before"
                        >
                            <i class="fas fa-calendar-alt mr-2"></i>
                            Reschedule
                        </button>
                        <button 
                            class="w-full py-3 rounded-2xl border-2 border-coral-sorbet text-coral-sorbet font-semibold hover:bg-coral-sorbet hover:text-white transition-all"
                            data-event="click:cancelBooking"
                            data-implementation="Allow cancellation up to 3 hours before"
                        >
                            <i class="fas fa-times mr-2"></i>
                            Cancel Booking
                        </button>
                    </div>

                    <!-- Contact Info -->
                    <div class="mt-6 text-xs text-gray-500">
                        Questions? Contact us at <a href="mailto:<EMAIL>" class="text-primary-lilac"><EMAIL></a>
                    </div>
                </div>
                <!-- @END_COMPONENT: ConfirmationStep -->

            </div>

            <!-- Navigation Buttons -->
            <div class="flex justify-between mt-6">
                <button 
                    id="back-btn"
                    class="px-6 py-3 rounded-2xl text-white/80 hover:text-white transition-colors hidden"
                    data-event="click:goToPreviousStep"
                >
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back
                </button>
                <div></div>
            </div>

        </div>
    </div>
    <!-- @END_COMPONENT: BookingApp -->

    <!-- Admin Dashboard Preview (Hidden by default) -->
    <!-- @COMPONENT: AdminDashboard [admin interface] -->
    <div id="admin-dashboard" class="hidden fixed inset-0 bg-white">
        <!-- Admin Header -->
        <header class="bg-white border-b border-gray-200 px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <span class="text-2xl font-butler font-bold text-deep-lilac">&</span>
                    <span class="ml-2 text-xl font-butler font-bold text-soft-black">kult admin</span>
                </div>
                <div class="flex items-center space-x-4">
                    <button class="p-2 rounded-lg hover:bg-gray-100">
                        <i class="fas fa-bell text-gray-600"></i>
                    </button>
                    <div class="w-8 h-8 rounded-full bg-primary-lilac flex items-center justify-center">
                        <span class="text-white text-sm font-semibold">A</span>
                    </div>
                </div>
            </div>
        </header>

        <!-- Admin Content -->
        <div class="flex h-full">
            <!-- Sidebar -->
            <aside class="w-64 bg-gray-50 border-r border-gray-200 p-6">
                <nav class="space-y-2">
                    <a href="#" class="flex items-center px-4 py-3 text-sm font-medium text-white bg-primary-lilac rounded-lg">
                        <i class="fas fa-calendar-check mr-3"></i>
                        Bookings
                    </a>
                    <a href="#" class="flex items-center px-4 py-3 text-sm font-medium text-gray-600 hover:bg-gray-100 rounded-lg">
                        <i class="fas fa-users mr-3"></i>
                        Sales Reps
                    </a>
                    <a href="#" class="flex items-center px-4 py-3 text-sm font-medium text-gray-600 hover:bg-gray-100 rounded-lg">
                        <i class="fas fa-map-marker-alt mr-3"></i>
                        Postal Codes
                    </a>
                    <a href="#" class="flex items-center px-4 py-3 text-sm font-medium text-gray-600 hover:bg-gray-100 rounded-lg">
                        <i class="fas fa-cog mr-3"></i>
                        Settings
                    </a>
                </nav>
            </aside>

            <!-- Main Content -->
            <main class="flex-1 p-6">
                <div class="mb-6">
                    <h1 class="text-2xl font-butler font-bold text-soft-black">Bookings Overview</h1>
                    <p class="text-gray-600">Manage all perfume trial appointments</p>
                </div>

                <!-- Stats Cards -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                    <div class="bg-white rounded-xl p-6 border border-gray-200">
                        <div class="flex items-center">
                            <div class="p-3 rounded-lg bg-primary-lilac/10">
                                <i class="fas fa-calendar-check text-primary-lilac"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm text-gray-600">Today's Bookings</p>
                                <p class="text-2xl font-bold text-soft-black" data-bind="todayBookings">12</p>
                            </div>
                        </div>
                    </div>
                    <!-- More stat cards would go here -->
                </div>

                <!-- Bookings Table -->
                <div class="bg-white rounded-xl border border-gray-200">
                    <div class="p-6 border-b border-gray-200">
                        <div class="flex items-center justify-between">
                            <h2 class="text-lg font-semibold text-soft-black">Recent Bookings</h2>
                            <button class="px-4 py-2 bg-primary-lilac text-white rounded-lg hover:bg-deep-lilac transition-colors">
                                <i class="fas fa-plus mr-2"></i>
                                Add Booking
                            </button>
                        </div>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date & Time</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Address</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rep Assigned</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <!-- @MAP: bookings.map(booking => ( -->
                                <tr data-mock="true">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900" data-bind="booking.name">Sarah Johnson</div>
                                        <div class="text-sm text-gray-500" data-bind="booking.phone">+****************</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900" data-bind="booking.dateTime">Dec 6, 2:00 PM</td>
                                    <td class="px-6 py-4 text-sm text-gray-900" data-bind="booking.address">123 Main St, Downtown</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900" data-bind="booking.rep">Emma Wilson</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-mint text-soft-black">Confirmed</span>
                                    </td>
                                </tr>
                                <!-- @END_MAP )) -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </main>
        </div>
    </div>
    <!-- @END_COMPONENT: AdminDashboard -->

    <script>
        // Simple step navigation demo
        // TODO: Implement full booking flow with API integration
        (function() {
            let currentStep = 1;
            const totalSteps = 4;
            
            const steps = {
                1: document.getElementById('step-postal'),
                2: document.getElementById('step-datetime'),
                3: document.getElementById('step-customer'),
                4: document.getElementById('step-confirmation')
            };

            function updateProgressIndicator() {
                const progressSteps = document.querySelectorAll('.w-8.h-8.rounded-full');
                const progressBars = document.querySelectorAll('.w-8.h-1');
                
                progressSteps.forEach((step, index) => {
                    const stepNumber = index + 1;
                    step.className = step.className.replace(/step-\w+/g, '');
                    
                    if (stepNumber < currentStep) {
                        step.classList.add('step-completed');
                    } else if (stepNumber === currentStep) {
                        step.classList.add('step-active');
                    } else {
                        step.classList.add('step-inactive');
                    }
                });
                
                progressBars.forEach((bar, index) => {
                    bar.className = bar.className.replace(/step-\w+/g, '');
                    if (index + 1 < currentStep) {
                        bar.classList.add('step-completed');
                    } else {
                        bar.classList.add('step-inactive');
                    }
                });
            }

            function showStep(stepNumber) {
                Object.values(steps).forEach(step => step.classList.add('hidden'));
                steps[stepNumber].classList.remove('hidden');
                updateProgressIndicator();
                
                // Show/hide back button
                const backBtn = document.getElementById('back-btn');
                if (stepNumber > 1 && stepNumber < totalSteps) {
                    backBtn.classList.remove('hidden');
                } else {
                    backBtn.classList.add('hidden');
                }
            }

            // Demo navigation - in production, this would be handled by form submissions
            document.addEventListener('click', function(e) {
                if (e.target.closest('[data-event*="click"]')) {
                    e.preventDefault();
                    
                    if (currentStep < totalSteps) {
                        currentStep++;
                        showStep(currentStep);
                    }
                }
                
                if (e.target.closest('#back-btn')) {
                    e.preventDefault();
                    if (currentStep > 1) {
                        currentStep--;
                        showStep(currentStep);
                    }
                }
            });

            // Initialize
            updateProgressIndicator();
        })();
    </script>
</body>
</html>