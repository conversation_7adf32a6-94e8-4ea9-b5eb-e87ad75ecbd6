#!/usr/bin/env python3

import os
import subprocess
import sys

def execute_sql_batch(sql_commands, batch_size=20):
    """Execute SQL commands in batches"""
    total = len(sql_commands)
    success_count = 0
    
    for i in range(0, total, batch_size):
        batch = sql_commands[i:i+batch_size]
        
        # Create batch INSERT with VALUES
        if len(batch) > 1:
            # Combine multiple VALUES into single INSERT
            first_cmd = batch[0]
            values_parts = []
            
            for cmd in batch:
                # Extract VALUES part from each command
                values_start = cmd.find("VALUES") + 6
                values_part = cmd[values_start:].rstrip(';').strip()
                values_parts.append(values_part)
            
            # Create combined INSERT
            insert_part = first_cmd[:first_cmd.find("VALUES") + 6]
            combined_sql = insert_part + ",\n".join(values_parts) + ";"
        else:
            combined_sql = batch[0]
        
        try:
            # Execute the batch
            result = subprocess.run([
                'psql', os.environ['DATABASE_URL'], '-c', combined_sql
            ], capture_output=True, text=True, check=True)
            
            success_count += len(batch)
            print(f"Progress: {success_count}/{total} bookings restored")
            
        except subprocess.CalledProcessError as e:
            print(f"Error in batch {i//batch_size + 1}: {e.stderr}")
            # Try individual commands if batch fails
            for cmd in batch:
                try:
                    subprocess.run([
                        'psql', os.environ['DATABASE_URL'], '-c', cmd
                    ], capture_output=True, text=True, check=True)
                    success_count += 1
                except:
                    print(f"Failed individual command: {cmd[:50]}...")
    
    return success_count

def main():
    # Read SQL file
    with open('restore_missing_bookings.sql', 'r') as f:
        content = f.read()
    
    # Split into individual INSERT statements
    commands = [cmd.strip() + ';' for cmd in content.split(');') if cmd.strip()]
    
    print(f"Found {len(commands)} INSERT commands to execute")
    
    # Check which bookings are already restored
    try:
        result = subprocess.run([
            'psql', os.environ['DATABASE_URL'], '-c', 
            'SELECT COUNT(*) FROM bookings WHERE id > 750;'
        ], capture_output=True, text=True, check=True)
        
        current_count = int(result.stdout.strip().split('\n')[-2].strip())
        print(f"Currently restored: {current_count} bookings")
        
        if current_count > 0:
            # Skip already restored commands
            commands = commands[current_count:]
            print(f"Skipping first {current_count}, remaining: {len(commands)}")
        
    except Exception as e:
        print(f"Could not check current status: {e}")
    
    if not commands:
        print("All bookings already restored!")
        return
    
    # Execute restoration
    success_count = execute_sql_batch(commands)
    print(f"\nRestoration complete! Successfully restored {success_count} bookings")

if __name__ == "__main__":
    main()