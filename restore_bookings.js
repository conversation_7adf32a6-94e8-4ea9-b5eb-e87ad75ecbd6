import fs from 'fs';
import csv from 'csv-parser';

// Parse CSV and extract missing bookings
function parseCSVAndFindMissing() {
  const csvBookings = [];
  
  return new Promise((resolve, reject) => {
    fs.createReadStream('./attached_assets/bookings_2025-07-22_1753169670985.csv')
      .pipe(csv())
      .on('data', (row) => {
        // Parse each booking from CSV
        const booking = {
          id: parseInt(row.ID),
          name: row.Name?.replace(/"/g, '') || '',
          phone: row.Phone?.replace(/"/g, '') || '',
          email: row.Email || null,
          date: row.Date ? new Date(row.Date).toISOString() : null,
          timeSlot: row.Time?.replace(/"/g, '') || '',
          status: row.Status?.replace(/"/g, '') || 'confirmed',
          address: row.Address?.replace(/"/g, '') || '',
          postalCode: row['Postal Code']?.replace(/"/g, '') || '',
          repAssigned: row['Sales Rep']?.replace(/"/g, '') || 'Unassigned',
          service: row.Service?.replace(/"/g, '') || 'Perfume Trial at Home',
          secureToken: row['Secure Token']?.replace(/"/g, '') || '',
          sessionId: row['Session ID']?.replace(/"/g, '') || null,
          utmSource: row['UTM Source']?.replace(/"/g, '') || null,
          utmMedium: row['UTM Medium']?.replace(/"/g, '') || null,
          utmCampaign: row['UTM Campaign']?.replace(/"/g, '') || null,
          utmTerm: row['UTM Term']?.replace(/"/g, '') || null,
          utmContent: row['UTM Content']?.replace(/"/g, '') || null,
          createdAt: parseCreatedDate(row['Created Date'], row['Created Time'])
        };
        
        csvBookings.push(booking);
      })
      .on('end', () => {
        console.log(`Parsed ${csvBookings.length} bookings from CSV`);
        
        // Find missing bookings (ID > 750)
        const missingBookings = csvBookings.filter(booking => booking.id > 750);
        console.log(`Found ${missingBookings.length} missing bookings (ID > 750)`);
        
        resolve(missingBookings);
      })
      .on('error', reject);
  });
}

function parseCreatedDate(dateStr, timeStr) {
  if (!dateStr || !timeStr) return new Date().toISOString();
  
  try {
    const [year, month, day] = dateStr.split('-');
    const [hour, minute, second] = timeStr.split(':');
    return new Date(year, month - 1, day, hour, minute, second || 0).toISOString();
  } catch (error) {
    return new Date().toISOString();
  }
}

// Generate SQL INSERT statements
function generateInsertSQL(bookings) {
  let sql = '';
  
  bookings.forEach(booking => {
    const values = [
      booking.id,
      `'${booking.name.replace(/'/g, "''")}'`,
      `'${booking.phone}'`,
      booking.email ? `'${booking.email}'` : 'NULL',
      `'${booking.address.replace(/'/g, "''")}'`,
      `'${booking.postalCode}'`,
      booking.date ? `'${booking.date}'` : 'NULL',
      `'${booking.timeSlot}'`,
      `'${booking.service}'`,
      `'${booking.repAssigned}'`,
      `'${booking.status}'`,
      `'${booking.secureToken}'`,
      'false', // appointmentCompleted
      'NULL', // customerSatisfaction
      'NULL', // feedbackNotes
      'NULL', // purchaseAmount
      'NULL', // orderId
      'NULL', // paymentStatus
      'NULL', // paymentMethod
      '0', // callAttempts
      'NULL', // lastCallDate
      '0', // whatsappSent
      '0', // emailsSent
      'NULL', // supportTicketId
      'NULL', // lastContactMethod
      booking.utmSource ? `'${booking.utmSource.replace(/'/g, "''")}'` : 'NULL',
      booking.utmMedium ? `'${booking.utmMedium.replace(/'/g, "''")}'` : 'NULL',
      booking.utmCampaign ? `'${booking.utmCampaign.replace(/'/g, "''")}'` : 'NULL',
      booking.utmTerm ? `'${booking.utmTerm}'` : 'NULL',
      booking.utmContent ? `'${booking.utmContent}'` : 'NULL',
      'NULL', // trafficSource - will be generated
      booking.sessionId ? `'${booking.sessionId}'` : 'NULL',
      `'${booking.createdAt}'`,
      `'${booking.createdAt}'` // updatedAt
    ];
    
    sql += `INSERT INTO bookings (id, name, phone, email, address, postal_code, date, time_slot, service, rep_assigned, status, secure_token, appointment_completed, customer_satisfaction, feedback_notes, purchase_amount, order_id, payment_status, payment_method, call_attempts, last_call_date, whatsapp_sent, emails_sent, support_ticket_id, last_contact_method, utm_source, utm_medium, utm_campaign, utm_term, utm_content, traffic_source, session_id, created_at, updated_at) VALUES (${values.join(', ')});\n`;
  });
  
  return sql;
}

// Main execution
async function main() {
  try {
    console.log('Starting booking restoration process...');
    const missingBookings = await parseCSVAndFindMissing();
    
    if (missingBookings.length === 0) {
      console.log('No missing bookings found!');
      return;
    }
    
    console.log(`Generating SQL for ${missingBookings.length} missing bookings...`);
    const insertSQL = generateInsertSQL(missingBookings);
    
    // Write SQL to file
    fs.writeFileSync('./restore_missing_bookings.sql', insertSQL);
    console.log('SQL file generated: restore_missing_bookings.sql');
    
    // Show first few missing booking IDs
    const missingIds = missingBookings.map(b => b.id).slice(0, 10);
    console.log('Sample missing booking IDs:', missingIds);
    
  } catch (error) {
    console.error('Error:', error);
  }
}

main();