# URGENT: Production Deployment Fix

## Problem Identified
The production deployment is not correctly routing API requests to the Express server. API endpoints return HTML instead of JSON, indicating the static file serving is intercepting API routes.

## Root Cause
The production deployment has a routing conflict where the static file server catch-all route (`app.use("*", ...)`) is intercepting API requests before they reach the Express API handlers.

## Immediate Solution

### Step 1: Deploy with Environment Fix
Make sure these environment variables are properly set in production:
- `NODE_ENV=production`
- All database connection variables
- API keys for external services

### Step 2: Manual Database Schema Fix (CRITICAL)
The production database needs the same schema fix that was applied to development:

```sql
-- Connect to production database and run:
ALTER TABLE user_sessions ALTER COLUMN completed_steps DROP DEFAULT;
ALTER TABLE user_sessions ALTER COLUMN completed_steps TYPE text[] USING CASE WHEN completed_steps = 0 THEN '{}' ELSE ARRAY[completed_steps::text] END;
ALTER TABLE user_sessions ALTER COLUMN completed_steps SET DEFAULT '{}';
```

### Step 3: Verify API Routes After Deployment
Test these endpoints to confirm they return JSON (not HTML):
- `GET /api/health` (if available)
- `POST /api/admin/login`
- `GET /api/settings`

## Why This Happened
1. Database schema mismatch between development and production
2. The `completed_steps` column was integer type in production but text[] in development
3. This caused the "value.map is not a function" error that crashed the dashboard

## Prevention
- Database migrations should be automated in future deployments
- Production should use the same schema as development
- API route testing should be part of deployment verification

## Next Steps After Fix
1. Deploy the application
2. Run the SQL commands above on production database
3. Test admin dashboard login
4. Verify UTM tracking data is appearing correctly

The dashboard should work normally once these fixes are applied to production.