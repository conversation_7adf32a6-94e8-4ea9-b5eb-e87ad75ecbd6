#!/usr/bin/env node

import { Pool } from '@neondatabase/serverless';
import fs from 'fs';
import path from 'path';

// Database connection
const pool = new Pool({ 
  connectionString: process.env.DATABASE_URL 
});

async function exportDatabase() {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const exportDir = `exports/export_${timestamp}`;
  
  // Create export directory
  fs.mkdirSync(exportDir, { recursive: true });
  
  console.log(`Starting database export to ${exportDir}...`);
  
  try {
    // Get all tables
    const tablesResult = await pool.query(`
      SELECT tablename 
      FROM pg_tables 
      WHERE schemaname = 'public'
      ORDER BY tablename;
    `);
    
    const tables = tablesResult.rows.map(row => row.tablename);
    console.log(`Found ${tables.length} tables:`, tables);
    
    // Export each table
    for (const tableName of tables) {
      console.log(`Exporting table: ${tableName}`);
      
      try {
        // Get table data
        const result = await pool.query(`SELECT * FROM ${tableName}`);
        
        // Convert to JSON
        const jsonData = {
          table: tableName,
          rowCount: result.rows.length,
          exportedAt: new Date().toISOString(),
          data: result.rows
        };
        
        // Save as JSON
        fs.writeFileSync(
          path.join(exportDir, `${tableName}.json`),
          JSON.stringify(jsonData, null, 2)
        );
        
        // Save as CSV
        if (result.rows.length > 0) {
          const headers = Object.keys(result.rows[0]);
          const csvContent = [
            headers.join(','),
            ...result.rows.map(row => 
              headers.map(header => {
                const value = row[header];
                if (value === null) return '';
                if (typeof value === 'string' && value.includes(',')) {
                  return `"${value.replace(/"/g, '""')}"`;
                }
                return value;
              }).join(',')
            )
          ].join('\n');
          
          fs.writeFileSync(
            path.join(exportDir, `${tableName}.csv`),
            csvContent
          );
        }
        
        console.log(`  ✓ ${tableName}: ${result.rows.length} rows exported`);
        
      } catch (error) {
        console.error(`  ✗ Error exporting ${tableName}:`, error.message);
      }
    }
    
    // Export database schema
    console.log('Exporting database schema...');
    const schemaResult = await pool.query(`
      SELECT 
        table_name,
        column_name,
        data_type,
        is_nullable,
        column_default
      FROM information_schema.columns 
      WHERE table_schema = 'public'
      ORDER BY table_name, ordinal_position;
    `);
    
    fs.writeFileSync(
      path.join(exportDir, 'schema_info.json'),
      JSON.stringify({
        exportedAt: new Date().toISOString(),
        tables: tables,
        columns: schemaResult.rows
      }, null, 2)
    );
    
    // Create summary
    const summary = {
      exportedAt: new Date().toISOString(),
      totalTables: tables.length,
      tables: {}
    };
    
    for (const tableName of tables) {
      try {
        const countResult = await pool.query(`SELECT COUNT(*) as count FROM ${tableName}`);
        summary.tables[tableName] = parseInt(countResult.rows[0].count);
      } catch (error) {
        summary.tables[tableName] = 'error';
      }
    }
    
    fs.writeFileSync(
      path.join(exportDir, 'export_summary.json'),
      JSON.stringify(summary, null, 2)
    );
    
    console.log('\n✅ Database export completed successfully!');
    console.log(`📁 Export location: ${exportDir}`);
    console.log('\n📊 Export Summary:');
    Object.entries(summary.tables).forEach(([table, count]) => {
      console.log(`  ${table}: ${count} rows`);
    });
    
  } catch (error) {
    console.error('❌ Export failed:', error);
  } finally {
    await pool.end();
  }
}

// Run export
exportDatabase().catch(console.error);
