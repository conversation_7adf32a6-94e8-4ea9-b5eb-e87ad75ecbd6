<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Complete Facebook Funnel</title>
    
    <!-- Facebook Pixel Code -->
    <script>
    !function(f,b,e,v,n,t,s)
    {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
    n.callMethod.apply(n,arguments):n.queue.push(arguments)};
    if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
    n.queue=[];t=b.createElement(e);t.async=!0;
    t.src=v;s=b.getElementsByTagName(e)[0];
    s.parentNode.insertBefore(t,s)}(window, document,'script',
    'https://connect.facebook.net/en_US/fbevents.js');
    
    fbq('init', '2478007082599449');
    fbq('track', 'PageView');
    </script>
    <noscript><img height="1" width="1" style="display:none"
    src="https://www.facebook.com/tr?id=2478007082599449&ev=PageView&noscript=1"
    /></noscript>
    <!-- End Facebook Pixel Code -->
    
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { background: white; padding: 30px; border-radius: 8px; max-width: 800px; margin: 0 auto; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        button { background: #1877f2; color: white; border: none; padding: 15px 25px; margin: 10px 5px; border-radius: 5px; cursor: pointer; font-size: 16px; }
        button:hover { background: #166fe5; }
        button:disabled { background: #ccc; cursor: not-allowed; }
        .step { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; background: #f9f9f9; }
        .step.active { background: #e8f4fd; border-color: #1877f2; }
        .step.completed { background: #d4edda; border-color: #28a745; }
        .log { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; font-family: monospace; font-size: 12px; max-height: 200px; overflow-y: auto; border: 1px solid #e9ecef; }
        .success { color: #28a745; font-weight: bold; }
        .info { color: #007bff; }
        .progress { background: #e9ecef; height: 20px; border-radius: 10px; margin: 20px 0; overflow: hidden; }
        .progress-bar { background: #1877f2; height: 100%; transition: width 0.3s ease; }
        h1 { color: #1877f2; text-align: center; }
        h2 { color: #333; border-bottom: 2px solid #1877f2; padding-bottom: 10px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Complete Facebook Funnel Test</h1>
        <p>This will simulate a complete customer booking journey and fire all Facebook events in sequence.</p>
        
        <div class="progress">
            <div class="progress-bar" id="progressBar" style="width: 0%"></div>
        </div>
        
        <button onclick="startCompleteTest()" id="startBtn">🚀 Start Complete Funnel Test</button>
        <button onclick="clearLog()" style="background: #dc3545;">Clear Log</button>
        
        <div id="eventLog" class="log">Ready to test complete Facebook conversion funnel...</div>
        
        <h2>Booking Journey Steps</h2>
        
        <div class="step" id="step1">
            <strong>Step 1:</strong> PageView - Customer lands on booking page
        </div>
        
        <div class="step" id="step2">
            <strong>Step 2:</strong> ViewContent + CheckAvailabilityPressed - Postal code validation (110001, Delhi)
        </div>
        
        <div class="step" id="step3">
            <strong>Step 3:</strong> InitiateCheckout + DateSelected - Date selection (Tomorrow)
        </div>
        
        <div class="step" id="step4">
            <strong>Step 4:</strong> AddPaymentInfo + TimeSlotSelected - Time slot selection (2:00 PM)
        </div>
        
        <div class="step" id="step5">
            <strong>Step 5:</strong> CompleteRegistration + OTPRequested - Phone OTP (+919876543210)
        </div>
        
        <div class="step" id="step6">
            <strong>Step 6:</strong> BeginCheckout + BookMyTrialPressed - Final booking button
        </div>
        
        <div class="step" id="step7">
            <strong>Step 7:</strong> Purchase + BookingConfirmed - Booking completion
        </div>
        
        <h2>Analytics Integration Test</h2>
        <p>After running the test, check these dashboards:</p>
        <ul>
            <li><strong>Facebook Events Manager:</strong> Verify all 8 standard events fired</li>
            <li><strong>Admin Analytics Dashboard:</strong> Check conversion funnel tracking</li>
            <li><strong>UTM Campaign Analytics:</strong> Verify session and event capture</li>
            <li><strong>Google Analytics:</strong> Cross-verify event tracking</li>
        </ul>
    </div>

    <script>
        let currentStep = 0;
        const totalSteps = 7;
        
        function log(message, type = 'info') {
            const logElement = document.getElementById('eventLog');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'success' ? 'success' : 'info';
            logElement.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        function updateProgress() {
            const progress = (currentStep / totalSteps) * 100;
            document.getElementById('progressBar').style.width = progress + '%';
            
            // Update step styling
            for (let i = 1; i <= totalSteps; i++) {
                const stepEl = document.getElementById(`step${i}`);
                if (i < currentStep) {
                    stepEl.className = 'step completed';
                } else if (i === currentStep) {
                    stepEl.className = 'step active';
                } else {
                    stepEl.className = 'step';
                }
            }
        }
        
        async function startCompleteTest() {
            document.getElementById('startBtn').disabled = true;
            log('🚀 Starting complete Facebook conversion funnel test...', 'success');
            currentStep = 0;
            updateProgress();
            
            // Generate test session data
            const testSessionId = `test_session_${Date.now()}`;
            const testUserId = `test_user_${Date.now()}`;
            
            // Step 1: PageView
            currentStep = 1;
            updateProgress();
            log('Step 1: Firing PageView event...');
            try {
                fbq('track', 'PageView', {
                    content_name: 'Booking Page Test',
                    content_category: 'Beauty'
                });
                
                // Send to backend
                await fetch('/api/marketing/facebook-event', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        event_name: 'PageView',
                        event_time: Math.floor(Date.now() / 1000),
                        user_data: { country: 'IN' },
                        custom_data: { content_name: 'Booking Page Test', content_category: 'Beauty' },
                        event_source_url: window.location.href,
                        action_source: 'website'
                    })
                });
                
                log('✅ PageView event fired successfully', 'success');
            } catch (e) {
                log(`❌ PageView failed: ${e.message}`);
            }
            
            await sleep(2000);
            
            // Step 2: ViewContent + CheckAvailabilityPressed
            currentStep = 2;
            updateProgress();
            log('Step 2: Firing ViewContent and CheckAvailabilityPressed events...');
            try {
                fbq('track', 'ViewContent', {
                    content_name: 'Postal Code Availability Check',
                    content_type: 'service',
                    content_ids: ['110001'],
                    postal_code: '110001',
                    city: 'Delhi',
                    state: 'Delhi'
                });
                
                fbq('trackCustom', 'CheckAvailabilityPressed', {
                    content_name: 'Check Availability Button Pressed',
                    postal_code: '110001',
                    city: 'Delhi',
                    state: 'Delhi'
                });
                
                log('✅ ViewContent and CheckAvailabilityPressed events fired', 'success');
            } catch (e) {
                log(`❌ Step 2 failed: ${e.message}`);
            }
            
            await sleep(2000);
            
            // Step 3: InitiateCheckout + DateSelected
            currentStep = 3;
            updateProgress();
            log('Step 3: Firing InitiateCheckout and DateSelected events...');
            try {
                const tomorrow = new Date();
                tomorrow.setDate(tomorrow.getDate() + 1);
                const dateStr = tomorrow.toISOString().split('T')[0];
                
                fbq('track', 'InitiateCheckout', {
                    content_name: 'Perfume Trial Booking Started',
                    content_type: 'appointment',
                    appointment_date: dateStr,
                    postal_code: '110001',
                    value: 0,
                    currency: 'INR'
                });
                
                fbq('trackCustom', 'DateSelected', {
                    content_name: 'Date Selected',
                    appointment_date: dateStr,
                    postal_code: '110001'
                });
                
                log('✅ InitiateCheckout and DateSelected events fired', 'success');
            } catch (e) {
                log(`❌ Step 3 failed: ${e.message}`);
            }
            
            await sleep(2000);
            
            // Step 4: AddPaymentInfo + TimeSlotSelected
            currentStep = 4;
            updateProgress();
            log('Step 4: Firing AddPaymentInfo and TimeSlotSelected events...');
            try {
                fbq('track', 'AddPaymentInfo', {
                    content_name: 'Time Slot Selected for Appointment',
                    content_type: 'appointment',
                    appointment_time: '14:00',
                    appointment_date: new Date().toISOString().split('T')[0],
                    postal_code: '110001'
                });
                
                fbq('trackCustom', 'TimeSlotSelected', {
                    content_name: 'Time Slot Selected',
                    appointment_time: '14:00',
                    appointment_date: new Date().toISOString().split('T')[0],
                    postal_code: '110001'
                });
                
                log('✅ AddPaymentInfo and TimeSlotSelected events fired', 'success');
            } catch (e) {
                log(`❌ Step 4 failed: ${e.message}`);
            }
            
            await sleep(2000);
            
            // Step 5: CompleteRegistration + OTPRequested
            currentStep = 5;
            updateProgress();
            log('Step 5: Firing CompleteRegistration and OTPRequested events...');
            try {
                fbq('track', 'CompleteRegistration', {
                    content_name: 'Customer Registration Started',
                    registration_method: 'whatsapp_otp',
                    phone_number: '+919876543210'
                });
                
                fbq('trackCustom', 'OTPRequested', {
                    content_name: 'OTP Requested',
                    phone_number: '+919876543210'
                });
                
                log('✅ CompleteRegistration and OTPRequested events fired', 'success');
            } catch (e) {
                log(`❌ Step 5 failed: ${e.message}`);
            }
            
            await sleep(2000);
            
            // Step 6: BeginCheckout + BookMyTrialPressed
            currentStep = 6;
            updateProgress();
            log('Step 6: Firing BeginCheckout and BookMyTrialPressed events...');
            try {
                fbq('track', 'BeginCheckout', {
                    content_name: 'Begin Booking Checkout',
                    content_type: 'appointment',
                    phone_number: '+919876543210'
                });
                
                fbq('trackCustom', 'BookMyTrialPressed', {
                    content_name: 'Book My Trial Button Clicked',
                    phone_number: '+919876543210'
                });
                
                log('✅ BeginCheckout and BookMyTrialPressed events fired', 'success');
            } catch (e) {
                log(`❌ Step 6 failed: ${e.message}`);
            }
            
            await sleep(2000);
            
            // Step 7: Purchase + BookingConfirmed
            currentStep = 7;
            updateProgress();
            log('Step 7: Firing Purchase and BookingConfirmed events...');
            try {
                const testBookingId = `test_booking_${Date.now()}`;
                
                fbq('track', 'Purchase', {
                    content_name: 'Perfume Trial Appointment Booked',
                    content_type: 'appointment',
                    content_ids: [testBookingId],
                    value: 0,
                    currency: 'INR',
                    booking_id: testBookingId,
                    postal_code: '110001',
                    city: 'Delhi',
                    state: 'Delhi',
                    appointment_date: new Date().toISOString().split('T')[0],
                    appointment_time: '14:00',
                    sales_rep: 'Test Rep',
                    phone_number: '+919876543210'
                });
                
                fbq('trackCustom', 'BookingConfirmed', {
                    content_name: 'Booking Confirmed',
                    booking_id: testBookingId,
                    postal_code: '110001',
                    city: 'Delhi',
                    state: 'Delhi',
                    appointment_date: new Date().toISOString().split('T')[0],
                    appointment_time: '14:00',
                    sales_rep: 'Test Rep',
                    phone_number: '+919876543210'
                });
                
                log('✅ Purchase and BookingConfirmed events fired', 'success');
            } catch (e) {
                log(`❌ Step 7 failed: ${e.message}`);
            }
            
            await sleep(1000);
            
            log('🎉 Complete Facebook conversion funnel test finished!', 'success');
            log('📊 Check Facebook Events Manager and Analytics Dashboard to verify tracking', 'info');
            
            document.getElementById('startBtn').disabled = false;
            document.getElementById('startBtn').textContent = '🔄 Run Test Again';
        }
        
        function sleep(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }
        
        function clearLog() {
            document.getElementById('eventLog').innerHTML = 'Event log cleared...';
        }
        
        // Initialize
        log('🔍 Facebook Funnel Test Tool loaded. Ready to simulate complete booking journey.', 'info');
    </script>
</body>
</html>