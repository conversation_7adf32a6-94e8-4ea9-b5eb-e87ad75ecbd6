# Facebook Conversion Funnel Setup Guide for Kult Perfume Trials

## Overview
This guide will help you set up end-to-end conversion tracking on Facebook to capture every user interaction from ad click to booking completion.

## Step 1: Facebook Business Manager Setup

### 1.1 Create/Access Business Manager
1. Go to [business.facebook.com](https://business.facebook.com)
2. Log in with your Facebook account
3. If you don't have a Business Manager, click "Create Account"
4. Add your business details:
   - Business Name: "Kult"
   - Your Name
   - Business Email: <EMAIL>

### 1.2 Add Your Website
1. In Business Manager, go to "Business Settings"
2. Click "Brand Safety" → "Domains"
3. Add your domain: `perfumestrial.kult.app`
4. Verify domain ownership (Facebook will provide verification methods)

## Step 2: Facebook Pixel Setup

### 2.1 Create Facebook Pixel
1. In Business Manager, go to "Events Manager"
2. Click "Connect Data Sources" → "Web"
3. Select "Facebook Pixel"
4. Name your pixel: "Kult Perfume Trial Pixel"
5. Enter your website URL: `https://perfumestrial.kult.app`

### 2.2 Install Pixel Code
Facebook will provide you with a pixel code that looks like this:
```html
<!-- Facebook Pixel Code -->
<script>
!function(f,b,e,v,n,t,s)
{if(f.fbq)return;n=f.fbq=function(){n.callMethod?
n.callMethod.apply(n,arguments):n.queue.push(arguments)};
if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
n.queue=[];t=b.createElement(e);t.async=!0;
t.src=v;s=b.getElementsByTagName(e)[0];
s.parentNode.insertBefore(t,s)}(window,document,'script',
'https://connect.facebook.net/en_US/fbevents.js');
fbq('init', 'YOUR_PIXEL_ID');
fbq('track', 'PageView');
</script>
<noscript>
<img height="1" width="1" 
src="https://www.facebook.com/tr?id=YOUR_PIXEL_ID&ev=PageView&noscript=1"/>
</noscript>
<!-- End Facebook Pixel Code -->
```

**I'll automatically add this to your website code once you provide the Pixel ID.**

## Step 3: Conversion Events Setup

### 3.1 Standard Events We'll Track
1. **PageView** - When users visit any page
2. **ViewContent** - When users view postal code entry page
3. **InitiateCheckout** - When users start booking process
4. **AddPaymentInfo** - When users enter contact details
5. **CompleteRegistration** - When OTP is verified
6. **Purchase** - When booking is confirmed

### 3.2 Custom Events We'll Track
1. **postal_code_entered** - When valid postal code is submitted
2. **date_selected** - When user picks appointment date
3. **time_slot_selected** - When user picks time slot
4. **otp_requested** - When OTP is sent
5. **booking_rescheduled** - When existing booking is modified
6. **booking_cancelled** - When booking is cancelled

## Step 4: Conversions API Setup (Server-Side Tracking)

### 4.1 Why Server-Side Tracking?
- More accurate data (bypasses ad blockers)
- Better iOS 14.5+ tracking
- Improved data quality
- Backup for pixel tracking

### 4.2 Access Token Setup
1. In Business Manager → "System Users"
2. Create new system user: "Kult API User"
3. Assign permissions:
   - Ads Management Standard Access
   - Business Management
4. Generate access token with these permissions:
   - ads_management
   - business_management
   - ads_read

## Step 5: Event Parameters and Custom Conversions

### 5.1 Event Parameters We'll Send
```javascript
// Example for booking completion
fbq('track', 'Purchase', {
  value: 0.00, // Free trial
  currency: 'INR',
  content_type: 'product',
  content_ids: ['perfume_trial'],
  content_name: 'Perfume Trial Booking',
  content_category: 'Beauty',
  postal_code: '110001',
  appointment_date: '2025-07-01',
  appointment_time: '14:00',
  sales_rep: 'rep_name'
});
```

### 5.2 Custom Conversions Setup
1. Go to Events Manager → "Custom Conversions"
2. Create conversions for:
   - **Trial Bookings** (Purchase events)
   - **Lead Generation** (CompleteRegistration events)
   - **High Intent** (AddPaymentInfo events)

## Step 6: Attribution Settings

### 6.1 Attribution Windows
1. In Events Manager → "Aggregated Event Measurement"
2. Configure attribution windows:
   - 1-day view, 7-day click (recommended)
   - Or 1-day view, 1-day click (strict)

### 6.2 Domain Verification Priority
1. Verify your domain in Business Manager
2. Set conversion priority for your events
3. Choose up to 8 conversion events to optimize for

## Step 7: Audience Creation

### 7.1 Website Custom Audiences
Create audiences for:
1. **All Website Visitors** (180 days)
2. **Postal Code Visitors** (30 days)
3. **Booking Started** (7 days)
4. **Booking Completed** (180 days)
5. **Booking Cancelled** (30 days)

### 7.2 Lookalike Audiences
Create lookalikes based on:
1. Booking completers (1% India)
2. High-value postal codes (1% Delhi/Gurgaon)

## Step 8: Campaign Optimization

### 8.1 Objective Selection
- **Awareness**: Reach, Brand Awareness
- **Consideration**: Traffic, Engagement
- **Conversion**: Conversions (optimize for Purchase event)

### 8.2 Optimization Events
- Primary: Purchase (booking completion)
- Secondary: CompleteRegistration (OTP verification)
- Volume campaigns: InitiateCheckout

## Step 9: Testing and Validation

### 9.1 Facebook Pixel Helper
1. Install Chrome extension "Facebook Pixel Helper"
2. Test on your website
3. Verify all events fire correctly

### 9.2 Events Manager Testing
1. Go to Events Manager → "Test Events"
2. Use Facebook Pixel Helper or test with your phone
3. Verify server-side events in Conversions API section

## Step 10: Implementation Checklist

### Before Launch:
- [ ] Business Manager account created
- [ ] Domain verified
- [ ] Pixel installed on website
- [ ] Conversions API configured
- [ ] Custom conversions created
- [ ] Attribution windows set
- [ ] Test events firing correctly

### After Launch:
- [ ] Monitor Events Manager daily
- [ ] Check pixel firing on all pages
- [ ] Verify conversion attribution
- [ ] Create custom audiences
- [ ] Set up lookalike audiences
- [ ] Optimize campaign delivery

## Next Steps

Once you provide me with:
1. **Facebook Pixel ID** (from step 2.1)
2. **Access Token** (from step 4.2)
3. **Ad Account ID** (format: act_1234567890)

I'll automatically implement all the tracking code, server-side events, and create a comprehensive conversion funnel dashboard that shows you exactly how users move through your booking process.

Would you like me to help you with any specific step, or shall we start with creating your Facebook Business Manager account?