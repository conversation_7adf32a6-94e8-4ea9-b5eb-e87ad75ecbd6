2025-06-26 17:39:25,015 - semgrep.run_scan - DEBUG - semgrep version 1.4.0
2025-06-26 17:39:25,020 - semgrep.git - DEBUG - Failed to get project url from 'git ls-remote': Command failed with exit code: 128
-----
Command failed with output:
fatal: No remote configured to list refs from.


Failed to run 'git ls-remote --get-url'. Possible reasons:

- the git binary is not available
- the current working directory is not a git repository
- the baseline commit is not a parent of the current commit
    (if you are running through semgrep-app, check if you are setting `SEMGREP_BRANCH` or `SEMGREP_BASELINE_COMMIT` properly)
- the current working directory is not marked as safe
    (fix with `git config --global --add safe.directory $(pwd)`)

Try running the command yourself to debug the issue.
2025-06-26 17:39:25,021 - semgrep.config_resolver - DEBUG - Loading local config from /home/<USER>/workspace/.config/.semgrep/semgrep_rules.json
2025-06-26 17:39:25,023 - semgrep.config_resolver - DEBUG - Done loading local config from /home/<USER>/workspace/.config/.semgrep/semgrep_rules.json
2025-06-26 17:39:25,027 - semgrep.config_resolver - DEBUG - Saving rules to /tmp/semgrep-em74c3z4.rules
2025-06-26 17:39:25,890 - semgrep.rule_lang - DEBUG - semgrep-core validation response: valid=True
2025-06-26 17:39:25,890 - semgrep.rule_lang - DEBUG - semgrep-core validation succeeded
2025-06-26 17:39:25,890 - semgrep.rule_lang - DEBUG - RPC validation succeeded
2025-06-26 17:39:25,891 - semgrep.config_resolver - DEBUG - loaded 1 configs in 0.8699438571929932
2025-06-26 17:39:25,991 - semgrep.run_scan - VERBOSE - running 714 rules from 1 config /home/<USER>/workspace/.config/.semgrep/semgrep_rules.json_0
2025-06-26 17:39:25,991 - semgrep.run_scan - VERBOSE - No .semgrepignore found. Using default .semgrepignore rules. See the docs for the list of default ignores: https://semgrep.dev/docs/cli-usage/#ignore-files
2025-06-26 17:39:25,993 - semgrep.run_scan - VERBOSE - Rules:
2025-06-26 17:39:25,993 - semgrep.run_scan - VERBOSE - <SKIPPED DATA (too many entries; use --max-log-list-entries)>
2025-06-26 17:39:26,681 - semgrep.core_runner - DEBUG - Passing whole rules directly to semgrep_core
2025-06-26 17:39:26,920 - semgrep.core_runner - DEBUG - Running Semgrep engine with command:
2025-06-26 17:39:26,920 - semgrep.core_runner - DEBUG - /tmp/_MEIOoBYsL/semgrep/bin/opengrep-core -json -rules /tmp/tmpow10ip7j.json -j 8 -targets /tmp/tmpnydqy2xi -timeout 5 -timeout_threshold 3 -max_memory 0 -fast
2025-06-26 17:39:43,310 - semgrep.core_runner - DEBUG - --- semgrep-core stderr ---
[00.08][[34mINFO[0m]: Executed as: /tmp/_MEIOoBYsL/semgrep/bin/opengrep-core -json -rules /tmp/tmpow10ip7j.json -j 8 -targets /tmp/tmpnydqy2xi -timeout 5 -timeout_threshold 3 -max_memory 0 -fast
[00.08][[34mINFO[0m]: Version: 1.4.0
[00.08][[34mINFO[0m]: Parsing rules in /tmp/tmpow10ip7j.json
[00.57][[34mINFO[0m]: scan: processing 434 files (skipping 0), with 456 rules (skipping 0 )
[01.47][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-require file: client/src/components/admin/marketing-dashboard.tsx func: MarketingDashboard:2206]
[0m[02.78][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.audit.remote-property-injection file: client/src/components/admin/marketing-dashboard.tsx func: MarketingDashboard:2206]
[0m[03.10][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.require-request file: client/src/components/admin/marketing-dashboard.tsx func: MarketingDashboard:2206]
[0m[03.35][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.injection.raw-html-format file: client/src/pages/booking-cancel.tsx func: BookingCancel:17248]
[0m[03.36][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.cors-misconfiguration file: client/src/components/admin/marketing-dashboard.tsx func: MarketingDashboard:2206]
[0m[04.58][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.injection.tainted-sql-string file: client/src/components/admin/users-management.tsx func: UsersManagement:23134]
[0m[04.87][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.injection.raw-html-format file: client/src/components/admin/users-management.tsx func: UsersManagement:23134]
[0m[06.16][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.x-frame-options-misconfiguration file: client/src/components/admin/theme-dashboard.tsx func: ThemeDashboard:29218]
[0m[06.36][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.require-request file: client/src/components/admin/theme-dashboard.tsx func: ThemeDashboard:29218]
[0m[07.66][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.detect-non-literal-require file: client/src/components/admin/settings-management-old.tsx func: SettingsManagement:54179]
[0m[07.67][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.injection.raw-html-format file: client/src/components/admin/bookings-table.tsx func: BookingsTable:48405]
[0m[08.05][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.injection.raw-html-format file: client/src/components/admin/settings-management-old.tsx func: SettingsManagement:54179]
[0m[09.84][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.typescript.react.security.audit.react-href-var file: server/routes.ts func: registerRoutes:66604]
[0m[10.66][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.lang.security.audit.code-string-concat file: server/routes.ts func: registerRoutes:66604]
[0m[11.27][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.injection.raw-html-format file: server/routes.ts func: registerRoutes:66604]
[0m[11.55][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.injection.raw-html-format file: server/routes.ts func: registerRoutes:66604]
[0m[11.97][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.audit.xss.direct-response-write file: server/routes.ts func: registerRoutes:66604]
[0m[12.21][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.audit.xss.direct-response-write file: server/routes.ts func: registerRoutes:66604]
[0m[13.26][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.audit.express-open-redirect file: server/routes.ts func: registerRoutes:66604]
[0m[13.61][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.express-wkhtmltoimage-injection file: server/routes.ts func: registerRoutes:66604]
[0m[13.87][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.express-wkhtmltoimage-injection file: server/routes.ts func: registerRoutes:66604]
[0m[14.28][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.x-frame-options-misconfiguration file: server/routes.ts func: registerRoutes:66604]
[0m[14.53][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.x-frame-options-misconfiguration file: server/routes.ts func: registerRoutes:66604]
[0m[14.82][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.require-request file: server/routes.ts func: registerRoutes:66604]
[0m[15.17][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.require-request file: server/routes.ts func: registerRoutes:66604]
[0m[15.68][[33mWARNING[0m]: Fixpoint timeout while performing taint analysis [rule: config..semgrep.vendored-rules.javascript.express.security.cors-misconfiguration file: server/routes.ts func: registerRoutes:66604]
[0m--- end semgrep-core stderr ---
2025-06-26 17:39:43,392 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('client/index.html'), 'config..semgrep.vendored-rules.html.security.audit.missing-integrity') match_id = 0ee74fd49637bebe183eca7188dbde26e386314e62cc2e7ba1ee60b377b638243fcd84e6c6fa04886198ccacfa6a711bfbcc61a28f9ddc913d5b3c53083cbc90_0
2025-06-26 17:39:43,393 - semgrep.rule_match - DEBUG - match_key = (' type="text/javascript" src="https://replit.com/public/js/replit-dev-banner.js" (?is).*integrity= (google-analytics\\.com|fonts\\.googleapis\\.com|fonts\\.gstatic\\.com|googletagmanager\\.com) .*rel\\s*=\\s*[\'"]?preconnect.* href="... :// ..." href="//..." href=\'... :// ...\' href=\'//...\' src="... :// ..." src="//..." src=\'... :// ...\' src=\'//...\' <link  type="text/javascript" src="https://replit.com/public/js/replit-dev-banner.js" > <script  type="text/javascript" src="https://replit.com/public/js/replit-dev-banner.js" >...</script>', PosixPath('client/index.html'), 'config..semgrep.vendored-rules.html.security.audit.missing-integrity') match_id = 0728b64e224596592d04447ba8a642ff94e1fb9fcc07be26d49dc7e7f6898e638ad16ffcaca086932c58f4c6400fe32603323afef02cf9bfebcb0e4a53562a40_0
2025-06-26 17:39:43,393 - semgrep.rule_match - DEBUG - match_key = (' type="text/javascript" src="https://replit.com/public/js/replit-dev-banner.js" (?is).*integrity= (google-analytics\\.com|fonts\\.googleapis\\.com|fonts\\.gstatic\\.com|googletagmanager\\.com) .*rel\\s*=\\s*[\'"]?preconnect.* href="... :// ..." href="//..." href=\'... :// ...\' href=\'//...\' src="... :// ..." src="//..." src=\'... :// ...\' src=\'//...\' <link  type="text/javascript" src="https://replit.com/public/js/replit-dev-banner.js" > <script  type="text/javascript" src="https://replit.com/public/js/replit-dev-banner.js" >...</script>', PosixPath('client/index.html'), 'config..semgrep.vendored-rules.html.security.audit.missing-integrity') match_id = 0728b64e224596592d04447ba8a642ff94e1fb9fcc07be26d49dc7e7f6898e638ad16ffcaca086932c58f4c6400fe32603323afef02cf9bfebcb0e4a53562a40_0
2025-06-26 17:39:43,394 - semgrep.rule_match - DEBUG - match_key = (' type="text/javascript" src="https://replit.com/public/js/replit-dev-banner.js" (?is).*integrity= (google-analytics\\.com|fonts\\.googleapis\\.com|fonts\\.gstatic\\.com|googletagmanager\\.com) .*rel\\s*=\\s*[\'"]?preconnect.* href="... :// ..." href="//..." href=\'... :// ...\' href=\'//...\' src="... :// ..." src="//..." src=\'... :// ...\' src=\'//...\' <link  type="text/javascript" src="https://replit.com/public/js/replit-dev-banner.js" > <script  type="text/javascript" src="https://replit.com/public/js/replit-dev-banner.js" >...</script>', PosixPath('client/index.html'), 'config..semgrep.vendored-rules.html.security.audit.missing-integrity') match_id = 0728b64e224596592d04447ba8a642ff94e1fb9fcc07be26d49dc7e7f6898e638ad16ffcaca086932c58f4c6400fe32603323afef02cf9bfebcb0e4a53562a40_0
2025-06-26 17:39:43,394 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('server/routes.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = e24e3288a32c9ad4921da20b227a0587a0596856ea79e7c29c73137fde834094f382cf9604ac765ec32471f83cf1196712bec140c3a2b18c933a6c2a040a826f_0
2025-06-26 17:39:43,395 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n ``Significant changes detected for booking id:` $UTIL = require(\'util\')\n...\n $UTIL.format(``Significant changes detected for booking id:`,{dateChanged originalBooking.date!==updatedBooking.date timeChanged originalBooking.timeSlot!==updatedBooking.timeSlot repChanged originalBooking.repAssigned!==updatedBooking.repAssigned statusChanged originalBooking.status!==updatedBooking.status newStatus updatedBooking.status},...)\n console.log(``Significant changes detected for booking id:`,{dateChanged originalBooking.date!==updatedBooking.date timeChanged originalBooking.timeSlot!==updatedBooking.timeSlot repChanged originalBooking.repAssigned!==updatedBooking.repAssigned statusChanged originalBooking.status!==updatedBooking.status newStatus updatedBooking.status},...)\n', PosixPath('server/routes.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = 898719b94d36dcbe1289e10d76e164c77bce285830b890dc33c64ae5fe675659155e0c02a2fbbc3f5a2811051c30dd82fb40b51afcf2c90cfa59a241cfc8693f_0
2025-06-26 17:39:43,396 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n ``Significant changes detected for booking id:` $UTIL = require(\'util\')\n...\n $UTIL.format(``Significant changes detected for booking id:`,{dateChanged originalBooking.date!==updatedBooking.date timeChanged originalBooking.timeSlot!==updatedBooking.timeSlot repChanged originalBooking.repAssigned!==updatedBooking.repAssigned statusChanged originalBooking.status!==updatedBooking.status newStatus updatedBooking.status},...)\n console.log(``Significant changes detected for booking id:`,{dateChanged originalBooking.date!==updatedBooking.date timeChanged originalBooking.timeSlot!==updatedBooking.timeSlot repChanged originalBooking.repAssigned!==updatedBooking.repAssigned statusChanged originalBooking.status!==updatedBooking.status newStatus updatedBooking.status},...)\n', PosixPath('server/routes.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = 898719b94d36dcbe1289e10d76e164c77bce285830b890dc33c64ae5fe675659155e0c02a2fbbc3f5a2811051c30dd82fb40b51afcf2c90cfa59a241cfc8693f_0
2025-06-26 17:39:43,396 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n ``Significant changes detected for booking id:` $UTIL = require(\'util\')\n...\n $UTIL.format(``Significant changes detected for booking id:`,{dateChanged originalBooking.date!==updatedBooking.date timeChanged originalBooking.timeSlot!==updatedBooking.timeSlot repChanged originalBooking.repAssigned!==updatedBooking.repAssigned statusChanged originalBooking.status!==updatedBooking.status newStatus updatedBooking.status},...)\n console.log(``Significant changes detected for booking id:`,{dateChanged originalBooking.date!==updatedBooking.date timeChanged originalBooking.timeSlot!==updatedBooking.timeSlot repChanged originalBooking.repAssigned!==updatedBooking.repAssigned statusChanged originalBooking.status!==updatedBooking.status newStatus updatedBooking.status},...)\n', PosixPath('server/routes.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = 898719b94d36dcbe1289e10d76e164c77bce285830b890dc33c64ae5fe675659155e0c02a2fbbc3f5a2811051c30dd82fb40b51afcf2c90cfa59a241cfc8693f_0
2025-06-26 17:39:43,397 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('server/routes.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = e24e3288a32c9ad4921da20b227a0587a0596856ea79e7c29c73137fde834094f382cf9604ac765ec32471f83cf1196712bec140c3a2b18c933a6c2a040a826f_0
2025-06-26 17:39:43,398 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n ``Failed to send email notificationType notification:` $UTIL = require(\'util\')\n...\n $UTIL.format(``Failed to send email notificationType notification:`,emailError,...)\n console.error(``Failed to send email notificationType notification:`,emailError,...)\n', PosixPath('server/routes.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = 2e8968ff66bba4758d5552021a546e1e334a2220bb000299adebb420acc2f7250bcec73f16f2d5394aaf32a2c0a54dcbb5a9a9a3da16bd0281083321e6fd2a06_0
2025-06-26 17:39:43,398 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n ``Failed to send email notificationType notification:` $UTIL = require(\'util\')\n...\n $UTIL.format(``Failed to send email notificationType notification:`,emailError,...)\n console.error(``Failed to send email notificationType notification:`,emailError,...)\n', PosixPath('server/routes.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = 2e8968ff66bba4758d5552021a546e1e334a2220bb000299adebb420acc2f7250bcec73f16f2d5394aaf32a2c0a54dcbb5a9a9a3da16bd0281083321e6fd2a06_0
2025-06-26 17:39:43,399 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n ``Failed to send email notificationType notification:` $UTIL = require(\'util\')\n...\n $UTIL.format(``Failed to send email notificationType notification:`,emailError,...)\n console.error(``Failed to send email notificationType notification:`,emailError,...)\n', PosixPath('server/routes.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = 2e8968ff66bba4758d5552021a546e1e334a2220bb000299adebb420acc2f7250bcec73f16f2d5394aaf32a2c0a54dcbb5a9a9a3da16bd0281083321e6fd2a06_0
2025-06-26 17:39:43,399 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('server/routes.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = e24e3288a32c9ad4921da20b227a0587a0596856ea79e7c29c73137fde834094f382cf9604ac765ec32471f83cf1196712bec140c3a2b18c933a6c2a040a826f_0
2025-06-26 17:39:43,400 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n ``Failed to send SMS notificationType notification:` $UTIL = require(\'util\')\n...\n $UTIL.format(``Failed to send SMS notificationType notification:`,smsError,...)\n console.error(``Failed to send SMS notificationType notification:`,smsError,...)\n', PosixPath('server/routes.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = fd2dba56d635064f413821a04eeda39850972116c1d1282bdac288978e68b93f8d89bd305536aea50bd8184ce25bc961e09ee9b3c91870b4f2eb8fa778683a13_0
2025-06-26 17:39:43,400 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n ``Failed to send SMS notificationType notification:` $UTIL = require(\'util\')\n...\n $UTIL.format(``Failed to send SMS notificationType notification:`,smsError,...)\n console.error(``Failed to send SMS notificationType notification:`,smsError,...)\n', PosixPath('server/routes.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = fd2dba56d635064f413821a04eeda39850972116c1d1282bdac288978e68b93f8d89bd305536aea50bd8184ce25bc961e09ee9b3c91870b4f2eb8fa778683a13_0
2025-06-26 17:39:43,401 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n ``Failed to send SMS notificationType notification:` $UTIL = require(\'util\')\n...\n $UTIL.format(``Failed to send SMS notificationType notification:`,smsError,...)\n console.error(``Failed to send SMS notificationType notification:`,smsError,...)\n', PosixPath('server/routes.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = fd2dba56d635064f413821a04eeda39850972116c1d1282bdac288978e68b93f8d89bd305536aea50bd8184ce25bc961e09ee9b3c91870b4f2eb8fa778683a13_0
2025-06-26 17:39:43,402 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('server/routes.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.html-in-template-string') match_id = 252d5e9e3e03042b085042d3b73c68eb69c19f4051b0e0598394b2319328aec1c19bc319257e0271964c432f360d6d25c2037663d24d0ece653155f447f28f20_0
2025-06-26 17:39:43,403 - semgrep.rule_match - DEBUG - match_key = (' hours away.</p>\n              <p>Please contact support for assistance.</p>\n            </body>\n          </html>\n         .*</?[a-zA-Z] ` hours away.</p>\n              <p>Please contact support for assistance.</p>\n            </body>\n          </html>\n        ${settings.cancelHoursLimit}...`\n `...${settings.cancelHoursLimit} hours away.</p>\n              <p>Please contact support for assistance.</p>\n            </body>\n          </html>\n        `\n', PosixPath('server/routes.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.html-in-template-string') match_id = a201666907b04fd1a11c4e0b81ecfc7e2f1a7fab832aa8f23dbaeacaeb2122d332427ab98a900c4642e58c6ec870244f8a3fc3e569457d224f64e49cc528de80_0
2025-06-26 17:39:43,403 - semgrep.rule_match - DEBUG - match_key = (' hours away.</p>\n              <p>Please contact support for assistance.</p>\n            </body>\n          </html>\n         .*</?[a-zA-Z] ` hours away.</p>\n              <p>Please contact support for assistance.</p>\n            </body>\n          </html>\n        ${settings.cancelHoursLimit}...`\n `...${settings.cancelHoursLimit} hours away.</p>\n              <p>Please contact support for assistance.</p>\n            </body>\n          </html>\n        `\n', PosixPath('server/routes.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.html-in-template-string') match_id = a201666907b04fd1a11c4e0b81ecfc7e2f1a7fab832aa8f23dbaeacaeb2122d332427ab98a900c4642e58c6ec870244f8a3fc3e569457d224f64e49cc528de80_0
2025-06-26 17:39:43,404 - semgrep.rule_match - DEBUG - match_key = (' hours away.</p>\n              <p>Please contact support for assistance.</p>\n            </body>\n          </html>\n         .*</?[a-zA-Z] ` hours away.</p>\n              <p>Please contact support for assistance.</p>\n            </body>\n          </html>\n        ${settings.cancelHoursLimit}...`\n `...${settings.cancelHoursLimit} hours away.</p>\n              <p>Please contact support for assistance.</p>\n            </body>\n          </html>\n        `\n', PosixPath('server/routes.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.html-in-template-string') match_id = a201666907b04fd1a11c4e0b81ecfc7e2f1a7fab832aa8f23dbaeacaeb2122d332427ab98a900c4642e58c6ec870244f8a3fc3e569457d224f64e49cc528de80_0
2025-06-26 17:39:43,405 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('server/routes.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.html-in-template-string') match_id = 252d5e9e3e03042b085042d3b73c68eb69c19f4051b0e0598394b2319328aec1c19bc319257e0271964c432f360d6d25c2037663d24d0ece653155f447f28f20_0
2025-06-26 17:39:43,407 - semgrep.rule_match - DEBUG - match_key = ('" class="btn">Book New Appointment</a>\n            </div>\n          </body>\n        </html>\n       .*</?[a-zA-Z] `" class="btn">Book New Appointment</a>\n            </div>\n          </body>\n        </html>\n      ${booking.postalCode}...`\n `...${booking.postalCode}" class="btn">Book New Appointment</a>\n            </div>\n          </body>\n        </html>\n      `\n', PosixPath('server/routes.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.html-in-template-string') match_id = f2493a847101d6118f089529b3e9e3d2c4a5f6ca4e064b72ffbe51b928350141917f73d590a5bf5352d2171198c53c7e51963182ae47d7a14f60119116777749_0
2025-06-26 17:39:43,408 - semgrep.rule_match - DEBUG - match_key = ('" class="btn">Book New Appointment</a>\n            </div>\n          </body>\n        </html>\n       .*</?[a-zA-Z] `" class="btn">Book New Appointment</a>\n            </div>\n          </body>\n        </html>\n      ${booking.postalCode}...`\n `...${booking.postalCode}" class="btn">Book New Appointment</a>\n            </div>\n          </body>\n        </html>\n      `\n', PosixPath('server/routes.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.html-in-template-string') match_id = f2493a847101d6118f089529b3e9e3d2c4a5f6ca4e064b72ffbe51b928350141917f73d590a5bf5352d2171198c53c7e51963182ae47d7a14f60119116777749_0
2025-06-26 17:39:43,409 - semgrep.rule_match - DEBUG - match_key = ('" class="btn">Book New Appointment</a>\n            </div>\n          </body>\n        </html>\n       .*</?[a-zA-Z] `" class="btn">Book New Appointment</a>\n            </div>\n          </body>\n        </html>\n      ${booking.postalCode}...`\n `...${booking.postalCode}" class="btn">Book New Appointment</a>\n            </div>\n          </body>\n        </html>\n      `\n', PosixPath('server/routes.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.html-in-template-string') match_id = f2493a847101d6118f089529b3e9e3d2c4a5f6ca4e064b72ffbe51b928350141917f73d590a5bf5352d2171198c53c7e51963182ae47d7a14f60119116777749_0
2025-06-26 17:39:43,410 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('server/services/email.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.html-in-template-string') match_id = 5bd97a89f8e2218b66bdab0a3867d4d883059bceb8e833827579ee77b2226de32d5ec553d8b43df270f420ee2754a481c328863699ec85987db5d0f5f9bde2e7_0
2025-06-26 17:39:43,410 - semgrep.rule_match - DEBUG - match_key = ('" style="display: inline-block; padding: 12px 24px; margin: 0 10px; background-color: #EF4444; color: white; text-decoration: none; border-radius: 8px; font-weight: bold;">Cancel</a>\n      </div>\n     .*</?[a-zA-Z] `" style="display: inline-block; padding: 12px 24px; margin: 0 10px; background-color: #EF4444; color: white; text-decoration: none; border-radius: 8px; font-weight: bold;">Cancel</a>\n      </div>\n    ${cancelUrl}...`\n `...${cancelUrl}" style="display: inline-block; padding: 12px 24px; margin: 0 10px; background-color: #EF4444; color: white; text-decoration: none; border-radius: 8px; font-weight: bold;">Cancel</a>\n      </div>\n    `\n', PosixPath('server/services/email.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.html-in-template-string') match_id = cc88fc169bda64d3d9cf6e7cfc01a2a790bb1e5819dd116d2daa993b7d63860e0f298c394963ae4ae63fc204634771c6351e625d473fac726842464d5c6ce662_0
2025-06-26 17:39:43,411 - semgrep.rule_match - DEBUG - match_key = ('" style="display: inline-block; padding: 12px 24px; margin: 0 10px; background-color: #EF4444; color: white; text-decoration: none; border-radius: 8px; font-weight: bold;">Cancel</a>\n      </div>\n     .*</?[a-zA-Z] `" style="display: inline-block; padding: 12px 24px; margin: 0 10px; background-color: #EF4444; color: white; text-decoration: none; border-radius: 8px; font-weight: bold;">Cancel</a>\n      </div>\n    ${cancelUrl}...`\n `...${cancelUrl}" style="display: inline-block; padding: 12px 24px; margin: 0 10px; background-color: #EF4444; color: white; text-decoration: none; border-radius: 8px; font-weight: bold;">Cancel</a>\n      </div>\n    `\n', PosixPath('server/services/email.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.html-in-template-string') match_id = cc88fc169bda64d3d9cf6e7cfc01a2a790bb1e5819dd116d2daa993b7d63860e0f298c394963ae4ae63fc204634771c6351e625d473fac726842464d5c6ce662_0
2025-06-26 17:39:43,411 - semgrep.rule_match - DEBUG - match_key = ('" style="display: inline-block; padding: 12px 24px; margin: 0 10px; background-color: #EF4444; color: white; text-decoration: none; border-radius: 8px; font-weight: bold;">Cancel</a>\n      </div>\n     .*</?[a-zA-Z] `" style="display: inline-block; padding: 12px 24px; margin: 0 10px; background-color: #EF4444; color: white; text-decoration: none; border-radius: 8px; font-weight: bold;">Cancel</a>\n      </div>\n    ${cancelUrl}...`\n `...${cancelUrl}" style="display: inline-block; padding: 12px 24px; margin: 0 10px; background-color: #EF4444; color: white; text-decoration: none; border-radius: 8px; font-weight: bold;">Cancel</a>\n      </div>\n    `\n', PosixPath('server/services/email.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.html-in-template-string') match_id = cc88fc169bda64d3d9cf6e7cfc01a2a790bb1e5819dd116d2daa993b7d63860e0f298c394963ae4ae63fc204634771c6351e625d473fac726842464d5c6ce662_0
2025-06-26 17:39:43,412 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('server/services/sms.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = 7d3316ce1cf3933e66a0ae8d3ac987902baf51473928a5be7364aaaebf701e69e4f99701f8f1f50d8a4d0509a2caeab03dc9e8eee40adcfcf067baee926bfd39_0
2025-06-26 17:39:43,412 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n ``Failed to send WhatsApp templateType to formattedPhone:` $UTIL = require(\'util\')\n...\n $UTIL.format(``Failed to send WhatsApp templateType to formattedPhone:`,result?.?.message||\'Unknown error\',...)\n console.error(``Failed to send WhatsApp templateType to formattedPhone:`,result?.?.message||\'Unknown error\',...)\n', PosixPath('server/services/sms.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = 21f54e9e042733770cdd4ba6ed611da1291edcbf65668677b6c39c3830b3b0ddbdce46248b741835bafdad0d74ce2f9233f5a3dcad5c3b90f950b3b01f55cb2a_0
2025-06-26 17:39:43,412 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n ``Failed to send WhatsApp templateType to formattedPhone:` $UTIL = require(\'util\')\n...\n $UTIL.format(``Failed to send WhatsApp templateType to formattedPhone:`,result?.?.message||\'Unknown error\',...)\n console.error(``Failed to send WhatsApp templateType to formattedPhone:`,result?.?.message||\'Unknown error\',...)\n', PosixPath('server/services/sms.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = 21f54e9e042733770cdd4ba6ed611da1291edcbf65668677b6c39c3830b3b0ddbdce46248b741835bafdad0d74ce2f9233f5a3dcad5c3b90f950b3b01f55cb2a_0
2025-06-26 17:39:43,413 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n ``Failed to send WhatsApp templateType to formattedPhone:` $UTIL = require(\'util\')\n...\n $UTIL.format(``Failed to send WhatsApp templateType to formattedPhone:`,result?.?.message||\'Unknown error\',...)\n console.error(``Failed to send WhatsApp templateType to formattedPhone:`,result?.?.message||\'Unknown error\',...)\n', PosixPath('server/services/sms.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = 21f54e9e042733770cdd4ba6ed611da1291edcbf65668677b6c39c3830b3b0ddbdce46248b741835bafdad0d74ce2f9233f5a3dcad5c3b90f950b3b01f55cb2a_0
2025-06-26 17:39:43,413 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('server/services/sms.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = 7d3316ce1cf3933e66a0ae8d3ac987902baf51473928a5be7364aaaebf701e69e4f99701f8f1f50d8a4d0509a2caeab03dc9e8eee40adcfcf067baee926bfd39_0
2025-06-26 17:39:43,414 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n ``Error sending WhatsApp templateType:` $UTIL = require(\'util\')\n...\n $UTIL.format(``Error sending WhatsApp templateType:`,error,...)\n console.error(``Error sending WhatsApp templateType:`,error,...)\n', PosixPath('server/services/sms.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = 6d6948301856aeea75e8d2d636bdc81b11235581e0765844b2d847f1d25d1ba21e75f95cb7651ff7516967517472873f1212defe11383eb2160b2140823f832c_0
2025-06-26 17:39:43,414 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n ``Error sending WhatsApp templateType:` $UTIL = require(\'util\')\n...\n $UTIL.format(``Error sending WhatsApp templateType:`,error,...)\n console.error(``Error sending WhatsApp templateType:`,error,...)\n', PosixPath('server/services/sms.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = 6d6948301856aeea75e8d2d636bdc81b11235581e0765844b2d847f1d25d1ba21e75f95cb7651ff7516967517472873f1212defe11383eb2160b2140823f832c_0
2025-06-26 17:39:43,415 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n ``Error sending WhatsApp templateType:` $UTIL = require(\'util\')\n...\n $UTIL.format(``Error sending WhatsApp templateType:`,error,...)\n console.error(``Error sending WhatsApp templateType:`,error,...)\n', PosixPath('server/services/sms.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = 6d6948301856aeea75e8d2d636bdc81b11235581e0765844b2d847f1d25d1ba21e75f95cb7651ff7516967517472873f1212defe11383eb2160b2140823f832c_0
2025-06-26 17:39:43,415 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('server/services/twilio-whatsapp.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = 6f1f2c350f14bea19c1b65924bf95d49f2ed688ae9852b85787ebba48bb104fe0e54ccedad6afe4e2c149e5e42f975e8a33d6592b1e7d278de85b4b44dd87843_0
2025-06-26 17:39:43,416 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n ``Sending WhatsApp templateType with variables:` $UTIL = require(\'util\')\n...\n $UTIL.format(``Sending WhatsApp templateType with variables:`,templateVariables,...)\n console.log(``Sending WhatsApp templateType with variables:`,templateVariables,...)\n', PosixPath('server/services/twilio-whatsapp.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = 572ce4f99596c34acf396ac280d8c98804e7c1da5c50cc0c3cc7adb47e3a572a0dd00a17c35feff8637ab59ba10f82d84a3d614541311b01205428113139166f_0
2025-06-26 17:39:43,416 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n ``Sending WhatsApp templateType with variables:` $UTIL = require(\'util\')\n...\n $UTIL.format(``Sending WhatsApp templateType with variables:`,templateVariables,...)\n console.log(``Sending WhatsApp templateType with variables:`,templateVariables,...)\n', PosixPath('server/services/twilio-whatsapp.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = 572ce4f99596c34acf396ac280d8c98804e7c1da5c50cc0c3cc7adb47e3a572a0dd00a17c35feff8637ab59ba10f82d84a3d614541311b01205428113139166f_0
2025-06-26 17:39:43,417 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n ``Sending WhatsApp templateType with variables:` $UTIL = require(\'util\')\n...\n $UTIL.format(``Sending WhatsApp templateType with variables:`,templateVariables,...)\n console.log(``Sending WhatsApp templateType with variables:`,templateVariables,...)\n', PosixPath('server/services/twilio-whatsapp.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = 572ce4f99596c34acf396ac280d8c98804e7c1da5c50cc0c3cc7adb47e3a572a0dd00a17c35feff8637ab59ba10f82d84a3d614541311b01205428113139166f_0
2025-06-26 17:39:43,417 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('server/services/twilio-whatsapp.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = 6f1f2c350f14bea19c1b65924bf95d49f2ed688ae9852b85787ebba48bb104fe0e54ccedad6afe4e2c149e5e42f975e8a33d6592b1e7d278de85b4b44dd87843_0
2025-06-26 17:39:43,418 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n ``WhatsApp templateType notification sent:` $UTIL = require(\'util\')\n...\n $UTIL.format(``WhatsApp templateType notification sent:`,message.sid,...)\n console.log(``WhatsApp templateType notification sent:`,message.sid,...)\n', PosixPath('server/services/twilio-whatsapp.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = 43d786e7c8ebdccbfcf6e9a26c42bb11687bfd5547780e8c35a823143ecda3a2e05c426d093d96a8e501de8631e3b1483f4bfa07eaf617d3b9273c82b9e8c270_0
2025-06-26 17:39:43,418 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n ``WhatsApp templateType notification sent:` $UTIL = require(\'util\')\n...\n $UTIL.format(``WhatsApp templateType notification sent:`,message.sid,...)\n console.log(``WhatsApp templateType notification sent:`,message.sid,...)\n', PosixPath('server/services/twilio-whatsapp.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = 43d786e7c8ebdccbfcf6e9a26c42bb11687bfd5547780e8c35a823143ecda3a2e05c426d093d96a8e501de8631e3b1483f4bfa07eaf617d3b9273c82b9e8c270_0
2025-06-26 17:39:43,419 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n ``WhatsApp templateType notification sent:` $UTIL = require(\'util\')\n...\n $UTIL.format(``WhatsApp templateType notification sent:`,message.sid,...)\n console.log(``WhatsApp templateType notification sent:`,message.sid,...)\n', PosixPath('server/services/twilio-whatsapp.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = 43d786e7c8ebdccbfcf6e9a26c42bb11687bfd5547780e8c35a823143ecda3a2e05c426d093d96a8e501de8631e3b1483f4bfa07eaf617d3b9273c82b9e8c270_0
2025-06-26 17:39:43,419 - semgrep.rule_match - DEBUG - match_key = ('', PosixPath('server/services/twilio-whatsapp.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = 6f1f2c350f14bea19c1b65924bf95d49f2ed688ae9852b85787ebba48bb104fe0e54ccedad6afe4e2c149e5e42f975e8a33d6592b1e7d278de85b4b44dd87843_0
2025-06-26 17:39:43,420 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n ``Sending CX follow-up message to formattedPhone with variables:` $UTIL = require(\'util\')\n...\n $UTIL.format(``Sending CX follow-up message to formattedPhone with variables:`,templateVariables,...)\n console.log(``Sending CX follow-up message to formattedPhone with variables:`,templateVariables,...)\n', PosixPath('server/services/twilio-whatsapp.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = 13e8f355c8cafa0e592eddf834f90518df68e21733de19303450749dc87280ec56b467679384e566e806ea0f3491e97821fb81dfd2bb73c3ea9531a406842b34_0
2025-06-26 17:39:43,420 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n ``Sending CX follow-up message to formattedPhone with variables:` $UTIL = require(\'util\')\n...\n $UTIL.format(``Sending CX follow-up message to formattedPhone with variables:`,templateVariables,...)\n console.log(``Sending CX follow-up message to formattedPhone with variables:`,templateVariables,...)\n', PosixPath('server/services/twilio-whatsapp.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = 13e8f355c8cafa0e592eddf834f90518df68e21733de19303450749dc87280ec56b467679384e566e806ea0f3491e97821fb81dfd2bb73c3ea9531a406842b34_0
2025-06-26 17:39:43,421 - semgrep.rule_match - DEBUG - match_key = ('"..." + "..."\n $X + $Y $X.concat($Y) `...${...}...`\n $X.concat("...")\n ``Sending CX follow-up message to formattedPhone with variables:` $UTIL = require(\'util\')\n...\n $UTIL.format(``Sending CX follow-up message to formattedPhone with variables:`,templateVariables,...)\n console.log(``Sending CX follow-up message to formattedPhone with variables:`,templateVariables,...)\n', PosixPath('server/services/twilio-whatsapp.ts'), 'config..semgrep.vendored-rules.javascript.lang.security.audit.unsafe-formatstring') match_id = 13e8f355c8cafa0e592eddf834f90518df68e21733de19303450749dc87280ec56b467679384e566e806ea0f3491e97821fb81dfd2bb73c3ea9531a406842b34_0
2025-06-26 17:39:43,422 - semgrep.core_runner - DEBUG - semgrep ran in 0:00:16.741477 on 165 files
2025-06-26 17:39:43,423 - semgrep.core_runner - DEBUG - findings summary: 4 warning, 0 error, 8 info
2025-06-26 17:39:43,426 - semgrep.app.auth - DEBUG - Getting API token from settings file
2025-06-26 17:39:43,426 - semgrep.app.auth - DEBUG - No API token found in settings file
2025-06-26 17:39:43,555 - semgrep.output - VERBOSE - 
========================================
Files skipped:
========================================

  Always skipped by Opengrep:

   • <none>

  Skipped by .gitignore:
  (Disable by passing --no-git-ignore)

   • <all files not listed by `git ls-files` were skipped>

  Skipped by .semgrepignore:
  - https://semgrep.dev/docs/ignoring-files-folders-code/#understand-semgrep-defaults

   • <none>

  Skipped by --include patterns:

   • <none>

  Skipped by --exclude patterns:

   • <none>

  Files skipped due to insufficient read permissions:

   • <none>

  Skipped by limiting to files smaller than 1000000 bytes:
  (Adjust with the --max-target-bytes flag)

   • attached_assets/IMG_0818_1750239339267.PNG
   • attached_assets/IMG_0886_1750927386285.PNG
   • attached_assets/Screenshot 2025-06-18 at 1.23.13 PM_1750233207035.png
   • attached_assets/Screenshot 2025-06-18 at 1.26.17 PM_1750233385994.png
   • attached_assets/Screenshot 2025-06-18 at 12.12.19 PM_1750230703507.png
   • attached_assets/Screenshot 2025-06-18 at 2.02.20 PM_1750235631225.png
   • attached_assets/Screenshot 2025-06-18 at 2.03.00 PM_1750235670706.png
   • attached_assets/Screenshot 2025-06-18 at 2.03.07 PM_1750235659757.png
   • attached_assets/Screenshot 2025-06-18 at 3.24.14 PM_1750240706263.png
   • attached_assets/Screenshot 2025-06-18 at 3.43.41 PM_1750241631492.png
   • attached_assets/Screenshot 2025-06-18 at 3.46.58 PM_1750241854751.png
   • attached_assets/Screenshot 2025-06-18 at 4.06.13 PM_1750242993728.png

  Partially analyzed due to parsing or internal Opengrep errors

   • tailwind.config.ts (1 lines skipped)

2025-06-26 17:39:43,557 - semgrep.output - INFO - Some files were skipped or only partially analyzed.
  Scan was limited to files tracked by git.
  Partially scanned: 1 files only partially analyzed due to parsing or internal Opengrep errors
  Scan skipped: 12 files larger than 1.0 MB
  For a full list of skipped files, run opengrep with the --verbose flag.

Ran 437 rules on 165 files: 12 findings.
2025-06-26 17:39:43,557 - semgrep.app.version - DEBUG - Version cache does not exist
2025-06-26 17:39:43,572 - semgrep.metrics - VERBOSE - Not sending pseudonymous metrics since metrics are configured to OFF and registry usage is False
