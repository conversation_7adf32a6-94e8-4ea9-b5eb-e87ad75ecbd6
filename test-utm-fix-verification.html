<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UTM Tracking Fix Verification Test</title>
    
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { background: white; padding: 30px; border-radius: 8px; max-width: 1000px; margin: 0 auto; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .test-section { background: #f8f9fa; border: 1px solid #dee2e6; padding: 20px; margin: 20px 0; border-radius: 8px; }
        .test-section.success { background: #d4edda; border-color: #c3e6cb; }
        .test-section.error { background: #f8d7da; border-color: #f5c6cb; }
        .test-section.running { background: #fff3cd; border-color: #ffeaa7; }
        button { background: #007bff; color: white; border: none; padding: 12px 20px; border-radius: 5px; cursor: pointer; margin: 10px 5px; font-size: 14px; }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        .log { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; font-family: monospace; font-size: 12px; max-height: 400px; overflow-y: auto; }
        .utm-params { background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .step { margin: 10px 0; padding: 10px; border-left: 4px solid #007bff; background: #f8f9fa; }
        .step.completed { border-left-color: #28a745; background: #d4edda; }
        .step.failed { border-left-color: #dc3545; background: #f8d7da; }
        h1, h2, h3 { color: #333; }
        .status { font-weight: bold; margin: 5px 0; }
        .success-text { color: #28a745; }
        .error-text { color: #dc3545; }
        .warning-text { color: #ffc107; }
        .info-text { color: #007bff; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 UTM Tracking Fix Verification</h1>
        <p><strong>Purpose:</strong> Test that UTM parameters are properly carried forward from sessionStorage to new sessions, fixing the attribution chain break.</p>
        
        <div class="test-section" id="urlTest">
            <h2>Step 1: Current URL Analysis</h2>
            <div id="currentUrlInfo"></div>
        </div>

        <div class="test-section" id="sessionStorageTest">
            <h2>Step 2: SessionStorage UTM Data</h2>
            <div id="sessionStorageInfo"></div>
        </div>

        <div class="test-section" id="trackingTest">
            <h2>Step 3: User Tracking System Test</h2>
            <button onclick="testUserTracking()">Test User Tracking Initialization</button>
            <div id="trackingResults"></div>
        </div>

        <div class="test-section" id="sessionSyncTest">
            <h2>Step 4: Session Backend Sync Test</h2>
            <button onclick="testSessionSync()">Test Session Sync to Backend</button>
            <div id="sessionSyncResults"></div>
        </div>

        <div class="test-section" id="endToEndTest">
            <h2>Step 5: End-to-End Attribution Test</h2>
            <button onclick="runEndToEndTest()">Run Complete Attribution Test</button>
            <div id="endToEndResults"></div>
        </div>

        <div class="test-section" id="instructions">
            <h2>🧪 Test Instructions</h2>
            <div class="step">
                <strong>1. Test with UTM URL:</strong> Visit 
                <a href="?utm_source=facebook&utm_medium=cpc&utm_campaign=Static|%20Kult%20|%20Perfume%20|%20Gurugram%20|%20Calendar_Old&utm_term=perfume%20trial&utm_content=static%20image" target="_blank">
                    this link with UTM parameters
                </a>
            </div>
            <div class="step">
                <strong>2. Test without UTM URL:</strong> Then visit 
                <a href="test-utm-fix-verification.html" target="_blank">
                    this same page without UTM parameters
                </a>
            </div>
            <div class="step">
                <strong>3. Expected Result:</strong> UTM parameters should be preserved across both page visits via sessionStorage
            </div>
        </div>

        <div class="log" id="debugLog">
            <strong>Debug Log:</strong><br>
            Starting UTM tracking verification...<br>
        </div>
    </div>

    <script>
        // Debug logging function
        function log(message) {
            const debugLog = document.getElementById('debugLog');
            const timestamp = new Date().toLocaleTimeString();
            debugLog.innerHTML += `[${timestamp}] ${message}<br>`;
            debugLog.scrollTop = debugLog.scrollHeight;
            console.log(message);
        }

        // Initialize tests on page load
        window.onload = function() {
            analyzeCurrentURL();
            checkSessionStorage();
            log('✅ Page loaded - ready for testing');
        };

        function analyzeCurrentURL() {
            const urlParams = new URLSearchParams(window.location.search);
            const hasUTM = ['utm_source', 'utm_medium', 'utm_campaign', 'utm_term', 'utm_content']
                .some(param => urlParams.get(param));
            
            const urlInfo = document.getElementById('currentUrlInfo');
            
            if (hasUTM) {
                document.getElementById('urlTest').className = 'test-section success';
                urlInfo.innerHTML = `
                    <div class="status success-text">✅ UTM Parameters Found in URL</div>
                    <div class="utm-params">
                        <strong>UTM Source:</strong> ${urlParams.get('utm_source') || 'Not set'}<br>
                        <strong>UTM Medium:</strong> ${urlParams.get('utm_medium') || 'Not set'}<br>
                        <strong>UTM Campaign:</strong> ${urlParams.get('utm_campaign') || 'Not set'}<br>
                        <strong>UTM Term:</strong> ${urlParams.get('utm_term') || 'Not set'}<br>
                        <strong>UTM Content:</strong> ${urlParams.get('utm_content') || 'Not set'}
                    </div>
                `;
                log('✅ URL contains UTM parameters - will be stored in sessionStorage');
            } else {
                document.getElementById('urlTest').className = 'test-section warning';
                urlInfo.innerHTML = `
                    <div class="status warning-text">⚠️ No UTM Parameters in Current URL</div>
                    <p>This is expected for the second test case. UTM data should come from sessionStorage.</p>
                `;
                log('⚠️ No UTM parameters in URL - expecting stored data from sessionStorage');
            }
        }

        function checkSessionStorage() {
            const stored = sessionStorage.getItem('utm_parameters');
            const timestamp = sessionStorage.getItem('utm_timestamp');
            
            const sessionInfo = document.getElementById('sessionStorageInfo');
            
            if (stored && timestamp) {
                try {
                    const utmData = JSON.parse(stored);
                    const age = Date.now() - parseInt(timestamp);
                    const ageMinutes = Math.floor(age / (1000 * 60));
                    
                    document.getElementById('sessionStorageTest').className = 'test-section success';
                    sessionInfo.innerHTML = `
                        <div class="status success-text">✅ Stored UTM Data Found</div>
                        <div class="utm-params">
                            <strong>UTM Source:</strong> ${utmData.utmSource || 'Not set'}<br>
                            <strong>UTM Medium:</strong> ${utmData.utmMedium || 'Not set'}<br>
                            <strong>UTM Campaign:</strong> ${utmData.utmCampaign || 'Not set'}<br>
                            <strong>UTM Term:</strong> ${utmData.utmTerm || 'Not set'}<br>
                            <strong>UTM Content:</strong> ${utmData.utmContent || 'Not set'}<br>
                            <strong>Stored:</strong> ${ageMinutes} minutes ago
                        </div>
                    `;
                    log(`✅ SessionStorage contains UTM data (${ageMinutes} minutes old)`);
                } catch (error) {
                    document.getElementById('sessionStorageTest').className = 'test-section error';
                    sessionInfo.innerHTML = `<div class="status error-text">❌ Error parsing stored UTM data</div>`;
                    log('❌ Error parsing stored UTM data: ' + error.message);
                }
            } else {
                document.getElementById('sessionStorageTest').className = 'test-section warning';
                sessionInfo.innerHTML = `
                    <div class="status warning-text">⚠️ No Stored UTM Data</div>
                    <p>This is expected if you haven't visited a UTM-enabled URL yet.</p>
                `;
                log('⚠️ No UTM data found in sessionStorage');
            }
        }

        function testUserTracking() {
            document.getElementById('trackingTest').className = 'test-section running';
            const resultsDiv = document.getElementById('trackingResults');
            resultsDiv.innerHTML = '<div class="status">🔄 Testing user tracking initialization...</div>';
            
            try {
                // Simulate the user tracking initialization logic
                const urlParams = new URLSearchParams(window.location.search);
                
                // Check URL parameters first
                const urlUTM = {
                    utmSource: urlParams.get('utm_source') || undefined,
                    utmMedium: urlParams.get('utm_medium') || undefined,
                    utmCampaign: urlParams.get('utm_campaign') || undefined,
                    utmTerm: urlParams.get('utm_term') || undefined,
                    utmContent: urlParams.get('utm_content') || undefined,
                };
                
                let finalUTM = {};
                
                // If URL has UTM parameters, use and store them
                if (Object.values(urlUTM).some(value => value !== undefined)) {
                    finalUTM = urlUTM;
                    sessionStorage.setItem('utm_parameters', JSON.stringify(urlUTM));
                    sessionStorage.setItem('utm_timestamp', Date.now().toString());
                    log('✅ UTM parameters found in URL and stored to sessionStorage');
                } else {
                    // Otherwise try to get from sessionStorage
                    const stored = sessionStorage.getItem('utm_parameters');
                    const timestamp = sessionStorage.getItem('utm_timestamp');
                    
                    if (stored && timestamp) {
                        const age = Date.now() - parseInt(timestamp);
                        const maxAge = 24 * 60 * 60 * 1000; // 24 hours
                        
                        if (age <= maxAge) {
                            finalUTM = JSON.parse(stored);
                            log('✅ UTM parameters retrieved from sessionStorage');
                        } else {
                            log('⚠️ Stored UTM parameters expired (older than 24 hours)');
                            sessionStorage.removeItem('utm_parameters');
                            sessionStorage.removeItem('utm_timestamp');
                        }
                    } else {
                        log('⚠️ No UTM parameters available from URL or sessionStorage');
                    }
                }
                
                document.getElementById('trackingTest').className = 'test-section success';
                resultsDiv.innerHTML = `
                    <div class="status success-text">✅ User Tracking Test Complete</div>
                    <div class="utm-params">
                        <strong>Final UTM Data:</strong><br>
                        <strong>Source:</strong> ${finalUTM.utmSource || 'Not available'}<br>
                        <strong>Medium:</strong> ${finalUTM.utmMedium || 'Not available'}<br>
                        <strong>Campaign:</strong> ${finalUTM.utmCampaign || 'Not available'}<br>
                        <strong>Term:</strong> ${finalUTM.utmTerm || 'Not available'}<br>
                        <strong>Content:</strong> ${finalUTM.utmContent || 'Not available'}
                    </div>
                `;
                
                // Store result for next test
                window.testUTMData = finalUTM;
                
            } catch (error) {
                document.getElementById('trackingTest').className = 'test-section error';
                resultsDiv.innerHTML = `<div class="status error-text">❌ User tracking test failed: ${error.message}</div>`;
                log('❌ User tracking test failed: ' + error.message);
            }
        }

        function testSessionSync() {
            if (!window.testUTMData) {
                document.getElementById('sessionSyncResults').innerHTML = `
                    <div class="status warning-text">⚠️ Please run User Tracking test first</div>
                `;
                return;
            }
            
            document.getElementById('sessionSyncTest').className = 'test-section running';
            const resultsDiv = document.getElementById('sessionSyncResults');
            resultsDiv.innerHTML = '<div class="status">🔄 Testing session sync to backend...</div>';
            
            const sessionData = {
                userId: 'test_user_' + Date.now(),
                sessionId: 'test_session_' + Date.now(),
                deviceFingerprint: 'test_device',
                utmSource: window.testUTMData.utmSource,
                utmMedium: window.testUTMData.utmMedium,
                utmCampaign: window.testUTMData.utmCampaign,
                utmTerm: window.testUTMData.utmTerm,
                utmContent: window.testUTMData.utmContent,
                referrer: document.referrer
            };
            
            log('🔄 Sending session data to backend: ' + JSON.stringify(sessionData, null, 2));
            
            fetch('/api/tracking/session', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(sessionData)
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('sessionSyncTest').className = 'test-section success';
                resultsDiv.innerHTML = `
                    <div class="status success-text">✅ Session Sync Successful</div>
                    <div class="utm-params">
                        <strong>Response:</strong> ${JSON.stringify(data, null, 2)}<br>
                        <strong>Session ID:</strong> ${data.sessionId || 'Not returned'}<br>
                        <strong>Status:</strong> ${data.success ? 'Success' : 'Failed'}
                    </div>
                `;
                log('✅ Session sync successful: ' + JSON.stringify(data));
            })
            .catch(error => {
                document.getElementById('sessionSyncTest').className = 'test-section error';
                resultsDiv.innerHTML = `<div class="status error-text">❌ Session sync failed: ${error.message}</div>`;
                log('❌ Session sync failed: ' + error.message);
            });
        }

        function runEndToEndTest() {
            document.getElementById('endToEndTest').className = 'test-section running';
            const resultsDiv = document.getElementById('endToEndResults');
            resultsDiv.innerHTML = '<div class="status">🔄 Running complete end-to-end attribution test...</div>';
            
            log('🧪 Starting end-to-end UTM attribution test');
            
            // Test the complete flow
            fetch('/api/tracking/test-utm', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    utm_source: 'facebook',
                    utm_medium: 'cpc',
                    utm_campaign: 'Static| Kult | Perfume | Gurugram | Calendar_Old',
                    utm_term: 'perfume trial',
                    utm_content: 'static image'
                })
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('endToEndTest').className = 'test-section success';
                resultsDiv.innerHTML = `
                    <div class="status success-text">✅ End-to-End Test Complete</div>
                    <div class="utm-params">
                        <strong>Test Result:</strong> ${data.success ? 'PASSED' : 'FAILED'}<br>
                        <strong>UTM Data Stored:</strong> ${data.hasUTMData ? 'YES' : 'NO'}<br>
                        <strong>Session Created:</strong> ${data.created?.sessionId || 'Unknown'}<br>
                        <strong>Verification:</strong> ${data.verified ? 'Database record confirmed' : 'No verification'}
                    </div>
                    <div class="log">
                        <strong>Full Response:</strong><br>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    </div>
                `;
                log('✅ End-to-end test completed: ' + JSON.stringify(data));
            })
            .catch(error => {
                document.getElementById('endToEndTest').className = 'test-section error';
                resultsDiv.innerHTML = `<div class="status error-text">❌ End-to-end test failed: ${error.message}</div>`;
                log('❌ End-to-end test failed: ' + error.message);
            });
        }

        // Update session storage info periodically
        setInterval(checkSessionStorage, 5000);
    </script>
</body>
</html>