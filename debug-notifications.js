// Quick debugging script for notification buttons
// Run this in browser console on the admin bookings page

console.log("=== Notification Button Debug Tool ===");

// Check if buttons exist
const alarmButtons = document.querySelectorAll('[title="Send WhatsApp reminder"]');
const phoneButtons = document.querySelectorAll('[title="Send \'Tried Reaching\' follow-up"]');

console.log(`Found ${alarmButtons.length} alarm buttons (reminders)`);
console.log(`Found ${phoneButtons.length} phone buttons (CX follow-up)`);

// Check if mutations are defined
console.log("Checking React Query mutations...");

// Function to test a specific booking ID
window.testNotification = function(bookingId, type = 'reminder') {
  const endpoint = type === 'reminder' 
    ? `/api/bookings/${bookingId}/reminder`
    : `/api/bookings/${bookingId}/cx-followup`;
    
  fetch(endpoint, { method: 'POST' })
    .then(response => response.json())
    .then(data => {
      console.log(`${type} test result:`, data);
    })
    .catch(error => {
      console.error(`${type} test error:`, error);
    });
};

// Test with the most recent confirmed booking
window.testNotification(97, 'reminder');
console.log("Test reminder sent to booking ID 97");

// Instructions
console.log("\nTo test manually:");
console.log("1. testNotification(97, 'reminder') - Test alarm button");
console.log("2. testNotification(97, 'cx-followup') - Test phone button");
console.log("3. Replace 97 with actual booking ID from your table");