# Deployment Troubleshooting Guide

## Recent Database Schema Changes (July 2, 2025)

### Critical Fix Applied: Array Column Schema Issue
- **Problem**: `completed_steps` column was defined as integer in database but text[] in schema
- **Fix Applied**: Updated database column from integer to text[] array type
- **SQL Commands Executed**:
  ```sql
  ALTER TABLE user_sessions ALTER COLUMN completed_steps DROP DEFAULT;
  ALTER TABLE user_sessions ALTER COLUMN completed_steps TYPE text[] USING CASE WHEN completed_steps = 0 THEN '{}' ELSE ARRAY[completed_steps::text] END;
  ALTER TABLE user_sessions ALTER COLUMN completed_steps SET DEFAULT '{}';
  ```

## Production Deployment Steps

### 1. Database Schema Sync
The production database needs the same schema updates that were applied to development:

```sql
-- Check current column type
SELECT column_name, data_type, column_default 
FROM information_schema.columns 
WHERE table_name = 'user_sessions' AND column_name = 'completed_steps';

-- If it shows integer type, apply these fixes:
ALTER TABLE user_sessions ALTER COLUMN completed_steps DROP DEFAULT;
ALTER TABLE user_sessions ALTER COLUMN completed_steps TYPE text[] USING CASE WHEN completed_steps = 0 THEN '{}' ELSE ARRAY[completed_steps::text] END;
ALTER TABLE user_sessions ALTER COLUMN completed_steps SET DEFAULT '{}';
```

### 2. Build Process
The current build script should work correctly:
```bash
npm run build
```

### 3. Environment Variables
Ensure all required secrets are available in production:
- DATABASE_URL
- PGHOST, PGUSER, PGPASSWORD, PGDATABASE, PGPORT
- VITE_GA_MEASUREMENT_ID
- META_ACCESS_TOKEN
- META_AD_ACCOUNT_ID

### 4. Common Production Issues

#### Array Column Errors
- **Symptom**: "value.map is not a function" errors
- **Cause**: Database schema mismatch between integer and text[] types
- **Solution**: Apply the SQL commands above

#### Session Storage Errors
- **Symptom**: Admin dashboard fails to load
- **Cause**: PostgreSQL session storage configuration
- **Solution**: Verify database connection and session table exists

#### UTM Tracking Errors
- **Symptom**: Analytics dashboard shows no data
- **Cause**: Missing UTM data in session tracking
- **Solution**: Verify tracking hooks include UTM parameters

## Deployment Verification

After deployment, test these endpoints:
1. `GET /api/admin/overview` - Should return dashboard metrics
2. `POST /api/tracking/session` - Should accept UTM data
3. `GET /api/analytics/session-tracking` - Should return funnel data

## Recovery Steps

If deployment fails:
1. Check server logs for specific error messages
2. Verify database connectivity
3. Run database schema fixes manually
4. Restart the application