/**
 * User Tracking System with Persistent Cookies
 * Similar to Google Analytics and Facebook Pixel
 */

interface UserTrackingData {
  userId: string;
  sessionId: string;
  firstVisit: string;
  lastVisit: string;
  visitCount: number;
  utmSource?: string;
  utmMedium?: string;
  utmCampaign?: string;
  utmTerm?: string;
  utmContent?: string;
  referrer?: string;
  deviceFingerprint: string;
}

class UserTracker {
  private cookieName = 'kult_user_tracking';
  private cookieExpiry = 2 * 365 * 24 * 60 * 60 * 1000; // 2 years (like Google Analytics)
  
  // Generate device fingerprint for cross-session tracking
  private generateDeviceFingerprint(): string {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    if (ctx) {
      ctx.textBaseline = 'top';
      ctx.font = '14px Arial';
      ctx.fillText('Device fingerprint', 2, 2);
    }
    
    const fingerprint = [
      navigator.userAgent,
      navigator.language,
      screen.width + 'x' + screen.height,
      screen.colorDepth,
      new Date().getTimezoneOffset(),
      canvas.toDataURL(),
      navigator.platform,
      navigator.hardwareConcurrency || 'unknown'
    ].join('|');
    
    // Simple hash function
    let hash = 0;
    for (let i = 0; i < fingerprint.length; i++) {
      const char = fingerprint.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    
    return Math.abs(hash).toString(36);
  }

  // Generate unique user ID
  private generateUserId(): string {
    return 'user_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  // Generate session ID
  private generateSessionId(): string {
    return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  // Set cookie with long expiration
  private setCookie(name: string, value: string, days: number): void {
    const expires = new Date();
    expires.setTime(expires.getTime() + (days * 24 * 60 * 60 * 1000));
    
    document.cookie = `${name}=${encodeURIComponent(value)}; expires=${expires.toUTCString()}; path=/; SameSite=Lax; Secure=${location.protocol === 'https:'}`;
  }

  // Get cookie value
  private getCookie(name: string): string | null {
    const nameEQ = name + "=";
    const ca = document.cookie.split(';');
    
    for (let i = 0; i < ca.length; i++) {
      let c = ca[i];
      while (c.charAt(0) === ' ') c = c.substring(1, c.length);
      if (c.indexOf(nameEQ) === 0) return decodeURIComponent(c.substring(nameEQ.length, c.length));
    }
    return null;
  }

  // Extract UTM parameters from URL or sessionStorage - Always capture source info
  private extractUTMParameters(): Partial<UserTrackingData> {
    const urlParams = new URLSearchParams(window.location.search);
    
    // First try to get from URL
    const urlUTM = {
      utmSource: urlParams.get('utm_source') || undefined,
      utmMedium: urlParams.get('utm_medium') || undefined,
      utmCampaign: urlParams.get('utm_campaign') || undefined,
      utmTerm: urlParams.get('utm_term') || undefined,
      utmContent: urlParams.get('utm_content') || undefined,
    };
    
    // If URL has UTM parameters, store them and return
    if (Object.values(urlUTM).some(value => value !== undefined)) {
      try {
        sessionStorage.setItem('utm_parameters', JSON.stringify(urlUTM));
        sessionStorage.setItem('utm_timestamp', Date.now().toString());
      } catch (error) {
        console.warn('Failed to store UTM parameters:', error);
      }
      return urlUTM;
    }
    
    // Try stored UTM parameters
    try {
      const stored = sessionStorage.getItem('utm_parameters');
      const timestamp = sessionStorage.getItem('utm_timestamp');
      
      if (stored && timestamp) {
        const age = Date.now() - parseInt(timestamp);
        const maxAge = 24 * 60 * 60 * 1000; // 24 hours
        
        if (age <= maxAge) {
          const storedUTM = JSON.parse(stored);
          console.log('🔄 Using stored UTM parameters for session tracking:', storedUTM);
          return storedUTM;
        } else {
          sessionStorage.removeItem('utm_parameters');
          sessionStorage.removeItem('utm_timestamp');
        }
      }
    } catch (error) {
      console.warn('Failed to retrieve stored UTM parameters:', error);
    }
    
    // If no UTM data, analyze referrer for source identification
    const referrer = document.referrer;
    if (referrer && !referrer.includes('localhost') && !referrer.includes('replit.dev')) {
      try {
        const refUrl = new URL(referrer);
        const hostname = refUrl.hostname.toLowerCase();
        
        // Map common referrers to sources
        if (hostname.includes('facebook') || hostname.includes('fb.')) {
          return { utmSource: 'facebook', utmMedium: 'referral' };
        } else if (hostname.includes('instagram')) {
          return { utmSource: 'instagram', utmMedium: 'referral' };
        } else if (hostname.includes('google')) {
          return { utmSource: 'google', utmMedium: 'referral' };
        } else if (hostname.includes('whatsapp') || hostname.includes('wa.me')) {
          return { utmSource: 'whatsapp', utmMedium: 'referral' };
        } else {
          return { utmSource: hostname, utmMedium: 'referral' };
        }
      } catch (error) {
        console.warn('Failed to parse referrer URL:', error);
      }
    }
    
    // Default to direct if no identifiable source
    return { utmSource: 'direct', utmMedium: 'direct' };
  }

  // Initialize or update user tracking
  public initializeTracking(): UserTrackingData {
    const now = new Date().toISOString();
    const deviceFingerprint = this.generateDeviceFingerprint();
    const utmParams = this.extractUTMParameters();
    const referrer = document.referrer || undefined;
    
    // Try to get existing tracking data
    const existingData = this.getCookie(this.cookieName);
    
    if (existingData) {
      try {
        const trackingData: UserTrackingData = JSON.parse(existingData);
        
        // Update existing user data
        const updatedData: UserTrackingData = {
          ...trackingData,
          sessionId: this.generateSessionId(), // New session ID for each visit
          lastVisit: now,
          visitCount: trackingData.visitCount + 1,
          deviceFingerprint, // Update device fingerprint
          
          // Always update UTM parameters with current session data (from URL or sessionStorage)
          ...utmParams,
          ...(referrer && { referrer })
        };
        
        // Save updated data
        this.setCookie(this.cookieName, JSON.stringify(updatedData), this.cookieExpiry / (24 * 60 * 60 * 1000));
        
        return updatedData;
      } catch (error) {
        console.warn('Failed to parse existing tracking data:', error);
      }
    }
    
    // Create new user tracking data
    const newTrackingData: UserTrackingData = {
      userId: this.generateUserId(),
      sessionId: this.generateSessionId(),
      firstVisit: now,
      lastVisit: now,
      visitCount: 1,
      deviceFingerprint,
      referrer,
      ...utmParams
    };
    
    // Save new tracking data
    this.setCookie(this.cookieName, JSON.stringify(newTrackingData), this.cookieExpiry / (24 * 60 * 60 * 1000));
    
    return newTrackingData;
  }

  // Get current tracking data
  public getTrackingData(): UserTrackingData | null {
    const data = this.getCookie(this.cookieName);
    if (data) {
      try {
        return JSON.parse(data);
      } catch (error) {
        console.warn('Failed to parse tracking data:', error);
      }
    }
    return null;
  }

  // Update session activity (extend session)
  public updateActivity(): void {
    const trackingData = this.getTrackingData();
    if (trackingData) {
      trackingData.lastVisit = new Date().toISOString();
      this.setCookie(this.cookieName, JSON.stringify(trackingData), this.cookieExpiry / (24 * 60 * 60 * 1000));
    }
  }

  // Clear tracking data (for privacy compliance)
  public clearTracking(): void {
    document.cookie = `${this.cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
  }

  // Check if this is a returning user
  public isReturningUser(): boolean {
    const data = this.getTrackingData();
    return data ? data.visitCount > 1 : false;
  }

  // Get user journey duration
  public getUserJourneyDuration(): number {
    const data = this.getTrackingData();
    if (data && data.firstVisit) {
      return Date.now() - new Date(data.firstVisit).getTime();
    }
    return 0;
  }
}

// Export singleton instance
export const userTracker = new UserTracker();

// Auto-initialize tracking on page load
if (typeof window !== 'undefined') {
  userTracker.initializeTracking();
  
  // Update activity periodically (like Google Analytics)
  setInterval(() => {
    userTracker.updateActivity();
  }, 30000); // Every 30 seconds

  // Update activity on page interaction
  ['click', 'scroll', 'keydown'].forEach(event => {
    document.addEventListener(event, () => userTracker.updateActivity(), { passive: true });
  });
}