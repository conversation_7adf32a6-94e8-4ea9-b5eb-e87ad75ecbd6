import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";

interface Step {
  title: string;
  component: React.ReactNode;
}

interface MultiStepFormProps {
  steps: Step[];
}

export function MultiStepForm({ steps }: MultiStepFormProps) {
  const [currentStep, setCurrentStep] = useState(0);

  const nextStep = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const getStepClass = (stepIndex: number) => {
    if (stepIndex < currentStep) return "step-completed";
    if (stepIndex === currentStep) return "step-active";
    return "step-inactive";
  };

  return (
    <>
      {/* Progress Steps */}
      <div className="flex justify-center mb-8">
        <div className="flex items-center space-x-2">
          {steps.map((_, index) => (
            <div key={index} className="flex items-center">
              <div className={`w-8 h-8 rounded-full ${getStepClass(index)} flex items-center justify-center text-xs font-semibold`}>
                {index + 1}
              </div>
              {index < steps.length - 1 && (
                <div className={`w-8 h-1 ${index < currentStep ? "step-completed" : "step-inactive"} rounded`}></div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Main Form Card */}
      <div className="glass-effect rounded-3xl p-8 shadow-2xl animate-slide-up">
        {React.cloneElement(steps[currentStep].component as React.ReactElement, {
          onNext: nextStep,
        })}
      </div>

      {/* Navigation Buttons */}
      <div className="flex justify-between mt-4">
        {currentStep > 0 && currentStep < steps.length - 1 ? (
          <Button
            variant="ghost"
            onClick={prevStep}
            className="px-6 py-3 rounded-2xl text-white/80 hover:text-white transition-colors"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
        ) : (
          <div></div>
        )}
        <div></div>
      </div>
    </>
  );
}
