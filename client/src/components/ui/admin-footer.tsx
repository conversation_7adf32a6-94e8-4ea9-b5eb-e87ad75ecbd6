import { Mail, Phone } from "lucide-react";

export default function AdminFooter() {
  return (
    <footer className="w-full py-4 px-6 mt-8 border-t border-gray-200 bg-white">
      <div className="max-w-md mx-auto text-center">
        <div className="text-gray-600 text-sm space-y-2">
          <p className="font-medium">Need help? Contact our support team</p>
          <div className="flex flex-col sm:flex-row items-center justify-center gap-4 text-xs">
            <a 
              href="mailto:<EMAIL>" 
              className="flex items-center gap-2 text-gray-500 hover:text-gray-700 transition-colors"
            >
              <Mail className="h-3 w-3" />
              <EMAIL>
            </a>
            <a 
              href="tel:+919987991000" 
              className="flex items-center gap-2 text-gray-500 hover:text-gray-700 transition-colors"
            >
              <Phone className="h-3 w-3" />
              (+91) 9987991000
            </a>
          </div>
        </div>
      </div>
    </footer>
  );
}