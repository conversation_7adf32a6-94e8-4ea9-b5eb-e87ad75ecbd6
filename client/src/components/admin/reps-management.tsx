import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Plus, Edit, Trash2 } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface SalesRep {
  id: number;
  name: string;
  email: string;
  phone: string;
  workingDays: string[];
  shiftStart: string;
  shiftEnd: string;
  isActive: boolean;
}

interface RepsManagementProps {
  readOnly?: boolean;
}

export default function RepsManagement({ readOnly = false }: RepsManagementProps) {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingRep, setEditingRep] = useState<SalesRep | null>(null);
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    workingDays: [] as string[],
    shiftStart: "10:00",
    shiftEnd: "20:00"
  });
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const { data: reps = [], isLoading } = useQuery<SalesRep[]>({
    queryKey: ["/api/admin/reps"],
  });

  const createRepMutation = useMutation({
    mutationFn: async (data: any) => {
      const response = await apiRequest("POST", "/api/admin/reps", data);
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/admin/reps"] });
      setIsDialogOpen(false);
      resetForm();
      toast({ title: "Sales rep created successfully" });
    },
    onError: () => {
      toast({
        title: "Error creating sales rep",
        variant: "destructive",
      });
    },
  });

  const updateRepMutation = useMutation({
    mutationFn: async ({ id, ...data }: any) => {
      const response = await apiRequest("PUT", `/api/admin/reps/${id}`, data);
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/admin/reps"] });
      setEditingRep(null);
      setIsDialogOpen(false);
      resetForm();
      toast({ title: "Sales rep updated successfully" });
    },
    onError: () => {
      toast({
        title: "Error updating sales rep",
        variant: "destructive",
      });
    },
  });

  const deleteRepMutation = useMutation({
    mutationFn: async (id: number) => {
      const response = await apiRequest("DELETE", `/api/admin/reps/${id}`);
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/admin/reps"] });
      toast({ title: "Sales rep deleted successfully" });
    },
    onError: () => {
      toast({
        title: "Error deleting sales rep",
        variant: "destructive",
      });
    },
  });

  const resetForm = () => {
    setFormData({
      name: "",
      email: "",
      phone: "",
      workingDays: [],
      shiftStart: "10:00",
      shiftEnd: "20:00"
    });
  };

  const handleEdit = (rep: SalesRep) => {
    setEditingRep(rep);
    setFormData({
      name: rep.name,
      email: rep.email,
      phone: rep.phone,
      workingDays: rep.workingDays || [],
      shiftStart: rep.shiftStart,
      shiftEnd: rep.shiftEnd
    });
    setIsDialogOpen(true);
  };

  const handleWorkingDayChange = (day: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      workingDays: checked 
        ? [...prev.workingDays, day]
        : prev.workingDays.filter(d => d !== day)
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name || !formData.email || !formData.phone) {
      toast({ title: "Please fill in all required fields (name, email, phone)", variant: "destructive" });
      return;
    }

    if (editingRep) {
      updateRepMutation.mutate({ id: editingRep.id, ...formData });
    } else {
      createRepMutation.mutate(formData);
    }
  };

  if (isLoading) {
    return <div className="text-center p-8">Loading sales reps...</div>;
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">Sales Representatives</h3>
        {!readOnly && (
          <Dialog open={isDialogOpen} onOpenChange={(open) => {
            setIsDialogOpen(open);
            if (!open) {
              setEditingRep(null);
              resetForm();
            }
          }}>
            <DialogTrigger asChild>
              <Button className="bg-purple-600 hover:bg-purple-700 text-white">
                <Plus className="h-4 w-4 mr-2" />
                Add Rep
              </Button>
            </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>{editingRep ? "Edit Sales Representative" : "Add Sales Representative"}</DialogTitle>
            </DialogHeader>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <Label htmlFor="name">Name</Label>
                <Input 
                  id="name" 
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  required 
                />
              </div>
              
              <div>
                <Label htmlFor="email">Email</Label>
                <Input 
                  id="email" 
                  type="email" 
                  value={formData.email}
                  onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                  placeholder="<EMAIL>" 
                  required 
                />
              </div>
              
              <div>
                <Label htmlFor="phone">Phone</Label>
                <Input 
                  id="phone" 
                  value={formData.phone}
                  onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
                  placeholder="+91-9876543210" 
                  required 
                />
              </div>
              
              <div>
                <Label>Working Days</Label>
                <p className="text-sm text-gray-500 mb-2">Select working days. Leave empty for extended leave periods.</p>
                <div className="flex flex-wrap gap-2 mt-2">
                  {["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"].map(day => (
                    <div key={day} className="flex items-center space-x-2">
                      <Checkbox 
                        id={day.toLowerCase()} 
                        checked={formData.workingDays.includes(day)}
                        onCheckedChange={(checked) => handleWorkingDayChange(day, checked as boolean)}
                      />
                      <Label htmlFor={day.toLowerCase()} className="text-sm">{day.slice(0, 3)}</Label>
                    </div>
                  ))}
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="shiftStart">Shift Start</Label>
                  <Input 
                    id="shiftStart" 
                    type="time" 
                    value={formData.shiftStart}
                    onChange={(e) => setFormData(prev => ({ ...prev, shiftStart: e.target.value }))}
                    required 
                  />
                </div>
                <div>
                  <Label htmlFor="shiftEnd">Shift End</Label>
                  <Input 
                    id="shiftEnd" 
                    type="time" 
                    value={formData.shiftEnd}
                    onChange={(e) => setFormData(prev => ({ ...prev, shiftEnd: e.target.value }))}
                    required 
                  />
                </div>
              </div>

              <Button 
                type="submit" 
                className="w-full bg-purple-600 hover:bg-purple-700 text-white"
                disabled={createRepMutation.isPending || updateRepMutation.isPending}
              >
                {editingRep 
                  ? (updateRepMutation.isPending ? "Updating..." : "Update Rep")
                  : (createRepMutation.isPending ? "Creating..." : "Create Rep")
                }
              </Button>
            </form>
          </DialogContent>
        </Dialog>
        )}
      </div>

      <div className="border rounded-lg overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Contact</TableHead>
              <TableHead>Working Days</TableHead>
              <TableHead>Shift Hours</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {reps?.map((rep: any) => (
              <TableRow key={rep.id}>
                <TableCell className="font-medium">{rep.name}</TableCell>
                <TableCell>
                  <div className="text-sm">
                    <div>{rep.email}</div>
                    <div className="text-gray-500">{rep.phone}</div>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="flex flex-wrap gap-1">
                    {rep.workingDays?.map((day: string) => (
                      <Badge key={day} variant="outline" className="text-xs">
                        {day.slice(0, 3)}
                      </Badge>
                    ))}
                  </div>
                </TableCell>
                <TableCell>
                  {rep.shiftStart} - {rep.shiftEnd}
                </TableCell>
                <TableCell>
                  <Badge variant={rep.isActive ? "default" : "secondary"}>
                    {rep.isActive ? "Active" : "Inactive"}
                  </Badge>
                </TableCell>
                <TableCell>
                  {!readOnly && (
                    <div className="flex space-x-2">
                      <Button 
                        variant="ghost" 
                        size="sm"
                        onClick={() => handleEdit(rep)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button 
                        variant="ghost" 
                        size="sm"
                        onClick={() => {
                          if (confirm(`Are you sure you want to delete ${rep.name}?`)) {
                            deleteRepMutation.mutate(rep.id);
                          }
                        }}
                        disabled={deleteRepMutation.isPending}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  )}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {(!reps || reps.length === 0) && (
        <div className="text-center p-8 text-gray-500">
          No sales representatives found. Add your first rep to get started.
        </div>
      )}
    </div>
  );
}
