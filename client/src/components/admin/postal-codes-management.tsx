import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import { MapPin, Plus, Search, Upload } from "lucide-react";

interface PostalCodesManagementProps {
  readOnly?: boolean;
}

export default function PostalCodesManagement({ readOnly = false }: PostalCodesManagementProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [newCode, setNewCode] = useState("");
  const [newCity, setNewCity] = useState("");
  const [newState, setNewState] = useState("");
  const [bulkCodes, setBulkCodes] = useState("");
  const [showBulkAdd, setShowBulkAdd] = useState(false);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const { data: postalCodes = [], isLoading } = useQuery({
    queryKey: ["/api/admin/postal-codes"],
    queryFn: () => fetch("/api/admin/postal-codes").then(res => res.json()),
  });

  const addPostalCodeMutation = useMutation({
    mutationFn: async (data: { code: string; city: string; state: string }) => {
      const response = await apiRequest("POST", "/api/admin/postal-codes", data);
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/admin/postal-codes"] });
      setNewCode("");
      setNewCity("");
      setNewState("");
      toast({ title: "Postal code added successfully" });
    },
    onError: () => {
      toast({ title: "Failed to add postal code", variant: "destructive" });
    },
  });

  const bulkAddMutation = useMutation({
    mutationFn: async (codes: string) => {
      const response = await apiRequest("POST", "/api/admin/postal-codes/bulk", { codes });
      return response.json();
    },
    onSuccess: (result) => {
      queryClient.invalidateQueries({ queryKey: ["/api/admin/postal-codes"] });
      setBulkCodes("");
      setShowBulkAdd(false);
      toast({ 
        title: `Added ${result.added} postal codes successfully`,
        description: result.skipped ? `${result.skipped} were already in the system` : undefined
      });
    },
    onError: () => {
      toast({ title: "Failed to add postal codes", variant: "destructive" });
    },
  });

  const togglePostalCodeMutation = useMutation({
    mutationFn: async ({ id, isActive }: { id: number; isActive: boolean }) => {
      const response = await apiRequest("PUT", `/api/admin/postal-codes/${id}`, { isActive });
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/admin/postal-codes"] });
      toast({ title: "Postal code updated successfully" });
    },
    onError: () => {
      toast({ title: "Failed to update postal code", variant: "destructive" });
    },
  });

  const filteredCodes = postalCodes.filter((code: any) =>
    code.code.includes(searchTerm) ||
    code.city.toLowerCase().includes(searchTerm.toLowerCase()) ||
    code.state.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleAddCode = (e: React.FormEvent) => {
    e.preventDefault();
    if (newCode && newCity && newState) {
      addPostalCodeMutation.mutate({ code: newCode, city: newCity, state: newState });
    }
  };

  const handleBulkAdd = (e: React.FormEvent) => {
    e.preventDefault();
    if (bulkCodes.trim()) {
      bulkAddMutation.mutate(bulkCodes.trim());
    }
  };

  const toggleStatus = (id: number, currentStatus: boolean) => {
    togglePostalCodeMutation.mutate({ id, isActive: !currentStatus });
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Postal Codes Management</h2>
          <p className="text-gray-600">Manage serviceable areas and postal codes</p>
        </div>
        <Button 
          onClick={() => setShowBulkAdd(!showBulkAdd)}
          className="bg-primary hover:bg-primary/90"
        >
          <Upload className="h-4 w-4 mr-2" />
          Bulk Add
        </Button>
      </div>

      {/* Search */}
      <Card>
        <CardContent className="pt-6">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search postal codes, cities, or states..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </CardContent>
      </Card>

      {/* Add Single Code */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Plus className="h-5 w-5 mr-2" />
            Add New Postal Code
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleAddCode} className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Input
              placeholder="Postal Code"
              value={newCode}
              onChange={(e) => setNewCode(e.target.value)}
              required
            />
            <Input
              placeholder="City"
              value={newCity}
              onChange={(e) => setNewCity(e.target.value)}
              required
            />
            <Input
              placeholder="State"
              value={newState}
              onChange={(e) => setNewState(e.target.value)}
              required
            />
            <Button 
              type="submit" 
              disabled={addPostalCodeMutation.isPending}
              className="bg-primary hover:bg-primary/90"
            >
              {addPostalCodeMutation.isPending ? "Adding..." : "Add Code"}
            </Button>
          </form>
        </CardContent>
      </Card>

      {/* Bulk Add */}
      {showBulkAdd && (
        <Card>
          <CardHeader>
            <CardTitle>Bulk Add Postal Codes</CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleBulkAdd} className="space-y-4">
              <Textarea
                placeholder="Enter postal codes with city and state (one per line):&#10;110001 Delhi Delhi&#10;400001 Mumbai Maharashtra&#10;560001 Bangalore Karnataka"
                value={bulkCodes}
                onChange={(e) => setBulkCodes(e.target.value)}
                rows={6}
                className="font-mono text-sm"
              />
              <div className="flex gap-2">
                <Button 
                  type="submit" 
                  disabled={bulkAddMutation.isPending}
                  className="bg-primary hover:bg-primary/90"
                >
                  {bulkAddMutation.isPending ? "Adding..." : "Add All Codes"}
                </Button>
                <Button 
                  type="button" 
                  variant="outline"
                  onClick={() => setShowBulkAdd(false)}
                >
                  Cancel
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      )}

      {/* Postal Codes List */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center">
              <MapPin className="h-5 w-5 mr-2" />
              Postal Codes ({filteredCodes.length})
            </div>
            <div className="text-sm text-gray-600">
              Active: {filteredCodes.filter((code: any) => code.isActive).length} | 
              Inactive: {filteredCodes.filter((code: any) => !code.isActive).length}
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="text-center py-8">Loading postal codes...</div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredCodes.map((code: any) => (
                <div
                  key={code.id}
                  className="border rounded-lg p-4 flex items-center justify-between"
                >
                  <div>
                    <div className="font-mono font-bold text-lg">{code.code}</div>
                    <div className="text-sm text-gray-600">{code.city}, {code.state}</div>
                    <Badge variant={code.isActive ? "default" : "secondary"} className="mt-1">
                      {code.isActive ? "Active" : "Inactive"}
                    </Badge>
                  </div>
                  <Switch
                    checked={code.isActive}
                    onCheckedChange={() => toggleStatus(code.id, code.isActive)}
                    disabled={togglePostalCodeMutation.isPending}
                  />
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}