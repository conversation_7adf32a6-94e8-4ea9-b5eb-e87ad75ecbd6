import { useQuery } from "@tanstack/react-query";
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { 
  Calendar, Users, Clock, CheckCircle, MapPin, 
  ArrowUpRight, ArrowDownRight, RefreshCw
} from "lucide-react";
import kultLogoPath from "@assets/KULT_LogoBrandmarks-1_1750232754843.png";
import { cn } from "@/lib/utils";

interface OverviewStats {
  totalBookings: number;
  totalUsers: number;
  totalSalesReps: number;
  todayBookings: number;
  upcomingBookings: number;
  completedBookings: number;
  cancelledBookings: number;
  postalCodes: number;
}

interface OverviewDashboardProps {
  setActiveTab: (tab: string) => void;
}

interface InteractiveKPICardProps {
  title: string;
  value: string;
  change: number;
  icon: React.ComponentType<{ className?: string }>;
  onClick: () => void;
  color: string;
  subtitle: string;
}

function InteractiveKPICard({ title, value, change, icon: Icon, onClick, color, subtitle }: InteractiveKPICardProps) {
  const colorClasses = {
    green: 'border-purple-200 hover:border-purple-300 bg-gradient-to-br from-purple-50 to-green-50 hover:from-purple-100 hover:to-green-100',
    blue: 'border-purple-200 hover:border-purple-300 bg-gradient-to-br from-purple-50 to-blue-50 hover:from-purple-100 hover:to-blue-100',
    purple: 'border-purple-200 hover:border-purple-300 bg-gradient-to-br from-purple-50 to-purple-100 hover:from-purple-100 hover:to-purple-200',
    orange: 'border-purple-200 hover:border-purple-300 bg-gradient-to-br from-purple-50 to-orange-50 hover:from-purple-100 hover:to-orange-100'
  };

  const iconColorClasses = {
    green: 'text-green-600',
    blue: 'text-blue-600',
    purple: 'text-purple-600',
    orange: 'text-orange-600'
  };

  return (
    <Card 
      className={`cursor-pointer transition-all duration-300 hover:shadow-lg hover:scale-105 border-2 ${colorClasses[color as keyof typeof colorClasses]}`}
      onClick={onClick}
    >
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 p-3 md:p-6">
        <CardTitle className="text-xs sm:text-sm font-medium text-gray-700 leading-tight">{title}</CardTitle>
        <div className={`p-1 sm:p-2 rounded-full bg-white shadow-sm ${iconColorClasses[color as keyof typeof iconColorClasses]}`}>
          <Icon className="h-3 w-3 sm:h-4 sm:w-4" />
        </div>
      </CardHeader>
      <CardContent className="space-y-1 sm:space-y-2 p-3 md:p-6 pt-0">
        <div className="text-lg sm:text-2xl font-bold text-gray-900">{value}</div>
        <div className="flex items-center justify-between">
          <div className={`flex items-center space-x-1 text-xs font-medium ${
            change >= 0 ? 'text-green-600' : 'text-red-600'
          }`}>
            {change >= 0 ? <ArrowUpRight className="h-3 w-3" /> : <ArrowDownRight className="h-3 w-3" />}
            <span>{Math.abs(change).toFixed(1)}%</span>
          </div>
          <span className="text-xs text-gray-500 hidden sm:inline">{subtitle}</span>
        </div>
      </CardContent>
    </Card>
  );
}

export default function OverviewDashboard({ setActiveTab }: OverviewDashboardProps) {
  const [refreshing, setRefreshing] = useState(false);

  const { data: stats, isLoading, refetch } = useQuery<OverviewStats>({
    queryKey: ['/api/admin/overview'],
    refetchInterval: 30000, // Refresh every 30 seconds
  });

  const handleRefresh = async () => {
    setRefreshing(true);
    await refetch();
    setTimeout(() => setRefreshing(false), 1000);
  };

  if (isLoading) {
    return (
      <div className="p-3 md:p-6 space-y-4 md:space-y-6">
        <div className="flex justify-between items-center">
          <div className="space-y-2">
            <div className="h-8 bg-gray-200 rounded w-64 animate-pulse"></div>
            <div className="h-4 bg-gray-200 rounded w-96 animate-pulse"></div>
          </div>
        </div>
        <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-3 md:gap-4">
          {[...Array(6)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader className="p-3">
                <div className="h-4 bg-gray-200 rounded w-20"></div>
              </CardHeader>
              <CardContent className="p-3">
                <div className="h-8 bg-gray-200 rounded w-16 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-12"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (!stats) {
    return (
      <div className="p-3 md:p-6">
        <div className="text-center text-gray-500">Unable to load dashboard data</div>
      </div>
    );
  }

  return (
    <div className="p-3 md:p-6 space-y-4 md:space-y-6">
      {/* Header with Real-time Indicators and Controls */}
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3">
        <div className="flex items-center gap-4">
          {/* Kult Logo */}
          <div className="flex-shrink-0">
            <img 
              src={kultLogoPath} 
              alt="Kult" 
              className="h-10 w-auto sm:h-12 object-contain"
            />
          </div>
          <div>
            <h1 className="text-xl sm:text-2xl md:text-3xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
              Executive Dashboard
            </h1>
            <p className="text-xs sm:text-sm text-muted-foreground mt-1">
              Real-time business intelligence and performance metrics
            </p>
          </div>
        </div>
        
        <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 sm:gap-3">
          {/* Real-time Indicator */}
          <div className="flex items-center space-x-2 px-2 sm:px-3 py-1 bg-green-50 border border-green-200 rounded-full">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
            <span className="text-xs font-medium text-green-700">Live</span>
          </div>
          
          {/* Refresh Button */}
          <Button 
            variant="outline" 
            size="sm" 
            onClick={handleRefresh}
            disabled={refreshing}
            className="flex items-center space-x-1 h-8 px-2 sm:px-3"
          >
            <RefreshCw className={`h-3 w-3 sm:h-4 sm:w-4 ${refreshing ? 'animate-spin' : ''}`} />
            <span className="text-xs">Refresh</span>
          </Button>
        </div>
      </div>

      {/* Key Performance Indicators */}
      <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-3 md:gap-4">
        <InteractiveKPICard
          title="Total Bookings"
          value={stats.totalBookings.toLocaleString()}
          change={0}
          icon={Calendar}
          onClick={() => setActiveTab('bookings')}
          color="blue"
          subtitle="all time"
        />
        <InteractiveKPICard
          title="Today's Bookings"
          value={stats.todayBookings.toLocaleString()}
          change={0}
          icon={Clock}
          onClick={() => setActiveTab('bookings')}
          color="green"
          subtitle="scheduled today"
        />
        <InteractiveKPICard
          title="Upcoming"
          value={stats.upcomingBookings.toLocaleString()}
          change={0}
          icon={CheckCircle}
          onClick={() => setActiveTab('bookings')}
          color="purple"
          subtitle="future bookings"
        />
        <InteractiveKPICard
          title="Completed"
          value={stats.completedBookings.toLocaleString()}
          change={0}
          icon={CheckCircle}
          onClick={() => setActiveTab('bookings')}
          color="orange"
          subtitle="finished"
        />
      </div>

      {/* Booking Status Distribution */}
      <Card className="cursor-pointer hover:shadow-lg transition-shadow border-purple-100 bg-gradient-to-br from-white to-purple-50" onClick={() => setActiveTab('bookings')}>
        <CardHeader className="p-3 md:p-6">
          <CardTitle className="flex items-center space-x-2 text-sm md:text-base">
            <Calendar className="h-4 w-4 md:h-5 md:w-5 text-purple-600" />
            <span>Booking Status Distribution</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="p-3 md:p-6">
          <div className="grid grid-cols-2 gap-2 md:gap-4">
            <div className="text-center">
              <div className="text-lg md:text-2xl font-bold text-blue-600">{stats.completedBookings}</div>
              <div className="text-xs text-gray-500">Completed</div>
            </div>
            <div className="text-center">
              <div className="text-lg md:text-2xl font-bold text-green-600">{stats.upcomingBookings}</div>
              <div className="text-xs text-gray-500">Upcoming</div>
            </div>
            <div className="text-center">
              <div className="text-lg md:text-2xl font-bold text-yellow-600">{stats.todayBookings}</div>
              <div className="text-xs text-gray-500">Today</div>
            </div>
            <div className="text-center">
              <div className="text-lg md:text-2xl font-bold text-red-600">{stats.cancelledBookings}</div>
              <div className="text-xs text-gray-500">Cancelled</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* System Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-6">
        <Card className="cursor-pointer hover:shadow-lg transition-shadow border-purple-100 bg-gradient-to-br from-white to-green-50" onClick={() => setActiveTab('reps')}>
          <CardHeader className="p-3 md:p-6">
            <CardTitle className="flex items-center space-x-2 text-sm md:text-base">
              <Users className="h-4 w-4 md:h-5 md:w-5 text-green-600" />
              <span>Team</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4 p-3 md:p-6">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Active Reps</span>
              <span className="text-lg font-bold text-green-700">{stats.totalSalesReps}</span>
            </div>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-lg transition-shadow border-purple-100 bg-gradient-to-br from-white to-blue-50" onClick={() => setActiveTab('postal-codes')}>
          <CardHeader className="p-3 md:p-6">
            <CardTitle className="flex items-center space-x-2 text-sm md:text-base">
              <MapPin className="h-4 w-4 md:h-5 md:w-5 text-blue-600" />
              <span>Coverage</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4 p-3 md:p-6">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Service Areas</span>
              <span className="text-lg font-bold text-blue-700">{stats.postalCodes}</span>
            </div>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-lg transition-shadow border-purple-100 bg-gradient-to-br from-white to-orange-50" onClick={() => setActiveTab('users')}>
          <CardHeader className="p-3 md:p-6">
            <CardTitle className="flex items-center space-x-2 text-sm md:text-base">
              <Users className="h-4 w-4 md:h-5 md:w-5 text-orange-600" />
              <span>Users</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4 p-3 md:p-6">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Total Users</span>
              <span className="text-lg font-bold text-orange-700">{stats.totalUsers}</span>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}