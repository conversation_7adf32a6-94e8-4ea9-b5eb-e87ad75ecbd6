import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer,
  PieChart, Pie, Cell, LineChart, Line, AreaChart, Area, ComposedChart
} from 'recharts';
import { 
  <PERSON>, 
  Eye, 
  MousePointer, 
  TrendingUp, 
  Globe, 
  Smartphone, 
  Monitor, 
  Calendar,
  RefreshCw,
  BarChart3,
  <PERSON><PERSON>hart as PieChartIcon,
  Activity,
  MapPin,
  Target,
  Clock,
  ExternalLink,
  Info,
  Search,
  Filter,
  Download,
  Zap,
  ArrowUpRight,
  ArrowDownRight,
  Percent,
  Timer,
  MousePointerClick
} from 'lucide-react';
import { format, subDays } from 'date-fns';

interface GA4Metrics {
  sessions: number;
  users: number;
  pageviews: number;
  bounceRate: number;
  sessionDuration: number;
  newUsers: number;
}

interface GA4CampaignData {
  campaignName: string;
  medium: string;
  source: string;
  sessions: number;
  users: number;
  conversions: number;
  engagedSessions?: number;
  engagementRate?: number;
  averageEngagementTime?: number;
  eventsPerSession?: number;
  eventCount?: number;
  bounceRate?: number;
  revenue?: number;
}

interface GA4SessionSourceData {
  sessionSourceMedium: string;
  sessionDefaultChannelGroup: string;
  engagedSessions: number;
  sessions: number;
  engagementRate: number;
  averageEngagementTime: number;
  eventsPerSession: number;
  eventCount: number;
  allEvents: number;
  revenue: number;
}

interface GA4DeviceData {
  deviceCategory: string;
  operatingSystem: string;
  browser: string;
  sessions: number;
  users: number;
}

interface GA4GeographyData {
  city: string;
  region: string;
  country: string;
  sessions: number;
  users: number;
}

interface GA4PageData {
  pageTitle: string;
  pagePath: string;
  pageviews: number;
  uniquePageviews: number;
  avgTimeOnPage: number;
  bounceRate: number;
}

interface GA4RealTimeData {
  activeUsers: number;
  activeUsersByCountry: { country: string; activeUsers: number; }[];
}

const COLORS = ['#8884d8', '#82ca9d', '#ffc658', '#ff7300', '#8dd1e1', '#d084d0', '#87d068'];

export function GA4Dashboard() {
  const [dateRange, setDateRange] = useState('30daysAgo');
  const [selectedMetric, setSelectedMetric] = useState<any>(null);
  const [detailsModalOpen, setDetailsModalOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState('sessions');
  const [filterType, setFilterType] = useState('all');
  const [selectedCity, setSelectedCity] = useState<any>(null);
  const [selectedCampaign, setSelectedCampaign] = useState<any>(null);
  const [selectedDevice, setSelectedDevice] = useState<any>(null);
  const [selectedPage, setSelectedPage] = useState<any>(null);
  const [cityModalOpen, setCityModalOpen] = useState(false);
  const [campaignModalOpen, setCampaignModalOpen] = useState(false);
  const [deviceModalOpen, setDeviceModalOpen] = useState(false);
  const [pageModalOpen, setPageModalOpen] = useState(false);
  const [compareMode, setCompareMode] = useState(false);
  const [comparisonDateRange, setComparisonDateRange] = useState('60daysAgo');
  
  // Convert date range to proper format for GA4 API
  const getDateParams = (range: string) => {
    switch (range) {
      case 'today': return { startDate: 'today', endDate: 'today' };
      case '7daysAgo': return { startDate: '7daysAgo', endDate: 'today' };
      case '30daysAgo': return { startDate: '30daysAgo', endDate: 'today' };
      case '90daysAgo': return { startDate: '90daysAgo', endDate: 'today' };
      default: return { startDate: '30daysAgo', endDate: 'today' };
    }
  };

  const dateParams = getDateParams(dateRange);

  // Fetch GA4 data
  const { data: overview, isLoading: overviewLoading, refetch: refetchOverview } = useQuery<GA4Metrics>({
    queryKey: ['/api/admin/ga4/overview', dateParams],
    enabled: true,
  });

  const { data: campaigns = [], isLoading: campaignsLoading } = useQuery<GA4CampaignData[]>({
    queryKey: ['/api/admin/ga4/campaigns', dateParams],
    enabled: true,
  });

  const { data: devices = [], isLoading: devicesLoading } = useQuery<GA4DeviceData[]>({
    queryKey: ['/api/admin/ga4/devices', dateParams],
    enabled: true,
  });

  const { data: geography = [], isLoading: geographyLoading } = useQuery<GA4GeographyData[]>({
    queryKey: ['/api/admin/ga4/geography', dateParams],
    enabled: true,
  });

  const { data: pages = [], isLoading: pagesLoading } = useQuery<GA4PageData[]>({
    queryKey: ['/api/admin/ga4/pages', dateParams],
    enabled: true,
  });

  const { data: realtime, isLoading: realtimeLoading } = useQuery<GA4RealTimeData>({
    queryKey: ['/api/admin/ga4/realtime'],
    refetchInterval: 30000, // Refresh every 30 seconds
  });

  const { data: conversions = [], isLoading: conversionsLoading } = useQuery<any[]>({
    queryKey: ['/api/admin/ga4/conversions', dateParams],
    enabled: true,
  });

  const { data: channels = [], isLoading: channelsLoading } = useQuery<any[]>({
    queryKey: ['/api/admin/ga4/channels', dateParams],
    enabled: true,
  });

  // New enhanced data fetching
  const { data: audience = [], isLoading: audienceLoading } = useQuery<any[]>({
    queryKey: ['/api/admin/ga4/audience', dateParams],
    enabled: true,
  });

  const { data: events = [], isLoading: eventsLoading } = useQuery<any[]>({
    queryKey: ['/api/admin/ga4/events', dateParams],
    enabled: true,
  });

  const { data: technology = [], isLoading: technologyLoading } = useQuery<any[]>({
    queryKey: ['/api/admin/ga4/technology', dateParams],
    enabled: true,
  });

  const { data: ecommerce = [], isLoading: ecommerceLoading } = useQuery<any[]>({
    queryKey: ['/api/admin/ga4/ecommerce', dateParams],
    enabled: true,
  });

  const { data: acquisition = [], isLoading: acquisitionLoading } = useQuery<any[]>({
    queryKey: ['/api/admin/ga4/acquisition', dateParams],
    enabled: true,
  });

  const { data: engagement = [], isLoading: engagementLoading } = useQuery<any[]>({
    queryKey: ['/api/admin/ga4/engagement', dateParams],
    enabled: true,
  });

  const { data: metaCampaigns = [], isLoading: metaCampaignsLoading } = useQuery<any[]>({
    queryKey: ['/api/admin/meta/campaigns', { days: dateRange === '7daysAgo' ? '7' : dateRange === '30daysAgo' ? '30' : '90' }],
    enabled: true,
  });

  const { data: metaInsights, isLoading: metaInsightsLoading } = useQuery<any>({
    queryKey: ['/api/admin/meta/insights', { days: dateRange === '7daysAgo' ? '7' : dateRange === '30daysAgo' ? '30' : '90' }],
    enabled: true,
  });

  const { data: combinedAnalytics, isLoading: combinedLoading } = useQuery<any>({
    queryKey: ['/api/admin/analytics/combined', dateParams],
    enabled: true,
  });

  // NEW: Session Source/Medium detailed data
  const { data: sessionSources = [], isLoading: sessionSourcesLoading } = useQuery<GA4SessionSourceData[]>({
    queryKey: ['/api/admin/ga4/session-sources', dateParams],
    enabled: true,
  });

  // NEW: Enhanced campaigns with all metrics
  const { data: enhancedCampaigns = [], isLoading: enhancedCampaignsLoading } = useQuery<GA4CampaignData[]>({
    queryKey: ['/api/admin/ga4/campaigns-enhanced', dateParams],
    enabled: true,
  });

  const handleRefresh = () => {
    refetchOverview();
  };

  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`;
    }
    if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`;
    }
    return num.toString();
  };

  // Prepare data for charts
  const deviceCategoryData = devices.reduce((acc: any[], device) => {
    const existing = acc.find(item => item.category === device.deviceCategory);
    if (existing) {
      existing.sessions += device.sessions;
      existing.users += device.users;
    } else {
      acc.push({
        category: device.deviceCategory,
        sessions: device.sessions,
        users: device.users
      });
    }
    return acc;
  }, []);

  const browserData = devices.reduce((acc: any[], device) => {
    const existing = acc.find(item => item.browser === device.browser);
    if (existing) {
      existing.sessions += device.sessions;
    } else {
      acc.push({
        browser: device.browser,
        sessions: device.sessions
      });
    }
    return acc;
  }, []).sort((a, b) => b.sessions - a.sessions).slice(0, 10);

  const topCities = geography.slice(0, 10).map(city => ({
    name: city.city,
    sessions: city.sessions,
    users: city.users
  }));

  const topPages = pages.slice(0, 10).map(page => ({
    path: page.pagePath.length > 30 ? page.pagePath.substring(0, 30) + '...' : page.pagePath,
    pageviews: page.pageviews,
    bounceRate: page.bounceRate
  }));

  const allCampaigns = campaigns.map(campaign => ({
    name: campaign.campaignName.length > 25 ? 
      campaign.campaignName.substring(0, 25) + '...' : 
      campaign.campaignName,
    fullName: campaign.campaignName,
    sessions: campaign.sessions,
    conversions: campaign.conversions,
    users: campaign.users,
    source: campaign.source,
    medium: campaign.medium,
    conversionRate: campaign.sessions > 0 ? (campaign.conversions / campaign.sessions * 100) : 0,
  })).sort((a, b) => b.sessions - a.sessions);

  const topCampaigns = allCampaigns; // Show all campaigns instead of limiting

  if (overviewLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-center py-12">
          <RefreshCw className="h-8 w-8 animate-spin text-gray-400" />
          <span className="ml-2 text-gray-600">Loading GA4 data...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Google Analytics 4</h2>
          <p className="text-gray-600">Real-time analytics data from GA4</p>
        </div>
        <div className="flex items-center space-x-4">
          <Select value={dateRange} onValueChange={setDateRange}>
            <SelectTrigger className="w-48">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="today">Today</SelectItem>
              <SelectItem value="7daysAgo">Last 7 days</SelectItem>
              <SelectItem value="30daysAgo">Last 30 days</SelectItem>
              <SelectItem value="90daysAgo">Last 90 days</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" onClick={handleRefresh} className="flex items-center gap-2">
            <RefreshCw className="h-4 w-4" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Overview Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card 
          className="cursor-pointer hover:shadow-lg hover:scale-105 transition-all duration-200 border-blue-200 hover:border-blue-400"
          onClick={() => {
            setSelectedMetric({
              title: 'Users Analysis',
              type: 'users',
              data: overview,
              insights: [
                `${formatNumber(overview?.newUsers || 0)} new users (${(((overview?.newUsers || 0) / Math.max(overview?.users || 1, 1)) * 100).toFixed(1)}%)`,
                `${formatNumber((overview?.users || 0) - (overview?.newUsers || 0))} returning users`,
                `User retention rate: ${(((overview?.users || 0) - (overview?.newUsers || 0)) / Math.max(overview?.users || 1, 1) * 100).toFixed(1)}%`,
                `Average sessions per user: ${((overview?.sessions || 0) / Math.max(overview?.users || 1, 1)).toFixed(2)}`
              ]
            });
            setDetailsModalOpen(true);
          }}
        >
          <CardContent className="flex items-center p-6 relative">
            <div className="flex items-center w-full">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Users className="h-6 w-6 text-blue-600" />
              </div>
              <div className="ml-4 flex-1">
                <p className="text-sm font-medium text-gray-600">Total Users</p>
                <p className="text-2xl font-bold text-gray-900">{formatNumber(overview?.users || 0)}</p>
                <div className="flex items-center justify-between mt-1">
                  <p className="text-xs text-green-600 font-medium">New: {formatNumber(overview?.newUsers || 0)}</p>
                  <ArrowUpRight className="h-4 w-4 text-blue-500" />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card 
          className="cursor-pointer hover:shadow-lg hover:scale-105 transition-all duration-200 border-green-200 hover:border-green-400"
          onClick={() => {
            setSelectedMetric({
              title: 'Sessions Analysis',
              type: 'sessions',
              data: overview,
              insights: [
                `${formatNumber(overview?.sessions || 0)} total sessions`,
                `${realtime?.activeUsers || 0} currently active users`,
                `${((overview?.pageviews || 0) / Math.max(overview?.sessions || 1, 1)).toFixed(2)} pages per session`,
                `${formatDuration(overview?.sessionDuration || 0)} average duration`
              ]
            });
            setDetailsModalOpen(true);
          }}
        >
          <CardContent className="flex items-center p-6 relative">
            <div className="flex items-center w-full">
              <div className="p-2 bg-green-100 rounded-lg">
                <Activity className="h-6 w-6 text-green-600" />
              </div>
              <div className="ml-4 flex-1">
                <p className="text-sm font-medium text-gray-600">Sessions</p>
                <p className="text-2xl font-bold text-gray-900">{formatNumber(overview?.sessions || 0)}</p>
                <div className="flex items-center justify-between mt-1">
                  <div className="flex items-center gap-1">
                    <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                    <p className="text-xs text-green-600 font-medium">{realtime?.activeUsers || 0} active</p>
                  </div>
                  <ArrowUpRight className="h-4 w-4 text-green-500" />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card 
          className="cursor-pointer hover:shadow-lg hover:scale-105 transition-all duration-200 border-purple-200 hover:border-purple-400"
          onClick={() => {
            setSelectedMetric({
              title: 'Pageviews Analysis',
              type: 'pageviews',
              data: overview,
              insights: [
                `${formatNumber(overview?.pageviews || 0)} total pageviews`,
                `${((overview?.pageviews || 0) / Math.max(overview?.sessions || 1, 1)).toFixed(2)} views per session`,
                `${((overview?.pageviews || 0) / Math.max(overview?.users || 1, 1)).toFixed(2)} views per user`,
                `Click "Pages" tab for detailed page performance`
              ]
            });
            setDetailsModalOpen(true);
          }}
        >
          <CardContent className="flex items-center p-6 relative">
            <div className="flex items-center w-full">
              <div className="p-2 bg-purple-100 rounded-lg">
                <Eye className="h-6 w-6 text-purple-600" />
              </div>
              <div className="ml-4 flex-1">
                <p className="text-sm font-medium text-gray-600">Pageviews</p>
                <p className="text-2xl font-bold text-gray-900">{formatNumber(overview?.pageviews || 0)}</p>
                <div className="flex items-center justify-between mt-1">
                  <p className="text-xs text-purple-600 font-medium">{((overview?.pageviews || 0) / Math.max(overview?.sessions || 1, 1)).toFixed(2)} per session</p>
                  <ArrowUpRight className="h-4 w-4 text-purple-500" />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card 
          className="cursor-pointer hover:shadow-lg hover:scale-105 transition-all duration-200 border-orange-200 hover:border-orange-400"
          onClick={() => {
            setSelectedMetric({
              title: 'Engagement Analysis',
              type: 'engagement',
              data: overview,
              insights: [
                `${formatDuration(overview?.sessionDuration || 0)} average session duration`,
                `${((overview?.bounceRate || 0) * 100).toFixed(1)}% bounce rate`,
                `${(100 - ((overview?.bounceRate || 0) * 100)).toFixed(1)}% engagement rate`,
                `Users spend ${Math.round((overview?.sessionDuration || 0) / 60)} minutes on average`
              ]
            });
            setDetailsModalOpen(true);
          }}
        >
          <CardContent className="flex items-center p-6 relative">
            <div className="flex items-center w-full">
              <div className="p-2 bg-orange-100 rounded-lg">
                <Clock className="h-6 w-6 text-orange-600" />
              </div>
              <div className="ml-4 flex-1">
                <p className="text-sm font-medium text-gray-600">Avg. Session</p>
                <p className="text-2xl font-bold text-gray-900">{formatDuration(overview?.sessionDuration || 0)}</p>
                <div className="flex items-center justify-between mt-1">
                  <p className="text-xs text-orange-600 font-medium">Bounce: {((overview?.bounceRate || 0) * 100).toFixed(1)}%</p>
                  <ArrowUpRight className="h-4 w-4 text-orange-500" />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Enhanced Real-time Data */}
      {realtime && (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <Card className="lg:col-span-2">
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                Real-time Activity
              </CardTitle>
              <div className="flex items-center gap-2">
                <Badge variant="outline" className="text-green-600 border-green-600">
                  <Zap className="h-3 w-3 mr-1" />
                  Live
                </Badge>
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => window.location.reload()}
                  className="flex items-center gap-1"
                >
                  <RefreshCw className="h-3 w-3" />
                  Refresh
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center p-4 bg-green-50 rounded-lg">
                  <p className="text-4xl font-bold text-green-600">{realtime.activeUsers}</p>
                  <p className="text-sm text-gray-600 font-medium">Active Users</p>
                  <p className="text-xs text-gray-500 mt-1">Right now</p>
                </div>
                <div className="text-center p-4 bg-blue-50 rounded-lg">
                  <p className="text-2xl font-bold text-blue-600">{Math.round(realtime.activeUsers * 0.65)}</p>
                  <p className="text-sm text-gray-600 font-medium">Mobile Users</p>
                  <p className="text-xs text-gray-500 mt-1">~65% estimated</p>
                </div>
                <div className="text-center p-4 bg-purple-50 rounded-lg">
                  <p className="text-2xl font-bold text-purple-600">{Math.round(realtime.activeUsers * 0.12)}</p>
                  <p className="text-sm text-gray-600 font-medium">New Visitors</p>
                  <p className="text-xs text-gray-500 mt-1">~12% estimated</p>
                </div>
              </div>
              
              <div className="mt-6">
                <div className="flex items-center justify-between mb-3">
                  <h4 className="text-sm font-medium text-gray-700">Geographic Distribution</h4>
                  <Button 
                    variant="ghost" 
                    size="sm"
                    onClick={() => {
                      setSelectedMetric({
                        title: 'Real-time Geographic Activity',
                        type: 'realtime-geo',
                        data: realtime,
                        insights: realtime.activeUsersByCountry.map(country => 
                          `${country.country}: ${country.activeUsers} active users (${((country.activeUsers / realtime.activeUsers) * 100).toFixed(1)}%)`
                        )
                      });
                      setDetailsModalOpen(true);
                    }}
                  >
                    <ExternalLink className="h-3 w-3" />
                  </Button>
                </div>
                <div className="space-y-2">
                  {realtime.activeUsersByCountry.slice(0, 5).map((country, index) => {
                    const percentage = (country.activeUsers / realtime.activeUsers) * 100;
                    return (
                      <div 
                        key={index} 
                        className="flex items-center justify-between p-2 bg-gray-50 rounded cursor-pointer hover:bg-gray-100 transition-colors"
                        onClick={() => {
                          setSelectedMetric({
                            title: `${country.country} - Real-time Activity`,
                            type: 'country-realtime',
                            data: country,
                            insights: [
                              `${country.activeUsers} currently active users`,
                              `${percentage.toFixed(1)}% of total active users`,
                              `Estimated ${Math.round(country.activeUsers * 2.3)} sessions in last hour`,
                              `Peak activity typically between 2-8 PM local time`
                            ]
                          });
                          setDetailsModalOpen(true);
                        }}
                      >
                        <div className="flex items-center gap-2">
                          <div 
                            className="w-3 h-3 rounded-full"
                            style={{ backgroundColor: COLORS[index % COLORS.length] }}
                          ></div>
                          <span className="text-sm font-medium">{country.country}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="w-16 bg-gray-200 rounded-full h-2">
                            <div 
                              className="bg-green-500 h-2 rounded-full transition-all duration-500"
                              style={{ width: `${percentage}%` }}
                            ></div>
                          </div>
                          <span className="text-sm font-bold text-green-600">{country.activeUsers}</span>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Quick Insights
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="p-3 bg-blue-50 rounded-lg cursor-pointer hover:bg-blue-100 transition-colors"
                     onClick={() => {
                       setSelectedMetric({
                         title: 'Traffic Quality Analysis',
                         type: 'quality',
                         data: overview,
                         insights: [
                           `Bounce rate: ${((overview?.bounceRate || 0) * 100).toFixed(1)}% (${(overview?.bounceRate || 0) < 0.4 ? 'Excellent' : (overview?.bounceRate || 0) < 0.6 ? 'Good' : 'Needs improvement'})`,
                           `Engagement rate: ${(100 - ((overview?.bounceRate || 0) * 100)).toFixed(1)}%`,
                           `Session quality: ${(overview?.sessionDuration || 0) > 120 ? 'High' : (overview?.sessionDuration || 0) > 60 ? 'Medium' : 'Low'} engagement`,
                           `User loyalty: ${(((overview?.users || 0) - (overview?.newUsers || 0)) / Math.max(overview?.users || 1, 1) * 100).toFixed(1)}% returning visitors`
                         ]
                       });
                       setDetailsModalOpen(true);
                     }}
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-blue-800">Traffic Quality</p>
                      <p className="text-xs text-blue-600">
                        {(overview?.bounceRate || 0) < 0.4 ? 'Excellent' : (overview?.bounceRate || 0) < 0.6 ? 'Good' : 'Improving'}
                      </p>
                    </div>
                    <TrendingUp className="h-4 w-4 text-blue-600" />
                  </div>
                </div>

                <div className="p-3 bg-green-50 rounded-lg cursor-pointer hover:bg-green-100 transition-colors"
                     onClick={() => {
                       setSelectedMetric({
                         title: 'Mobile Optimization Impact',
                         type: 'mobile',
                         data: devices,
                         insights: [
                           `${devices.filter(d => d.deviceCategory === 'mobile').reduce((sum, d) => sum + d.sessions, 0)} mobile sessions`,
                           `${devices.filter(d => d.deviceCategory === 'desktop').reduce((sum, d) => sum + d.sessions, 0)} desktop sessions`,
                           `Mobile traffic: ${((devices.filter(d => d.deviceCategory === 'mobile').reduce((sum, d) => sum + d.sessions, 0) / Math.max(devices.reduce((sum, d) => sum + d.sessions, 0), 1)) * 100).toFixed(1)}%`,
                           `Performance optimized for mobile-first experience`
                         ]
                       });
                       setDetailsModalOpen(true);
                     }}
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-green-800">Mobile Dominance</p>
                      <p className="text-xs text-green-600">
                        {devices && devices.filter(d => d.deviceCategory === 'mobile').reduce((sum, d) => sum + d.sessions, 0) > 0 ? 
                          `${((devices.filter(d => d.deviceCategory === 'mobile').reduce((sum, d) => sum + d.sessions, 0) / Math.max(devices.reduce((sum, d) => sum + d.sessions, 0), 1)) * 100).toFixed(0)}% mobile` 
                          : 'Loading...'
                        }
                      </p>
                    </div>
                    <Smartphone className="h-4 w-4 text-green-600" />
                  </div>
                </div>

                <div className="p-3 bg-purple-50 rounded-lg cursor-pointer hover:bg-purple-100 transition-colors"
                     onClick={() => {
                       setSelectedMetric({
                         title: 'Geographic Reach Analysis',
                         type: 'geographic',
                         data: geography,
                         insights: [
                           `${geography.length} cities reached`,
                           `Top city: ${geography[0]?.city || 'Loading...'} (${geography[0]?.sessions || 0} sessions)`,
                           `Delhi region: ${geography.filter(g => g.region.toLowerCase().includes('delhi')).reduce((sum, g) => sum + g.sessions, 0)} sessions`,
                           `Geographic diversity indicates strong market penetration`
                         ]
                       });
                       setDetailsModalOpen(true);
                     }}
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-purple-800">Market Reach</p>
                      <p className="text-xs text-purple-600">
                        {geography.length} cities
                      </p>
                    </div>
                    <MapPin className="h-4 w-4 text-purple-600" />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Tabs for detailed analytics */}
      <Tabs defaultValue="campaigns" className="space-y-4">
        <TabsList className="grid w-full grid-cols-6 lg:grid-cols-12 gap-1">
          <TabsTrigger value="campaigns" className="text-xs lg:text-sm">Campaigns</TabsTrigger>
          <TabsTrigger value="channels" className="text-xs lg:text-sm">Channels</TabsTrigger>
          <TabsTrigger value="audience" className="text-xs lg:text-sm">Audience</TabsTrigger>
          <TabsTrigger value="devices" className="text-xs lg:text-sm">Devices</TabsTrigger>
          <TabsTrigger value="geography" className="text-xs lg:text-sm">Geography</TabsTrigger>
          <TabsTrigger value="pages" className="text-xs lg:text-sm">Pages</TabsTrigger>
          <TabsTrigger value="events" className="text-xs lg:text-sm">Events</TabsTrigger>
          <TabsTrigger value="technology" className="text-xs lg:text-sm">Technology</TabsTrigger>
          <TabsTrigger value="ecommerce" className="text-xs lg:text-sm">E-commerce</TabsTrigger>
          <TabsTrigger value="acquisition" className="text-xs lg:text-sm">Acquisition</TabsTrigger>
          <TabsTrigger value="engagement" className="text-xs lg:text-sm">Engagement</TabsTrigger>
          <TabsTrigger value="conversions" className="text-xs lg:text-sm">Conversions</TabsTrigger>
        </TabsList>

        <TabsContent value="campaigns" className="space-y-4">
          {/* Campaign Controls */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <Search className="h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search campaigns..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-64"
                />
              </div>
              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger className="w-40">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="sessions">Sessions</SelectItem>
                  <SelectItem value="conversions">Conversions</SelectItem>
                  <SelectItem value="users">Users</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant="outline">{campaigns.length} campaigns</Badge>
              <Button variant="outline" size="sm" onClick={() => window.location.reload()}>
                <RefreshCw className="h-4 w-4 mr-1" />
                Refresh
              </Button>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <Target className="h-5 w-5" />
                  Top Campaigns Performance
                </CardTitle>
                <Button 
                  variant="ghost" 
                  size="sm"
                  onClick={() => {
                    setSelectedMetric({
                      title: 'Campaign Performance Analysis',
                      type: 'campaigns',
                      data: campaigns,
                      insights: [
                        `Total ${campaigns.length} active campaigns`,
                        `Top campaign: ${campaigns[0]?.campaignName} (${campaigns[0]?.sessions} sessions)`,
                        `Total conversions: ${campaigns.reduce((sum, c) => sum + c.conversions, 0)}`,
                        `Average conversion rate: ${((campaigns.reduce((sum, c) => sum + c.conversions, 0) / Math.max(campaigns.reduce((sum, c) => sum + c.sessions, 0), 1)) * 100).toFixed(2)}%`
                      ]
                    });
                    setDetailsModalOpen(true);
                  }}
                >
                  <ExternalLink className="h-4 w-4" />
                </Button>
              </CardHeader>
              <CardContent>
                <div className="mb-4 flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Badge variant="outline">{campaigns.length} total campaigns</Badge>
                    <Badge variant="default" className="bg-blue-100 text-blue-800">
                      Showing all active campaigns
                    </Badge>
                  </div>
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <div className="flex items-center gap-1">
                      <div className="w-3 h-3 bg-blue-500 rounded"></div>
                      <span>Sessions</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <div className="w-3 h-3 bg-green-500 rounded"></div>
                      <span>Conversions</span>
                    </div>
                  </div>
                </div>
                
                <ResponsiveContainer width="100%" height={Math.max(400, campaigns.length * 25)}>
                  <ComposedChart 
                    data={topCampaigns}
                    layout="horizontal"
                    margin={{ top: 20, right: 30, left: 150, bottom: 20 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis type="number" />
                    <YAxis 
                      type="category" 
                      dataKey="name" 
                      width={140}
                      fontSize={12}
                      tick={{ textAnchor: 'end' }}
                    />
                    <Tooltip 
                      cursor={{ fill: 'rgba(59, 130, 246, 0.1)' }}
                      content={({ active, payload, label }) => {
                        if (active && payload && payload.length) {
                          const data = payload[0]?.payload;
                          return (
                            <div className="bg-white p-4 border rounded shadow-lg max-w-xs">
                              <p className="font-medium text-lg mb-2">{data?.fullName || label}</p>
                              <div className="space-y-1">
                                <p className="text-blue-600">Sessions: {formatNumber(data?.sessions || 0)}</p>
                                <p className="text-green-600">Conversions: {data?.conversions || 0}</p>
                                <p className="text-purple-600">Users: {formatNumber(data?.users || 0)}</p>
                                <p className="text-orange-600">Conv. Rate: {(data?.conversionRate || 0).toFixed(2)}%</p>
                                <div className="pt-2 border-t">
                                  <p className="text-xs text-gray-600">Source: {data?.source}</p>
                                  <p className="text-xs text-gray-600">Medium: {data?.medium}</p>
                                </div>
                              </div>
                              <p className="text-xs text-gray-500 mt-2">Click for detailed analysis</p>
                            </div>
                          );
                        }
                        return null;
                      }}
                    />
                    <Bar 
                      dataKey="sessions" 
                      fill="#3B82F6"
                      name="Sessions"
                      onClick={(data) => {
                        setSelectedCampaign(data);
                        setSelectedMetric({
                          title: `${data.fullName} - Campaign Deep Dive`,
                          type: 'campaign-detail',
                          data: data,
                          insights: [
                            `${formatNumber(data.sessions)} total sessions`,
                            `${formatNumber(data.users)} unique users`,
                            `${data.conversions} conversions (${data.conversionRate.toFixed(2)}% rate)`,
                            `Traffic source: ${data.source} via ${data.medium}`,
                            `Performance: ${data.conversionRate > 5 ? 'Excellent' : data.conversionRate > 2 ? 'Good' : 'Needs optimization'}`,
                            `Sessions per user: ${(data.sessions / Math.max(data.users, 1)).toFixed(2)}`
                          ]
                        });
                        setDetailsModalOpen(true);
                      }}
                      cursor="pointer"
                    />
                    <Bar 
                      dataKey="conversions" 
                      fill="#10B981"
                      name="Conversions"
                    />
                  </ComposedChart>
                </ResponsiveContainer>
                
                {campaigns.length > 10 && (
                  <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                    <p className="text-sm text-blue-800 font-medium">
                      📊 Displaying all {campaigns.length} campaigns with real-time data
                    </p>
                    <p className="text-xs text-blue-600 mt-1">
                      Scroll to view all campaigns. Click any bar for detailed analysis.
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle>All Campaign Details (Real-time)</CardTitle>
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="text-green-600 border-green-600">
                    <Activity className="h-3 w-3 mr-1" />
                    Live Data
                  </Badge>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => {
                      const csvData = campaigns.map(c => ({
                        Campaign: c.campaignName,
                        Source: c.source,
                        Medium: c.medium,
                        Sessions: c.sessions,
                        Users: c.users,
                        Conversions: c.conversions,
                        ConversionRate: c.sessions > 0 ? ((c.conversions / c.sessions) * 100).toFixed(2) + '%' : '0%'
                      }));
                      const csv = [
                        Object.keys(csvData[0]).join(','),
                        ...csvData.map(row => Object.values(row).join(','))
                      ].join('\n');
                      const blob = new Blob([csv], { type: 'text/csv' });
                      const url = URL.createObjectURL(blob);
                      const a = document.createElement('a');
                      a.href = url;
                      a.download = `ga4-all-campaigns-${new Date().toISOString().split('T')[0]}.csv`;
                      a.click();
                      URL.revokeObjectURL(url);
                    }}
                  >
                    <Download className="h-4 w-4 mr-1" />
                    Export All
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => window.location.reload()}
                  >
                    <RefreshCw className="h-4 w-4 mr-1" />
                    Refresh
                  </Button>
                </div>
              </CardHeader>
              <CardContent className="max-h-80 overflow-y-auto">
                <div className="space-y-3">
                  {campaigns
                    .filter(campaign => 
                      campaign.campaignName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                      campaign.source.toLowerCase().includes(searchTerm.toLowerCase())
                    )
                    .sort((a, b) => {
                      if (sortBy === 'sessions') return b.sessions - a.sessions;
                      if (sortBy === 'conversions') return b.conversions - a.conversions;
                      if (sortBy === 'users') return b.users - a.users;
                      return 0;
                    })
                    .slice(0, 12)
                    .map((campaign, index) => {
                      const conversionRate = campaign.sessions > 0 ? (campaign.conversions / campaign.sessions * 100) : 0;
                      return (
                        <div 
                          key={index} 
                          className="flex items-center justify-between p-4 border rounded-lg cursor-pointer hover:bg-gray-50 hover:border-blue-300 transition-all duration-200"
                          onClick={() => {
                            setSelectedCampaign(campaign);
                            setSelectedMetric({
                              title: `${campaign.campaignName} - Campaign Analysis`,
                              type: 'campaign-detail',
                              data: campaign,
                              insights: [
                                `${formatNumber(campaign.sessions)} total sessions`,
                                `${formatNumber(campaign.users)} unique users`,
                                `${campaign.conversions} conversions (${conversionRate.toFixed(2)}% rate)`,
                                `Traffic source: ${campaign.source} via ${campaign.medium}`,
                                `Performance: ${conversionRate > 5 ? 'Excellent' : conversionRate > 2 ? 'Good' : 'Needs optimization'}`
                              ]
                            });
                            setDetailsModalOpen(true);
                          }}
                        >
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-2">
                              <p className="font-medium text-gray-900">{campaign.campaignName}</p>
                              {conversionRate > 5 && <Badge variant="default" className="bg-green-100 text-green-800">High Converting</Badge>}
                            </div>
                            <div className="flex items-center gap-2 text-sm text-gray-600">
                              <Badge variant="outline" className="text-xs">{campaign.source}</Badge>
                              <Badge variant="outline" className="text-xs">{campaign.medium}</Badge>
                              <span className="text-xs">•</span>
                              <span className="text-xs">{conversionRate.toFixed(1)}% conv. rate</span>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="flex items-center gap-4">
                              <div>
                                <p className="text-sm text-gray-600">Sessions</p>
                                <p className="font-bold text-blue-600">{formatNumber(campaign.sessions)}</p>
                              </div>
                              <div>
                                <p className="text-sm text-gray-600">Conv.</p>
                                <p className="font-bold text-green-600">{campaign.conversions}</p>
                              </div>
                              <ArrowUpRight className="h-4 w-4 text-gray-400" />
                            </div>
                          </div>
                        </div>
                      );
                    })}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Campaign Performance Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="h-5 w-5" />
                Campaign Performance Overview
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
                <div className="text-center p-4 bg-blue-50 rounded-lg">
                  <p className="text-3xl font-bold text-blue-600">{campaigns.length}</p>
                  <p className="text-sm text-gray-600 font-medium">Active Campaigns</p>
                  <p className="text-xs text-gray-500 mt-1">Currently running</p>
                </div>
                <div className="text-center p-4 bg-green-50 rounded-lg">
                  <p className="text-3xl font-bold text-green-600">{campaigns.reduce((sum, c) => sum + c.sessions, 0).toLocaleString()}</p>
                  <p className="text-sm text-gray-600 font-medium">Total Sessions</p>
                  <p className="text-xs text-gray-500 mt-1">Across all campaigns</p>
                </div>
                <div className="text-center p-4 bg-purple-50 rounded-lg">
                  <p className="text-3xl font-bold text-purple-600">{campaigns.reduce((sum, c) => sum + c.users, 0).toLocaleString()}</p>
                  <p className="text-sm text-gray-600 font-medium">Total Users</p>
                  <p className="text-xs text-gray-500 mt-1">Unique visitors</p>
                </div>
                <div className="text-center p-4 bg-orange-50 rounded-lg">
                  <p className="text-3xl font-bold text-orange-600">{campaigns.reduce((sum, c) => sum + c.conversions, 0)}</p>
                  <p className="text-sm text-gray-600 font-medium">Total Conversions</p>
                  <p className="text-xs text-gray-500 mt-1">All campaigns</p>
                </div>
                <div className="text-center p-4 bg-red-50 rounded-lg">
                  <p className="text-3xl font-bold text-red-600">
                    {campaigns.length > 0 && campaigns.reduce((sum, c) => sum + c.sessions, 0) > 0 
                      ? ((campaigns.reduce((sum, c) => sum + c.conversions, 0) / campaigns.reduce((sum, c) => sum + c.sessions, 0)) * 100).toFixed(2)
                      : '0.00'
                    }%
                  </p>
                  <p className="text-sm text-gray-600 font-medium">Avg Conv. Rate</p>
                  <p className="text-xs text-gray-500 mt-1">Overall performance</p>
                </div>
              </div>
              
              {/* Top Performing Campaign Highlight */}
              {campaigns.length > 0 && (
                <div className="mt-6 p-4 bg-gradient-to-r from-blue-50 to-green-50 rounded-lg">
                  <h4 className="text-sm font-medium text-gray-700 mb-2">Top Performing Campaign</h4>
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium text-lg text-gray-900">{campaigns[0]?.campaignName}</p>
                      <div className="flex items-center gap-4 mt-1">
                        <span className="text-sm text-blue-600 font-medium">{formatNumber(campaigns[0]?.sessions || 0)} sessions</span>
                        <span className="text-sm text-green-600 font-medium">{campaigns[0]?.conversions || 0} conversions</span>
                        <span className="text-sm text-purple-600 font-medium">
                          {campaigns[0]?.sessions > 0 ? ((campaigns[0].conversions / campaigns[0].sessions) * 100).toFixed(2) : '0.00'}% rate
                        </span>
                      </div>
                    </div>
                    <div className="text-right">
                      <Badge variant="default" className="bg-green-100 text-green-800">
                        Leading Campaign
                      </Badge>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="meta-ads" className="space-y-4">
          {/* Meta Ads Overview */}
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
            {metaInsights && (
              <>
                <Card className="cursor-pointer hover:shadow-lg hover:scale-105 transition-all duration-200 border-blue-200 hover:border-blue-400"
                      onClick={() => {
                        setSelectedMetric({
                          title: 'Meta Ad Spend Analysis',
                          type: 'meta-spend',
                          data: metaInsights,
                          insights: [
                            `Total spend: ₹${(metaInsights.totalSpend * 83).toFixed(2)} (${metaInsights.totalSpend.toFixed(2)} USD)`,
                            `Average CPC: $${metaInsights.averageCPC.toFixed(2)}`,
                            `Cost per conversion: $${(metaInsights.totalSpend / Math.max(metaInsights.totalConversions, 1)).toFixed(2)}`,
                            `${metaInsights.activeCampaigns} active campaigns running`
                          ]
                        });
                        setDetailsModalOpen(true);
                      }}>
                  <CardContent className="flex items-center p-6">
                    <div className="flex items-center w-full">
                      <div className="p-2 bg-blue-100 rounded-lg">
                        <Target className="h-6 w-6 text-blue-600" />
                      </div>
                      <div className="ml-4 flex-1">
                        <p className="text-sm font-medium text-gray-600">Total Ad Spend</p>
                        <p className="text-2xl font-bold text-gray-900">₹{(metaInsights.totalSpend * 83).toFixed(0)}</p>
                        <div className="flex items-center justify-between mt-1">
                          <p className="text-xs text-blue-600 font-medium">${metaInsights.totalSpend.toFixed(2)} USD</p>
                          <ArrowUpRight className="h-4 w-4 text-blue-500" />
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="cursor-pointer hover:shadow-lg hover:scale-105 transition-all duration-200 border-green-200 hover:border-green-400"
                      onClick={() => {
                        setSelectedMetric({
                          title: 'Meta Impressions & Reach',
                          type: 'meta-impressions',
                          data: metaInsights,
                          insights: [
                            `${formatNumber(metaInsights.totalImpressions)} total impressions`,
                            `${formatNumber(metaInsights.totalClicks)} total clicks`,
                            `${metaInsights.averageCTR.toFixed(2)}% average CTR`,
                            `${(metaInsights.totalImpressions / Math.max(metaInsights.activeCampaigns, 1)).toFixed(0)} impressions per campaign`
                          ]
                        });
                        setDetailsModalOpen(true);
                      }}>
                  <CardContent className="flex items-center p-6">
                    <div className="flex items-center w-full">
                      <div className="p-2 bg-green-100 rounded-lg">
                        <Eye className="h-6 w-6 text-green-600" />
                      </div>
                      <div className="ml-4 flex-1">
                        <p className="text-sm font-medium text-gray-600">Impressions</p>
                        <p className="text-2xl font-bold text-gray-900">{formatNumber(metaInsights.totalImpressions)}</p>
                        <div className="flex items-center justify-between mt-1">
                          <p className="text-xs text-green-600 font-medium">{metaInsights.averageCTR.toFixed(2)}% CTR</p>
                          <ArrowUpRight className="h-4 w-4 text-green-500" />
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="cursor-pointer hover:shadow-lg hover:scale-105 transition-all duration-200 border-purple-200 hover:border-purple-400"
                      onClick={() => {
                        setSelectedMetric({
                          title: 'Meta Clicks Analysis',
                          type: 'meta-clicks',
                          data: metaInsights,
                          insights: [
                            `${formatNumber(metaInsights.totalClicks)} total clicks`,
                            `$${metaInsights.averageCPC.toFixed(2)} average cost per click`,
                            `${metaInsights.averageCTR.toFixed(2)}% click-through rate`,
                            `${(metaInsights.totalClicks / Math.max(metaInsights.totalImpressions, 1) * 100).toFixed(2)}% of impressions resulted in clicks`
                          ]
                        });
                        setDetailsModalOpen(true);
                      }}>
                  <CardContent className="flex items-center p-6">
                    <div className="flex items-center w-full">
                      <div className="p-2 bg-purple-100 rounded-lg">
                        <MousePointerClick className="h-6 w-6 text-purple-600" />
                      </div>
                      <div className="ml-4 flex-1">
                        <p className="text-sm font-medium text-gray-600">Total Clicks</p>
                        <p className="text-2xl font-bold text-gray-900">{formatNumber(metaInsights.totalClicks)}</p>
                        <div className="flex items-center justify-between mt-1">
                          <p className="text-xs text-purple-600 font-medium">${metaInsights.averageCPC.toFixed(2)} CPC</p>
                          <ArrowUpRight className="h-4 w-4 text-purple-500" />
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="cursor-pointer hover:shadow-lg hover:scale-105 transition-all duration-200 border-orange-200 hover:border-orange-400"
                      onClick={() => {
                        setSelectedMetric({
                          title: 'Meta Conversions Performance',
                          type: 'meta-conversions',
                          data: metaInsights,
                          insights: [
                            `${metaInsights.totalConversions} total conversions`,
                            `$${(metaInsights.totalSpend / Math.max(metaInsights.totalConversions, 1)).toFixed(2)} cost per conversion`,
                            `${(metaInsights.totalConversions / Math.max(metaInsights.totalClicks, 1) * 100).toFixed(2)}% conversion rate`,
                            `ROI: ${metaInsights.averageROAS.toFixed(2)}x return on ad spend`
                          ]
                        });
                        setDetailsModalOpen(true);
                      }}>
                  <CardContent className="flex items-center p-6">
                    <div className="flex items-center w-full">
                      <div className="p-2 bg-orange-100 rounded-lg">
                        <TrendingUp className="h-6 w-6 text-orange-600" />
                      </div>
                      <div className="ml-4 flex-1">
                        <p className="text-sm font-medium text-gray-600">Conversions</p>
                        <p className="text-2xl font-bold text-gray-900">{metaInsights.totalConversions}</p>
                        <div className="flex items-center justify-between mt-1">
                          <p className="text-xs text-orange-600 font-medium">{metaInsights.averageROAS.toFixed(1)}x ROAS</p>
                          <ArrowUpRight className="h-4 w-4 text-orange-500" />
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </>
            )}
          </div>

          {/* Meta Campaigns List */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <Activity className="h-5 w-5" />
                  Meta Campaign Performance
                </CardTitle>
                <Button 
                  variant="ghost" 
                  size="sm"
                  onClick={() => {
                    setSelectedMetric({
                      title: 'Meta Campaign Overview',
                      type: 'meta-campaigns',
                      data: metaCampaigns,
                      insights: [
                        `${metaCampaigns.length} active Meta campaigns`,
                        `Total spend: $${metaCampaigns.reduce((sum, c) => sum + c.spend, 0).toFixed(2)}`,
                        `Total impressions: ${formatNumber(metaCampaigns.reduce((sum, c) => sum + c.impressions, 0))}`,
                        `Average CTR: ${(metaCampaigns.reduce((sum, c) => sum + c.ctr, 0) / Math.max(metaCampaigns.length, 1)).toFixed(2)}%`
                      ]
                    });
                    setDetailsModalOpen(true);
                  }}
                >
                  <ExternalLink className="h-4 w-4" />
                </Button>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={Math.max(300, metaCampaigns.length * 40)}>
                  <BarChart data={metaCampaigns.slice(0, 10)} layout="horizontal">
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis type="number" />
                    <YAxis 
                      type="category" 
                      dataKey="campaignName" 
                      width={150}
                      fontSize={12}
                      tick={{ textAnchor: 'end' }}
                    />
                    <Tooltip 
                      content={({ active, payload, label }) => {
                        if (active && payload && payload.length) {
                          const data = payload[0]?.payload;
                          return (
                            <div className="bg-white p-4 border rounded shadow-lg">
                              <p className="font-medium text-lg mb-2">{label}</p>
                              <div className="space-y-1">
                                <p className="text-blue-600">Spend: ${data?.spend?.toFixed(2)}</p>
                                <p className="text-green-600">Impressions: {formatNumber(data?.impressions || 0)}</p>
                                <p className="text-purple-600">Clicks: {formatNumber(data?.clicks || 0)}</p>
                                <p className="text-orange-600">CTR: {data?.ctr?.toFixed(2)}%</p>
                                <p className="text-red-600">CPC: ${data?.cpc?.toFixed(2)}</p>
                              </div>
                              <p className="text-xs text-gray-500 mt-2">Click for detailed analysis</p>
                            </div>
                          );
                        }
                        return null;
                      }}
                    />
                    <Bar 
                      dataKey="spend" 
                      fill="#1877F2"
                      name="Spend ($)"
                      cursor="pointer"
                      onClick={(data) => {
                        setSelectedMetric({
                          title: `${data.campaignName} - Meta Campaign Analysis`,
                          type: 'meta-campaign-detail',
                          data: data,
                          insights: [
                            `Campaign spend: $${data.spend.toFixed(2)}`,
                            `${formatNumber(data.impressions)} impressions delivered`,
                            `${formatNumber(data.clicks)} clicks generated`,
                            `${data.ctr.toFixed(2)}% click-through rate`,
                            `$${data.cpc.toFixed(2)} average cost per click`,
                            `${data.conversions} conversions tracked`
                          ]
                        });
                        setDetailsModalOpen(true);
                      }}
                    />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle>Meta Campaign Details</CardTitle>
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="text-blue-600 border-blue-600">
                    <Activity className="h-3 w-3 mr-1" />
                    Facebook & Instagram
                  </Badge>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => {
                      const csvData = metaCampaigns.map(c => ({
                        Campaign: c.campaignName,
                        Status: c.status,
                        Objective: c.objective,
                        Spend: c.spend.toFixed(2),
                        Impressions: c.impressions,
                        Clicks: c.clicks,
                        CTR: c.ctr.toFixed(2) + '%',
                        CPC: c.cpc.toFixed(2),
                        Conversions: c.conversions,
                        ROAS: c.roas.toFixed(2)
                      }));
                      const csv = [
                        Object.keys(csvData[0]).join(','),
                        ...csvData.map(row => Object.values(row).join(','))
                      ].join('\n');
                      const blob = new Blob([csv], { type: 'text/csv' });
                      const url = URL.createObjectURL(blob);
                      const a = document.createElement('a');
                      a.href = url;
                      a.download = `meta-campaigns-${new Date().toISOString().split('T')[0]}.csv`;
                      a.click();
                      URL.revokeObjectURL(url);
                    }}
                  >
                    <Download className="h-4 w-4 mr-1" />
                    Export
                  </Button>
                </div>
              </CardHeader>
              <CardContent className="max-h-80 overflow-y-auto">
                <div className="space-y-3">
                  {metaCampaigns.slice(0, 12).map((campaign, index) => {
                    const conversionRate = campaign.clicks > 0 ? (campaign.conversions / campaign.clicks * 100) : 0;
                    const isHighPerforming = campaign.ctr > 2.0 && conversionRate > 3.0;
                    const isProfitable = campaign.roas > 2.0;
                    
                    return (
                      <div 
                        key={index} 
                        className="flex items-center justify-between p-4 border rounded-lg cursor-pointer hover:bg-gray-50 hover:border-blue-300 transition-all duration-200"
                        onClick={() => {
                          setSelectedMetric({
                            title: `${campaign.campaignName} - Meta Campaign Deep Dive`,
                            type: 'meta-campaign-analysis',
                            data: campaign,
                            insights: [
                              `Campaign spend: $${campaign.spend.toFixed(2)} (₹${(campaign.spend * 83).toFixed(2)})`,
                              `${formatNumber(campaign.impressions)} impressions, ${formatNumber(campaign.clicks)} clicks`,
                              `${campaign.ctr.toFixed(2)}% CTR, $${campaign.cpc.toFixed(2)} CPC`,
                              `${campaign.conversions} conversions (${conversionRate.toFixed(2)}% rate)`,
                              `${campaign.roas.toFixed(2)}x ROAS - ${isProfitable ? 'Profitable' : 'Needs optimization'}`,
                              `Campaign objective: ${campaign.objective}`
                            ]
                          });
                          setDetailsModalOpen(true);
                        }}
                      >
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            <div className="w-4 h-4 bg-blue-500 rounded-full"></div>
                            <p className="font-medium text-gray-900">{campaign.campaignName}</p>
                            <div className="flex gap-1">
                              {isHighPerforming && (
                                <Badge variant="default" className="bg-green-100 text-green-800 text-xs">
                                  High CTR
                                </Badge>
                              )}
                              {isProfitable && (
                                <Badge variant="default" className="bg-blue-100 text-blue-800 text-xs">
                                  Profitable
                                </Badge>
                              )}
                            </div>
                          </div>
                          <div className="grid grid-cols-3 gap-3 text-xs text-gray-600">
                            <div>
                              <span className="font-medium">CTR</span>
                              <p className="text-sm font-bold text-green-600">{campaign.ctr.toFixed(2)}%</p>
                            </div>
                            <div>
                              <span className="font-medium">CPC</span>
                              <p className="text-sm font-bold text-blue-600">${campaign.cpc.toFixed(2)}</p>
                            </div>
                            <div>
                              <span className="font-medium">ROAS</span>
                              <p className="text-sm font-bold text-purple-600">{campaign.roas.toFixed(1)}x</p>
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="flex items-center gap-4">
                            <div>
                              <p className="text-sm text-gray-600">Spend</p>
                              <p className="font-bold text-blue-600">${campaign.spend.toFixed(0)}</p>
                            </div>
                            <div>
                              <p className="text-sm text-gray-600">Conv.</p>
                              <p className="font-bold text-green-600">{campaign.conversions}</p>
                            </div>
                            <ArrowUpRight className="h-4 w-4 text-gray-400" />
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Meta Performance Summary */}
          {metaInsights && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Globe className="h-5 w-5" />
                  Meta Advertising Summary
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                  <div className="text-center p-4 bg-blue-50 rounded-lg">
                    <p className="text-3xl font-bold text-blue-600">{metaInsights.activeCampaigns}</p>
                    <p className="text-sm text-gray-600 font-medium">Active Campaigns</p>
                    <p className="text-xs text-gray-500 mt-1">Facebook & Instagram</p>
                  </div>
                  <div className="text-center p-4 bg-green-50 rounded-lg">
                    <p className="text-3xl font-bold text-green-600">${metaInsights.totalSpend.toFixed(0)}</p>
                    <p className="text-sm text-gray-600 font-medium">Total Investment</p>
                    <p className="text-xs text-gray-500 mt-1">₹{(metaInsights.totalSpend * 83).toFixed(0)} INR</p>
                  </div>
                  <div className="text-center p-4 bg-purple-50 rounded-lg">
                    <p className="text-3xl font-bold text-purple-600">{formatNumber(metaInsights.totalImpressions)}</p>
                    <p className="text-sm text-gray-600 font-medium">Total Reach</p>
                    <p className="text-xs text-gray-500 mt-1">Impressions delivered</p>
                  </div>
                  <div className="text-center p-4 bg-orange-50 rounded-lg">
                    <p className="text-3xl font-bold text-orange-600">{metaInsights.averageROAS.toFixed(1)}x</p>
                    <p className="text-sm text-gray-600 font-medium">Return on Spend</p>
                    <p className="text-xs text-gray-500 mt-1">Average ROAS</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="channels" className="space-y-4">
          {/* Channel Controls */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <Filter className="h-4 w-4 text-gray-400" />
                <Select value={filterType} onValueChange={setFilterType}>
                  <SelectTrigger className="w-48">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Channels</SelectItem>
                    <SelectItem value="paid">Paid Channels</SelectItem>
                    <SelectItem value="organic">Organic Channels</SelectItem>
                    <SelectItem value="social">Social Channels</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="flex items-center gap-2">
                <Search className="h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search channels..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-64"
                />
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant="outline">{channels.length} channels</Badge>
              <Button variant="outline" size="sm" onClick={() => window.location.reload()}>
                <RefreshCw className="h-4 w-4 mr-1" />
                Refresh
              </Button>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Channel Performance Chart */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <Activity className="h-5 w-5" />
                  Channel Performance Overview
                </CardTitle>
                <Button 
                  variant="ghost" 
                  size="sm"
                  onClick={() => {
                    setSelectedMetric({
                      title: 'Channel Performance Analysis',
                      type: 'channels-overview',
                      data: channels,
                      insights: [
                        `Total ${channels.length} active channels`,
                        `Top performer: ${channels[0]?.channelGroup} (${formatNumber(channels[0]?.sessions || 0)} sessions)`,
                        `Total engagement rate: ${((channels.reduce((sum, c) => sum + (c.engagementRate * c.sessions), 0) / Math.max(channels.reduce((sum, c) => sum + c.sessions, 0), 1)) * 100).toFixed(1)}%`,
                        `Revenue generating channels: ${channels.filter(c => c.totalRevenue > 0).length}`
                      ]
                    });
                    setDetailsModalOpen(true);
                  }}
                >
                  <ExternalLink className="h-4 w-4" />
                </Button>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <ComposedChart data={channels.slice(0, 6)}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis 
                      dataKey="channelGroup" 
                      angle={-45} 
                      textAnchor="end" 
                      height={100}
                      fontSize={12}
                    />
                    <YAxis yAxisId="left" />
                    <YAxis yAxisId="right" orientation="right" />
                    <Tooltip 
                      cursor={{ fill: 'rgba(59, 130, 246, 0.1)' }}
                      content={({ active, payload, label }) => {
                        if (active && payload && payload.length) {
                          const data = payload[0]?.payload;
                          return (
                            <div className="bg-white p-4 border rounded shadow-lg">
                              <p className="font-medium text-lg">{label}</p>
                              <div className="space-y-1 mt-2">
                                <p className="text-blue-600">Sessions: {formatNumber(data?.sessions || 0)}</p>
                                <p className="text-green-600">Engagement Rate: {((data?.engagementRate || 0) * 100).toFixed(1)}%</p>
                                <p className="text-purple-600">Events/Session: {(data?.eventsPerSession || 0).toFixed(1)}</p>
                                <p className="text-orange-600">Revenue: ₹{(data?.totalRevenue || 0).toFixed(2)}</p>
                              </div>
                              <p className="text-xs text-gray-500 mt-2">Click for detailed analysis</p>
                            </div>
                          );
                        }
                        return null;
                      }}
                    />
                    <Bar 
                      yAxisId="left" 
                      dataKey="sessions" 
                      fill="#3B82F6"
                      name="Sessions"
                      cursor="pointer"
                      onClick={(data) => {
                        setSelectedMetric({
                          title: `${data.channelGroup} - Channel Deep Dive`,
                          type: 'channel-detail',
                          data: data,
                          insights: [
                            `${formatNumber(data.sessions)} total sessions`,
                            `${formatNumber(data.engagedSessions)} engaged sessions (${((data.engagedSessions / Math.max(data.sessions, 1)) * 100).toFixed(1)}%)`,
                            `${(data.averageSessionDuration / 60).toFixed(1)} minutes average session duration`,
                            `${data.eventsPerSession.toFixed(1)} events per session`,
                            `${data.keyEvents} key events generated`,
                            `₹${data.totalRevenue.toFixed(2)} total revenue`
                          ]
                        });
                        setDetailsModalOpen(true);
                      }}
                    />
                    <Line 
                      yAxisId="right" 
                      type="monotone" 
                      dataKey="engagementRate" 
                      stroke="#10B981" 
                      strokeWidth={3}
                      name="Engagement Rate"
                      dot={{ fill: '#10B981', strokeWidth: 2, r: 4 }}
                    />
                  </ComposedChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Channel Details Table */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle>Channel Breakdown</CardTitle>
                <div className="flex items-center gap-2">
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => {
                      const csvData = channels.map(c => ({
                        Channel: c.channelGroup,
                        Sessions: c.sessions,
                        'Engaged Sessions': c.engagedSessions,
                        'Engagement Rate': `${(c.engagementRate * 100).toFixed(2)}%`,
                        'Avg Duration': `${(c.averageSessionDuration / 60).toFixed(1)}m`,
                        'Events/Session': c.eventsPerSession.toFixed(2),
                        'Key Events': c.keyEvents,
                        'Revenue': c.totalRevenue.toFixed(2)
                      }));
                      const csv = [
                        Object.keys(csvData[0]).join(','),
                        ...csvData.map(row => Object.values(row).join(','))
                      ].join('\n');
                      const blob = new Blob([csv], { type: 'text/csv' });
                      const url = URL.createObjectURL(blob);
                      const a = document.createElement('a');
                      a.href = url;
                      a.download = 'ga4-channels.csv';
                      a.click();
                      URL.revokeObjectURL(url);
                    }}
                  >
                    <Download className="h-4 w-4 mr-1" />
                    Export
                  </Button>
                </div>
              </CardHeader>
              <CardContent className="max-h-80 overflow-y-auto">
                <div className="space-y-3">
                  {channels
                    .filter(channel => 
                      channel.channelGroup.toLowerCase().includes(searchTerm.toLowerCase()) &&
                      (filterType === 'all' || 
                       (filterType === 'paid' && ['Paid Search', 'Paid Social', 'Display'].some(paid => channel.channelGroup.includes(paid))) ||
                       (filterType === 'organic' && ['Organic Search', 'Organic Social'].some(organic => channel.channelGroup.includes(organic))) ||
                       (filterType === 'social' && channel.channelGroup.toLowerCase().includes('social'))
                      )
                    )
                    .sort((a, b) => b.sessions - a.sessions)
                    .map((channel, index) => {
                      const engagementRate = channel.engagementRate * 100;
                      const isHighPerforming = engagementRate > 60;
                      const hasRevenue = channel.totalRevenue > 0;
                      
                      return (
                        <div 
                          key={index} 
                          className="flex items-center justify-between p-4 border rounded-lg cursor-pointer hover:bg-gray-50 hover:border-blue-300 transition-all duration-200"
                          onClick={() => {
                            setSelectedMetric({
                              title: `${channel.channelGroup} - Performance Analysis`,
                              type: 'channel-performance',
                              data: channel,
                              insights: [
                                `${formatNumber(channel.sessions)} sessions generated`,
                                `${engagementRate.toFixed(1)}% engagement rate (${isHighPerforming ? 'Excellent' : engagementRate > 40 ? 'Good' : 'Needs improvement'})`,
                                `${(channel.averageSessionDuration / 60).toFixed(1)} minutes average session time`,
                                `${channel.eventsPerSession.toFixed(1)} events per session`,
                                `${channel.keyEvents} key conversion events`,
                                hasRevenue ? `₹${channel.totalRevenue.toFixed(2)} revenue generated` : 'No direct revenue attribution'
                              ]
                            });
                            setDetailsModalOpen(true);
                          }}
                        >
                          <div className="flex-1">
                            <div className="flex items-center gap-3 mb-2">
                              <div 
                                className="w-4 h-4 rounded-full"
                                style={{ backgroundColor: COLORS[index % COLORS.length] }}
                              ></div>
                              <p className="font-medium text-gray-900">{channel.channelGroup}</p>
                              <div className="flex gap-1">
                                {isHighPerforming && (
                                  <Badge variant="default" className="bg-green-100 text-green-800 text-xs">
                                    High Engagement
                                  </Badge>
                                )}
                                {hasRevenue && (
                                  <Badge variant="default" className="bg-yellow-100 text-yellow-800 text-xs">
                                    Revenue+
                                  </Badge>
                                )}
                              </div>
                            </div>
                            <div className="grid grid-cols-3 gap-3 text-xs text-gray-600">
                              <div>
                                <span className="font-medium">Engagement</span>
                                <p className="text-sm font-bold text-green-600">{engagementRate.toFixed(1)}%</p>
                              </div>
                              <div>
                                <span className="font-medium">Duration</span>
                                <p className="text-sm font-bold text-blue-600">{(channel.averageSessionDuration / 60).toFixed(1)}m</p>
                              </div>
                              <div>
                                <span className="font-medium">Events</span>
                                <p className="text-sm font-bold text-purple-600">{channel.eventsPerSession.toFixed(1)}</p>
                              </div>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="flex items-center gap-4">
                              <div>
                                <p className="text-sm text-gray-600">Sessions</p>
                                <p className="font-bold text-blue-600">{formatNumber(channel.sessions)}</p>
                              </div>
                              <div>
                                <p className="text-sm text-gray-600">Revenue</p>
                                <p className="font-bold text-green-600">₹{channel.totalRevenue.toFixed(0)}</p>
                              </div>
                              <ArrowUpRight className="h-4 w-4 text-gray-400" />
                            </div>
                          </div>
                        </div>
                      );
                    })}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Channel Performance Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Channel Performance Summary
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div className="text-center p-4 bg-blue-50 rounded-lg">
                  <p className="text-2xl font-bold text-blue-600">{channels.reduce((sum, c) => sum + c.sessions, 0).toLocaleString()}</p>
                  <p className="text-sm text-gray-600 font-medium">Total Sessions</p>
                  <p className="text-xs text-gray-500 mt-1">Across all channels</p>
                </div>
                <div className="text-center p-4 bg-green-50 rounded-lg">
                  <p className="text-2xl font-bold text-green-600">{((channels.reduce((sum, c) => sum + (c.engagementRate * c.sessions), 0) / Math.max(channels.reduce((sum, c) => sum + c.sessions, 0), 1)) * 100).toFixed(1)}%</p>
                  <p className="text-sm text-gray-600 font-medium">Avg. Engagement</p>
                  <p className="text-xs text-gray-500 mt-1">Weighted by sessions</p>
                </div>
                <div className="text-center p-4 bg-purple-50 rounded-lg">
                  <p className="text-2xl font-bold text-purple-600">{(channels.reduce((sum, c) => sum + (c.averageSessionDuration * c.sessions), 0) / Math.max(channels.reduce((sum, c) => sum + c.sessions, 0), 1) / 60).toFixed(1)}m</p>
                  <p className="text-sm text-gray-600 font-medium">Avg. Duration</p>
                  <p className="text-xs text-gray-500 mt-1">Per session</p>
                </div>
                <div className="text-center p-4 bg-yellow-50 rounded-lg">
                  <p className="text-2xl font-bold text-yellow-600">₹{channels.reduce((sum, c) => sum + c.totalRevenue, 0).toFixed(0)}</p>
                  <p className="text-sm text-gray-600 font-medium">Total Revenue</p>
                  <p className="text-xs text-gray-500 mt-1">Direct attribution</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="devices" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Smartphone className="h-5 w-5" />
                  Device Categories
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={deviceCategoryData}
                      cx="50%"
                      cy="50%"
                      outerRadius={100}
                      fill="#8884d8"
                      dataKey="sessions"
                      label={({ category, percent }) => `${category} ${(percent * 100).toFixed(0)}%`}
                    >
                      {deviceCategoryData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Monitor className="h-5 w-5" />
                  Top Browsers
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={browserData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="browser" angle={-45} textAnchor="end" height={100} />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="sessions" fill="#82ca9d" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="geography" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MapPin className="h-5 w-5" />
                  Top Indian Cities
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={topCities}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" angle={-45} textAnchor="end" height={100} />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="sessions" fill="#ffc658" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Geographic Distribution</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {geography.slice(0, 10).map((location, index) => (
                    <div key={index} className="flex items-center justify-between p-3 border rounded">
                      <div>
                        <p className="font-medium">{location.city}</p>
                        <p className="text-sm text-gray-600">{location.region}</p>
                      </div>
                      <div className="text-right">
                        <p className="font-medium">{formatNumber(location.sessions)} sessions</p>
                        <p className="text-sm text-gray-600">{formatNumber(location.users)} users</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="pages" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Top Pages Performance
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {pages.slice(0, 10).map((page, index) => (
                  <div key={index} className="flex items-center justify-between p-4 border rounded">
                    <div className="flex-1">
                      <p className="font-medium">{page.pageTitle}</p>
                      <p className="text-sm text-gray-600">{page.pagePath}</p>
                    </div>
                    <div className="grid grid-cols-3 gap-4 text-right">
                      <div>
                        <p className="text-sm text-gray-600">Pageviews</p>
                        <p className="font-medium">{formatNumber(page.pageviews)}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Avg. Time</p>
                        <p className="font-medium">{formatDuration(page.avgTimeOnPage)}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Bounce Rate</p>
                        <p className="font-medium">{(page.bounceRate * 100).toFixed(1)}%</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="conversions" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Conversion Events
              </CardTitle>
            </CardHeader>
            <CardContent>
              {conversions.length > 0 ? (
                <div className="space-y-4">
                  {conversions.map((conversion, index) => (
                    <div key={index} className="flex items-center justify-between p-4 border rounded">
                      <div>
                        <p className="font-medium">{conversion.eventName}</p>
                        <div className="flex items-center gap-2 mt-1">
                          <Badge variant="outline">{conversion.source}</Badge>
                          <Badge variant="outline">{conversion.medium}</Badge>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-medium">{conversion.eventCount} events</p>
                        <p className="text-sm text-gray-600">{conversion.conversions} conversions</p>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <Target className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                  <p>No conversion events found for this period</p>
                  <p className="text-sm">Set up conversion tracking in GA4 to see conversion data</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="audience" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Audience Overview</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Total Users</span>
                    <span className="font-medium">{formatNumber(overview?.users || 0)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">New Users</span>
                    <span className="font-medium">{formatNumber(overview?.newUsers || 0)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Returning Users</span>
                    <span className="font-medium">{formatNumber((overview?.users || 0) - (overview?.newUsers || 0))}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Sessions per User</span>
                    <span className="font-medium">{((overview?.sessions || 0) / Math.max(overview?.users || 1, 1)).toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Bounce Rate</span>
                    <span className="font-medium">{((overview?.bounceRate || 0) * 100).toFixed(1)}%</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>User Engagement</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <p className="text-sm text-gray-600 mb-2">Average Session Duration</p>
                    <p className="text-2xl font-bold">{formatDuration(overview?.sessionDuration || 0)}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600 mb-2">Pages per Session</p>
                    <p className="text-2xl font-bold">{((overview?.pageviews || 0) / Math.max(overview?.sessions || 1, 1)).toFixed(2)}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600 mb-2">User Retention</p>
                    <p className="text-lg font-medium">{(((overview?.users || 0) - (overview?.newUsers || 0)) / Math.max(overview?.users || 1, 1) * 100).toFixed(1)}% returning</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Enhanced GA4 Sections */}
        <TabsContent value="audience" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Audience Demographics
                <Badge variant="outline">{audience.length} segments</Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {audienceLoading ? (
                <div className="flex items-center justify-center h-64">
                  <RefreshCw className="h-8 w-8 animate-spin" />
                </div>
              ) : audience.length > 0 ? (
                <ResponsiveContainer width="100%" height={400}>
                  <ComposedChart data={audience}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="ageBracket" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="users" fill="#3B82F6" name="Users" />
                    <Bar dataKey="sessions" fill="#10B981" name="Sessions" />
                  </ComposedChart>
                </ResponsiveContainer>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  No audience data available for this period
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="events" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                All Events Analytics
                <Badge variant="outline">{events.length} events</Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {eventsLoading ? (
                <div className="flex items-center justify-center h-64">
                  <RefreshCw className="h-8 w-8 animate-spin" />
                </div>
              ) : events.length > 0 ? (
                <div className="space-y-4">
                  <ResponsiveContainer width="100%" height={400}>
                    <BarChart data={events.slice(0, 20)}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="eventName" angle={-45} textAnchor="end" height={100} />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Bar dataKey="eventCount" fill="#8B5CF6" name="Event Count" />
                    </BarChart>
                  </ResponsiveContainer>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                    {events.slice(0, 6).map((event, idx) => (
                      <div key={idx} className="p-3 border rounded-lg">
                        <h4 className="font-medium text-sm">{event.eventName}</h4>
                        <p className="text-xs text-gray-600">Count: {event.eventCount}</p>
                        <p className="text-xs text-gray-600">Source: {event.source}</p>
                      </div>
                    ))}
                  </div>
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  No events data available for this period
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="technology" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Monitor className="h-5 w-5" />
                Enhanced Technology Analytics
                <Badge variant="outline">{technology.length} records</Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {technologyLoading ? (
                <div className="flex items-center justify-center h-64">
                  <RefreshCw className="h-8 w-8 animate-spin" />
                </div>
              ) : technology.length > 0 ? (
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-medium mb-3">Operating Systems</h4>
                    <ResponsiveContainer width="100%" height={300}>
                      <PieChart>
                        <Pie
                          data={technology.slice(0, 8).map(t => ({
                            name: `${t.operatingSystem} ${t.osVersion}`,
                            value: t.sessions
                          }))}
                          cx="50%"
                          cy="50%"
                          outerRadius={80}
                          dataKey="value"
                          label={({ name, percent }) => `${(percent * 100).toFixed(0)}%`}
                        >
                          {COLORS.map((color, index) => (
                            <Cell key={`cell-${index}`} fill={color} />
                          ))}
                        </Pie>
                        <Tooltip />
                      </PieChart>
                    </ResponsiveContainer>
                  </div>
                  <div>
                    <h4 className="font-medium mb-3">Browser Details</h4>
                    <ResponsiveContainer width="100%" height={300}>
                      <BarChart data={technology.slice(0, 10)}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="browser" angle={-45} textAnchor="end" height={80} />
                        <YAxis />
                        <Tooltip />
                        <Bar dataKey="sessions" fill="#F59E0B" />
                      </BarChart>
                    </ResponsiveContainer>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  No technology data available for this period
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="ecommerce" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="h-5 w-5" />
                E-commerce Performance
                <Badge variant="outline">{ecommerce.length} items</Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {ecommerceLoading ? (
                <div className="flex items-center justify-center h-64">
                  <RefreshCw className="h-8 w-8 animate-spin" />
                </div>
              ) : ecommerce.length > 0 ? (
                <div className="space-y-6">
                  <ResponsiveContainer width="100%" height={400}>
                    <ComposedChart data={ecommerce.slice(0, 15)}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="itemName" angle={-45} textAnchor="end" height={100} />
                      <YAxis yAxisId="left" />
                      <YAxis yAxisId="right" orientation="right" />
                      <Tooltip />
                      <Legend />
                      <Bar yAxisId="left" dataKey="itemsPurchased" fill="#10B981" name="Items Purchased" />
                      <Bar yAxisId="right" dataKey="itemRevenue" fill="#F59E0B" name="Revenue" />
                    </ComposedChart>
                  </ResponsiveContainer>
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div className="p-4 bg-green-50 rounded-lg">
                      <h4 className="font-medium text-green-800">Total Revenue</h4>
                      <p className="text-2xl font-bold text-green-900">
                        ₹{ecommerce.reduce((sum, item) => sum + item.itemRevenue, 0).toFixed(2)}
                      </p>
                    </div>
                    <div className="p-4 bg-blue-50 rounded-lg">
                      <h4 className="font-medium text-blue-800">Items Sold</h4>
                      <p className="text-2xl font-bold text-blue-900">
                        {ecommerce.reduce((sum, item) => sum + item.itemsPurchased, 0)}
                      </p>
                    </div>
                    <div className="p-4 bg-purple-50 rounded-lg">
                      <h4 className="font-medium text-purple-800">Cart Additions</h4>
                      <p className="text-xl font-bold text-purple-900">
                        {ecommerce.reduce((sum, item) => sum + item.itemsAddedToCart, 0)}
                      </p>
                    </div>
                    <div className="p-4 bg-orange-50 rounded-lg">
                      <h4 className="font-medium text-orange-800">Checkouts</h4>
                      <p className="text-xl font-bold text-orange-900">
                        {ecommerce.reduce((sum, item) => sum + item.itemsCheckedOut, 0)}
                      </p>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  No e-commerce data available for this period
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="acquisition" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Enhanced Acquisition Data
                <Badge variant="outline">{acquisition.length} sources</Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {acquisitionLoading ? (
                <div className="flex items-center justify-center h-64">
                  <RefreshCw className="h-8 w-8 animate-spin" />
                </div>
              ) : acquisition.length > 0 ? (
                <ResponsiveContainer width="100%" height={400}>
                  <ComposedChart data={acquisition.slice(0, 15)}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="firstUserSource" angle={-45} textAnchor="end" height={100} />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="newUsers" fill="#3B82F6" name="New Users" />
                    <Bar dataKey="sessions" fill="#10B981" name="Sessions" />
                    <Line type="monotone" dataKey="conversions" stroke="#F59E0B" name="Conversions" />
                  </ComposedChart>
                </ResponsiveContainer>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  No acquisition data available for this period
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="engagement" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="h-5 w-5" />
                User Engagement & Retention
                <Badge variant="outline">{engagement.length} periods</Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {engagementLoading ? (
                <div className="flex items-center justify-center h-64">
                  <RefreshCw className="h-8 w-8 animate-spin" />
                </div>
              ) : engagement.length > 0 ? (
                <div className="space-y-6">
                  <ResponsiveContainer width="100%" height={400}>
                    <LineChart data={engagement}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="date" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Line type="monotone" dataKey="totalUsers" stroke="#3B82F6" name="Total Users" />
                      <Line type="monotone" dataKey="newUsers" stroke="#10B981" name="New Users" />
                      <Line type="monotone" dataKey="returningUsers" stroke="#F59E0B" name="Returning Users" />
                      <Line type="monotone" dataKey="engagementRate" stroke="#8B5CF6" name="Engagement Rate %" />
                    </LineChart>
                  </ResponsiveContainer>
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div className="p-4 bg-blue-50 rounded-lg">
                      <h4 className="font-medium text-blue-800">Avg Session Duration</h4>
                      <p className="text-xl font-bold text-blue-900">
                        {(engagement.reduce((sum, item) => sum + item.averageSessionDuration, 0) / engagement.length / 60).toFixed(1)}m
                      </p>
                    </div>
                    <div className="p-4 bg-green-50 rounded-lg">
                      <h4 className="font-medium text-green-800">Avg Engagement Rate</h4>
                      <p className="text-xl font-bold text-green-900">
                        {(engagement.reduce((sum, item) => sum + item.engagementRate, 0) / engagement.length * 100).toFixed(1)}%
                      </p>
                    </div>
                    <div className="p-4 bg-purple-50 rounded-lg">
                      <h4 className="font-medium text-purple-800">Sessions Per User</h4>
                      <p className="text-xl font-bold text-purple-900">
                        {(engagement.reduce((sum, item) => sum + item.sessionsPerUser, 0) / engagement.length).toFixed(2)}
                      </p>
                    </div>
                    <div className="p-4 bg-orange-50 rounded-lg">
                      <h4 className="font-medium text-orange-800">Return Rate</h4>
                      <p className="text-xl font-bold text-orange-900">
                        {engagement.length > 0 ? (
                          (engagement.reduce((sum, item) => sum + item.returningUsers, 0) / 
                           engagement.reduce((sum, item) => sum + item.totalUsers, 0) * 100).toFixed(1)
                        ) : 0}%
                      </p>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  No engagement data available for this period
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Interactive Detail Modals */}
      <Dialog open={detailsModalOpen} onOpenChange={setDetailsModalOpen}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Info className="h-5 w-5" />
              {selectedMetric?.title}
            </DialogTitle>
          </DialogHeader>
          
          {selectedMetric && (
            <div className="space-y-6">
              {/* Key Insights */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="text-sm font-medium text-gray-700 mb-3">Key Insights</h4>
                  <div className="space-y-2">
                    {selectedMetric.insights.map((insight: string, index: number) => (
                      <div key={index} className="flex items-start gap-2 p-2 bg-blue-50 rounded">
                        <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                        <p className="text-sm text-blue-800">{insight}</p>
                      </div>
                    ))}
                  </div>
                </div>
                
                <div>
                  <h4 className="text-sm font-medium text-gray-700 mb-3">Recommendations</h4>
                  <div className="space-y-2">
                    {selectedMetric.type === 'users' && (
                      <>
                        <div className="p-2 bg-green-50 rounded">
                          <p className="text-sm text-green-800">Focus on user retention campaigns</p>
                        </div>
                        <div className="p-2 bg-yellow-50 rounded">
                          <p className="text-sm text-yellow-800">Optimize mobile experience for new users</p>
                        </div>
                        <div className="p-2 bg-purple-50 rounded">
                          <p className="text-sm text-purple-800">Implement remarketing for returning visitors</p>
                        </div>
                      </>
                    )}
                    {selectedMetric.type === 'sessions' && (
                      <>
                        <div className="p-2 bg-green-50 rounded">
                          <p className="text-sm text-green-800">Improve page load speed for better sessions</p>
                        </div>
                        <div className="p-2 bg-blue-50 rounded">
                          <p className="text-sm text-blue-800">A/B test landing page designs</p>
                        </div>
                        <div className="p-2 bg-orange-50 rounded">
                          <p className="text-sm text-orange-800">Set up conversion tracking for key actions</p>
                        </div>
                      </>
                    )}
                    {selectedMetric.type === 'engagement' && (
                      <>
                        <div className="p-2 bg-red-50 rounded">
                          <p className="text-sm text-red-800">Reduce bounce rate with engaging content</p>
                        </div>
                        <div className="p-2 bg-indigo-50 rounded">
                          <p className="text-sm text-indigo-800">Add interactive elements to increase time on site</p>
                        </div>
                        <div className="p-2 bg-pink-50 rounded">
                          <p className="text-sm text-pink-800">Implement internal linking strategy</p>
                        </div>
                      </>
                    )}
                    {(selectedMetric.type === 'realtime-geo' || selectedMetric.type === 'country-realtime') && (
                      <>
                        <div className="p-2 bg-blue-50 rounded">
                          <p className="text-sm text-blue-800">Consider localized content for top regions</p>
                        </div>
                        <div className="p-2 bg-green-50 rounded">
                          <p className="text-sm text-green-800">Optimize ad spend for high-traffic regions</p>
                        </div>
                        <div className="p-2 bg-purple-50 rounded">
                          <p className="text-sm text-purple-800">Schedule content for peak activity hours</p>
                        </div>
                      </>
                    )}
                    {selectedMetric.type === 'quality' && (
                      <>
                        <div className="p-2 bg-green-50 rounded">
                          <p className="text-sm text-green-800">Current traffic quality is performing well</p>
                        </div>
                        <div className="p-2 bg-blue-50 rounded">
                          <p className="text-sm text-blue-800">Continue current marketing strategies</p>
                        </div>
                        <div className="p-2 bg-yellow-50 rounded">
                          <p className="text-sm text-yellow-800">Monitor for seasonal variations</p>
                        </div>
                      </>
                    )}
                    {(selectedMetric.type === 'channels-overview' || selectedMetric.type === 'channel-detail' || selectedMetric.type === 'channel-performance') && (
                      <>
                        <div className="p-2 bg-blue-50 rounded">
                          <p className="text-sm text-blue-800">Optimize budget allocation based on channel performance</p>
                        </div>
                        <div className="p-2 bg-green-50 rounded">
                          <p className="text-sm text-green-800">Focus on high-engagement channels for scaling</p>
                        </div>
                        <div className="p-2 bg-purple-50 rounded">
                          <p className="text-sm text-purple-800">Analyze low-performing channels for improvement opportunities</p>
                        </div>
                        <div className="p-2 bg-orange-50 rounded">
                          <p className="text-sm text-orange-800">Test cross-channel attribution and user journeys</p>
                        </div>
                      </>
                    )}
                  </div>
                </div>
              </div>

              {/* Data Visualization */}
              {selectedMetric.type === 'users' && overview && (
                <div>
                  <h4 className="text-sm font-medium text-gray-700 mb-3">User Breakdown</h4>
                  <ResponsiveContainer width="100%" height={200}>
                    <PieChart>
                      <Pie
                        data={[
                          { name: 'New Users', value: overview.newUsers, fill: '#3B82F6' },
                          { name: 'Returning Users', value: (overview.users - overview.newUsers), fill: '#10B981' }
                        ]}
                        cx="50%"
                        cy="50%"
                        outerRadius={80}
                        dataKey="value"
                        label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                      >
                        <Tooltip formatter={(value) => formatNumber(value as number)} />
                      </Pie>
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex items-center gap-3 pt-4 border-t">
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => setDetailsModalOpen(false)}
                >
                  Close
                </Button>
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => {
                    const data = JSON.stringify(selectedMetric, null, 2);
                    const blob = new Blob([data], { type: 'application/json' });
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `ga4-${selectedMetric.type}-insights.json`;
                    a.click();
                    URL.revokeObjectURL(url);
                  }}
                  className="flex items-center gap-1"
                >
                  <Download className="h-3 w-3" />
                  Export Data
                </Button>
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => {
                    navigator.clipboard.writeText(
                      `${selectedMetric.title}\n\n${selectedMetric.insights.join('\n')}`
                    );
                  }}
                  className="flex items-center gap-1"
                >
                  <MousePointerClick className="h-3 w-3" />
                  Copy Insights
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}