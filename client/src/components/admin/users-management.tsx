import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { format } from "date-fns";
import { Plus, Edit, Trash2, Users, Shield, Headphones, TrendingUp, ChevronDown, Clock, Globe, Monitor } from "lucide-react";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { useToast } from "@/hooks/use-toast";

interface User {
  id: number;
  username: string;
  name: string;
  email: string | null;
  role: string;
  isActive: boolean;
  createdAt: string;
}

interface AdminSession {
  id: number;
  userId: number;
  sessionId: string;
  loginTime: string;
  logoutTime: string | null;
  ipAddress: string | null;
  userAgent: string | null;
  isActive: boolean;
}

const roleConfig = {
  admin: { label: "Admin", icon: Shield, color: "bg-red-100 text-red-800", description: "Full system access" },
  support: { label: "Customer Support", icon: Headphones, color: "bg-blue-100 text-blue-800", description: "Edit bookings, templates, notifications" },
  marketing: { label: "Marketing", icon: TrendingUp, color: "bg-green-100 text-green-800", description: "View bookings, export data" },
  sales: { label: "Sales Person", icon: Users, color: "bg-purple-100 text-purple-800", description: "Field representative" }
};

export default function UsersManagement() {
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [expandedUsers, setExpandedUsers] = useState<Set<number>>(new Set());
  const [formData, setFormData] = useState({
    username: "",
    password: "",
    name: "",
    email: "",
    role: "support",
    isActive: true
  });

  const generateStrongPassword = () => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
    let password = 'Kult#';
    for (let i = 0; i < 10; i++) {
      password += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    password += '!';
    return password;
  };
  
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const { data: users = [], isLoading } = useQuery<User[]>({
    queryKey: ["/api/admin/users"],
  });

  const getAdminSessions = (userId: number) => useQuery<AdminSession[]>({
    queryKey: ["/api/admin/sessions", userId],
    enabled: expandedUsers.has(userId),
  });

  const toggleUserExpansion = (userId: number) => {
    const newExpanded = new Set(expandedUsers);
    if (newExpanded.has(userId)) {
      newExpanded.delete(userId);
    } else {
      newExpanded.add(userId);
    }
    setExpandedUsers(newExpanded);
  };

  const createUserMutation = useMutation({
    mutationFn: async (data: any) => {
      const response = await apiRequest("POST", "/api/admin/users", data);
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/admin/users"] });
      setIsCreateDialogOpen(false);
      resetForm();
      toast({ title: "User created successfully" });
    },
    onError: () => {
      toast({ title: "Failed to create user", variant: "destructive" });
    }
  });

  const updateUserMutation = useMutation({
    mutationFn: async ({ id, ...data }: any) => {
      const response = await apiRequest("PUT", `/api/admin/users/${id}`, data);
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/admin/users"] });
      setEditingUser(null);
      resetForm();
      toast({ title: "User updated successfully" });
    },
    onError: () => {
      toast({ title: "Failed to update user", variant: "destructive" });
    }
  });

  const deleteUserMutation = useMutation({
    mutationFn: async (id: number) => {
      const response = await apiRequest("DELETE", `/api/admin/users/${id}`);
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/admin/users"] });
      toast({ title: "User deleted successfully" });
    },
    onError: () => {
      toast({ title: "Failed to delete user", variant: "destructive" });
    }
  });

  const resetForm = () => {
    setFormData({
      username: "",
      password: "",
      name: "",
      email: "",
      role: "support",
      isActive: true
    });
  };

  const handleCreate = () => {
    if (formData.email && formData.password && formData.name) {
      const userData = {
        ...formData,
        username: formData.email // Use email as username
      };
      createUserMutation.mutate(userData);
    }
  };

  const handleGeneratePassword = () => {
    const newPassword = generateStrongPassword();
    setFormData({ ...formData, password: newPassword });
  };

  const handleEdit = (user: User) => {
    setEditingUser(user);
    setFormData({
      username: user.username,
      password: "", // Don't pre-fill password
      name: user.name,
      email: user.email || "",
      role: user.role,
      isActive: user.isActive
    });
  };

  const handleUpdate = () => {
    if (editingUser && formData.username && formData.name) {
      const updateData = { ...formData };
      if (!updateData.password) {
        delete updateData.password; // Don't update password if empty
      }
      updateUserMutation.mutate({ id: editingUser.id, ...updateData });
    }
  };

  const handleDelete = (user: User) => {
    if (confirm(`Are you sure you want to delete user "${user.name}"?`)) {
      deleteUserMutation.mutate(user.id);
    }
  };

  const getRoleIcon = (role: string) => {
    const config = roleConfig[role as keyof typeof roleConfig];
    if (!config) return null;
    const Icon = config.icon;
    return <Icon className="w-4 h-4" />;
  };

  const getRoleBadge = (role: string) => {
    const config = roleConfig[role as keyof typeof roleConfig] || roleConfig.support;
    return (
      <Badge className={config.color}>
        <div className="flex items-center space-x-1">
          {getRoleIcon(role)}
          <span>{config.label}</span>
        </div>
      </Badge>
    );
  };

  if (isLoading) {
    return <div className="p-6">Loading users...</div>;
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-lg font-semibold">User Management</h3>
          <p className="text-sm text-gray-600">Manage team members and their access levels</p>
        </div>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button className="flex items-center space-x-2">
              <Plus className="w-4 h-4" />
              <span>Add User</span>
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Create New User</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="email">Email Address (Username)</Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => setFormData({ ...formData, email: e.target.value, username: e.target.value })}
                  placeholder="Enter email address (e.g., <EMAIL>)"
                />
              </div>
              <div>
                <Label htmlFor="password">Password</Label>
                <div className="flex gap-2">
                  <Input
                    id="password"
                    type="password"
                    value={formData.password}
                    onChange={(e) => setFormData({ ...formData, password: e.target.value })}
                    placeholder="Enter complex password"
                  />
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleGeneratePassword}
                    className="px-3"
                  >
                    Generate
                  </Button>
                </div>
                <p className="text-xs text-gray-500 mt-1">Password must include uppercase, lowercase, numbers, and special characters</p>
              </div>
              <div>
                <Label htmlFor="name">Full Name</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  placeholder="Enter full name"
                />
              </div>
              <div>
                <Label htmlFor="email">Email (Optional)</Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                  placeholder="Enter email address"
                />
              </div>
              <div>
                <Label htmlFor="role">Role</Label>
                <Select value={formData.role} onValueChange={(value) => setFormData({ ...formData, role: value })}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.entries(roleConfig).map(([key, config]) => (
                      <SelectItem key={key} value={key}>
                        <div className="flex items-center space-x-2">
                          <config.icon className="w-4 h-4" />
                          <div>
                            <div>{config.label}</div>
                            <div className="text-xs text-gray-500">{config.description}</div>
                          </div>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="isActive"
                  checked={formData.isActive}
                  onCheckedChange={(checked) => setFormData({ ...formData, isActive: checked })}
                />
                <Label htmlFor="isActive">Active User</Label>
              </div>
              <Button onClick={handleCreate} disabled={createUserMutation.isPending} className="w-full">
                {createUserMutation.isPending ? "Creating..." : "Create User"}
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      <div className="border rounded-lg">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>User</TableHead>
              <TableHead>Role</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Created</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {users.map((user) => (
              <UserRowWithSessions 
                key={user.id} 
                user={user} 
                isExpanded={expandedUsers.has(user.id)}
                onToggleExpansion={() => toggleUserExpansion(user.id)}
                getAdminSessions={getAdminSessions}
                onEdit={handleEdit}
                onDelete={handleDelete}
                getRoleBadge={getRoleBadge}
              />
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Edit User Dialog */}
      <Dialog open={!!editingUser} onOpenChange={(open) => !open && setEditingUser(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit User</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="edit-username">Username</Label>
              <Input
                id="edit-username"
                value={formData.username}
                onChange={(e) => setFormData({ ...formData, username: e.target.value })}
                placeholder="Enter username"
              />
            </div>
            <div>
              <Label htmlFor="edit-password">Password (leave empty to keep current)</Label>
              <Input
                id="edit-password"
                type="password"
                value={formData.password}
                onChange={(e) => setFormData({ ...formData, password: e.target.value })}
                placeholder="Enter new password"
              />
            </div>
            <div>
              <Label htmlFor="edit-name">Full Name</Label>
              <Input
                id="edit-name"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                placeholder="Enter full name"
              />
            </div>
            <div>
              <Label htmlFor="edit-email">Email</Label>
              <Input
                id="edit-email"
                type="email"
                value={formData.email}
                onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                placeholder="Enter email address"
              />
            </div>
            <div>
              <Label htmlFor="edit-role">Role</Label>
              <Select value={formData.role} onValueChange={(value) => setFormData({ ...formData, role: value })}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {Object.entries(roleConfig).map(([key, config]) => (
                    <SelectItem key={key} value={key}>
                      <div className="flex items-center space-x-2">
                        <config.icon className="w-4 h-4" />
                        <div>
                          <div>{config.label}</div>
                          <div className="text-xs text-gray-500">{config.description}</div>
                        </div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="edit-isActive"
                checked={formData.isActive}
                onCheckedChange={(checked) => setFormData({ ...formData, isActive: checked })}
              />
              <Label htmlFor="edit-isActive">Active User</Label>
            </div>
            <Button onClick={handleUpdate} disabled={updateUserMutation.isPending} className="w-full">
              {updateUserMutation.isPending ? "Updating..." : "Update User"}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}

// Component for user row with collapsible admin sessions
function UserRowWithSessions({ 
  user, 
  isExpanded, 
  onToggleExpansion, 
  getAdminSessions, 
  onEdit, 
  onDelete, 
  getRoleBadge 
}: {
  user: User;
  isExpanded: boolean;
  onToggleExpansion: () => void;
  getAdminSessions: (userId: number) => any;
  onEdit: (user: User) => void;
  onDelete: (user: User) => void;
  getRoleBadge: (role: string) => JSX.Element;
}) {
  const sessionsQuery = getAdminSessions(user.id);
  const sessions = sessionsQuery.data || [];

  return (
    <>
      <TableRow>
        <TableCell>
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={onToggleExpansion}
              className="p-1"
            >
              <ChevronDown className={`w-4 h-4 transition-transform ${isExpanded ? 'rotate-180' : ''}`} />
            </Button>
            <div>
              <div className="font-medium">{user.name}</div>
              <div className="text-sm text-gray-500">@{user.username}</div>
              {user.email && <div className="text-xs text-gray-400">{user.email}</div>}
            </div>
          </div>
        </TableCell>
        <TableCell>{getRoleBadge(user.role)}</TableCell>
        <TableCell>
          <Badge variant={user.isActive ? "default" : "secondary"}>
            {user.isActive ? "Active" : "Inactive"}
          </Badge>
        </TableCell>
        <TableCell>{format(new Date(user.createdAt), "MMM d, yyyy")}</TableCell>
        <TableCell className="text-right">
          <div className="flex justify-end space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => onEdit(user)}
            >
              <Edit className="w-4 h-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => onDelete(user)}
            >
              <Trash2 className="w-4 h-4" />
            </Button>
          </div>
        </TableCell>
      </TableRow>
      
      {isExpanded && (
        <TableRow>
          <TableCell colSpan={5} className="p-0">
            <div className="bg-gray-50 p-4">
              <div className="mb-3">
                <h4 className="font-medium text-sm flex items-center space-x-2">
                  <Clock className="w-4 h-4" />
                  <span>Login Sessions</span>
                </h4>
              </div>
              
              {sessionsQuery.isLoading ? (
                <div className="text-sm text-gray-500">Loading sessions...</div>
              ) : sessions.length === 0 ? (
                <div className="text-sm text-gray-500">No login sessions recorded</div>
              ) : (
                <div className="space-y-2">
                  {sessions.slice(0, 5).map((session: AdminSession) => (
                    <div key={session.id} className="bg-white rounded p-3 text-sm">
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                        <div>
                          <div className="font-medium text-xs text-gray-500">LOGIN TIME</div>
                          <div>{format(new Date(session.loginTime), "MMM d, HH:mm")}</div>
                        </div>
                        <div>
                          <div className="font-medium text-xs text-gray-500">STATUS</div>
                          <Badge variant={session.isActive ? "default" : "secondary"} className="text-xs">
                            {session.isActive ? "Active" : "Ended"}
                          </Badge>
                        </div>
                        <div>
                          <div className="font-medium text-xs text-gray-500">IP ADDRESS</div>
                          <div className="flex items-center space-x-1">
                            <Globe className="w-3 h-3" />
                            <span>{session.ipAddress || "Unknown"}</span>
                          </div>
                        </div>
                        <div>
                          <div className="font-medium text-xs text-gray-500">BROWSER</div>
                          <div className="flex items-center space-x-1">
                            <Monitor className="w-3 h-3" />
                            <span className="truncate">{session.userAgent ? session.userAgent.split(' ')[0] : "Unknown"}</span>
                          </div>
                        </div>
                      </div>
                      {session.logoutTime && (
                        <div className="mt-2 pt-2 border-t text-xs text-gray-500">
                          Logged out: {format(new Date(session.logoutTime), "MMM d, HH:mm")}
                        </div>
                      )}
                    </div>
                  ))}
                  {sessions.length > 5 && (
                    <div className="text-xs text-gray-500 text-center">
                      ... and {sessions.length - 5} more sessions
                    </div>
                  )}
                </div>
              )}
            </div>
          </TableCell>
        </TableRow>
      )}
    </>
  );
}