import { useQuery } from "@tanstack/react-query";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { <PERSON><PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { CalendarIcon, Users, Target, TrendingUp, Activity, Search, Download, ExternalLink } from "lucide-react";
import { useState } from "react";
import { format, subDays } from "date-fns";

interface SessionAnalytics {
  totalSessions: number;
  sessionsWithBookings: number;
  conversionRate: string;
  avgTimeToBooking: number;
  trafficSources: {
    [key: string]: {
      sessions: number;
      bookings: number;
      conversionRate: string;
      sessionIds: string[];
    };
  };
  sessionDetails: Array<{
    session_id: string;
    completed_steps: number;
    final_outcome: string;
    booking_id: number | null;
    time_spent: number;
    first_visit: string;
    last_activity: string;
    customer_name: string | null;
    phone: string | null;
    postal_code: string | null;
    booking_status: string | null;
    traffic_source: string | null;
    utm_source: string | null;
    utm_campaign: string | null;
    booking_created: string | null;
  }>;
  eventTimeline: Array<{
    session_id: string;
    event_type: string;
    event_data: string;
    timestamp: string;
    referrer: string | null;
    user_agent: string | null;
  }>;
}

export default function SessionTrackingDashboard() {
  const [dateFrom, setDateFrom] = useState(format(subDays(new Date(), 30), 'yyyy-MM-dd'));
  const [dateTo, setDateTo] = useState(format(new Date(), 'yyyy-MM-dd'));
  const [sessionFilter, setSessionFilter] = useState('');
  const [selectedSessionId, setSelectedSessionId] = useState<string | null>(null);

  const { data: sessionAnalytics, isLoading, refetch } = useQuery<SessionAnalytics>({
    queryKey: ['/api/analytics/session-tracking', dateFrom, dateTo],
    queryFn: async () => {
      const params = new URLSearchParams({
        dateFrom,
        dateTo
      });
      const response = await fetch(`/api/analytics/session-tracking?${params}`);
      return response.json();
    }
  });

  const filteredSessions = sessionAnalytics?.sessionDetails?.filter(session => 
    !sessionFilter || 
    session.session_id.toLowerCase().includes(sessionFilter.toLowerCase()) ||
    session.customer_name?.toLowerCase().includes(sessionFilter.toLowerCase()) ||
    session.phone?.includes(sessionFilter) ||
    session.traffic_source?.toLowerCase().includes(sessionFilter.toLowerCase())
  ) || [];

  const getOutcomeBadge = (outcome: string | null, bookingStatus: string | null) => {
    if (bookingStatus) {
      switch (bookingStatus) {
        case 'confirmed':
          return <Badge className="bg-green-100 text-green-800">Booking Confirmed</Badge>;
        case 'completed':
          return <Badge className="bg-blue-100 text-blue-800">Completed</Badge>;
        case 'cancelled':
          return <Badge className="bg-red-100 text-red-800">Cancelled</Badge>;
        default:
          return <Badge className="bg-yellow-100 text-yellow-800">Pending</Badge>;
      }
    }
    
    switch (outcome) {
      case 'booking_complete':
        return <Badge className="bg-green-100 text-green-800">Converted</Badge>;
      case 'abandoned':
        return <Badge className="bg-red-100 text-red-800">Abandoned</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800">In Progress</Badge>;
    }
  };

  const exportSessionData = () => {
    if (!sessionAnalytics) return;
    
    const csvData = filteredSessions.map(session => ({
      'Session ID': session.session_id,
      'Traffic Source': session.traffic_source || 'Direct',
      'UTM Source': session.utm_source || '',
      'UTM Campaign': session.utm_campaign || '',
      'Customer Name': session.customer_name || '',
      'Phone': session.phone || '',
      'Postal Code': session.postal_code || '',
      'Booking Status': session.booking_status || 'No Booking',
      'Time Spent (min)': Math.round(session.time_spent / 60),
      'First Visit': format(new Date(session.first_visit), 'yyyy-MM-dd HH:mm'),
      'Conversion': session.booking_id ? 'Yes' : 'No'
    }));

    const csv = [
      Object.keys(csvData[0]).join(','),
      ...csvData.map(row => Object.values(row).map(val => `"${val}"`).join(','))
    ].join('\n');

    const blob = new Blob([csv], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `session-tracking-${dateFrom}-to-${dateTo}.csv`;
    a.click();
  };

  if (isLoading) {
    return <div className="p-6">Loading session analytics...</div>;
  }

  return (
    <div className="space-y-6">
      {/* Header Controls */}
      <div className="flex flex-col sm:flex-row gap-4 items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Session Tracking Dashboard</h2>
          <p className="text-gray-600">Complete end-to-end funnel tracking with 100% accuracy</p>
        </div>
        <div className="flex gap-2">
          <Button onClick={exportSessionData} variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export CSV
          </Button>
          <Button onClick={() => refetch()} variant="outline" size="sm">
            <Activity className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Date Range Controls */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CalendarIcon className="h-5 w-5" />
            Date Range & Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4">
            <div>
              <label className="block text-sm font-medium mb-1">From Date</label>
              <Input
                type="date"
                value={dateFrom}
                onChange={(e) => setDateFrom(e.target.value)}
                className="w-full"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">To Date</label>
              <Input
                type="date"
                value={dateTo}
                onChange={(e) => setDateTo(e.target.value)}
                className="w-full"
              />
            </div>
            <div className="flex-1">
              <label className="block text-sm font-medium mb-1">Search Sessions</label>
              <div className="relative">
                <Search className="h-4 w-4 absolute left-3 top-3 text-gray-400" />
                <Input
                  placeholder="Session ID, customer name, phone, or traffic source..."
                  value={sessionFilter}
                  onChange={(e) => setSessionFilter(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Sessions</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{sessionAnalytics?.totalSessions || 0}</div>
            <p className="text-xs text-muted-foreground">Complete user journeys tracked</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Sessions with Bookings</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{sessionAnalytics?.sessionsWithBookings || 0}</div>
            <p className="text-xs text-muted-foreground">Converted sessions</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Conversion Rate</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{sessionAnalytics?.conversionRate || 0}%</div>
            <p className="text-xs text-muted-foreground">Session to booking ratio</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Traffic Sources</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{Object.keys(sessionAnalytics?.trafficSources || {}).length}</div>
            <p className="text-xs text-muted-foreground">Unique traffic sources</p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="sessions" className="space-y-4">
        <TabsList>
          <TabsTrigger value="sessions">Session Details</TabsTrigger>
          <TabsTrigger value="traffic-sources">Traffic Sources</TabsTrigger>
          <TabsTrigger value="events">Event Timeline</TabsTrigger>
        </TabsList>

        <TabsContent value="sessions">
          <Card>
            <CardHeader>
              <CardTitle>Session Journey Tracking</CardTitle>
              <CardDescription>
                Complete session details with booking outcomes ({filteredSessions.length} sessions)
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Session ID</TableHead>
                      <TableHead>Traffic Source</TableHead>
                      <TableHead>Customer</TableHead>
                      <TableHead>Journey</TableHead>
                      <TableHead>Outcome</TableHead>
                      <TableHead>Time Spent</TableHead>
                      <TableHead>Started</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredSessions.map((session) => (
                      <TableRow key={session.session_id}>
                        <TableCell className="font-mono text-xs">
                          {session.session_id.replace('session_', '')}
                        </TableCell>
                        <TableCell>
                          <div className="text-sm max-w-xs">
                            <div className="font-medium break-words overflow-wrap-anywhere">{session.traffic_source || 'Direct'}</div>
                            {session.utm_campaign && (
                              <div className="text-xs text-gray-500 break-words overflow-wrap-anywhere">{session.utm_campaign}</div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">
                            <div className="font-medium">{session.customer_name || 'Anonymous'}</div>
                            {session.phone && (
                              <div className="text-xs text-gray-500">{session.phone}</div>
                            )}
                            {session.postal_code && (
                              <div className="text-xs text-gray-500">PIN: {session.postal_code}</div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">
                            <div>Steps: {session.completed_steps || 0}</div>
                            <div className="text-xs text-gray-500">
                              {session.final_outcome || 'In progress'}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          {getOutcomeBadge(session.final_outcome, session.booking_status)}
                        </TableCell>
                        <TableCell className="text-sm">
                          {Math.round(session.time_spent / 60)} min
                        </TableCell>
                        <TableCell className="text-sm">
                          {format(new Date(session.first_visit), 'MMM d, HH:mm')}
                        </TableCell>
                        <TableCell>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setSelectedSessionId(session.session_id)}
                          >
                            <ExternalLink className="h-4 w-4" />
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="traffic-sources">
          <Card>
            <CardHeader>
              <CardTitle>Traffic Source Performance</CardTitle>
              <CardDescription>
                Session and conversion metrics by traffic source
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {Object.entries(sessionAnalytics?.trafficSources || {}).map(([source, data]) => (
                  <div key={source} className="border rounded-lg p-4">
                    <div className="flex justify-between items-center mb-2">
                      <h3 className="font-medium">{source}</h3>
                      <Badge variant="outline">{data.conversionRate}% conversion</Badge>
                    </div>
                    <div className="grid grid-cols-3 gap-4 text-sm">
                      <div>
                        <div className="font-medium">{data.sessions}</div>
                        <div className="text-gray-500">Sessions</div>
                      </div>
                      <div>
                        <div className="font-medium">{data.bookings}</div>
                        <div className="text-gray-500">Bookings</div>
                      </div>
                      <div>
                        <div className="font-medium">{data.sessionIds.length}</div>
                        <div className="text-gray-500">Unique IDs</div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="events">
          <Card>
            <CardHeader>
              <CardTitle>Event Timeline</CardTitle>
              <CardDescription>
                Complete event tracking across all sessions ({sessionAnalytics?.eventTimeline.length || 0} events)
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Session ID</TableHead>
                      <TableHead>Event Type</TableHead>
                      <TableHead>Timestamp</TableHead>
                      <TableHead>Event Data</TableHead>
                      <TableHead>Referrer</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {sessionAnalytics?.eventTimeline.slice(0, 100).map((event, index) => (
                      <TableRow key={index}>
                        <TableCell className="font-mono text-xs">
                          {event.session_id.replace('session_', '')}
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">{event.event_type}</Badge>
                        </TableCell>
                        <TableCell className="text-sm">
                          {format(new Date(event.timestamp), 'MMM d, HH:mm:ss')}
                        </TableCell>
                        <TableCell className="text-xs max-w-xs truncate">
                          {event.event_data}
                        </TableCell>
                        <TableCell className="text-xs max-w-xs truncate">
                          {event.referrer || '-'}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}