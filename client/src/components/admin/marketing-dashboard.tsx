import { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { CalendarIcon, TrendingUp, Users, Activity, Target, ArrowDown, Eye, UserPlus, ShoppingBag, CheckCircle, AlertTriangle, TrendingDown, BarChart3, PieChart, MapPin, Search, Plus } from "lucide-react";
import { apiRequest } from "@/lib/queryClient";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";
import { PieChart as RechartsPieChart, Pie, Cell, ResponsiveContainer, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, LineChart, Line, Area, AreaChart } from 'recharts';
import { format, subDays, startOfDay, endOfDay } from "date-fns";
import { cn } from "@/lib/utils";

interface EventLog {
  id: number;
  sessionId: string;
  eventType: string;
  eventData: string | null;
  timestamp: string;
  userAgent: string | null;
  ipAddress: string | null;
  referrer: string | null;
}

interface UserSession {
  id: number;
  sessionId: string;
  firstVisit: string;
  lastActivity: string;
  currentStep: string;
  completedSteps: string[];
  finalOutcome: string | null;
  bookingId: number | null;
  totalEvents: number;
  timeSpent: number;
}

interface DropOffAnalytics {
  stepCounts: {
    landing: number;
    postal_code: number;
    date_time: number;
    contact_info: number;
    confirmation: number;
    completed: number;
  };
  dropOffPoints: {
    landing_to_postal: number;
    postal_to_datetime: number;
    datetime_to_contact: number;
    contact_to_confirmation: number;
    confirmation_to_completed: number;
  };
  dropOffReasons?: {
    breakdown: Array<{
      reason: string;
      count: number;
      percentage: string;
    }>;
    biggestReason?: {
      reason: string;
      count: number;
      percentage: string;
      impact: string;
    };
    totalDropOffs: number;
    insights: string[];
  };
  totalSessions: number;
  conversionRate: number;
}

interface ConversionFunnel {
  funnelSteps: Array<{
    step: string;
    count: number;
    percentage: number;
  }>;
  totalSessions: number;
  completionRate: number;
}

interface MarketingDashboardProps {
  readOnly?: boolean;
}

export default function MarketingDashboard({ readOnly = false }: MarketingDashboardProps) {
  const [dateRange, setDateRange] = useState<{
    from: Date;
    to: Date;
  }>({
    from: subDays(new Date(), 30),
    to: new Date()
  });
  const [eventTypeFilter, setEventTypeFilter] = useState<string>("all");
  const [showInvalidCodesModal, setShowInvalidCodesModal] = useState(false);
  
  const queryClient = useQueryClient();
  const { toast } = useToast();

  // Add postal code to service area mutation
  const addPostalCodeMutation = useMutation({
    mutationFn: async (data: { code: string; city: string; state: string }) => {
      const response = await apiRequest("POST", "/api/marketing/add-postal-code", data);
      return response.json();
    },
    onSuccess: (result) => {
      queryClient.invalidateQueries({ queryKey: ['/api/marketing/invalid-postal-codes'] });
      queryClient.invalidateQueries({ queryKey: ['/api/admin/postal-codes'] });
      toast({ 
        title: "Added to service area",
        description: result.message 
      });
    },
    onError: (error: any) => {
      toast({ 
        title: "Failed to add postal code",
        description: error.message || "Already exists in service area",
        variant: "destructive" 
      });
    },
  });

  // Fetch event logs
  const { data: eventLogs, isLoading: eventsLoading } = useQuery<EventLog[]>({
    queryKey: ['/api/marketing/event-logs', dateRange, eventTypeFilter],
    queryFn: async () => {
      const params = new URLSearchParams({
        dateFrom: startOfDay(dateRange.from).toISOString(),
        dateTo: endOfDay(dateRange.to).toISOString(),
      });
      if (eventTypeFilter !== 'all') {
        params.append('eventType', eventTypeFilter);
      }
      const response = await fetch(`/api/marketing/event-logs?${params}`);
      return response.json();
    }
  });

  // Fetch user sessions
  const { data: userSessions, isLoading: sessionsLoading } = useQuery<UserSession[]>({
    queryKey: ['/api/marketing/sessions', dateRange],
    queryFn: async () => {
      const params = new URLSearchParams({
        dateFrom: startOfDay(dateRange.from).toISOString(),
        dateTo: endOfDay(dateRange.to).toISOString(),
      });
      const response = await fetch(`/api/marketing/sessions?${params}`);
      return response.json();
    }
  });

  // Fetch drop-off analytics
  const { data: dropOffAnalytics, isLoading: analyticsLoading } = useQuery<DropOffAnalytics>({
    queryKey: ['/api/marketing/drop-off-analytics', dateRange],
    queryFn: async () => {
      const params = new URLSearchParams({
        dateFrom: startOfDay(dateRange.from).toISOString(),
        dateTo: endOfDay(dateRange.to).toISOString(),
      });
      const response = await fetch(`/api/marketing/drop-off-analytics?${params}`);
      return response.json();
    }
  });

  // Fetch conversion funnel
  const { data: conversionFunnel, isLoading: funnelLoading } = useQuery<ConversionFunnel>({
    queryKey: ['/api/marketing/conversion-funnel', dateRange],
    queryFn: async () => {
      const params = new URLSearchParams({
        dateFrom: startOfDay(dateRange.from).toISOString(),
        dateTo: endOfDay(dateRange.to).toISOString(),
      });
      const response = await fetch(`/api/marketing/conversion-funnel?${params}`);
      return response.json();
    }
  });

  // Fetch postal code analytics
  const { data: postalCodeAnalytics, isLoading: postalLoading } = useQuery({
    queryKey: ['/api/marketing/postal-code-analytics', dateRange],
    queryFn: async () => {
      const params = new URLSearchParams({
        dateFrom: startOfDay(dateRange.from).toISOString(),
        dateTo: endOfDay(dateRange.to).toISOString(),
      });
      const response = await fetch(`/api/marketing/postal-code-analytics?${params}`);
      return response.json();
    }
  });

  // Fetch detailed invalid postal codes
  const { data: invalidPostalCodes, isLoading: invalidCodesLoading } = useQuery({
    queryKey: ['/api/marketing/invalid-postal-codes', dateRange],
    queryFn: async () => {
      const params = new URLSearchParams({
        dateFrom: startOfDay(dateRange.from).toISOString(),
        dateTo: endOfDay(dateRange.to).toISOString(),
      });
      const response = await fetch(`/api/marketing/invalid-postal-codes?${params}`);
      return response.json();
    },
    enabled: showInvalidCodesModal
  });

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}m ${secs}s`;
  };

  const getEventIcon = (eventType: string) => {
    switch (eventType) {
      case 'page_view': return <Eye className="h-4 w-4" />;
      case 'form_start': return <UserPlus className="h-4 w-4" />;
      case 'form_submit': return <ShoppingBag className="h-4 w-4" />;
      case 'booking_complete': return <CheckCircle className="h-4 w-4" />;
      case 'error': return <AlertTriangle className="h-4 w-4" />;
      default: return <Activity className="h-4 w-4" />;
    }
  };

  const getEventTypeColor = (eventType: string) => {
    switch (eventType) {
      case 'page_view': return 'bg-blue-100 text-blue-800';
      case 'form_start': return 'bg-yellow-100 text-yellow-800';
      case 'form_submit': return 'bg-purple-100 text-purple-800';
      case 'booking_complete': return 'bg-green-100 text-green-800';
      case 'error': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Marketing Analytics</h2>
          <p className="text-muted-foreground">
            Track user behavior and conversion metrics
          </p>
        </div>
        
        <div className="flex gap-4">
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className={cn(
                  "w-[280px] justify-start text-left font-normal",
                  !dateRange && "text-muted-foreground"
                )}
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {dateRange?.from ? (
                  dateRange.to ? (
                    <>
                      {format(dateRange.from, "LLL dd, y")} -{" "}
                      {format(dateRange.to, "LLL dd, y")}
                    </>
                  ) : (
                    format(dateRange.from, "LLL dd, y")
                  )
                ) : (
                  <span>Pick a date</span>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar
                initialFocus
                mode="range"
                defaultMonth={dateRange?.from}
                selected={dateRange}
                onSelect={(range) => {
                  if (range?.from) {
                    setDateRange({ 
                      from: range.from, 
                      to: range.to || range.from 
                    });
                  }
                }}
                numberOfMonths={2}
              />
            </PopoverContent>
          </Popover>
        </div>
      </div>

      {/* Key Metrics Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Sessions</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {dropOffAnalytics?.totalSessions || 0}
            </div>
            <p className="text-xs text-muted-foreground">
              Unique visitor sessions
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Conversion Rate</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {dropOffAnalytics?.conversionRate?.toFixed(1) || 0}%
            </div>
            <p className="text-xs text-muted-foreground">
              Visitors who completed booking
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completed Bookings</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {dropOffAnalytics?.stepCounts?.completed || 0}
            </div>
            <p className="text-xs text-muted-foreground">
              Successful conversions
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Events</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {eventLogs?.length || 0}
            </div>
            <p className="text-xs text-muted-foreground">
              User interactions tracked
            </p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="funnel" className="space-y-4">
        <TabsList>
          <TabsTrigger value="funnel">Conversion Funnel</TabsTrigger>
          <TabsTrigger value="postal">Postal Codes</TabsTrigger>
          <TabsTrigger value="events">Event Logs</TabsTrigger>
          <TabsTrigger value="sessions">User Sessions</TabsTrigger>
          <TabsTrigger value="dropoff">Drop-off Analysis</TabsTrigger>
        </TabsList>

        <TabsContent value="funnel" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Conversion Funnel</CardTitle>
              <CardDescription>
                Track how users progress through the booking flow
              </CardDescription>
            </CardHeader>
            <CardContent>
              {funnelLoading ? (
                <div>Loading funnel data...</div>
              ) : (
                <div className="space-y-4">
                  {conversionFunnel?.funnelSteps?.map((step, index) => (
                    <div key={step.step} className="flex items-center gap-4">
                      <div className="w-32 text-sm font-medium">{step.step}</div>
                      <div className="flex-1 flex items-center gap-2">
                        <div className="w-full bg-gray-200 rounded-full h-6 relative">
                          <div
                            className="bg-purple-500 h-6 rounded-full flex items-center justify-center"
                            style={{ width: `${step.percentage}%` }}
                          >
                            <span className="text-xs text-white font-medium px-2">
                              {step.percentage.toFixed(1)}%
                            </span>
                          </div>
                        </div>
                        <div className="text-sm text-muted-foreground w-16">
                          {step.count}
                        </div>
                      </div>
                      {index < (conversionFunnel?.funnelSteps?.length || 0) - 1 && (
                        <ArrowDown className="h-4 w-4 text-muted-foreground" />
                      )}
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="postal" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Attempts</CardTitle>
                <MapPin className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {postalCodeAnalytics?.totalAttempts || 0}
                </div>
                <p className="text-xs text-muted-foreground">
                  Postal code validations
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
                <CheckCircle className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {postalCodeAnalytics?.successRate || 0}%
                </div>
                <p className="text-xs text-muted-foreground">
                  Valid postal codes
                </p>
              </CardContent>
            </Card>

            <Card 
              className="cursor-pointer hover:shadow-md transition-shadow" 
              onClick={() => setShowInvalidCodesModal(true)}
            >
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Wrong Codes</CardTitle>
                <div className="flex items-center gap-2">
                  <Search className="h-3 w-3 text-muted-foreground" />
                  <AlertTriangle className="h-4 w-4 text-muted-foreground" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {postalCodeAnalytics?.invalidCodes || 0}
                </div>
                <p className="text-xs text-muted-foreground">
                  Invalid entries • Click to view details
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">System Errors</CardTitle>
                <TrendingDown className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {postalCodeAnalytics?.errors || 0}
                </div>
                <p className="text-xs text-muted-foreground">
                  Validation failures
                </p>
              </CardContent>
            </Card>
          </div>

          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Postal Code Validation Breakdown</CardTitle>
                <CardDescription>Distribution of valid vs invalid postal codes</CardDescription>
              </CardHeader>
              <CardContent>
                {postalLoading ? (
                  <div>Loading postal code data...</div>
                ) : postalCodeAnalytics?.totalAttempts > 0 ? (
                  <ResponsiveContainer width="100%" height={300}>
                    <RechartsPieChart>
                      <Pie
                        data={[
                          { name: 'Valid Codes', value: postalCodeAnalytics.validCodes, fill: '#10b981' },
                          { name: 'Invalid Codes', value: postalCodeAnalytics.invalidCodes, fill: '#ef4444' },
                          { name: 'System Errors', value: postalCodeAnalytics.errors, fill: '#f59e0b' }
                        ].filter(item => item.value > 0)}
                        cx="50%"
                        cy="50%"
                        outerRadius={80}
                        dataKey="value"
                        label={({ name, value, percent }) => `${name}: ${value} (${(percent * 100).toFixed(1)}%)`}
                      >
                        {[
                          { name: 'Valid Codes', value: postalCodeAnalytics.validCodes, fill: '#10b981' },
                          { name: 'Invalid Codes', value: postalCodeAnalytics.invalidCodes, fill: '#ef4444' },
                          { name: 'System Errors', value: postalCodeAnalytics.errors, fill: '#f59e0b' }
                        ].filter(item => item.value > 0).map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.fill} />
                        ))}
                      </Pie>
                      <Tooltip />
                    </RechartsPieChart>
                  </ResponsiveContainer>
                ) : (
                  <div className="flex items-center justify-center h-[300px] text-muted-foreground">
                    No postal code data available
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Daily Postal Code Trends</CardTitle>
                <CardDescription>Valid vs invalid postal codes over time</CardDescription>
              </CardHeader>
              <CardContent>
                {postalLoading ? (
                  <div>Loading trend data...</div>
                ) : postalCodeAnalytics?.dailyStats?.length > 0 ? (
                  <ResponsiveContainer width="100%" height={300}>
                    <AreaChart data={postalCodeAnalytics.dailyStats}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="date" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Area
                        type="monotone"
                        dataKey="valid"
                        stackId="1"
                        stroke="#10b981"
                        fill="#10b981"
                        name="Valid Codes"
                      />
                      <Area
                        type="monotone"
                        dataKey="invalid"
                        stackId="1"
                        stroke="#ef4444"
                        fill="#ef4444"
                        name="Invalid Codes"
                      />
                      <Area
                        type="monotone"
                        dataKey="errors"
                        stackId="1"
                        stroke="#f59e0b"
                        fill="#f59e0b"
                        name="System Errors"
                      />
                    </AreaChart>
                  </ResponsiveContainer>
                ) : (
                  <div className="flex items-center justify-center h-[300px] text-muted-foreground">
                    No trend data available
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {postalCodeAnalytics?.topInvalidCodes?.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Most Attempted Invalid Postal Codes</CardTitle>
                <CardDescription>Postal codes users tried that aren't in our service area</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={postalCodeAnalytics.topInvalidCodes}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="code" />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="count" fill="#ef4444" name="Attempts" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          )}

          {postalCodeAnalytics?.insights?.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Postal Code Insights</CardTitle>
                <CardDescription>Actionable recommendations based on postal code data</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {postalCodeAnalytics.insights.map((insight: string, index: number) => (
                    <div key={index} className="flex items-start gap-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                      <BarChart3 className="h-5 w-5 text-blue-600 mt-0.5" />
                      <p className="text-sm text-blue-800">{insight}</p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="events" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Event Logs</CardTitle>
              <CardDescription>
                Detailed tracking of user interactions
              </CardDescription>
              <div className="flex gap-2">
                <Select value={eventTypeFilter} onValueChange={setEventTypeFilter}>
                  <SelectTrigger className="w-48">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Events</SelectItem>
                    <SelectItem value="page_view">Page Views</SelectItem>
                    <SelectItem value="form_start">Form Starts</SelectItem>
                    <SelectItem value="form_submit">Form Submits</SelectItem>
                    <SelectItem value="booking_complete">Bookings</SelectItem>
                    <SelectItem value="error">Errors</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardHeader>
            <CardContent>
              {eventsLoading ? (
                <div>Loading events...</div>
              ) : (
                <div className="space-y-2 max-h-96 overflow-y-auto">
                  {eventLogs?.map((event) => (
                    <div key={event.id} className="flex items-center gap-3 p-2 border rounded">
                      <div className="flex items-center gap-2">
                        {getEventIcon(event.eventType)}
                        <Badge className={getEventTypeColor(event.eventType)}>
                          {event.eventType}
                        </Badge>
                      </div>
                      <div className="flex-1">
                        <div className="text-sm font-medium">
                          Session: {event.sessionId.slice(-8)}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {format(new Date(event.timestamp), 'MMM dd, HH:mm:ss')}
                        </div>
                      </div>
                      {event.eventData && (
                        <div className="text-xs text-muted-foreground max-w-48 truncate">
                          {event.eventData}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="sessions" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>User Sessions</CardTitle>
              <CardDescription>
                Individual user journey tracking
              </CardDescription>
            </CardHeader>
            <CardContent>
              {sessionsLoading ? (
                <div>Loading sessions...</div>
              ) : (
                <div className="space-y-2 max-h-96 overflow-y-auto">
                  {userSessions?.map((session) => (
                    <div key={session.id} className="flex items-center gap-3 p-3 border rounded">
                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <span className="text-sm font-medium">
                            {session.sessionId.slice(-8)}
                          </span>
                          {session.finalOutcome && (
                            <Badge 
                              className={
                                session.finalOutcome === 'completed' 
                                  ? 'bg-green-100 text-green-800'
                                  : 'bg-red-100 text-red-800'
                              }
                            >
                              {session.finalOutcome}
                            </Badge>
                          )}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          Started: {format(new Date(session.firstVisit), 'MMM dd, HH:mm')}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          Steps: {session.completedSteps.join(' → ')}
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-sm">{session.totalEvents} events</div>
                        <div className="text-xs text-muted-foreground">
                          {formatDuration(session.timeSpent)}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="dropoff" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Drop-off Analysis</CardTitle>
              <CardDescription>
                Identify where users leave the booking process
              </CardDescription>
            </CardHeader>
            <CardContent>
              {analyticsLoading ? (
                <div>Loading analytics...</div>
              ) : (
                <div className="space-y-6">
                  {/* Biggest Drop-off Reason */}
                  {dropOffAnalytics?.dropOffReasons?.biggestReason && (
                    <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                      <h4 className="font-semibold text-red-800 mb-2 flex items-center gap-2">
                        <AlertTriangle className="h-4 w-4" />
                        Biggest Drop-off Reason
                      </h4>
                      <p className="text-red-700 font-medium text-lg">
                        {dropOffAnalytics.dropOffReasons.biggestReason.reason}
                      </p>
                      <p className="text-sm text-red-600 mt-1">
                        {dropOffAnalytics.dropOffReasons.biggestReason.count} users ({dropOffAnalytics.dropOffReasons.biggestReason.percentage}% of all drop-offs)
                      </p>
                      <p className="text-xs text-red-500 mt-2 p-2 bg-red-100 rounded">
                        💡 {dropOffAnalytics.dropOffReasons.biggestReason.impact}
                      </p>
                    </div>
                  )}

                  {/* Key Insights */}
                  {dropOffAnalytics?.dropOffReasons?.insights && dropOffAnalytics.dropOffReasons.insights.length > 0 && (
                    <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                      <h4 className="font-medium text-blue-800 mb-3 flex items-center gap-2">
                        <TrendingDown className="h-4 w-4" />
                        Key Insights & Recommendations
                      </h4>
                      <ul className="space-y-2">
                        {dropOffAnalytics.dropOffReasons.insights.map((insight, index) => (
                          <li key={index} className="text-sm text-blue-700 flex items-start gap-2">
                            <span className="text-blue-500 mt-1">•</span>
                            {insight}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}

                  <div className="grid gap-6 md:grid-cols-2">
                    {/* Drop-off Breakdown */}
                    <div>
                      <h4 className="text-sm font-medium mb-3 flex items-center gap-2">
                        <BarChart3 className="h-4 w-4" />
                        All Drop-off Reasons
                      </h4>
                      <div className="space-y-3">
                        {dropOffAnalytics?.dropOffReasons?.breakdown?.map((reason, index) => (
                          <div key={reason.reason} className="border rounded-lg p-3">
                            <div className="flex justify-between items-center mb-2">
                              <span className="text-sm font-medium">{reason.reason}</span>
                              <span className="text-sm text-gray-600">
                                {reason.count} users
                              </span>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2 mb-1">
                              <div
                                className="bg-red-500 h-2 rounded-full"
                                style={{ width: `${reason.percentage}%` }}
                              />
                            </div>
                            <span className="text-xs text-gray-500">{reason.percentage}% of drop-offs</span>
                          </div>
                        ))}
                      </div>
                    </div>
                    
                    {/* Step Completion vs Drop-offs */}
                    <div>
                      <h4 className="text-sm font-medium mb-3 flex items-center gap-2">
                        <Users className="h-4 w-4" />
                        Step Completion Overview
                      </h4>
                      <div className="space-y-2">
                        {dropOffAnalytics && Object.entries(dropOffAnalytics.stepCounts).map(([step, count]) => (
                          <div key={step} className="flex justify-between items-center p-2 rounded border">
                            <span className="text-sm capitalize">{step.replace('_', ' ')}</span>
                            <span className="text-sm font-medium">{count} users</span>
                          </div>
                        ))}
                      </div>
                      
                      <div className="mt-4 pt-4 border-t">
                        <h5 className="text-sm font-medium mb-2 text-gray-600">Drop-off Transitions</h5>
                        <div className="space-y-2">
                          {dropOffAnalytics && Object.entries(dropOffAnalytics.dropOffPoints).map(([transition, count]) => (
                            <div key={transition} className="flex justify-between text-sm">
                              <span className="text-gray-600">{transition.replace(/_/g, ' → ')}</span>
                              <span className="font-medium text-red-600">{count}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Invalid Postal Codes Modal */}
      <Dialog open={showInvalidCodesModal} onOpenChange={setShowInvalidCodesModal}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Search className="h-5 w-5" />
              Invalid Postal Codes Details
            </DialogTitle>
          </DialogHeader>
          
          {invalidCodesLoading ? (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mx-auto mb-4"></div>
                <p className="text-gray-600">Loading invalid postal codes...</p>
              </div>
            </div>
          ) : invalidPostalCodes ? (
            <div className="space-y-6">
              {/* Summary Stats */}
              <div className="grid grid-cols-3 gap-4">
                <div className="bg-red-50 p-4 rounded-lg border border-red-200">
                  <h3 className="text-lg font-semibold text-red-800">
                    {invalidPostalCodes.totalInvalidAttempts}
                  </h3>
                  <p className="text-sm text-red-600">Total Invalid Attempts</p>
                </div>
                <div className="bg-orange-50 p-4 rounded-lg border border-orange-200">
                  <h3 className="text-lg font-semibold text-orange-800">
                    {invalidPostalCodes.uniqueCodes}
                  </h3>
                  <p className="text-sm text-orange-600">Unique Invalid Codes</p>
                </div>
                <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                  <h3 className="text-lg font-semibold text-blue-800">
                    {Math.round((invalidPostalCodes.totalInvalidAttempts / Math.max(1, (postalCodeAnalytics?.totalAttempts || 1))) * 100)}%
                  </h3>
                  <p className="text-sm text-blue-600">Invalid Rate</p>
                </div>
              </div>

              {/* Top Invalid Codes Table */}
              <div>
                <h3 className="text-lg font-semibold mb-4">Most Attempted Invalid Postal Codes</h3>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Postal Code</TableHead>
                      <TableHead>City</TableHead>
                      <TableHead>Total Attempts</TableHead>
                      <TableHead>Unique Sessions</TableHead>
                      <TableHead>First Seen</TableHead>
                      <TableHead>Last Seen</TableHead>
                      <TableHead>Action</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {invalidPostalCodes.topInvalidCodes?.slice(0, 15).map((code: any, index: number) => (
                      <TableRow key={`${code.code}-${index}`}>
                        <TableCell className="font-mono font-semibold text-red-600">
                          {code.code}
                        </TableCell>
                        <TableCell className="text-sm text-gray-600">
                          {code.city || 'Unknown'}
                        </TableCell>
                        <TableCell>
                          <Badge variant="destructive" className="bg-red-100 text-red-800">
                            {code.totalAttempts}
                          </Badge>
                        </TableCell>
                        <TableCell>{code.uniqueSessions}</TableCell>
                        <TableCell className="text-sm text-gray-600">
                          {format(new Date(code.firstSeen), 'MMM d, h:mm a')}
                        </TableCell>
                        <TableCell className="text-sm text-gray-600">
                          {format(new Date(code.lastSeen), 'MMM d, h:mm a')}
                        </TableCell>
                        <TableCell>
                          {code.code !== 'Unknown' && (
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => {
                                const [city, state] = (code.city || 'Unknown, Unknown').split(', ');
                                addPostalCodeMutation.mutate({ 
                                  code: code.code, 
                                  city: city || 'Unknown', 
                                  state: state || 'Unknown' 
                                });
                              }}
                              disabled={addPostalCodeMutation.isPending}
                              className="flex items-center gap-1 text-xs h-7"
                            >
                              <Plus className="h-3 w-3" />
                              Add to Service
                            </Button>
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>

              {/* All Attempts Table */}
              <div>
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold">All Wrong Postal Codes (Latest First)</h3>
                  <div className="text-sm text-gray-600">
                    Showing {invalidPostalCodes.allAttempts?.length || 0} entries
                  </div>
                </div>
                <div className="max-h-96 overflow-y-auto border rounded-lg">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Postal Code</TableHead>
                        <TableHead>City</TableHead>
                        <TableHead>Timestamp</TableHead>
                        <TableHead>Session ID</TableHead>
                        <TableHead>IP Address</TableHead>
                        <TableHead>Action</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {invalidPostalCodes.allAttempts?.map((attempt: any, index: number) => (
                        <TableRow key={`${attempt.sessionId}-${index}`}>
                          <TableCell className="font-mono text-red-600 font-semibold">
                            {attempt.code}
                          </TableCell>
                          <TableCell className="text-sm text-gray-600">
                            {attempt.city || 'Unknown'}
                          </TableCell>
                          <TableCell className="text-sm">
                            {format(new Date(attempt.timestamp), 'MMM d, h:mm:ss a')}
                          </TableCell>
                          <TableCell className="font-mono text-xs text-gray-600">
                            {attempt.sessionId.slice(-8)}
                          </TableCell>
                          <TableCell className="text-sm text-gray-600">
                            {attempt.ipAddress || 'Unknown'}
                          </TableCell>
                          <TableCell>
                            {attempt.code !== 'Unknown' && (
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => {
                                  const [city, state] = (attempt.city || 'Unknown, Unknown').split(', ');
                                  addPostalCodeMutation.mutate({ 
                                    code: attempt.code, 
                                    city: city || 'Unknown', 
                                    state: state || 'Unknown' 
                                  });
                                }}
                                disabled={addPostalCodeMutation.isPending}
                                className="flex items-center gap-1 text-xs h-7"
                              >
                                <Plus className="h-3 w-3" />
                                Add to Service
                              </Button>
                            )}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </div>

              {/* Insights */}
              <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
                <h3 className="text-lg font-semibold text-yellow-800 mb-2">Service Area Expansion Insights</h3>
                <p className="text-sm text-yellow-700 mb-3">
                  These are areas where customers are trying to book but we don't currently serve:
                </p>
                <div className="grid grid-cols-2 gap-2">
                  {invalidPostalCodes.topInvalidCodes?.slice(0, 10).map((code: any, index: number) => (
                    <div key={index} className="text-sm bg-yellow-100 p-2 rounded border">
                      <span className="font-mono font-semibold text-yellow-800">{code.code}</span>
                      <span className="text-yellow-600 ml-2">({code.totalAttempts} attempts)</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          ) : (
            <div className="text-center py-8">
              <p className="text-gray-600">No invalid postal code data available for the selected date range.</p>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}