import { useQuery } from "@tanstack/react-query";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { 
  RefreshCw, 
  TrendingUp, 
  TrendingDown, 
  Users, 
  Target, 
  CreditCard, 
  Activity,
  Eye,
  MousePointer,
  Calendar,
  Clock,
  AlertTriangle,
  CheckCircle,
  HelpCircle,
  ArrowUp,
  ArrowDown,
  BarChart3,
  Info,
  ExternalLink
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { 
  <PERSON><PERSON>hart, 
  Line, 
  XAxis, 
  <PERSON><PERSON><PERSON>s, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer, 
  BarChart, 
  Bar, 
  PieChart, 
  Pie, 
  Cell,
  Area,
  AreaChart
} from 'recharts';
import { useState, useEffect } from 'react';

// UTM Campaign Tracker Component
function UTMCampaignTracker({ period }: { period: string }) {
  const [sortBy, setSortBy] = useState<'sessions' | 'bookings' | 'conversion'>('sessions');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  
  const { data: utmData, isLoading } = useQuery<any[]>({
    queryKey: [`/api/analytics/utm-campaigns/${period}`],
    refetchInterval: 15000,
  });

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>UTM Campaign Performance</CardTitle>
          <CardDescription>Loading campaign data...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-20 bg-gray-200 rounded"></div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  const rawCampaigns = utmData || [];
  
  // Sort campaigns based on selected criteria
  const campaigns = [...rawCampaigns].sort((a, b) => {
    let aValue, bValue;
    
    switch (sortBy) {
      case 'sessions':
        aValue = a.sessions || 0;
        bValue = b.sessions || 0;
        break;
      case 'bookings':
        aValue = a.bookings || a.conversions || 0;
        bValue = b.bookings || b.conversions || 0;
        break;
      case 'conversion':
        aValue = a.conversionRate || (a.sessions > 0 ? ((a.bookings || a.conversions || 0) / a.sessions) * 100 : 0);
        bValue = b.conversionRate || (b.sessions > 0 ? ((b.bookings || b.conversions || 0) / b.sessions) * 100 : 0);
        break;
      default:
        aValue = a.sessions || 0;
        bValue = b.sessions || 0;
    }
    
    if (sortOrder === 'desc') {
      return bValue - aValue;
    } else {
      return aValue - bValue;
    }
  });

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="w-5 h-5 text-purple-600" />
                UTM Campaign Performance
              </CardTitle>
              <CardDescription>
                Real-time tracking of all UTM campaigns with 100% accuracy
              </CardDescription>
            </div>
            <div className="flex items-center gap-3">
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium text-gray-700">Sort by:</span>
                <Select value={sortBy} onValueChange={(value: 'sessions' | 'bookings' | 'conversion') => setSortBy(value)}>
                  <SelectTrigger className="w-32">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="sessions">Sessions</SelectItem>
                    <SelectItem value="bookings">Bookings</SelectItem>
                    <SelectItem value="conversion">Conversion Rate</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setSortOrder(sortOrder === 'desc' ? 'asc' : 'desc')}
                className="p-2"
              >
                {sortOrder === 'desc' ? (
                  <ArrowDown className="w-4 h-4" />
                ) : (
                  <ArrowUp className="w-4 h-4" />
                )}
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {campaigns.length > 0 ? (
            <div className="space-y-4">
              {campaigns.map((campaign: any, index: number) => (
                <div key={index} className="p-4 border rounded-lg hover:shadow-md transition-shadow">
                  <div className="flex items-center justify-between mb-3">
                    <div>
                      <h3 className="font-semibold text-lg">{campaign.utm_campaign || 'Unknown Campaign'}</h3>
                      <p className="text-sm text-gray-600">
                        {campaign.utm_source} • {campaign.utm_medium}
                      </p>
                    </div>
                    <div className="flex gap-2">
                      <Badge variant="outline" className="px-3 py-1">
                        {campaign.sessions} sessions
                      </Badge>
                      {campaign.performance && (
                        <Badge 
                          variant={campaign.performance === 'High' ? 'default' : 
                                   campaign.performance === 'Medium' ? 'secondary' : 'outline'} 
                          className="px-3 py-1"
                        >
                          {campaign.performance}
                        </Badge>
                      )}
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div className="text-center p-3 bg-purple-50 rounded-lg">
                      <p className="text-2xl font-bold text-purple-600">{campaign.sessions}</p>
                      <p className="text-gray-600">Sessions</p>
                    </div>
                    <div className="text-center p-3 bg-blue-50 rounded-lg">
                      <p className="text-2xl font-bold text-blue-600">{campaign.bookings || campaign.conversions || '0'}</p>
                      <p className="text-gray-600">Bookings</p>
                    </div>
                    <div className="text-center p-3 bg-green-50 rounded-lg">
                      <p className="text-2xl font-bold text-green-600">
                        {campaign.conversionRate ? campaign.conversionRate.toFixed(1) : 
                         (campaign.sessions > 0 ? (((campaign.bookings || campaign.conversions || 0) / campaign.sessions) * 100).toFixed(1) : '0.0')}%
                      </p>
                      <p className="text-gray-600">Conversion Rate</p>
                    </div>
                    <div className="text-center p-3 bg-orange-50 rounded-lg">
                      <p className="text-2xl font-bold text-orange-600">{campaign.events || campaign.totalEvents || '0'}</p>
                      <p className="text-gray-600">Events</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              <BarChart3 className="w-12 h-12 mx-auto mb-3 text-gray-300" />
              <p>No UTM campaign data available for the selected period</p>
              <p className="text-sm mt-1">Make sure campaigns are running with proper UTM parameters</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* UTM Accuracy Guarantee */}
      <Card className="border-blue-200 bg-blue-50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-blue-800">
            <CheckCircle className="w-5 h-5 text-blue-600" />
            UTM Tracking Accuracy
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              <span>Real-time database tracking</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              <span>All UTM parameters captured</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              <span>Session-to-booking attribution</span>
            </div>
          </div>
          <p className="text-blue-700 text-sm mt-2">
            Every campaign interaction is tracked from landing page to booking completion
          </p>
        </CardContent>
      </Card>
    </div>
  );
}

// Executive Analytics Interface - Focus on UTM Accuracy and Direct Visits
interface ExecutiveMetrics {
  totalVisitors: number;
  totalSessions: number;
  totalBookings: number;
  confirmedBookings: number;
  completedBookings: number;
  conversionRate: number;
  
  // Traffic Attribution - UTM vs Direct vs Unknown
  utmSessions: number;
  directSessions: number;
  utmTrafficRate: number;
  directTrafficRate: number;
  otherSourcesRate: number;
  totalUtmEvents: number;
  totalDirectEvents: number;
  totalUnknownEvents: number;
  
  // Trend indicators (no revenue)
  visitorsTrend: number;
  sessionsTrend: number;
  bookingsTrend: number;
  conversionTrend: number;
  
  // Time period
  period: string;
  lastUpdated: string;
}

interface CampaignPerformance {
  name: string;
  source: string;
  spend: number;
  visitors: number;
  bookings: number;
  revenue: number;
  roas: number; // Return on Ad Spend
  cpa: number;  // Cost Per Acquisition
  status: 'Active' | 'Paused' | 'Ended';
  trend: 'up' | 'down' | 'stable';
}

interface FunnelStep {
  name: string;
  users: number;
  percentage: number;
  dropOffRate: number;
  previousStep?: string;
}

interface CustomerJourney {
  stage: string;
  count: number;
  value: number;
  avgTimeSpent: number;
  conversionRate: number;
}

const TREND_COLORS = {
  positive: '#10B981', // Green
  negative: '#EF4444', // Red
  neutral: '#6B7280'   // Gray
};

const PERFORMANCE_COLORS = ['#8B5CF6', '#06B6D4', '#10B981', '#F59E0B', '#EF4444'];

// Helper function to format currency
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: 'INR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount);
};

// Helper function to format percentage
const formatPercentage = (value: number, decimals = 1) => {
  return `${value.toFixed(decimals)}%`;
};

// Helper function to format large numbers
const formatNumber = (num: number) => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M';
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K';
  }
  return num.toString();
};

// Trend indicator component
function TrendIndicator({ value, showIcon = true, className = "" }: { 
  value: number; 
  showIcon?: boolean; 
  className?: string; 
}) {
  const isPositive = value > 0;
  const isNegative = value < 0;
  
  return (
    <div className={`flex items-center gap-1 ${className}`}>
      {showIcon && (
        <>
          {isPositive && <ArrowUp className="w-4 h-4 text-green-500" />}
          {isNegative && <ArrowDown className="w-4 h-4 text-red-500" />}
        </>
      )}
      <span className={`text-sm font-medium ${
        isPositive ? 'text-green-600' : isNegative ? 'text-red-600' : 'text-gray-600'
      }`}>
        {isPositive ? '+' : ''}{formatPercentage(value)}
      </span>
    </div>
  );
}

// Executive KPI Card Component
function ExecutiveKPICard({ 
  title, 
  value, 
  trend, 
  icon: Icon, 
  format = 'number',
  subtitle,
  onClick
}: {
  title: string;
  value: number;
  trend: number;
  icon: any;
  format?: 'number' | 'currency' | 'percentage';
  subtitle?: string;
  onClick?: () => void;
}) {
  const formatValue = () => {
    switch (format) {
      case 'currency':
        return formatCurrency(value);
      case 'percentage':
        return formatPercentage(value);
      default:
        return formatNumber(value);
    }
  };

  return (
    <Card className={`transition-all duration-200 ${onClick ? 'cursor-pointer hover:shadow-lg hover:scale-105' : ''}`} onClick={onClick}>
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-purple-100 rounded-lg">
              <Icon className="w-5 h-5 text-purple-600" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-600">{title}</p>
              <p className="text-2xl font-bold text-gray-900">{formatValue()}</p>
              {subtitle && <p className="text-xs text-gray-500">{subtitle}</p>}
            </div>
          </div>
          <div className="flex items-center gap-2">
            <TrendIndicator value={trend} />
            {onClick && <Info className="w-4 h-4 text-gray-400" />}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export default function ExecutiveAnalyticsDashboard() {
  const { toast } = useToast();
  const [selectedPeriod, setSelectedPeriod] = useState('7d');
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [selectedMetric, setSelectedMetric] = useState<{
    title: string;
    description: string;
    data: any;
    type: string;
  } | null>(null);

  // Executive metrics query
  const { data: executiveMetrics, isLoading: metricsLoading, refetch: refetchMetrics } = useQuery({
    queryKey: ['/api/analytics/executive-metrics', selectedPeriod],
    enabled: true,
  });

  // Campaign performance query
  const { data: campaignData, isLoading: campaignLoading, refetch: refetchCampaigns } = useQuery({
    queryKey: ['/api/analytics/campaign-performance', selectedPeriod],
    enabled: true,
  });

  // Conversion funnel query
  const { data: funnelData, isLoading: funnelLoading, refetch: refetchFunnel } = useQuery({
    queryKey: ['/api/analytics/conversion-funnel', selectedPeriod],
    enabled: true,
  });

  // Customer journey query
  const { data: journeyData, isLoading: journeyLoading, refetch: refetchJourney } = useQuery({
    queryKey: ['/api/analytics/customer-journey', selectedPeriod],
    enabled: true,
  });

  // Refresh all data
  const handleRefreshAll = async () => {
    setIsRefreshing(true);
    try {
      await Promise.all([
        refetchMetrics(),
        refetchCampaigns(),
        refetchFunnel(),
        refetchJourney()
      ]);
      toast({
        title: "Analytics Updated",
        description: "All data has been refreshed with the latest information.",
      });
    } catch (error) {
      toast({
        title: "Update Failed",
        description: "Unable to refresh analytics data. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsRefreshing(false);
    }
  };

  // Default data for when API returns null/undefined - Updated for UTM vs Direct vs Unknown
  const safeExecutiveMetrics: ExecutiveMetrics = (executiveMetrics as ExecutiveMetrics) || {
    totalVisitors: 0,
    totalSessions: 0,
    totalBookings: 0,
    confirmedBookings: 0,
    completedBookings: 0,
    conversionRate: 0,
    
    // Traffic Attribution
    utmSessions: 0,
    directSessions: 0,
    utmTrafficRate: 0,
    directTrafficRate: 0,
    otherSourcesRate: 0,
    totalUtmEvents: 0,
    totalDirectEvents: 0,
    totalUnknownEvents: 0,
    
    // Trends (no revenue)
    visitorsTrend: 0,
    sessionsTrend: 0,
    bookingsTrend: 0,
    conversionTrend: 0,
    
    period: selectedPeriod,
    lastUpdated: new Date().toISOString()
  };

  const safeCampaignData: CampaignPerformance[] = Array.isArray(campaignData) ? campaignData : [];
  const safeFunnelData: FunnelStep[] = Array.isArray(funnelData) ? funnelData : [];
  const safeJourneyData: CustomerJourney[] = Array.isArray(journeyData) ? journeyData : [];

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Performance Overview</h1>
          <p className="text-gray-600">
            Comprehensive analytics with 100% accurate UTM tracking and campaign performance
          </p>
          <p className="text-xs text-gray-500 mt-1">
            Data collection started: June 26, 2025 • Real-time tracking with {safeExecutiveMetrics.totalSessions.toLocaleString()} total sessions analyzed
          </p>
        </div>
        <div className="flex items-center gap-3">
          <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="Select period" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="1d">Today</SelectItem>
              <SelectItem value="7d">Last 7 Days</SelectItem>
              <SelectItem value="30d">Last Month</SelectItem>
              <SelectItem value="all">All Time</SelectItem>
            </SelectContent>
          </Select>
          <Button 
            onClick={handleRefreshAll}
            disabled={isRefreshing}
            variant="outline"
            className="flex items-center gap-2"
          >
            <RefreshCw className={`w-4 h-4 ${isRefreshing ? 'animate-spin' : ''}`} />
            {isRefreshing ? 'Updating...' : 'Refresh'}
          </Button>
        </div>
      </div>

      {/* Executive KPIs - Focus on UTM Accuracy and Direct Visits */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <ExecutiveKPICard
          title="Total Visitors"
          value={safeExecutiveMetrics.totalVisitors}
          trend={safeExecutiveMetrics.visitorsTrend}
          icon={Users}
          subtitle="Unique website visitors"
          onClick={() => setSelectedMetric({
            title: "Total Visitors Breakdown",
            description: `Detailed analysis of ${safeExecutiveMetrics.totalVisitors.toLocaleString()} unique visitors over ${selectedPeriod === '1d' ? 'today' : selectedPeriod === '7d' ? 'the last 7 days' : selectedPeriod === '30d' ? 'the last month' : 'all time'}`,
            data: {
              total: safeExecutiveMetrics.totalVisitors,
              utm: safeExecutiveMetrics.utmSessions,
              direct: safeExecutiveMetrics.directSessions,
              unknown: safeExecutiveMetrics.unknownSessions,
              utmRate: safeExecutiveMetrics.utmTrafficRate,
              directRate: safeExecutiveMetrics.directTrafficRate,
              unknownRate: safeExecutiveMetrics.unknownTrafficRate,
              trend: safeExecutiveMetrics.visitorsTrend
            },
            type: "visitors"
          })}
        />
        <ExecutiveKPICard
          title="Total Sessions"
          value={safeExecutiveMetrics.totalSessions}
          trend={safeExecutiveMetrics.sessionsTrend}
          icon={Activity}
          subtitle="Website sessions"
          onClick={() => setSelectedMetric({
            title: "Session Analytics",
            description: `Comprehensive breakdown of ${safeExecutiveMetrics.totalSessions.toLocaleString()} website sessions with traffic source analysis`,
            data: {
              total: safeExecutiveMetrics.totalSessions,
              utm: safeExecutiveMetrics.utmSessions,
              direct: safeExecutiveMetrics.directSessions,
              unknown: safeExecutiveMetrics.unknownSessions,
              utmRate: safeExecutiveMetrics.utmTrafficRate,
              directRate: safeExecutiveMetrics.directTrafficRate,
              unknownRate: safeExecutiveMetrics.unknownTrafficRate
            },
            type: "sessions"
          })}
        />
        <ExecutiveKPICard
          title="Total Bookings"
          value={safeExecutiveMetrics.totalBookings}
          trend={safeExecutiveMetrics.bookingsTrend}
          icon={Calendar}
          subtitle="All appointments"
          onClick={() => setSelectedMetric({
            title: "Booking Performance",
            description: `Analysis of ${safeExecutiveMetrics.totalBookings} appointments with status breakdown and conversion metrics`,
            data: {
              total: safeExecutiveMetrics.totalBookings,
              confirmed: safeExecutiveMetrics.confirmedBookings,
              completed: safeExecutiveMetrics.completedBookings,
              conversionRate: safeExecutiveMetrics.conversionRate
            },
            type: "bookings"
          })}
        />
        <ExecutiveKPICard
          title="Conversion Rate"
          value={safeExecutiveMetrics.conversionRate}
          trend={safeExecutiveMetrics.conversionTrend}
          icon={Target}
          format="percentage"
          subtitle="Sessions to bookings"
        />
      </div>

      {/* Traffic Attribution KPIs - UTM vs Direct vs Unknown */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <ExecutiveKPICard
          title="UTM Traffic"
          value={safeExecutiveMetrics.utmSessions}
          trend={0}
          icon={Eye}
          subtitle={`${safeExecutiveMetrics.utmTrafficRate.toFixed(1)}% of sessions`}
        />
        <ExecutiveKPICard
          title="Direct Traffic"
          value={safeExecutiveMetrics.directSessions}
          trend={0}
          icon={MousePointer}
          subtitle={`${safeExecutiveMetrics.directTrafficRate.toFixed(1)}% of sessions`}
        />
        <ExecutiveKPICard
          title="Unknown Traffic"
          value={safeExecutiveMetrics.unknownSessions}
          trend={0}
          icon={AlertTriangle}
          subtitle={`${safeExecutiveMetrics.unknownTrafficRate.toFixed(1)}% (pre-July 1)`}
        />
        <ExecutiveKPICard
          title="Confirmed"
          value={safeExecutiveMetrics.confirmedBookings}
          trend={0}
          icon={CheckCircle}
          subtitle="Active appointments"
        />
      </div>

      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Business Overview</TabsTrigger>
          <TabsTrigger value="campaigns">Ad Performance</TabsTrigger>
          <TabsTrigger value="funnel">Conversion Flow</TabsTrigger>
          <TabsTrigger value="journey">Customer Journey</TabsTrigger>
        </TabsList>

        {/* Business Overview Tab - UTM & Direct Traffic Focus */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Traffic Sources Distribution */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Eye className="w-5 h-5 text-blue-500" />
                  Traffic Sources
                </CardTitle>
                <CardDescription>UTM vs Direct vs Unknown traffic distribution</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={[
                          { name: 'UTM Traffic', value: safeExecutiveMetrics.utmSessions, fill: '#8B5CF6' },
                          { name: 'Direct Traffic', value: safeExecutiveMetrics.directSessions, fill: '#06B6D4' },
                          { name: 'Unknown Traffic', value: safeExecutiveMetrics.unknownSessions, fill: '#F59E0B' },
                        ]}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                      >
                        <Cell fill="#8B5CF6" />
                        <Cell fill="#06B6D4" />
                        <Cell fill="#F59E0B" />
                      </Pie>
                      <Tooltip />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            {/* Conversion Metrics */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Target className="w-5 h-5 text-blue-500" />
                  Conversion Metrics
                </CardTitle>
                <CardDescription>Key performance indicators</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Overall Conversion Rate</span>
                    <span className="text-lg font-bold">{formatPercentage(safeExecutiveMetrics.conversionRate)}</span>
                  </div>
                  <Progress value={safeExecutiveMetrics.conversionRate} className="h-2" />
                </div>
                
                <div className="grid grid-cols-2 gap-4 pt-4">
                  <div className="text-center p-3 bg-gray-50 rounded-lg">
                    <p className="text-2xl font-bold text-gray-900">{safeExecutiveMetrics.totalSessions}</p>
                    <p className="text-sm text-gray-600">Total Sessions</p>
                  </div>
                  <div className="text-center p-3 bg-gray-50 rounded-lg">
                    <p className="text-2xl font-bold text-gray-900">{safeExecutiveMetrics.totalBookings}</p>
                    <p className="text-sm text-gray-600">Bookings Made</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Data Accuracy Guarantee */}
          <Card className="border-green-200 bg-green-50">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-green-800">
                <CheckCircle className="w-5 h-5 text-green-600" />
                Data Accuracy Guarantee
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span>Real-time database queries</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span>No cached or estimated data</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span>100% verified metrics</span>
                </div>
              </div>
              <p className="text-green-700 text-sm mt-2">
                Last updated: {new Date(safeExecutiveMetrics.lastUpdated).toLocaleString('en-IN')}
              </p>
            </CardContent>
          </Card>
        </TabsContent>

        {/* UTM Campaign Tracking Tab */}
        <TabsContent value="campaigns" className="space-y-6">
          {/* Traffic Source Overview */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="w-5 h-5" />
                Traffic Sources Overview
              </CardTitle>
              <CardDescription>
                Complete breakdown of traffic sources including UTM campaigns, direct visits, and WhatsApp notifications
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="text-2xl font-bold text-blue-600">
                        {safeExecutiveMetrics.utmSessions.toLocaleString()}
                      </div>
                      <div className="text-sm text-blue-600 font-medium">UTM Campaign Traffic</div>
                      <div className="text-xs text-blue-500">
                        {safeExecutiveMetrics.utmTrafficRate.toFixed(1)}% of total sessions
                      </div>
                    </div>
                    <Target className="w-8 h-8 text-blue-400" />
                  </div>
                </div>
                
                <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="text-2xl font-bold text-green-600">
                        {safeExecutiveMetrics.directSessions.toLocaleString()}
                      </div>
                      <div className="text-sm text-green-600 font-medium">Direct Traffic</div>
                      <div className="text-xs text-green-500">
                        {safeExecutiveMetrics.directTrafficRate.toFixed(1)}% of total sessions
                      </div>
                    </div>
                    <MousePointer className="w-8 h-8 text-green-400" />
                  </div>
                </div>
                
                <div className="bg-purple-50 p-4 rounded-lg border border-purple-200">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="text-2xl font-bold text-purple-600">
                        {(safeExecutiveMetrics.totalSessions - safeExecutiveMetrics.utmSessions - safeExecutiveMetrics.directSessions).toLocaleString()}
                      </div>
                      <div className="text-sm text-purple-600 font-medium">Other Sources</div>
                      <div className="text-xs text-purple-500">
                        Referral & organic traffic
                      </div>
                    </div>
                    <AlertTriangle className="w-8 h-8 text-purple-400" />
                  </div>
                </div>
              </div>
              
              {/* Traffic Pie Chart */}
              <div className="h-64">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={[
                        { name: 'UTM Campaigns', value: safeExecutiveMetrics.utmSessions, fill: '#3B82F6' },
                        { name: 'Direct Traffic', value: safeExecutiveMetrics.directSessions, fill: '#10B981' },
                        { name: 'Unknown Traffic', value: safeExecutiveMetrics.unknownSessions, fill: '#8B5CF6' }
                      ]}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(1)}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      <Cell fill="#3B82F6" />
                      <Cell fill="#10B981" />
                      <Cell fill="#8B5CF6" />
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>

          {/* UTM Campaign Performance */}
          <UTMCampaignTracker period={selectedPeriod} />
        </TabsContent>

        {/* Conversion Flow Tab */}
        <TabsContent value="funnel" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Conversion Funnel</CardTitle>
              <CardDescription>Step-by-step customer journey and drop-off analysis</CardDescription>
            </CardHeader>
            <CardContent>
              {safeFunnelData.length > 0 ? (
                <div className="space-y-4">
                  {safeFunnelData.map((step, index) => (
                    <div key={index} className="relative">
                      <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                        <div className="flex items-center gap-4">
                          <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
                            <span className="font-bold text-purple-600">{index + 1}</span>
                          </div>
                          <div>
                            <h3 className="font-semibold">{step.name}</h3>
                            {step.previousStep && step.dropOffRate > 0 && (
                              <p className="text-sm text-red-600">
                                {formatPercentage(step.dropOffRate)} drop-off from {step.previousStep}
                              </p>
                            )}
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="text-2xl font-bold">{formatNumber(step.users)}</p>
                          <p className="text-sm text-gray-500">{formatPercentage(step.percentage)} of total</p>
                        </div>
                      </div>
                      
                      {/* Progress bar showing relative size */}
                      <div className="mt-2">
                        <Progress value={step.percentage} className="h-2" />
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <Target className="w-12 h-12 mx-auto mb-3 text-gray-300" />
                  <p>No funnel data available for the selected period</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Customer Journey Tab - DISABLED: Replaced with accurate conversion funnel */}
        <TabsContent value="journey" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Customer Journey Analysis</CardTitle>
              <CardDescription>This feature has been disabled due to data accuracy issues. Use the Conversion Funnel tab for accurate user journey insights.</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12 text-gray-500">
                <AlertTriangle className="w-16 h-16 mx-auto mb-4 text-orange-400" />
                <h3 className="text-lg font-medium mb-2">Feature Temporarily Disabled</h3>
                <p className="mb-4">The Customer Journey Analysis was generating inaccurate data that didn't reflect real user behavior.</p>
                <p className="text-sm">For accurate conversion insights, please use:</p>
                <div className="mt-4 space-y-2 text-sm">
                  <div className="p-2 bg-blue-50 rounded border-l-4 border-blue-400">
                    <strong>UTM Campaigns:</strong> Real campaign performance data
                  </div>
                  <div className="p-2 bg-green-50 rounded border-l-4 border-green-400">
                    <strong>Conversion Funnel:</strong> Accurate step-by-step user flow
                  </div>
                  <div className="p-2 bg-purple-50 rounded border-l-4 border-purple-400">
                    <strong>Individual Bookings:</strong> Real customer journey timelines
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Detailed Metric Modal */}
      <Dialog open={!!selectedMetric} onOpenChange={() => setSelectedMetric(null)}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <BarChart3 className="w-5 h-5" />
              {selectedMetric?.title}
            </DialogTitle>
            <DialogDescription>
              {selectedMetric?.description}
            </DialogDescription>
          </DialogHeader>
          
          {selectedMetric && (
            <div className="space-y-6">
              {/* Metric Overview */}
              <div className="grid grid-cols-2 gap-4">
                <div className="p-4 bg-purple-50 rounded-lg">
                  <p className="text-sm text-purple-600 font-medium">Total Count</p>
                  <p className="text-2xl font-bold text-purple-900">
                    {selectedMetric.data.total?.toLocaleString() || 0}
                  </p>
                </div>
                <div className="p-4 bg-blue-50 rounded-lg">
                  <p className="text-sm text-blue-600 font-medium">Growth Trend</p>
                  <p className={`text-2xl font-bold ${selectedMetric.data.trend >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                    {selectedMetric.data.trend >= 0 ? '+' : ''}{selectedMetric.data.trend?.toFixed(1)}%
                  </p>
                </div>
              </div>

              {/* Traffic Source Breakdown */}
              {selectedMetric.type === 'visitors' || selectedMetric.type === 'sessions' ? (
                <div className="space-y-4">
                  <h4 className="font-semibold text-gray-900">Traffic Source Breakdown</h4>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
                        <span className="font-medium">UTM Traffic</span>
                      </div>
                      <div className="text-right">
                        <p className="font-bold">{selectedMetric.data.utm?.toLocaleString() || 0}</p>
                        <p className="text-sm text-gray-600">{selectedMetric.data.utmRate?.toFixed(1) || 0}%</p>
                      </div>
                    </div>
                    <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                        <span className="font-medium">Direct Traffic</span>
                      </div>
                      <div className="text-right">
                        <p className="font-bold">{selectedMetric.data.direct?.toLocaleString() || 0}</p>
                        <p className="text-sm text-gray-600">{selectedMetric.data.directRate?.toFixed(1) || 0}%</p>
                      </div>
                    </div>
                    <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className="w-3 h-3 bg-amber-500 rounded-full"></div>
                        <span className="font-medium">Other Sources</span>
                        <span className="text-xs text-gray-500">(referral/organic)</span>
                      </div>
                      <div className="text-right">
                        <p className="font-bold">{(selectedMetric.data.total - selectedMetric.data.utm - selectedMetric.data.direct) || 0}</p>
                        <p className="text-sm text-gray-600">{(100 - (selectedMetric.data.utmRate || 0) - (selectedMetric.data.directRate || 0)).toFixed(1)}%</p>
                      </div>
                    </div>
                  </div>
                </div>
              ) : null}

              {/* Booking Status Breakdown */}
              {selectedMetric.type === 'bookings' ? (
                <div className="space-y-4">
                  <h4 className="font-semibold text-gray-900">Booking Status Distribution</h4>
                  <div className="grid grid-cols-3 gap-3">
                    <div className="p-3 bg-gray-50 rounded-lg text-center">
                      <p className="text-2xl font-bold text-gray-900">{selectedMetric.data.total}</p>
                      <p className="text-sm text-gray-600">Total</p>
                    </div>
                    <div className="p-3 bg-green-50 rounded-lg text-center">
                      <p className="text-2xl font-bold text-green-600">{selectedMetric.data.confirmed}</p>
                      <p className="text-sm text-green-600">Confirmed</p>
                    </div>
                    <div className="p-3 bg-blue-50 rounded-lg text-center">
                      <p className="text-2xl font-bold text-blue-600">{selectedMetric.data.completed}</p>
                      <p className="text-sm text-blue-600">Completed</p>
                    </div>
                  </div>
                  <div className="p-4 bg-purple-50 rounded-lg">
                    <p className="text-sm text-purple-600 font-medium">Conversion Rate</p>
                    <p className="text-2xl font-bold text-purple-900">{selectedMetric.data.conversionRate?.toFixed(2)}%</p>
                    <p className="text-xs text-purple-600 mt-1">Sessions to bookings</p>
                  </div>
                </div>
              ) : null}

              <div className="flex justify-end">
                <Button onClick={() => setSelectedMetric(null)} variant="outline">
                  Close Details
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}