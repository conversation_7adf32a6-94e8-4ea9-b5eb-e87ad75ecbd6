import { useState } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { toast } from "@/hooks/use-toast";
import { Plus, Edit2, Trash2, Setting<PERSON>, Key, Co<PERSON>, RefreshCw } from "lucide-react";
import { apiRequest, queryClient } from "@/lib/queryClient";

interface User {
  id: number;
  username: string;
  role: string;
  name: string;
  email?: string;
  isActive: boolean;
  dashboardPermissions?: string;
  createdAt: string;
}

interface DashboardPermission {
  dashboard: string;
  access: 'none' | 'view' | 'edit';
}

// Updated to match current dashboard navigation structure
const AVAILABLE_DASHBOARDS = [
  { id: 'overview', name: 'Overview', description: 'Dashboard overview and stats' },
  { id: 'bookings', name: 'Bookings', description: 'Manage customer appointments' },
  { id: 'executive-analytics', name: 'Performance Overview', description: 'Executive dashboard with KPIs and conversion metrics' },
  { id: 'user-behaviour', name: 'User Behaviour', description: 'Interactive user behaviour analytics and session insights' },
  { id: 'calendar', name: 'Calendar', description: 'Calendar view of appointments' },
  { id: 'reps', name: 'Sales Reps', description: 'Manage sales representatives' },
  { id: 'postal-codes', name: 'Service Areas', description: 'Manage postal codes and areas' },
  { id: 'users', name: 'Users', description: 'User management and creation' },
  { id: 'permissions', name: 'Permissions', description: 'Manage user access levels' },
  { id: 'sessions', name: 'Sessions', description: 'Admin login activity tracking' },
  { id: 'customization', name: 'Theme', description: 'Customize appearance and branding' },
  { id: 'settings', name: 'Settings', description: 'System configuration' },
  { id: 'password', name: 'Security', description: 'Change password and security' }
];

export default function UserPermissions() {
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [passwordUser, setPasswordUser] = useState<User | null>(null);
  const [newPassword, setNewPassword] = useState('');
  const [generatedPassword, setGeneratedPassword] = useState('');
  const [showCredentials, setShowCredentials] = useState(false);
  const [formData, setFormData] = useState({
    username: '',
    password: '',
    name: '',
    email: '',
    role: 'support'
  });
  const [permissions, setPermissions] = useState<DashboardPermission[]>(
    AVAILABLE_DASHBOARDS.map(d => ({ dashboard: d.id, access: 'none' as const }))
  );

  const { data: users, isLoading } = useQuery<User[]>({
    queryKey: ['/api/admin/users'],
  });

  const createUserMutation = useMutation({
    mutationFn: async (userData: any) => {
      const response = await apiRequest("POST", "/api/admin/users", {
        ...userData,
        dashboardPermissions: JSON.stringify(permissions)
      });
      return response.json();
    },
    onSuccess: () => {
      toast({ title: "User created successfully" });
      setIsCreateDialogOpen(false);
      resetForm();
      queryClient.invalidateQueries({ queryKey: ['/api/admin/users'] });
    },
    onError: () => {
      toast({ title: "Failed to create user", variant: "destructive" });
    }
  });

  const updateUserMutation = useMutation({
    mutationFn: async ({ id, ...userData }: any) => {
      const response = await apiRequest("PUT", `/api/admin/users/${id}`, {
        ...userData,
        dashboardPermissions: JSON.stringify(permissions)
      });
      return response.json();
    },
    onSuccess: () => {
      toast({ title: "User updated successfully" });
      setEditingUser(null);
      resetForm();
      queryClient.invalidateQueries({ queryKey: ['/api/admin/users'] });
    },
    onError: () => {
      toast({ title: "Failed to update user", variant: "destructive" });
    }
  });

  const deleteUserMutation = useMutation({
    mutationFn: async (id: number) => {
      const response = await apiRequest("DELETE", `/api/admin/users/${id}`);
      return response.json();
    },
    onSuccess: () => {
      toast({ title: "User deleted successfully" });
      queryClient.invalidateQueries({ queryKey: ['/api/admin/users'] });
    },
    onError: () => {
      toast({ title: "Failed to delete user", variant: "destructive" });
    }
  });

  const changePasswordMutation = useMutation({
    mutationFn: async ({ userId, password }: { userId: number; password: string }) => {
      const response = await apiRequest("POST", `/api/admin/users/${userId}/change-password`, { password });
      return response.json();
    },
    onSuccess: () => {
      toast({ title: "Password changed successfully" });
      setPasswordUser(null);
      setNewPassword('');
      setGeneratedPassword('');
    },
    onError: () => {
      toast({ title: "Failed to change password", variant: "destructive" });
    }
  });

  const generatePasswordMutation = useMutation({
    mutationFn: async () => {
      const response = await apiRequest("POST", "/api/admin/generate-password");
      return response.json();
    },
    onSuccess: (data) => {
      setGeneratedPassword(data.password);
      setNewPassword(data.password);
    },
    onError: () => {
      toast({ title: "Failed to generate password", variant: "destructive" });
    }
  });

  const resetForm = () => {
    setFormData({
      username: '',
      password: '',
      name: '',
      email: '',
      role: 'support'
    });
    setPermissions(AVAILABLE_DASHBOARDS.map(d => ({ dashboard: d.id, access: 'none' as const })));
  };

  const handleEdit = (user: User) => {
    setEditingUser(user);
    setFormData({
      username: user.username,
      password: '',
      name: user.name,
      email: user.email || '',
      role: user.role
    });
    
    // Parse existing permissions
    try {
      const existingPermissions = user.dashboardPermissions 
        ? JSON.parse(user.dashboardPermissions) as DashboardPermission[]
        : [];
      
      setPermissions(AVAILABLE_DASHBOARDS.map(d => {
        const existing = existingPermissions.find(p => p.dashboard === d.id);
        return { dashboard: d.id, access: existing?.access || 'none' };
      }));
    } catch {
      setPermissions(AVAILABLE_DASHBOARDS.map(d => ({ dashboard: d.id, access: 'none' as const })));
    }
  };

  const handlePermissionChange = (dashboardId: string, access: 'none' | 'view' | 'edit') => {
    setPermissions(prev => prev.map(p => 
      p.dashboard === dashboardId ? { ...p, access } : p
    ));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (editingUser) {
      updateUserMutation.mutate({ id: editingUser.id, ...formData });
    } else {
      createUserMutation.mutate(formData);
    }
  };

  const getUserPermissions = (user: User): DashboardPermission[] => {
    try {
      return user.dashboardPermissions 
        ? JSON.parse(user.dashboardPermissions) as DashboardPermission[]
        : [];
    } catch {
      return [];
    }
  };

  const getPermissionSummary = (user: User): string => {
    const perms = getUserPermissions(user);
    const viewCount = perms.filter(p => p.access === 'view').length;
    const editCount = perms.filter(p => p.access === 'edit').length;
    
    if (editCount > 0) {
      return `${editCount} edit, ${viewCount} view`;
    } else if (viewCount > 0) {
      return `${viewCount} view only`;
    }
    return 'No access';
  };

  if (isLoading) {
    return <div className="text-center p-8">Loading users...</div>;
  }

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold">User Management</h1>
          <p className="text-gray-600">Manage user accounts and dashboard permissions</p>
        </div>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={resetForm}>
              <Plus className="h-4 w-4 mr-2" />
              Add User
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Create New User</DialogTitle>
            </DialogHeader>
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="username">Username</Label>
                  <Input
                    id="username"
                    value={formData.username}
                    onChange={(e) => setFormData(prev => ({ ...prev, username: e.target.value }))}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="password">Password</Label>
                  <Input
                    id="password"
                    type="password"
                    value={formData.password}
                    onChange={(e) => setFormData(prev => ({ ...prev, password: e.target.value }))}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="name">Full Name</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    value={formData.email}
                    onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                  />
                </div>
                <div>
                  <Label htmlFor="role">Role</Label>
                  <Select value={formData.role} onValueChange={(value) => setFormData(prev => ({ ...prev, role: value }))}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="admin">Admin</SelectItem>
                      <SelectItem value="support">Support</SelectItem>
                      <SelectItem value="sales">Sales</SelectItem>
                      <SelectItem value="marketing">Marketing</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div>
                <Label className="text-base font-semibold">Dashboard Permissions</Label>
                <p className="text-sm text-gray-600 mb-4">Configure access levels for each dashboard section</p>
                
                <div className="grid gap-3">
                  {AVAILABLE_DASHBOARDS.map(dashboard => (
                    <Card key={dashboard.id} className="p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-medium">{dashboard.name}</h4>
                          <p className="text-sm text-gray-600">{dashboard.description}</p>
                        </div>
                        <div className="flex gap-4">
                          {(['none', 'view', 'edit'] as const).map(access => (
                            <label key={access} className="flex items-center gap-2 cursor-pointer">
                              <input
                                type="radio"
                                name={`${dashboard.id}-access`}
                                value={access}
                                checked={permissions.find(p => p.dashboard === dashboard.id)?.access === access}
                                onChange={() => handlePermissionChange(dashboard.id, access)}
                                className="text-purple-600"
                              />
                              <span className="text-sm capitalize">{access}</span>
                            </label>
                          ))}
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              </div>

              <div className="flex gap-2">
                <Button type="submit" disabled={createUserMutation.isPending}>
                  Create User
                </Button>
                <Button type="button" variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                  Cancel
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Users</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Username</TableHead>
                <TableHead>Role</TableHead>
                <TableHead>Permissions</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {users?.map(user => (
                <TableRow key={user.id}>
                  <TableCell>
                    <div>
                      <div className="font-medium">{user.name}</div>
                      {user.email && <div className="text-sm text-gray-500">{user.email}</div>}
                    </div>
                  </TableCell>
                  <TableCell>{user.username}</TableCell>
                  <TableCell>
                    <Badge variant={user.role === 'admin' ? 'default' : 'secondary'}>
                      {user.role}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <span className="text-sm text-gray-600">
                      {getPermissionSummary(user)}
                    </span>
                  </TableCell>
                  <TableCell>
                    <Badge variant={user.isActive ? 'default' : 'destructive'}>
                      {user.isActive ? 'Active' : 'Inactive'}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex gap-2">
                      <Dialog open={editingUser?.id === user.id} onOpenChange={(open) => !open && setEditingUser(null)}>
                        <DialogTrigger asChild>
                          <Button 
                            variant="ghost" 
                            size="sm"
                            onClick={() => handleEdit(user)}
                          >
                            <Edit2 className="h-4 w-4" />
                          </Button>
                        </DialogTrigger>
                        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
                          <DialogHeader>
                            <DialogTitle>Edit User: {user.name}</DialogTitle>
                          </DialogHeader>
                          <form onSubmit={handleSubmit} className="space-y-6">
                            <div className="grid grid-cols-2 gap-4">
                              <div>
                                <Label htmlFor="edit-username">Username</Label>
                                <Input
                                  id="edit-username"
                                  value={formData.username}
                                  onChange={(e) => setFormData(prev => ({ ...prev, username: e.target.value }))}
                                  required
                                />
                              </div>
                              <div>
                                <Label htmlFor="edit-password">Password (leave blank to keep current)</Label>
                                <Input
                                  id="edit-password"
                                  type="password"
                                  value={formData.password}
                                  onChange={(e) => setFormData(prev => ({ ...prev, password: e.target.value }))}
                                  placeholder="Leave blank to keep current password"
                                />
                              </div>
                              <div>
                                <Label htmlFor="edit-name">Full Name</Label>
                                <Input
                                  id="edit-name"
                                  value={formData.name}
                                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                                  required
                                />
                              </div>
                              <div>
                                <Label htmlFor="edit-email">Email</Label>
                                <Input
                                  id="edit-email"
                                  type="email"
                                  value={formData.email}
                                  onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                                />
                              </div>
                              <div>
                                <Label htmlFor="edit-role">Role</Label>
                                <Select value={formData.role} onValueChange={(value) => setFormData(prev => ({ ...prev, role: value }))}>
                                  <SelectTrigger>
                                    <SelectValue />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value="admin">Admin</SelectItem>
                                    <SelectItem value="support">Support</SelectItem>
                                    <SelectItem value="sales">Sales</SelectItem>
                                    <SelectItem value="marketing">Marketing</SelectItem>
                                  </SelectContent>
                                </Select>
                              </div>
                            </div>

                            <div>
                              <Label className="text-base font-semibold">Dashboard Permissions</Label>
                              <p className="text-sm text-gray-600 mb-4">Configure access levels for each dashboard section</p>
                              
                              <div className="grid gap-3">
                                {AVAILABLE_DASHBOARDS.map(dashboard => (
                                  <Card key={dashboard.id} className="p-4">
                                    <div className="flex items-center justify-between">
                                      <div>
                                        <h4 className="font-medium">{dashboard.name}</h4>
                                        <p className="text-sm text-gray-600">{dashboard.description}</p>
                                      </div>
                                      <div className="flex gap-4">
                                        {(['none', 'view', 'edit'] as const).map(access => (
                                          <label key={access} className="flex items-center gap-2 cursor-pointer">
                                            <input
                                              type="radio"
                                              name={`edit-${dashboard.id}-access`}
                                              value={access}
                                              checked={permissions.find(p => p.dashboard === dashboard.id)?.access === access}
                                              onChange={() => handlePermissionChange(dashboard.id, access)}
                                              className="text-purple-600"
                                            />
                                            <span className="text-sm capitalize">{access}</span>
                                          </label>
                                        ))}
                                      </div>
                                    </div>
                                  </Card>
                                ))}
                              </div>
                            </div>

                            <div className="flex gap-2">
                              <Button type="submit" disabled={updateUserMutation.isPending}>
                                Update User
                              </Button>
                              <Button type="button" variant="outline" onClick={() => setEditingUser(null)}>
                                Cancel
                              </Button>
                            </div>
                          </form>
                        </DialogContent>
                      </Dialog>
                      
                      <Dialog open={passwordUser?.id === user.id} onOpenChange={(open) => !open && setPasswordUser(null)}>
                        <DialogTrigger asChild>
                          <Button 
                            variant="ghost" 
                            size="sm"
                            onClick={() => setPasswordUser(user)}
                          >
                            <Key className="h-4 w-4" />
                          </Button>
                        </DialogTrigger>
                        <DialogContent>
                          <DialogHeader>
                            <DialogTitle>Change Password for {user.name}</DialogTitle>
                          </DialogHeader>
                          <div className="space-y-4">
                            <div>
                              <Label htmlFor="newPassword">New Password</Label>
                              <div className="flex gap-2 mt-1">
                                <Input
                                  id="newPassword"
                                  type="text"
                                  value={newPassword}
                                  onChange={(e) => setNewPassword(e.target.value)}
                                  placeholder="Enter new password"
                                />
                                <Button 
                                  type="button" 
                                  variant="outline" 
                                  onClick={() => generatePasswordMutation.mutate()}
                                  disabled={generatePasswordMutation.isPending}
                                >
                                  <RefreshCw className="h-4 w-4" />
                                </Button>
                              </div>
                            </div>
                            
                            {generatedPassword && (
                              <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
                                <div className="flex items-center justify-between">
                                  <div>
                                    <p className="text-sm font-medium text-green-800">Generated Password</p>
                                    <p className="text-sm text-green-700 font-mono">{generatedPassword}</p>
                                  </div>
                                  <Button
                                    type="button"
                                    variant="outline"
                                    size="sm"
                                    onClick={() => {
                                      navigator.clipboard.writeText(generatedPassword);
                                      toast({ title: "Password copied to clipboard" });
                                    }}
                                  >
                                    <Copy className="h-4 w-4" />
                                  </Button>
                                </div>
                              </div>
                            )}
                            
                            <div className="flex gap-2">
                              <Button 
                                onClick={() => {
                                  if (newPassword.trim()) {
                                    changePasswordMutation.mutate({ 
                                      userId: user.id, 
                                      password: newPassword 
                                    });
                                  }
                                }}
                                disabled={!newPassword.trim() || changePasswordMutation.isPending}
                              >
                                Change Password
                              </Button>
                              <Button 
                                variant="outline" 
                                onClick={() => {
                                  setPasswordUser(null);
                                  setNewPassword('');
                                  setGeneratedPassword('');
                                }}
                              >
                                Cancel
                              </Button>
                            </div>
                          </div>
                        </DialogContent>
                      </Dialog>
                      
                      <Button 
                        variant="ghost" 
                        size="sm"
                        onClick={() => {
                          if (confirm(`Delete user ${user.name}?`)) {
                            deleteUserMutation.mutate(user.id);
                          }
                        }}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}