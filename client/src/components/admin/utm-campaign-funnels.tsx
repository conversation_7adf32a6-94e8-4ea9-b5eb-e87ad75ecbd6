import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer,
  FunnelChart, Funnel, Cell, LabelList,
  LineChart, Line, Area, AreaChart
} from 'recharts';
import { 
  TrendingUp, 
  TrendingDown, 
  Users, 
  Target, 
  Clock, 
  Eye,
  Filter,
  Download,
  ExternalLink,
  Activity
} from 'lucide-react';

interface CampaignFunnel {
  campaign: string;
  source: string;
  medium: string;
  isMetaAd: boolean;
  isGoogleAd: boolean;
  totalSessions: number;
  conversions: number;
  conversionRate: number;
  funnelSteps: {
    landing: number;
    postal_code_entry: number;
    date_selection: number;
    time_selection: number;
    address_submission: number;
    booking_confirmation: number;
  };
  dropOffRates: {
    landing_to_postal: number;
    postal_to_date: number;
    date_to_time: number;
    time_to_address: number;
    address_to_booking: number;
  };
  firstSeen: string;
  lastSeen: string;
  campaignType: string;
}

interface FunnelStage {
  stage: string;
  sessions: number;
  percentage: number;
  dropOff: number;
}

interface IndividualFunnelData {
  campaign: string;
  totalSessions: number;
  completedJourneys: number;
  conversionRate: number;
  funnelStages: FunnelStage[];
  journeyInsights: {
    avgSessionDuration: string;
    topDropOffStage: { stage?: string; dropOff: number };
    completionRate: string;
  };
}

export default function UTMCampaignFunnels() {
  const [dateRange, setDateRange] = useState(30); // days
  const [selectedCampaign, setSelectedCampaign] = useState<string | null>(null);
  const [debugMode, setDebugMode] = useState(false);
  const [filters, setFilters] = useState({
    source: '',
    medium: '',
    campaignType: ''
  });

  const startDate = new Date(Date.now() - dateRange * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
  const endDate = new Date().toISOString().split('T')[0];

  // Fetch all campaign funnels with debug support
  const { data: campaignFunnelsResponse, isLoading: funnelsLoading } = useQuery({
    queryKey: ['/api/admin/analytics/campaign-funnels', startDate, endDate, filters.source, filters.medium, debugMode],
    queryFn: async () => {
      const params = new URLSearchParams({
        startDate,
        endDate,
        ...(filters.source && { source: filters.source }),
        ...(filters.medium && { medium: filters.medium }),
        ...(debugMode && { debug: 'true' })
      });
      const response = await fetch(`/api/admin/analytics/campaign-funnels?${params}`, { credentials: 'include' });
      if (!response.ok) throw new Error('Failed to fetch campaign funnels');
      return response.json();
    },
    refetchInterval: 60000, // Refresh every minute
  });

  // Extract the actual data (handle both debug and non-debug responses)
  const campaignFunnels = debugMode && campaignFunnelsResponse?.data ? campaignFunnelsResponse.data : campaignFunnelsResponse;

  // Fetch individual campaign details
  const { data: individualFunnel, isLoading: individualLoading } = useQuery({
    queryKey: ['/api/admin/analytics/campaign-funnel', selectedCampaign, startDate, endDate],
    enabled: !!selectedCampaign,
  });

  const getPerformanceColor = (conversionRate: number) => {
    if (conversionRate >= 5) return 'text-green-600 bg-green-100';
    if (conversionRate >= 2) return 'text-yellow-600 bg-yellow-100';
    return 'text-red-600 bg-red-100';
  };

  const getCampaignTypeIcon = (type: string) => {
    switch (type) {
      case 'Meta Ad': return '📘';
      case 'Google Ad': return '🔍';
      default: return '📊';
    }
  };

  const exportFunnelData = (funnel: CampaignFunnel) => {
    const csvData = [
      ['Campaign', 'Source', 'Medium', 'Total Sessions', 'Conversions', 'Conversion Rate'],
      [funnel.campaign, funnel.source, funnel.medium, funnel.totalSessions, funnel.conversions, `${funnel.conversionRate}%`],
      [],
      ['Funnel Stage', 'Sessions', 'Drop-off Rate'],
      ['Landing', funnel.funnelSteps.landing, '0%'],
      ['Postal Code Entry', funnel.funnelSteps.postal_code_entry, `${funnel.dropOffRates.landing_to_postal.toFixed(1)}%`],
      ['Date Selection', funnel.funnelSteps.date_selection, `${funnel.dropOffRates.postal_to_date.toFixed(1)}%`],
      ['Time Selection', funnel.funnelSteps.time_selection, `${funnel.dropOffRates.date_to_time.toFixed(1)}%`],
      ['Address Submission', funnel.funnelSteps.address_submission, `${funnel.dropOffRates.time_to_address.toFixed(1)}%`],
      ['Booking Confirmation', funnel.funnelSteps.booking_confirmation, `${funnel.dropOffRates.address_to_booking.toFixed(1)}%`]
    ];

    const csvContent = csvData.map(row => row.join(',')).join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `campaign-funnel-${funnel.campaign}-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  const filteredFunnels = (campaignFunnels as CampaignFunnel[] || []).filter(funnel => {
    if (filters.campaignType && funnel.campaignType !== filters.campaignType) return false;
    return true;
  });

  const totalSessions = filteredFunnels.reduce((sum, funnel) => sum + funnel.totalSessions, 0);
  const totalConversions = filteredFunnels.reduce((sum, funnel) => sum + funnel.conversions, 0);
  const avgConversionRate = totalSessions > 0 ? (totalConversions / totalSessions * 100) : 0;

  if (funnelsLoading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/3"></div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-32 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">UTM Campaign Funnels</h2>
          <p className="text-gray-600">Real-time campaign performance tracking across the full booking flow</p>
        </div>
        
        <div className="flex items-center gap-3">
          <Button
            variant={debugMode ? "default" : "outline"}
            size="sm"
            onClick={() => setDebugMode(!debugMode)}
            className="flex items-center gap-2"
          >
            <Activity className="h-4 w-4" />
            {debugMode ? "Debug ON" : "Debug"}
          </Button>
          
          <Select value={dateRange.toString()} onValueChange={(value) => setDateRange(Number(value))}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7">Last 7 days</SelectItem>
              <SelectItem value="30">Last 30 days</SelectItem>
              <SelectItem value="90">Last 90 days</SelectItem>
            </SelectContent>
          </Select>
          
          <Dialog>
            <DialogTrigger asChild>
              <Button variant="outline" size="sm">
                <Filter className="h-4 w-4 mr-2" />
                Filters
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Filter Campaigns</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label>Campaign Type</Label>
                  <Select value={filters.campaignType} onValueChange={(value) => setFilters(prev => ({ ...prev, campaignType: value }))}>
                    <SelectTrigger>
                      <SelectValue placeholder="All types" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">All Types</SelectItem>
                      <SelectItem value="Meta Ad">Meta Ads</SelectItem>
                      <SelectItem value="Google Ad">Google Ads</SelectItem>
                      <SelectItem value="Other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div>
                  <Label>Source</Label>
                  <Input
                    value={filters.source}
                    onChange={(e) => setFilters(prev => ({ ...prev, source: e.target.value }))}
                    placeholder="Filter by source"
                  />
                </div>
                
                <div>
                  <Label>Medium</Label>
                  <Input
                    value={filters.medium}
                    onChange={(e) => setFilters(prev => ({ ...prev, medium: e.target.value }))}
                    placeholder="Filter by medium"
                  />
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Active Campaigns</p>
                <p className="text-2xl font-bold">{filteredFunnels.length}</p>
              </div>
              <Activity className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Total Sessions</p>
                <p className="text-2xl font-bold">{totalSessions.toLocaleString()}</p>
              </div>
              <Users className="h-8 w-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Total Conversions</p>
                <p className="text-2xl font-bold">{totalConversions}</p>
              </div>
              <Target className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Avg. Conversion Rate</p>
                <p className="text-2xl font-bold">{avgConversionRate.toFixed(1)}%</p>
              </div>
              <TrendingUp className="h-8 w-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Debug Information */}
      {debugMode && campaignFunnelsResponse?.debug && (
        <Card className="border-blue-200 bg-blue-50">
          <CardHeader>
            <CardTitle className="text-lg text-blue-800 flex items-center gap-2">
              <Activity className="h-5 w-5" />
              Debug Information
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
              <div>
                <p className="font-medium text-blue-700">Date Range</p>
                <p className="text-blue-600">
                  {new Date(campaignFunnelsResponse.debug.dateRange.from).toLocaleDateString()} - 
                  {new Date(campaignFunnelsResponse.debug.dateRange.to).toLocaleDateString()}
                </p>
              </div>
              <div>
                <p className="font-medium text-blue-700">Events Processed</p>
                <p className="text-blue-600">{campaignFunnelsResponse.debug.totalEventsProcessed.toLocaleString()}</p>
              </div>
              <div>
                <p className="font-medium text-blue-700">Sessions Analyzed</p>
                <p className="text-blue-600">{campaignFunnelsResponse.debug.totalSessionsProcessed.toLocaleString()}</p>
              </div>
              <div>
                <p className="font-medium text-blue-700">Campaigns Found</p>
                <p className="text-blue-600">
                  {campaignFunnelsResponse.debug.campaignsWithTraffic} of {campaignFunnelsResponse.debug.campaignsFoundInData} with traffic
                </p>
              </div>
              <div className="col-span-2 lg:col-span-4">
                <p className="font-medium text-blue-700">Last Updated</p>
                <p className="text-blue-600">{new Date(campaignFunnelsResponse.debug.lastUpdated).toLocaleString()}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Campaign Funnels Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {filteredFunnels.map((funnel, index) => (
          <Card key={index} className="hover:shadow-lg transition-shadow">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-lg flex items-center gap-2">
                    <span>{getCampaignTypeIcon(funnel.campaignType)}</span>
                    {funnel.campaign}
                  </CardTitle>
                  <CardDescription>
                    {funnel.source} / {funnel.medium}
                  </CardDescription>
                </div>
                <div className="flex items-center gap-2">
                  <Badge className={getPerformanceColor(funnel.conversionRate)}>
                    {funnel.conversionRate}% conv.
                  </Badge>
                  <Button 
                    variant="ghost" 
                    size="sm"
                    onClick={() => exportFunnelData(funnel)}
                  >
                    <Download className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            
            <CardContent className="space-y-4">
              {/* Key Metrics */}
              <div className="grid grid-cols-3 gap-3 text-sm">
                <div className="text-center">
                  <p className="text-gray-500">Sessions</p>
                  <p className="font-semibold">{funnel.totalSessions.toLocaleString()}</p>
                </div>
                <div className="text-center">
                  <p className="text-gray-500">Conversions</p>
                  <p className="font-semibold">{funnel.conversions}</p>
                </div>
                <div className="text-center">
                  <p className="text-gray-500">Active Since</p>
                  <p className="font-semibold">
                    {new Date(funnel.firstSeen).toLocaleDateString()}
                  </p>
                </div>
              </div>

              {/* Mini Funnel Chart */}
              <div className="h-40">
                <ResponsiveContainer width="100%" height="100%">
                  <FunnelChart>
                    <Funnel
                      dataKey="value"
                      data={[
                        { name: 'Landing', value: funnel.funnelSteps.landing, fill: '#8884d8' },
                        { name: 'Postal', value: funnel.funnelSteps.postal_code_entry, fill: '#82ca9d' },
                        { name: 'Date', value: funnel.funnelSteps.date_selection, fill: '#ffc658' },
                        { name: 'Time', value: funnel.funnelSteps.time_selection, fill: '#ff7300' },
                        { name: 'Address', value: funnel.funnelSteps.address_submission, fill: '#8dd1e1' },
                        { name: 'Booking', value: funnel.funnelSteps.booking_confirmation, fill: '#d084d0' }
                      ]}
                      isAnimationActive
                    >
                      <LabelList position="center" fill="#000" stroke="none" />
                    </Funnel>
                    <Tooltip />
                  </FunnelChart>
                </ResponsiveContainer>
              </div>

              {/* Drop-off Analysis */}
              <div className="space-y-2">
                <h4 className="font-medium text-sm">Biggest Drop-offs:</h4>
                {Object.entries(funnel.dropOffRates)
                  .sort(([,a], [,b]) => b - a)
                  .slice(0, 2)
                  .map(([stage, rate]) => (
                    <div key={stage} className="flex items-center justify-between text-sm">
                      <span className="text-gray-600 capitalize">
                        {stage.replace(/_/g, ' → ')}
                      </span>
                      <span className={`font-medium ${rate > 50 ? 'text-red-600' : rate > 30 ? 'text-yellow-600' : 'text-green-600'}`}>
                        {rate.toFixed(1)}%
                      </span>
                    </div>
                  ))}
              </div>

              {/* Action Buttons */}
              <div className="flex gap-2 pt-2">
                <Dialog>
                  <DialogTrigger asChild>
                    <Button 
                      variant="outline" 
                      size="sm" 
                      className="flex-1"
                      onClick={() => setSelectedCampaign(funnel.campaign)}
                    >
                      <Eye className="h-4 w-4 mr-2" />
                      View Details
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="max-w-4xl">
                    <DialogHeader>
                      <DialogTitle>Campaign Funnel: {selectedCampaign}</DialogTitle>
                    </DialogHeader>
                    
                    {individualLoading ? (
                      <div className="animate-pulse space-y-4">
                        <div className="h-8 bg-gray-200 rounded"></div>
                        <div className="h-64 bg-gray-200 rounded"></div>
                      </div>
                    ) : individualFunnel ? (
                      <div className="space-y-6">
                        {/* Summary Stats */}
                        <div className="grid grid-cols-4 gap-4">
                          <div className="text-center">
                            <p className="text-2xl font-bold">{individualFunnel.totalSessions}</p>
                            <p className="text-sm text-gray-600">Total Sessions</p>
                          </div>
                          <div className="text-center">
                            <p className="text-2xl font-bold">{individualFunnel.completedJourneys}</p>
                            <p className="text-sm text-gray-600">Completed</p>
                          </div>
                          <div className="text-center">
                            <p className="text-2xl font-bold">{individualFunnel.conversionRate}%</p>
                            <p className="text-sm text-gray-600">Conversion Rate</p>
                          </div>
                          <div className="text-center">
                            <p className="text-2xl font-bold">{individualFunnel.journeyInsights.avgSessionDuration}</p>
                            <p className="text-sm text-gray-600">Avg. Duration</p>
                          </div>
                        </div>

                        {/* Detailed Funnel */}
                        <div className="h-80">
                          <ResponsiveContainer width="100%" height="100%">
                            <BarChart data={individualFunnel.funnelStages}>
                              <CartesianGrid strokeDasharray="3 3" />
                              <XAxis dataKey="stage" tick={{ fontSize: 12 }} />
                              <YAxis />
                              <Tooltip />
                              <Bar dataKey="sessions" fill="#8884d8" />
                            </BarChart>
                          </ResponsiveContainer>
                        </div>

                        {/* Insights */}
                        <div className="bg-gray-50 p-4 rounded-lg">
                          <h4 className="font-medium mb-2">Journey Insights</h4>
                          <div className="space-y-2 text-sm">
                            <p><strong>Top Drop-off Stage:</strong> {individualFunnel.journeyInsights.topDropOffStage.stage || 'None identified'}</p>
                            <p><strong>Completion Rate:</strong> {individualFunnel.journeyInsights.completionRate}</p>
                          </div>
                        </div>
                      </div>
                    ) : (
                      <div className="text-center py-8">
                        <p className="text-gray-600">No detailed funnel data available</p>
                      </div>
                    )}
                  </DialogContent>
                </Dialog>
                
                <Button 
                  variant="ghost" 
                  size="sm"
                  className="px-3"
                  onClick={() => window.open(`https://business.facebook.com/`, '_blank')}
                >
                  <ExternalLink className="h-4 w-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Empty State */}
      {filteredFunnels.length === 0 && (
        <Card>
          <CardContent className="py-12 text-center">
            <Activity className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Campaign Funnels Found</h3>
            <p className="text-gray-600 mb-4">
              Campaign funnels will appear here automatically when UTM campaigns are detected.
            </p>
            <p className="text-sm text-gray-500">
              Launch Meta ads or Google campaigns with UTM parameters to start tracking conversion funnels.
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}