import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { useToast } from "@/hooks/use-toast";
import { Badge } from "@/components/ui/badge";
import { RefreshCw, Eye, Save, RotateCcw } from "lucide-react";

interface ThemeConfig {
  backgroundGradient: string;
  primaryColor: string;
  secondaryColor: string;
  buttonColor: string;
  buttonHoverColor: string;
  textColor: string;
  headingColor: string;
  accentColor: string;
}

const defaultTheme: ThemeConfig = {
  backgroundGradient: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
  primaryColor: "#3b82f6",
  secondaryColor: "#64748b",
  buttonColor: "#3b82f6",
  buttonHoverColor: "#2563eb",
  textColor: "#1f2937",
  headingColor: "#111827",
  accentColor: "#f59e0b"
};

const gradientPresets = [
  { name: "Blue Purple", value: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)" },
  { name: "Ocean", value: "linear-gradient(135deg, #00c6ff 0%, #0072ff 100%)" },
  { name: "Sunset", value: "linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%)" },
  { name: "Forest", value: "linear-gradient(135deg, #134e5e 0%, #71b280 100%)" },
  { name: "Rose Gold", value: "linear-gradient(135deg, #f093fb 0%, #f5576c 100%)" },
  { name: "Purple Rain", value: "linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)" },
  { name: "Warm Flame", value: "linear-gradient(135deg, #ff9a9e 0%, #fad0c4 100%)" },
  { name: "Night Fade", value: "linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)" },
];

const colorPresets = [
  { name: "Kult Blue", primary: "#3b82f6", secondary: "#64748b" },
  { name: "Emerald", primary: "#10b981", secondary: "#6b7280" },
  { name: "Purple", primary: "#8b5cf6", secondary: "#6b7280" },
  { name: "Rose", primary: "#f43f5e", secondary: "#6b7280" },
  { name: "Orange", primary: "#f97316", secondary: "#6b7280" },
  { name: "Teal", primary: "#14b8a6", secondary: "#6b7280" },
];

export default function FrontendCustomization() {
  const [theme, setTheme] = useState<ThemeConfig>(defaultTheme);
  const [isPreviewMode, setIsPreviewMode] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    // Load saved theme from localStorage or API
    const savedTheme = localStorage.getItem('kult-theme-config');
    if (savedTheme) {
      try {
        setTheme(JSON.parse(savedTheme));
      } catch (error) {
        console.error('Failed to parse saved theme:', error);
      }
    }
  }, []);

  const applyThemeToDOM = (themeConfig: ThemeConfig) => {
    const root = document.documentElement;
    root.style.setProperty('--custom-bg-gradient', themeConfig.backgroundGradient);
    root.style.setProperty('--custom-primary', themeConfig.primaryColor);
    root.style.setProperty('--custom-secondary', themeConfig.secondaryColor);
    root.style.setProperty('--custom-button', themeConfig.buttonColor);
    root.style.setProperty('--custom-button-hover', themeConfig.buttonHoverColor);
    root.style.setProperty('--custom-text', themeConfig.textColor);
    root.style.setProperty('--custom-heading', themeConfig.headingColor);
    root.style.setProperty('--custom-accent', themeConfig.accentColor);
  };

  const handlePreview = () => {
    setIsPreviewMode(!isPreviewMode);
    if (!isPreviewMode) {
      applyThemeToDOM(theme);
      toast({
        title: "Preview Mode On",
        description: "Theme changes are now visible. Click Preview again to turn off.",
      });
    } else {
      // Reset to default or saved theme
      const savedTheme = localStorage.getItem('kult-theme-config');
      const resetTheme = savedTheme ? JSON.parse(savedTheme) : defaultTheme;
      applyThemeToDOM(resetTheme);
      toast({
        title: "Preview Mode Off",
        description: "Reverted to current saved theme.",
      });
    }
  };

  const handleSave = async () => {
    setIsSaving(true);
    try {
      // Save to localStorage for now - could be extended to save to database
      localStorage.setItem('kult-theme-config', JSON.stringify(theme));
      applyThemeToDOM(theme);
      setIsPreviewMode(false);
      
      toast({
        title: "Theme Saved",
        description: "Your customizations have been applied successfully.",
      });
    } catch (error) {
      toast({
        title: "Save Failed",
        description: "There was an error saving your theme changes.",
        variant: "destructive",
      });
    }
    setIsSaving(false);
  };

  const handleReset = () => {
    setTheme(defaultTheme);
    applyThemeToDOM(defaultTheme);
    setIsPreviewMode(false);
    toast({
      title: "Theme Reset",
      description: "Reverted to default theme settings.",
    });
  };

  const updateTheme = (key: keyof ThemeConfig, value: string) => {
    const newTheme = { ...theme, [key]: value };
    setTheme(newTheme);
    if (isPreviewMode) {
      applyThemeToDOM(newTheme);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Frontend Customization</h2>
          <p className="text-muted-foreground">
            Customize the look and feel of your booking application
          </p>
        </div>
        <div className="flex gap-2">
          <Button 
            variant={isPreviewMode ? "destructive" : "outline"} 
            onClick={handlePreview}
            className="gap-2"
          >
            <Eye className="h-4 w-4" />
            {isPreviewMode ? "Stop Preview" : "Preview"}
          </Button>
          <Button onClick={handleReset} variant="outline" className="gap-2">
            <RotateCcw className="h-4 w-4" />
            Reset
          </Button>
          <Button onClick={handleSave} disabled={isSaving} className="gap-2">
            {isSaving ? <RefreshCw className="h-4 w-4 animate-spin" /> : <Save className="h-4 w-4" />}
            Save Changes
          </Button>
        </div>
      </div>

      {isPreviewMode && (
        <div className="bg-blue-50 border border-blue-200 p-4 rounded-lg">
          <p className="text-blue-800 font-medium">🎨 Preview Mode Active</p>
          <p className="text-blue-600 text-sm">Changes are visible on the site. Click "Save Changes" to make them permanent.</p>
        </div>
      )}

      <Tabs defaultValue="background" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="background">Background</TabsTrigger>
          <TabsTrigger value="colors">Colors</TabsTrigger>
          <TabsTrigger value="buttons">Buttons</TabsTrigger>
          <TabsTrigger value="typography">Typography</TabsTrigger>
        </TabsList>

        <TabsContent value="background" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Background Gradient</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="bg-gradient">Custom Gradient CSS</Label>
                <Input
                  id="bg-gradient"
                  value={theme.backgroundGradient}
                  onChange={(e) => updateTheme('backgroundGradient', e.target.value)}
                  placeholder="linear-gradient(135deg, #667eea 0%, #764ba2 100%)"
                />
              </div>
              
              <div>
                <Label>Gradient Presets</Label>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-2 mt-2">
                  {gradientPresets.map((preset) => (
                    <button
                      key={preset.name}
                      onClick={() => updateTheme('backgroundGradient', preset.value)}
                      className="h-16 rounded-lg border-2 border-gray-200 hover:border-gray-400 transition-colors relative overflow-hidden"
                      style={{ background: preset.value }}
                    >
                      <div className="absolute inset-0 bg-black bg-opacity-20 flex items-center justify-center">
                        <span className="text-white text-xs font-medium text-center px-1">
                          {preset.name}
                        </span>
                      </div>
                    </button>
                  ))}
                </div>
              </div>

              <div className="p-4 rounded-lg border" style={{ background: theme.backgroundGradient }}>
                <p className="text-white font-medium">Preview</p>
                <p className="text-white/80 text-sm">This is how your background gradient will look</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="colors" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Color Scheme</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="primary-color">Primary Color</Label>
                  <div className="flex gap-2">
                    <Input
                      id="primary-color"
                      type="color"
                      value={theme.primaryColor}
                      onChange={(e) => updateTheme('primaryColor', e.target.value)}
                      className="w-16 h-10"
                    />
                    <Input
                      value={theme.primaryColor}
                      onChange={(e) => updateTheme('primaryColor', e.target.value)}
                      placeholder="#3b82f6"
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="secondary-color">Secondary Color</Label>
                  <div className="flex gap-2">
                    <Input
                      id="secondary-color"
                      type="color"
                      value={theme.secondaryColor}
                      onChange={(e) => updateTheme('secondaryColor', e.target.value)}
                      className="w-16 h-10"
                    />
                    <Input
                      value={theme.secondaryColor}
                      onChange={(e) => updateTheme('secondaryColor', e.target.value)}
                      placeholder="#64748b"
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="accent-color">Accent Color</Label>
                  <div className="flex gap-2">
                    <Input
                      id="accent-color"
                      type="color"
                      value={theme.accentColor}
                      onChange={(e) => updateTheme('accentColor', e.target.value)}
                      className="w-16 h-10"
                    />
                    <Input
                      value={theme.accentColor}
                      onChange={(e) => updateTheme('accentColor', e.target.value)}
                      placeholder="#f59e0b"
                    />
                  </div>
                </div>
              </div>

              <Separator />

              <div>
                <Label>Color Presets</Label>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-2 mt-2">
                  {colorPresets.map((preset) => (
                    <button
                      key={preset.name}
                      onClick={() => {
                        updateTheme('primaryColor', preset.primary);
                        updateTheme('secondaryColor', preset.secondary);
                      }}
                      className="p-3 rounded-lg border-2 border-gray-200 hover:border-gray-400 transition-colors"
                    >
                      <div className="flex gap-2 mb-2">
                        <div className="w-6 h-6 rounded" style={{ backgroundColor: preset.primary }}></div>
                        <div className="w-6 h-6 rounded" style={{ backgroundColor: preset.secondary }}></div>
                      </div>
                      <span className="text-sm font-medium">{preset.name}</span>
                    </button>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="buttons" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Button Styling</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="button-color">Button Color</Label>
                  <div className="flex gap-2">
                    <Input
                      id="button-color"
                      type="color"
                      value={theme.buttonColor}
                      onChange={(e) => updateTheme('buttonColor', e.target.value)}
                      className="w-16 h-10"
                    />
                    <Input
                      value={theme.buttonColor}
                      onChange={(e) => updateTheme('buttonColor', e.target.value)}
                      placeholder="#3b82f6"
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="button-hover-color">Button Hover Color</Label>
                  <div className="flex gap-2">
                    <Input
                      id="button-hover-color"
                      type="color"
                      value={theme.buttonHoverColor}
                      onChange={(e) => updateTheme('buttonHoverColor', e.target.value)}
                      className="w-16 h-10"
                    />
                    <Input
                      value={theme.buttonHoverColor}
                      onChange={(e) => updateTheme('buttonHoverColor', e.target.value)}
                      placeholder="#2563eb"
                    />
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <Label>Button Preview</Label>
                <div className="flex gap-2">
                  <button
                    className="px-6 py-2 rounded-lg text-white font-medium transition-colors"
                    style={{ 
                      backgroundColor: theme.buttonColor,
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.backgroundColor = theme.buttonHoverColor;
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.backgroundColor = theme.buttonColor;
                    }}
                  >
                    Primary Button
                  </button>
                  <button
                    className="px-6 py-2 rounded-lg border-2 font-medium transition-colors"
                    style={{ 
                      borderColor: theme.buttonColor,
                      color: theme.buttonColor,
                    }}
                  >
                    Secondary Button
                  </button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="typography" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Typography</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="text-color">Body Text Color</Label>
                  <div className="flex gap-2">
                    <Input
                      id="text-color"
                      type="color"
                      value={theme.textColor}
                      onChange={(e) => updateTheme('textColor', e.target.value)}
                      className="w-16 h-10"
                    />
                    <Input
                      value={theme.textColor}
                      onChange={(e) => updateTheme('textColor', e.target.value)}
                      placeholder="#1f2937"
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="heading-color">Heading Color</Label>
                  <div className="flex gap-2">
                    <Input
                      id="heading-color"
                      type="color"
                      value={theme.headingColor}
                      onChange={(e) => updateTheme('headingColor', e.target.value)}
                      className="w-16 h-10"
                    />
                    <Input
                      value={theme.headingColor}
                      onChange={(e) => updateTheme('headingColor', e.target.value)}
                      placeholder="#111827"
                    />
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <Label>Typography Preview</Label>
                <div className="p-4 border rounded-lg space-y-2">
                  <h1 className="text-2xl font-bold" style={{ color: theme.headingColor }}>
                    Sample Heading
                  </h1>
                  <h2 className="text-lg font-semibold" style={{ color: theme.headingColor }}>
                    Subheading Example
                  </h2>
                  <p style={{ color: theme.textColor }}>
                    This is sample body text that shows how your content will look with the selected colors. 
                    The text should be readable and provide good contrast.
                  </p>
                  <p className="text-sm" style={{ color: theme.secondaryColor }}>
                    Secondary text example for less important content.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}