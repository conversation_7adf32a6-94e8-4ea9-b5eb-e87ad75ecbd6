import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { useAuth } from "@/hooks/use-auth";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useToast } from "@/hooks/use-toast";
import { Alarm<PERSON>lock, Bell, Clock } from "lucide-react";

export default function SettingsManagement() {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const { user } = useAuth();
  
  const isSupport = user?.isSupport || false;
  const settingsEndpoint = isSupport ? "/api/support/settings" : "/api/admin/settings";

  const { data: settings = {}, isLoading } = useQuery<any>({
    queryKey: [settingsEndpoint],
  });

  const updateSettingsMutation = useMutation({
    mutationFn: async (data: any) => {
      const response = await apiRequest("PUT", settingsEndpoint, data);
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [settingsEndpoint] });
      toast({ title: "Settings updated successfully" });
    },
    onError: () => {
      toast({
        title: "Error updating settings",
        variant: "destructive",
      });
    },
  });

  const handleReminderSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const formData = new FormData(e.target as HTMLFormElement);
    
    const updateData: any = {};
    
    // Reminder settings
    if (formData.has("firstReminderHours")) updateData.firstReminderHours = parseInt(formData.get("firstReminderHours") as string);
    if (formData.has("firstReminderSms")) updateData.firstReminderSms = formData.get("firstReminderSms") === "on";
    if (formData.has("secondReminderHours")) updateData.secondReminderHours = parseInt(formData.get("secondReminderHours") as string);
    if (formData.has("secondReminderSms")) updateData.secondReminderSms = formData.get("secondReminderSms") === "on";
    if (formData.has("finalReminderHours")) updateData.finalReminderHours = parseInt(formData.get("finalReminderHours") as string);
    if (formData.has("finalReminderSms")) updateData.finalReminderSms = formData.get("finalReminderSms") === "on";
    
    updateSettingsMutation.mutate(updateData);
  };

  const handleRulesSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const formData = new FormData(e.target as HTMLFormElement);
    
    const updateData: any = {};
    
    // Business rules
    if (formData.has("appointmentDuration")) updateData.appointmentDuration = parseInt(formData.get("appointmentDuration") as string);
    if (formData.has("gapBetweenAppointments")) updateData.gapBetweenAppointments = parseInt(formData.get("gapBetweenAppointments") as string);
    if (formData.has("shiftStartBuffer")) updateData.shiftStartBuffer = parseInt(formData.get("shiftStartBuffer") as string);
    if (formData.has("timeSlotInterval")) updateData.timeSlotInterval = parseInt(formData.get("timeSlotInterval") as string);
    if (formData.has("maxBookingDaysAhead")) updateData.maxBookingDaysAhead = parseInt(formData.get("maxBookingDaysAhead") as string);
    if (formData.has("minAdvanceHours")) updateData.minAdvanceHours = parseInt(formData.get("minAdvanceHours") as string);
    if (formData.has("maxBookingsPerRep")) updateData.maxBookingsPerRep = parseInt(formData.get("maxBookingsPerRep") as string);
    if (formData.has("defaultShiftStart")) updateData.defaultShiftStart = formData.get("defaultShiftStart");
    if (formData.has("defaultShiftEnd")) updateData.defaultShiftEnd = formData.get("defaultShiftEnd");
    if (formData.has("shiftEndBuffer")) updateData.shiftEndBuffer = parseInt(formData.get("shiftEndBuffer") as string);
    if (formData.has("rescheduleHoursLimit")) updateData.rescheduleHoursLimit = parseInt(formData.get("rescheduleHoursLimit") as string);
    if (formData.has("cancelHoursLimit")) updateData.cancelHoursLimit = parseInt(formData.get("cancelHoursLimit") as string);
    
    updateSettingsMutation.mutate(updateData);
  };

  if (isLoading) {
    return <div className="text-center p-8">Loading settings...</div>;
  }

  return (
    <div className="space-y-6">
      <Tabs defaultValue="reminders" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="reminders">Reminder Settings</TabsTrigger>
          <TabsTrigger value="rules">Business Rules</TabsTrigger>
        </TabsList>

        <TabsContent value="reminders">
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Clock className="w-5 h-5" />
                  <span>Automated Reminder Settings</span>
                </CardTitle>
                <p className="text-sm text-gray-600">Configure when WhatsApp reminders are sent automatically</p>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleReminderSubmit} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    {/* First Reminder */}
                    <div className="space-y-4 p-4 border rounded-lg">
                      <h3 className="text-lg font-medium flex items-center space-x-2">
                        <Bell className="w-4 h-4" />
                        <span>First Reminder</span>
                      </h3>
                      <div>
                        <Label htmlFor="firstReminderHours">Hours before appointment</Label>
                        <Input
                          id="firstReminderHours"
                          name="firstReminderHours"
                          type="number"
                          min="1"
                          max="72"
                          defaultValue={settings?.firstReminderHours || 24}
                          placeholder="24"
                          className="mt-1"
                        />
                      </div>
                      <div className="flex items-center space-x-2">
                        <Switch
                          id="firstReminderSms"
                          name="firstReminderSms"
                          defaultChecked={settings?.firstReminderSms !== false}
                        />
                        <Label htmlFor="firstReminderSms">Enable WhatsApp</Label>
                      </div>
                    </div>

                    {/* Second Reminder */}
                    <div className="space-y-4 p-4 border rounded-lg">
                      <h3 className="text-lg font-medium flex items-center space-x-2">
                        <Bell className="w-4 h-4" />
                        <span>Second Reminder</span>
                      </h3>
                      <div>
                        <Label htmlFor="secondReminderHours">Hours before appointment</Label>
                        <Input
                          id="secondReminderHours"
                          name="secondReminderHours"
                          type="number"
                          min="1"
                          max="24"
                          defaultValue={settings?.secondReminderHours || 4}
                          placeholder="4"
                          className="mt-1"
                        />
                      </div>
                      <div className="flex items-center space-x-2">
                        <Switch
                          id="secondReminderSms"
                          name="secondReminderSms"
                          defaultChecked={settings?.secondReminderSms !== false}
                        />
                        <Label htmlFor="secondReminderSms">Enable WhatsApp</Label>
                      </div>
                    </div>

                    {/* Final Reminder */}
                    <div className="space-y-4 p-4 border rounded-lg">
                      <h3 className="text-lg font-medium flex items-center space-x-2">
                        <AlarmClock className="w-4 h-4" />
                        <span>Final Reminder</span>
                      </h3>
                      <div>
                        <Label htmlFor="finalReminderHours">Hours before appointment</Label>
                        <Input
                          id="finalReminderHours"
                          name="finalReminderHours"
                          type="number"
                          min="1"
                          max="12"
                          defaultValue={settings?.finalReminderHours || 2}
                          placeholder="2"
                          className="mt-1"
                        />
                      </div>
                      <div className="flex items-center space-x-2">
                        <Switch
                          id="finalReminderSms"
                          name="finalReminderSms"
                          defaultChecked={settings?.finalReminderSms !== false}
                        />
                        <Label htmlFor="finalReminderSms">Enable WhatsApp</Label>
                      </div>
                    </div>
                  </div>

                  <Button 
                    type="submit" 
                    className="btn-kult-active w-full"
                    disabled={updateSettingsMutation.isPending}
                  >
                    {updateSettingsMutation.isPending ? "Saving..." : "Save Reminder Settings"}
                  </Button>
                </form>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <AlarmClock className="w-5 h-5" />
                  <span>Manual Reminder Control</span>
                </CardTitle>
                <p className="text-sm text-gray-600">Send reminders manually from the bookings dashboard</p>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="bg-blue-50 p-4 rounded-lg border-l-4 border-blue-400">
                    <div className="flex items-center space-x-2 mb-2">
                      <AlarmClock className="w-5 h-5 text-blue-600" />
                      <span className="text-blue-800 font-medium">How to send manual reminders:</span>
                    </div>
                    <ul className="text-blue-700 space-y-1 text-sm">
                      <li>• Go to the Bookings page</li>
                      <li>• Find the booking you want to remind</li>
                      <li>• Click the alarm icon next to the booking</li>
                      <li>• The reminder will be sent instantly via WhatsApp</li>
                    </ul>
                  </div>
                  
                  <div className="bg-green-50 p-4 rounded-lg border-l-4 border-green-400">
                    <div className="flex items-center space-x-2 mb-2">
                      <Bell className="w-5 h-5 text-green-600" />
                      <span className="text-green-800 font-medium">Template used:</span>
                    </div>
                    <p className="text-green-700 text-sm">
                      Manual reminders use the same WhatsApp Business API template as automated reminders,
                      ensuring consistent messaging and compliance with Meta guidelines.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="rules">
          <Card>
            <CardHeader>
              <CardTitle>Global Booking Rules</CardTitle>
              <p className="text-sm text-gray-600">Configure system-wide business rules for appointment scheduling</p>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleRulesSubmit} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <Label htmlFor="appointmentDuration">Appointment Duration (minutes)</Label>
                    <Input
                      id="appointmentDuration"
                      name="appointmentDuration"
                      type="number"
                      min="30"
                      max="180"
                      defaultValue={settings?.appointmentDuration || 90}
                      placeholder="90"
                    />
                    <p className="text-xs text-gray-500 mt-1">How long each perfume trial appointment lasts</p>
                  </div>

                  <div>
                    <Label htmlFor="gapBetweenAppointments">Gap Between Appointments (minutes)</Label>
                    <Input
                      id="gapBetweenAppointments"
                      name="gapBetweenAppointments"
                      type="number"
                      min="0"
                      max="180"
                      defaultValue={settings?.gapBetweenAppointments || 60}
                      placeholder="60"
                    />
                    <p className="text-xs text-gray-500 mt-1">Minimum break time between appointments for each representative</p>
                  </div>

                  <div>
                    <Label htmlFor="shiftStartBuffer">Shift Start Buffer (minutes)</Label>
                    <Input
                      id="shiftStartBuffer"
                      name="shiftStartBuffer"
                      type="number"
                      min="0"
                      max="180"
                      defaultValue={settings?.shiftStartBuffer || 90}
                      placeholder="90"
                    />
                    <p className="text-xs text-gray-500 mt-1">How long after shift start before first appointment can be scheduled</p>
                  </div>

                  <div>
                    <Label htmlFor="timeSlotInterval">Time Slot Interval (minutes)</Label>
                    <Input
                      id="timeSlotInterval"
                      name="timeSlotInterval"
                      type="number"
                      min="15"
                      max="60"
                      defaultValue={settings?.timeSlotInterval || 30}
                      placeholder="30"
                    />
                    <p className="text-xs text-gray-500 mt-1">How often new time slots are available (e.g., every 30 minutes)</p>
                  </div>

                  <div>
                    <Label htmlFor="maxBookingDaysAhead">Max Booking Days Ahead</Label>
                    <Input
                      id="maxBookingDaysAhead"
                      name="maxBookingDaysAhead"
                      type="number"
                      min="1"
                      max="30"
                      defaultValue={settings?.maxBookingDaysAhead || 7}
                      placeholder="7"
                    />
                    <p className="text-xs text-gray-500 mt-1">How many days in advance customers can book appointments</p>
                  </div>

                  <div>
                    <Label htmlFor="minAdvanceHours">Minimum Advance Hours</Label>
                    <Input
                      id="minAdvanceHours"
                      name="minAdvanceHours"
                      type="number"
                      min="1"
                      max="48"
                      defaultValue={settings?.minAdvanceHours || 1}
                      placeholder="1"
                    />
                    <p className="text-xs text-gray-500 mt-1">How far in advance customers must book</p>
                  </div>

                  <div>
                    <Label htmlFor="shiftEndBuffer">Shift End Buffer (minutes)</Label>
                    <Input
                      id="shiftEndBuffer"
                      name="shiftEndBuffer"
                      type="number"
                      min="0"
                      max="120"
                      defaultValue={settings?.shiftEndBuffer || 30}
                      placeholder="30"
                    />
                    <p className="text-xs text-gray-500 mt-1">Last appointment must end this many minutes before shift ends</p>
                  </div>

                  <div>
                    <Label htmlFor="maxBookingsPerRep">Max Bookings per Rep per Day</Label>
                    <Input
                      id="maxBookingsPerRep"
                      name="maxBookingsPerRep"
                      type="number"
                      min="1"
                      max="20"
                      defaultValue={settings?.maxBookingsPerRep || 8}
                      placeholder="8"
                    />
                    <p className="text-xs text-gray-500 mt-1">Maximum appointments one representative can handle in a day</p>
                  </div>

                  <div>
                    <Label htmlFor="defaultShiftStart">Default Shift Start Time</Label>
                    <Input
                      id="defaultShiftStart"
                      name="defaultShiftStart"
                      type="time"
                      defaultValue={settings?.defaultShiftStart || "10:00"}
                    />
                    <p className="text-xs text-gray-500 mt-1">Default start time for new sales representatives</p>
                  </div>

                  <div>
                    <Label htmlFor="defaultShiftEnd">Default Shift End Time</Label>
                    <Input
                      id="defaultShiftEnd"
                      name="defaultShiftEnd"
                      type="time"
                      defaultValue={settings?.defaultShiftEnd || "20:00"}
                    />
                    <p className="text-xs text-gray-500 mt-1">Default end time for new sales representatives</p>
                  </div>

                  <div>
                    <Label htmlFor="rescheduleHoursLimit">Reschedule Time Limit (hours)</Label>
                    <Input
                      id="rescheduleHoursLimit"
                      name="rescheduleHoursLimit"
                      type="number"
                      min="0"
                      max="72"
                      defaultValue={settings?.rescheduleHoursLimit || 0}
                      placeholder="0"
                    />
                    <p className="text-xs text-gray-500 mt-1">Hours before appointment when rescheduling is blocked (0 = no limit)</p>
                  </div>

                  <div>
                    <Label htmlFor="cancelHoursLimit">Cancellation Time Limit (hours)</Label>
                    <Input
                      id="cancelHoursLimit"
                      name="cancelHoursLimit"
                      type="number"
                      min="0"
                      max="72"
                      defaultValue={settings?.cancelHoursLimit || 0}
                      placeholder="0"
                    />
                    <p className="text-xs text-gray-500 mt-1">Hours before appointment when cancellation is blocked (0 = no limit)</p>
                  </div>
                </div>

                <div className="pt-6 border-t">
                  <Button 
                    type="submit" 
                    className="btn-kult-active w-full"
                    disabled={updateSettingsMutation.isPending}
                  >
                    {updateSettingsMutation.isPending ? "Saving..." : "Save Business Rules"}
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}