import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { 
  Phone, MessageSquare, Mail, CheckCircle, XCircle, Clock, 
  DollarSign, Star, User, Calendar, MapPin, CreditCard,
  PhoneCall, MessageCircle, Plus, Eye, Bell, Send, AlertCircle,
  Edit, Trash2, Users, Calendar as CalendarIcon
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { format } from "date-fns";

interface ExtendedBooking {
  id: number;
  name: string;
  phone: string;
  email: string | null;
  address: string;
  postalCode: string;
  date: string;
  timeSlot: string;
  status: string;
  appointmentCompleted: boolean;
  customerSatisfaction: number | null;
  feedbackNotes: string | null;
  purchaseAmount: number | null;
  orderId: string | null;
  paymentStatus: string | null;
  paymentMethod: string | null;
  callAttempts: number;
  lastCallDate: string | null;
  whatsappSent: number;
  emailsSent: number;
  supportTicketId: string | null;
  lastContactMethod: string | null;
  createdAt: string;
  updatedAt: string;
}

interface SupportTicket {
  id: number;
  ticketId: string;
  bookingId: number | null;
  customerName: string;
  customerPhone: string;
  issueCategory: string;
  priority: string;
  status: string;
  description: string;
  resolution: string | null;
  assignedAgent: string | null;
  customerSatisfactionScore: number | null;
  createdAt: string;
  updatedAt: string;
}

interface CustomerInteraction {
  id: number;
  bookingId: number | null;
  customerPhone: string;
  interactionType: string;
  direction: string;
  agentName: string | null;
  duration: number | null;
  outcome: string;
  notes: string | null;
  followUpRequired: boolean;
  followUpDate: string | null;
  createdAt: string;
}

export default function CustomerSupportDashboard() {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  
  const [selectedBooking, setSelectedBooking] = useState<ExtendedBooking | null>(null);
  const [editingBooking, setEditingBooking] = useState<ExtendedBooking | null>(null);
  const [customMessageBooking, setCustomMessageBooking] = useState<ExtendedBooking | null>(null);
  const [scheduleReminderBooking, setScheduleReminderBooking] = useState<ExtendedBooking | null>(null);
  const [newTicketOpen, setNewTicketOpen] = useState(false);
  const [newInteractionOpen, setNewInteractionOpen] = useState(false);
  const [customMessage, setCustomMessage] = useState("");
  const [reminderDateTime, setReminderDateTime] = useState("");
  const [reminderType, setReminderType] = useState("appointment");
  const [statusFilter, setStatusFilter] = useState("all");
  
  // Fetch enhanced bookings - use same data source as bookings table
  const { data: bookings } = useQuery({
    queryKey: ["/api/admin/bookings"],
    queryFn: () => apiRequest("GET", "/api/admin/bookings").then(res => res.json()),
  });

  // Fetch support tickets
  const { data: tickets } = useQuery({
    queryKey: ["/api/support/tickets"],
    queryFn: () => apiRequest("GET", "/api/support/tickets").then(res => res.json()),
  });

  // Get customer interactions for selected booking
  const { data: interactions } = useQuery({
    queryKey: ["/api/support/interactions", selectedBooking?.id],
    queryFn: () => selectedBooking 
      ? apiRequest("GET", `/api/support/interactions?bookingId=${selectedBooking.id}`).then(res => res.json())
      : Promise.resolve([]),
    enabled: !!selectedBooking,
  });

  // Mark appointment completed
  const completeAppointmentMutation = useMutation({
    mutationFn: async (data: { bookingId: number; satisfaction?: number; notes?: string }) => {
      const response = await apiRequest("POST", `/api/bookings/${data.bookingId}/complete`, {
        body: JSON.stringify(data)
      });
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/admin/bookings"] });
      toast({ title: "Appointment marked as completed" });
    },
  });

  // Add transaction
  const addTransactionMutation = useMutation({
    mutationFn: async (data: { bookingId: number; amount: number; orderId: string; paymentMethod: string }) => {
      const response = await apiRequest("POST", `/api/bookings/${data.bookingId}/transaction`, {
        body: JSON.stringify(data)
      });
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/admin/bookings"] });
      toast({ title: "Transaction recorded successfully" });
    },
  });

  // Record communication
  const recordCommunicationMutation = useMutation({
    mutationFn: async (data: { bookingId: number; method: string; notes?: string }) => {
      const response = await apiRequest("POST", `/api/bookings/${data.bookingId}/communication`, {
        body: JSON.stringify(data)
      });
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/admin/bookings"] });
      toast({ title: "Communication recorded" });
    },
  });

  // Send reminder
  const sendReminderMutation = useMutation({
    mutationFn: async (data: { bookingId: number; type: string; customMessage?: string }) => {
      const response = await apiRequest("POST", `/api/bookings/${data.bookingId}/reminder`, {
        body: JSON.stringify(data)
      });
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/admin/bookings"] });
      toast({ title: "Reminder sent successfully" });
    },
  });

  // Send custom message
  const sendCustomMessageMutation = useMutation({
    mutationFn: async (data: { bookingId: number; message: string }) => {
      const response = await apiRequest("POST", `/api/bookings/${data.bookingId}/custom-message`, {
        body: JSON.stringify(data)
      });
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/admin/bookings"] });
      setCustomMessage("");
      setCustomMessageBooking(null);
      toast({ title: "Custom message sent" });
    },
  });

  // Update booking
  const updateBookingMutation = useMutation({
    mutationFn: async (data: { bookingId: number; updates: any }) => {
      const response = await apiRequest("PATCH", `/api/bookings/${data.bookingId}`, {
        body: JSON.stringify(data.updates)
      });
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/admin/bookings"] });
      setEditingBooking(null);
      toast({ title: "Booking updated successfully" });
    },
  });

  // Schedule reminder
  const scheduleReminderMutation = useMutation({
    mutationFn: async (data: { bookingId: number; reminderDateTime: string; type: string }) => {
      const response = await apiRequest("POST", `/api/bookings/${data.bookingId}/schedule-reminder`, {
        body: JSON.stringify(data)
      });
      return response.json();
    },
    onSuccess: () => {
      setScheduleReminderBooking(null);
      setReminderDateTime("");
      toast({ title: "Reminder scheduled successfully" });
    },
  });

  const getStatusBadge = (status: string) => {
    const variants: Record<string, "default" | "secondary" | "destructive" | "outline"> = {
      confirmed: "default",
      completed: "secondary",
      cancelled: "destructive",
      "no-show": "outline"
    };
    return <Badge variant={variants[status] || "default"}>{status}</Badge>;
  };

  const getPaymentStatusBadge = (status: string | null) => {
    if (!status) return <Badge variant="outline">No Payment</Badge>;
    
    const variants: Record<string, "default" | "secondary" | "destructive" | "outline"> = {
      completed: "secondary",
      pending: "default",
      failed: "destructive",
      refunded: "outline"
    };
    return <Badge variant={variants[status] || "default"}>{status}</Badge>;
  };

  const formatCurrency = (amount: number | null) => {
    if (!amount) return "₹0";
    return `₹${(amount / 100).toFixed(2)}`;
  };

  const renderStarRating = (rating: number | null) => {
    if (!rating) return <span className="text-gray-400">No rating</span>;
    
    return (
      <div className="flex items-center space-x-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`w-4 h-4 ${star <= rating ? 'text-yellow-400 fill-current' : 'text-gray-300'}`}
          />
        ))}
        <span className="ml-1 text-sm">({rating})</span>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        {/* Summary Cards */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Bookings</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{bookings?.length || 0}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completed</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {bookings?.filter((b: ExtendedBooking) => b.appointmentCompleted).length || 0}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(bookings?.reduce((sum: number, b: ExtendedBooking) => 
                sum + (b.purchaseAmount || 0), 0) || 0)}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Open Tickets</CardTitle>
            <MessageSquare className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {tickets?.filter((t: SupportTicket) => t.status === 'open').length || 0}
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="bookings" className="w-full">
        <TabsList>
          <TabsTrigger value="bookings">Customer Bookings</TabsTrigger>
          <TabsTrigger value="tickets">Support Tickets</TabsTrigger>
          <TabsTrigger value="interactions">Interactions</TabsTrigger>
        </TabsList>

        <TabsContent value="bookings" className="space-y-4">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium">Customer Bookings</h3>
            <div className="flex items-center space-x-2">
              <Label htmlFor="status-filter">Filter by Status:</Label>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-40">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="confirmed">Confirmed</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Customer</TableHead>
                  <TableHead>Date & Time</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Communications</TableHead>
                  <TableHead>Purchase</TableHead>
                  <TableHead>Rating</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {bookings?.filter((booking: ExtendedBooking) => 
                  statusFilter === "all" || booking.status === statusFilter
                ).map((booking: ExtendedBooking) => (
                  <TableRow key={booking.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{booking.name}</div>
                        <div className="text-sm text-gray-500">{booking.phone}</div>
                        {booking.supportTicketId && (
                          <Badge variant="outline" className="mt-1">
                            Ticket: {booking.supportTicketId}
                          </Badge>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div>
                        <div>{format(new Date(booking.date), 'MMM d, yyyy')}</div>
                        <div className="text-sm text-gray-500">{booking.timeSlot}</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        {getStatusBadge(booking.status)}
                        {booking.appointmentCompleted && (
                          <Badge variant="secondary" className="ml-2">Completed</Badge>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2 text-sm">
                        <div className="flex items-center space-x-1">
                          <Phone className="w-3 h-3" />
                          <span>{booking.callAttempts}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <MessageCircle className="w-3 h-3" />
                          <span>{booking.whatsappSent}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Mail className="w-3 h-3" />
                          <span>{booking.emailsSent}</span>
                        </div>
                      </div>
                      {booking.lastContactMethod && (
                        <div className="text-xs text-gray-500 mt-1">
                          Last: {booking.lastContactMethod}
                        </div>
                      )}
                    </TableCell>
                    <TableCell>
                      <div>
                        <div>{formatCurrency(booking.purchaseAmount)}</div>
                        {booking.paymentStatus && getPaymentStatusBadge(booking.paymentStatus)}
                        {booking.orderId && (
                          <div className="text-xs text-gray-500 mt-1">#{booking.orderId}</div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      {renderStarRating(booking.customerSatisfaction)}
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-wrap items-center gap-1">
                        {/* View Details */}
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setSelectedBooking(booking)}
                          title="View details"
                        >
                          <Eye className="w-4 h-4" />
                        </Button>
                        
                        {/* Edit Booking */}
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setEditingBooking(booking)}
                          title="Edit booking"
                        >
                          <Edit className="w-4 h-4" />
                        </Button>
                        
                        {/* Quick Communication Actions */}
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => recordCommunicationMutation.mutate({
                            bookingId: booking.id,
                            method: "call"
                          })}
                          title="Record call attempt"
                        >
                          <PhoneCall className="w-4 h-4" />
                        </Button>
                        
                        {/* Send WhatsApp Reminder */}
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => sendReminderMutation.mutate({
                            bookingId: booking.id,
                            type: "reminder"
                          })}
                          title="Send WhatsApp reminder"
                        >
                          <Bell className="w-4 h-4" />
                        </Button>
                        
                        {/* Send Custom Message */}
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setCustomMessageBooking(booking)}
                          title="Send custom message"
                        >
                          <Send className="w-4 h-4" />
                        </Button>
                        
                        {/* CX Follow-up "Tried Reaching" */}
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => sendReminderMutation.mutate({
                            bookingId: booking.id,
                            type: "cx-followup"
                          })}
                          title="Send 'Tried Reaching' follow-up"
                          className="text-orange-600 hover:text-orange-700"
                        >
                          <Phone className="w-4 h-4" />
                        </Button>

                        {/* Follow-up Actions */}
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => sendReminderMutation.mutate({
                            bookingId: booking.id,
                            type: "followup"
                          })}
                          title="Send follow-up message"
                        >
                          <MessageCircle className="w-4 h-4" />
                        </Button>
                        
                        {/* Mark Complete */}
                        {!booking.appointmentCompleted && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => completeAppointmentMutation.mutate({
                              bookingId: booking.id
                            })}
                            title="Mark as completed"
                          >
                            <CheckCircle className="w-4 h-4" />
                          </Button>
                        )}
                        
                        {/* Schedule Follow-up */}
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setScheduleReminderBooking(booking)}
                          title="Schedule reminder"
                        >
                          <CalendarIcon className="w-4 h-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </TabsContent>

        <TabsContent value="tickets" className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium">Support Tickets</h3>
            <Button onClick={() => setNewTicketOpen(true)}>
              <Plus className="w-4 h-4 mr-2" />
              New Ticket
            </Button>
          </div>
          
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Ticket ID</TableHead>
                  <TableHead>Customer</TableHead>
                  <TableHead>Issue</TableHead>
                  <TableHead>Priority</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Assigned</TableHead>
                  <TableHead>Created</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {tickets?.map((ticket: SupportTicket) => (
                  <TableRow key={ticket.id}>
                    <TableCell>
                      <div className="font-medium">{ticket.ticketId}</div>
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">{ticket.customerName}</div>
                        <div className="text-sm text-gray-500">{ticket.customerPhone}</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">{ticket.issueCategory}</div>
                        <div className="text-sm text-gray-500 max-w-xs truncate">
                          {ticket.description}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant={ticket.priority === 'high' ? 'destructive' : 'default'}>
                        {ticket.priority}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge variant={ticket.status === 'resolved' ? 'secondary' : 'default'}>
                        {ticket.status}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {ticket.assignedAgent || 'Unassigned'}
                    </TableCell>
                    <TableCell>
                      {format(new Date(ticket.createdAt), 'MMM d, yyyy')}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </TabsContent>

        <TabsContent value="interactions" className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium">Customer Interactions</h3>
            <Button onClick={() => setNewInteractionOpen(true)}>
              <Plus className="w-4 h-4 mr-2" />
              Log Interaction
            </Button>
          </div>
          
          {/* Interactions would be displayed here */}
        </TabsContent>
      </Tabs>

      {/* Detailed Booking Modal */}
      {selectedBooking && (
        <Dialog open={!!selectedBooking} onOpenChange={() => setSelectedBooking(null)}>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Customer Support - {selectedBooking.name}</DialogTitle>
            </DialogHeader>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Customer Information */}
              <div className="space-y-4">
                <div className="space-y-2">
                  <h4 className="font-medium flex items-center">
                    <User className="w-4 h-4 mr-2" />
                    Customer Details
                  </h4>
                  <div className="space-y-1 text-sm">
                    <div><strong>Name:</strong> {selectedBooking.name}</div>
                    <div><strong>Phone:</strong> {selectedBooking.phone}</div>
                    <div><strong>Email:</strong> {selectedBooking.email || 'Not provided'}</div>
                    <div><strong>Address:</strong> {selectedBooking.address}</div>
                    <div><strong>Postal Code:</strong> {selectedBooking.postalCode}</div>
                  </div>
                </div>

                {/* Appointment Details */}
                <div className="space-y-2">
                  <h4 className="font-medium flex items-center">
                    <Calendar className="w-4 h-4 mr-2" />
                    Appointment
                  </h4>
                  <div className="space-y-1 text-sm">
                    <div><strong>Date:</strong> {format(new Date(selectedBooking.date), 'EEEE, MMMM d, yyyy')}</div>
                    <div><strong>Time:</strong> {selectedBooking.timeSlot}</div>
                    <div><strong>Status:</strong> {getStatusBadge(selectedBooking.status)}</div>
                    <div><strong>Completed:</strong> {selectedBooking.appointmentCompleted ? 'Yes' : 'No'}</div>
                  </div>
                </div>

                {/* Transaction Details */}
                <div className="space-y-2">
                  <h4 className="font-medium flex items-center">
                    <CreditCard className="w-4 h-4 mr-2" />
                    Transaction
                  </h4>
                  <div className="space-y-1 text-sm">
                    <div><strong>Amount:</strong> {formatCurrency(selectedBooking.purchaseAmount)}</div>
                    <div><strong>Order ID:</strong> {selectedBooking.orderId || 'No order'}</div>
                    <div><strong>Payment Status:</strong> {selectedBooking.paymentStatus ? getPaymentStatusBadge(selectedBooking.paymentStatus) : 'No payment'}</div>
                    <div><strong>Payment Method:</strong> {selectedBooking.paymentMethod || 'Not specified'}</div>
                  </div>
                </div>
              </div>

              {/* Communication & Support */}
              <div className="space-y-4">
                {/* Communication Stats */}
                <div className="space-y-2">
                  <h4 className="font-medium">Communication History</h4>
                  <div className="grid grid-cols-3 gap-2 text-sm">
                    <div className="text-center p-2 bg-gray-50 rounded">
                      <Phone className="w-4 h-4 mx-auto mb-1" />
                      <div className="font-medium">{selectedBooking.callAttempts}</div>
                      <div className="text-xs text-gray-500">Calls</div>
                    </div>
                    <div className="text-center p-2 bg-gray-50 rounded">
                      <MessageCircle className="w-4 h-4 mx-auto mb-1" />
                      <div className="font-medium">{selectedBooking.whatsappSent}</div>
                      <div className="text-xs text-gray-500">WhatsApp</div>
                    </div>
                    <div className="text-center p-2 bg-gray-50 rounded">
                      <Mail className="w-4 h-4 mx-auto mb-1" />
                      <div className="font-medium">{selectedBooking.emailsSent}</div>
                      <div className="text-xs text-gray-500">Emails</div>
                    </div>
                  </div>
                  {selectedBooking.lastContactMethod && (
                    <div className="text-sm text-gray-600">
                      Last contact: {selectedBooking.lastContactMethod}
                      {selectedBooking.lastCallDate && ` on ${format(new Date(selectedBooking.lastCallDate), 'MMM d, yyyy')}`}
                    </div>
                  )}
                </div>

                {/* Customer Satisfaction */}
                <div className="space-y-2">
                  <h4 className="font-medium">Customer Feedback</h4>
                  <div className="space-y-2">
                    <div>
                      <strong>Rating:</strong> {renderStarRating(selectedBooking.customerSatisfaction)}
                    </div>
                    {selectedBooking.feedbackNotes && (
                      <div>
                        <strong>Notes:</strong>
                        <div className="mt-1 p-2 bg-gray-50 rounded text-sm">
                          {selectedBooking.feedbackNotes}
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Quick Actions */}
                <div className="space-y-2">
                  <h4 className="font-medium">Quick Actions</h4>
                  <div className="flex flex-wrap gap-2">
                    <Button size="sm" onClick={() => recordCommunicationMutation.mutate({
                      bookingId: selectedBooking.id,
                      method: "call"
                    })}>
                      <Phone className="w-4 h-4 mr-1" />
                      Log Call
                    </Button>
                    
                    <Button size="sm" onClick={() => recordCommunicationMutation.mutate({
                      bookingId: selectedBooking.id,
                      method: "whatsapp"
                    })}>
                      <MessageCircle className="w-4 h-4 mr-1" />
                      Log WhatsApp
                    </Button>
                    
                    {!selectedBooking.appointmentCompleted && (
                      <Button size="sm" onClick={() => completeAppointmentMutation.mutate({
                        bookingId: selectedBooking.id
                      })}>
                        <CheckCircle className="w-4 h-4 mr-1" />
                        Mark Complete
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}

      {/* Edit Booking Dialog */}
      {editingBooking && (
        <Dialog open={!!editingBooking} onOpenChange={() => setEditingBooking(null)}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Edit Booking - {editingBooking.name}</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="edit-name">Customer Name</Label>
                  <Input
                    id="edit-name"
                    defaultValue={editingBooking.name}
                    onBlur={(e) => updateBookingMutation.mutate({
                      bookingId: editingBooking.id,
                      updates: { name: e.target.value }
                    })}
                  />
                </div>
                <div>
                  <Label htmlFor="edit-phone">Phone Number</Label>
                  <Input
                    id="edit-phone"
                    defaultValue={editingBooking.phone}
                    onBlur={(e) => updateBookingMutation.mutate({
                      bookingId: editingBooking.id,
                      updates: { phone: e.target.value }
                    })}
                  />
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="edit-date">Date</Label>
                  <Input
                    id="edit-date"
                    type="date"
                    defaultValue={editingBooking.date}
                    onBlur={(e) => updateBookingMutation.mutate({
                      bookingId: editingBooking.id,
                      updates: { date: e.target.value }
                    })}
                  />
                </div>
                <div>
                  <Label htmlFor="edit-time">Time</Label>
                  <Input
                    id="edit-time"
                    defaultValue={editingBooking.timeSlot}
                    onBlur={(e) => updateBookingMutation.mutate({
                      bookingId: editingBooking.id,
                      updates: { timeSlot: e.target.value }
                    })}
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="edit-address">Address</Label>
                <Textarea
                  id="edit-address"
                  defaultValue={editingBooking.address}
                  onBlur={(e) => updateBookingMutation.mutate({
                    bookingId: editingBooking.id,
                    updates: { address: e.target.value }
                  })}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="edit-status">Status</Label>
                  <Select onValueChange={(value) => updateBookingMutation.mutate({
                    bookingId: editingBooking.id,
                    updates: { status: value }
                  })}>
                    <SelectTrigger>
                      <SelectValue placeholder={editingBooking.status} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="confirmed">Confirmed</SelectItem>
                      <SelectItem value="completed">Completed</SelectItem>
                      <SelectItem value="cancelled">Cancelled</SelectItem>
                      <SelectItem value="no-show">No Show</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="edit-satisfaction">Satisfaction (1-5)</Label>
                  <Input
                    id="edit-satisfaction"
                    type="number"
                    min="1"
                    max="5"
                    defaultValue={editingBooking.customerSatisfaction || ""}
                    onBlur={(e) => updateBookingMutation.mutate({
                      bookingId: editingBooking.id,
                      updates: { customerSatisfaction: parseInt(e.target.value) || null }
                    })}
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="edit-notes">Feedback Notes</Label>
                <Textarea
                  id="edit-notes"
                  defaultValue={editingBooking.feedbackNotes || ""}
                  onBlur={(e) => updateBookingMutation.mutate({
                    bookingId: editingBooking.id,
                    updates: { feedbackNotes: e.target.value }
                  })}
                />
              </div>

              <div>
                <Label htmlFor="edit-transaction">Transaction ID</Label>
                <Input
                  id="edit-transaction"
                  defaultValue={editingBooking.orderId || ""}
                  placeholder="Enter transaction ID if purchase happened"
                  onBlur={(e) => updateBookingMutation.mutate({
                    bookingId: editingBooking.id,
                    updates: { orderId: e.target.value }
                  })}
                />
              </div>

              <Button onClick={() => setEditingBooking(null)} className="w-full">
                Close
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      )}

      {/* Custom Message Dialog */}
      {customMessageBooking && (
        <Dialog open={!!customMessageBooking} onOpenChange={() => setCustomMessageBooking(null)}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Send Custom Message to {customMessageBooking.name}</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="custom-message">Message</Label>
                <Textarea
                  id="custom-message"
                  value={customMessage}
                  onChange={(e) => setCustomMessage(e.target.value)}
                  placeholder="Enter your custom message..."
                  rows={4}
                />
              </div>
              <div className="flex space-x-2">
                <Button
                  onClick={() => sendCustomMessageMutation.mutate({
                    bookingId: customMessageBooking.id,
                    message: customMessage
                  })}
                  disabled={!customMessage.trim()}
                >
                  <Send className="w-4 h-4 mr-2" />
                  Send Message
                </Button>
                <Button variant="outline" onClick={() => setCustomMessageBooking(null)}>
                  Cancel
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}

      {/* Schedule Reminder Dialog */}
      {scheduleReminderBooking && (
        <Dialog open={!!scheduleReminderBooking} onOpenChange={() => setScheduleReminderBooking(null)}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Schedule Reminder for {scheduleReminderBooking.name}</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="reminder-datetime">Reminder Date & Time</Label>
                <Input
                  id="reminder-datetime"
                  type="datetime-local"
                  value={reminderDateTime}
                  onChange={(e) => setReminderDateTime(e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor="reminder-type">Reminder Type</Label>
                <Select onValueChange={setReminderType} defaultValue={reminderType}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="appointment">Appointment Reminder</SelectItem>
                    <SelectItem value="followup">Follow-up Call</SelectItem>
                    <SelectItem value="feedback">Feedback Request</SelectItem>
                    <SelectItem value="custom">Custom Reminder</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="flex space-x-2">
                <Button
                  onClick={() => scheduleReminderMutation.mutate({
                    bookingId: scheduleReminderBooking.id,
                    reminderDateTime,
                    type: reminderType
                  })}
                  disabled={!reminderDateTime}
                >
                  <CalendarIcon className="w-4 h-4 mr-2" />
                  Schedule Reminder
                </Button>
                <Button variant="outline" onClick={() => setScheduleReminderBooking(null)}>
                  Cancel
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}