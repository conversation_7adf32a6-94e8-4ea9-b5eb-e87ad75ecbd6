import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { 
  BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer,
  <PERSON><PERSON>hart, Pie, Cell, LineChart, Line
} from 'recharts';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { DialogTrigger } from '@/components/ui/dialog';
import { 
  Users, 
  Eye, 
  MousePointer, 
  TrendingUp, 
  Target,
  RefreshCw,
  BarChart3,
  Activity,
  ExternalLink,
  Zap,
  ArrowUpRight,
  Percent
} from 'lucide-react';
import { format, subDays } from 'date-fns';
import { useToast } from '@/hooks/use-toast';

interface GA4Metrics {
  sessions: number;
  users: number;
  pageviews: number;
  bounceRate: number;
  sessionDuration: number;
  newUsers: number;
}

interface GA4CampaignData {
  campaignName: string;
  medium: string;
  source: string;
  sessions: number;
  users: number;
  conversions: number;
  bookings: number;
  engagedSessions?: number;
  engagementRate?: number;
  averageEngagementTime?: number;
  eventsPerSession?: number;
  eventCount?: number;
  bounceRate?: number;
  revenue?: number;
}

interface ConversionFunnelData {
  step: string;
  count: number;
  rate: number;
  dropOff: number;
}

const COLORS = ['#8884d8', '#82ca9d', '#ffc658', '#ff7300', '#00C49F', '#FFBB28', '#FF8042'];

// Date range options
const DATE_RANGES = [
  { label: 'Last 7 days', value: '7' },
  { label: 'Last 14 days', value: '14' },
  { label: 'Last 30 days', value: '30' },
  { label: 'Last 90 days', value: '90' },
];

export default function UTMConversionDashboard() {
  const [dateRange, setDateRange] = useState('30');
  const [selectedCampaign, setSelectedCampaign] = useState<string>('all');
  const [campaignModalData, setCampaignModalData] = useState<GA4CampaignData | null>(null);
  const [showCampaignModal, setShowCampaignModal] = useState(false);
  const { toast } = useToast();

  // Helper function to get date parameters
  const getDateParams = (days: string) => {
    const endDate = format(new Date(), 'yyyy-MM-dd');
    const startDate = format(subDays(new Date(), parseInt(days)), 'yyyy-MM-dd');
    return { startDate, endDate };
  };

  const dateParams = getDateParams(dateRange);

  // Fetch GA4 data (only what actually works)
  const { data: overview, isLoading: overviewLoading, refetch: refetchOverview } = useQuery<GA4Metrics>({
    queryKey: ['/api/admin/ga4/overview', dateParams],
    enabled: true,
  });

  const { data: campaigns = [], isLoading: campaignsLoading } = useQuery<GA4CampaignData[]>({
    queryKey: ['/api/admin/ga4/campaigns', dateParams],
    enabled: true,
  });

  // Fetch UTM conversion funnel data
  const { data: conversionFunnel = [], isLoading: funnelLoading } = useQuery<ConversionFunnelData[]>({
    queryKey: ['/api/admin/analytics/utm-funnel', dateParams],
    enabled: true,
  });

  // Fetch UTM source performance
  const { data: utmSources = [], isLoading: sourcesLoading } = useQuery<any[]>({
    queryKey: ['/api/admin/analytics/utm-sources', dateParams],
    enabled: true,
  });

  // Fetch campaign bookings when modal is open
  const { data: campaignBookings = [], isLoading: bookingsLoading } = useQuery<any[]>({
    queryKey: ['/api/analytics/campaign-bookings', campaignModalData?.campaignName],
    enabled: !!campaignModalData,
  });

  // Fetch campaign funnel when modal is open
  const { data: campaignFunnel, isLoading: funnelModalLoading } = useQuery<any>({
    queryKey: ['/api/admin/analytics/campaign-funnel', campaignModalData?.campaignName],
    enabled: !!campaignModalData,
  });

  const refreshAll = () => {
    refetchOverview();
  };

  const handleCampaignClick = (campaign: GA4CampaignData) => {
    setCampaignModalData(campaign);
    setShowCampaignModal(true);
  };

  // Calculate campaign performance metrics
  const totalSessions = campaigns.reduce((sum, campaign) => sum + campaign.sessions, 0);
  const totalConversions = campaigns.reduce((sum, campaign) => sum + campaign.conversions, 0);
  const overallConversionRate = totalSessions > 0 ? (totalConversions / totalSessions) * 100 : 0;

  // Prepare campaign chart data
  const campaignChartData = campaigns
    .filter(campaign => campaign.sessions > 0)
    .sort((a, b) => b.sessions - a.sessions)
    .slice(0, 10)
    .map(campaign => ({
      name: campaign.campaignName.length > 30 
        ? campaign.campaignName.substring(0, 30) + '...' 
        : campaign.campaignName,
      sessions: campaign.sessions,
      conversions: campaign.conversions,
      conversionRate: campaign.sessions > 0 ? (campaign.conversions / campaign.sessions) * 100 : 0,
    }));

  // Prepare UTM source breakdown
  const utmSourceData = utmSources.map(source => ({
    name: source.utm_source || 'Direct',
    medium: source.utm_medium || 'None',
    sessions: source.sessions || 0,
    conversions: source.conversions || 0,
    conversionRate: source.sessions > 0 ? (source.conversions / source.sessions) * 100 : 0,
  }));

  return (
    <div className="space-y-6">
      {/* Header with controls */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">UTM & Conversion Tracking</h2>
          <p className="text-gray-600">Real-time campaign attribution and conversion analytics</p>
        </div>
        <div className="flex space-x-4">
          <Select value={dateRange} onValueChange={setDateRange}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {DATE_RANGES.map((range) => (
                <SelectItem key={range.value} value={range.value}>
                  {range.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Button onClick={refreshAll} variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Overview metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Sessions</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{overview?.sessions.toLocaleString() || 0}</div>
            <p className="text-xs text-muted-foreground">From all campaigns</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Conversions</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalConversions.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">Booking completions</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Conversion Rate</CardTitle>
            <Percent className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{overallConversionRate.toFixed(2)}%</div>
            <p className="text-xs text-muted-foreground">Overall performance</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Campaigns</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{campaigns.length}</div>
            <p className="text-xs text-muted-foreground">With sessions</p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="campaigns" className="space-y-6">
        <TabsList>
          <TabsTrigger value="campaigns">Campaign Performance</TabsTrigger>
          <TabsTrigger value="utm">UTM Sources</TabsTrigger>
          <TabsTrigger value="funnel">Conversion Funnel</TabsTrigger>
        </TabsList>

        <TabsContent value="campaigns" className="space-y-6">
          {/* Campaign performance chart */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <BarChart3 className="h-5 w-5 mr-2" />
                Campaign Performance Overview
              </CardTitle>
            </CardHeader>
            <CardContent>
              {campaignsLoading ? (
                <div className="h-96 flex items-center justify-center">
                  <div className="text-center">
                    <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4" />
                    <p>Loading campaign data...</p>
                  </div>
                </div>
              ) : (
                <ResponsiveContainer width="100%" height={400}>
                  <BarChart data={campaignChartData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis 
                      dataKey="name" 
                      angle={-45}
                      textAnchor="end"
                      height={100}
                      fontSize={12}
                    />
                    <YAxis yAxisId="left" />
                    <YAxis yAxisId="right" orientation="right" />
                    <Tooltip 
                      formatter={(value, name) => [
                        typeof value === 'number' ? value.toLocaleString() : value,
                        name
                      ]}
                    />
                    <Legend />
                    <Bar yAxisId="left" dataKey="sessions" fill="#8884d8" name="Sessions" />
                    <Bar yAxisId="right" dataKey="conversions" fill="#82ca9d" name="Conversions" />
                  </BarChart>
                </ResponsiveContainer>
              )}
            </CardContent>
          </Card>

          {/* Campaign summary table */}
          <Card>
            <CardHeader>
              <CardTitle>Campaign Details</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left p-3">Campaign Name</th>
                      <th className="text-left p-3">Source</th>
                      <th className="text-left p-3">Medium</th>
                      <th className="text-right p-3">Sessions</th>
                      <th className="text-right p-3">Events</th>
                      <th className="text-right p-3">Bookings</th>
                      <th className="text-right p-3">Conv. Rate</th>
                      <th className="text-center p-3">UTM Link</th>
                    </tr>
                  </thead>
                  <tbody>
                    {campaigns.map((campaign, index) => {
                      const generateUTMLink = (camp: any) => {
                        const baseUrl = 'https://perfumestrial.kult.app';
                        const params = new URLSearchParams({
                          utm_source: camp.utm_source || camp.source || '',
                          utm_medium: camp.utm_medium || camp.medium || '',
                          utm_campaign: camp.utm_campaign || camp.campaignName || '',
                          utm_content: camp.utm_content || '',
                          utm_term: camp.utm_term || ''
                        });
                        return `${baseUrl}?${params.toString()}`;
                      };

                      const copyToClipboard = async (text: string) => {
                        try {
                          await navigator.clipboard.writeText(text);
                          toast({
                            title: "UTM Link Copied",
                            description: "The UTM link has been copied to your clipboard.",
                          });
                        } catch (err) {
                          toast({
                            title: "Copy Failed",
                            description: "Unable to copy to clipboard. Please try again.",
                            variant: "destructive",
                          });
                        }
                      };

                      return (
                        <tr key={index} className="border-b hover:bg-gray-50">
                          <td className="p-3 font-medium">
                            <div className="max-w-xs">
                              <div className="font-semibold text-sm text-gray-900 truncate" title={(campaign as any).campaign_name || campaign.campaignName}>
                                {(campaign as any).campaign_name || campaign.campaignName || 'Unknown Campaign'}
                              </div>
                              <div className="text-xs text-gray-500">
                                {(campaign as any).utm_campaign || 'No UTM campaign'}
                              </div>
                            </div>
                          </td>
                          <td className="p-3">
                            <Badge variant="secondary" className="text-xs">
                              {(campaign as any).utm_source || campaign.source || 'Unknown'}
                            </Badge>
                          </td>
                          <td className="p-3">
                            <div className="text-xs text-gray-600 max-w-32 truncate" title={(campaign as any).utm_medium || campaign.medium}>
                              {(campaign as any).utm_medium || campaign.medium || 'Unknown'}
                            </div>
                          </td>
                          <td className="p-3 text-right font-semibold">
                            {(campaign.sessions || 0).toLocaleString()}
                          </td>
                          <td className="p-3 text-right">
                            {((campaign as any).events || 0).toLocaleString()}
                          </td>
                          <td className="p-3 text-right font-semibold">
                            {(campaign.bookings || 0).toLocaleString()}
                          </td>
                          <td className="p-3 text-right">
                            <Badge variant={(campaign.sessions > 0 && (campaign.bookings || 0) / campaign.sessions > 0.05) ? "default" : "secondary"}>
                              {campaign.sessions > 0 
                                ? (((campaign.bookings || 0) / campaign.sessions) * 100).toFixed(2) + '%'
                                : '0.00%'
                              }
                            </Badge>
                          </td>
                          <td className="p-3 text-center">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => copyToClipboard(generateUTMLink(campaign))}
                              className="text-xs"
                              title="Copy UTM link to clipboard"
                            >
                              <ExternalLink className="h-3 w-3 mr-1" />
                              Copy UTM
                            </Button>
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="utm" className="space-y-6">
          {/* UTM source breakdown */}
          <Card>
            <CardHeader>
              <CardTitle>UTM Source Performance</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <BarChart data={utmSourceData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey="sessions" fill="#8884d8" name="Sessions" />
                  <Bar dataKey="conversions" fill="#82ca9d" name="Conversions" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="funnel" className="space-y-6">
          {/* Conversion funnel */}
          <Card>
            <CardHeader>
              <CardTitle>UTM Attribution Funnel</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <BarChart data={conversionFunnel}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="step" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="count" fill="#8884d8" name="Users" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Campaign Details Modal */}
      <Dialog open={showCampaignModal} onOpenChange={setShowCampaignModal}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Target className="w-5 h-5" />
              Campaign Details: {campaignModalData?.campaignName}
            </DialogTitle>
          </DialogHeader>
          
          {campaignModalData && (
            <div className="space-y-6">
              {/* Campaign Overview */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <Card>
                  <CardContent className="p-4 text-center">
                    <div className="text-2xl font-bold text-blue-600">
                      {campaignModalData.sessions.toLocaleString()}
                    </div>
                    <p className="text-sm text-gray-600">Sessions</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4 text-center">
                    <div className="text-2xl font-bold text-green-600">
                      {campaignModalData.conversions.toLocaleString()}
                    </div>
                    <p className="text-sm text-gray-600">Conversions</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4 text-center">
                    <div className="text-2xl font-bold text-purple-600">
                      {campaignModalData.bookings || 0}
                    </div>
                    <p className="text-sm text-gray-600">Bookings</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4 text-center">
                    <div className="text-2xl font-bold text-orange-600">
                      {campaignModalData.sessions > 0 
                        ? ((campaignModalData.conversions / campaignModalData.sessions) * 100).toFixed(2)
                        : '0.00'
                      }%
                    </div>
                    <p className="text-sm text-gray-600">Conv. Rate</p>
                  </CardContent>
                </Card>
              </div>

              {/* Campaign Information */}
              <Card>
                <CardHeader>
                  <CardTitle>Campaign Information</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-gray-600">Source</label>
                      <p className="text-sm bg-gray-50 p-2 rounded">{campaignModalData.source}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-600">Medium</label>
                      <p className="text-sm bg-gray-50 p-2 rounded">{campaignModalData.medium}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Campaign Bookings */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Users className="w-4 h-4" />
                    Campaign Bookings ({campaignBookings.length})
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {bookingsLoading ? (
                    <div className="text-center py-4">Loading bookings...</div>
                  ) : campaignBookings.length > 0 ? (
                    <div className="overflow-x-auto">
                      <table className="w-full text-sm">
                        <thead>
                          <tr className="border-b">
                            <th className="text-left p-2">Name</th>
                            <th className="text-left p-2">Phone</th>
                            <th className="text-left p-2">Date</th>
                            <th className="text-left p-2">Time</th>
                            <th className="text-left p-2">Status</th>
                            <th className="text-left p-2">Created</th>
                          </tr>
                        </thead>
                        <tbody>
                          {campaignBookings.map((booking: any, index: number) => (
                            <tr key={index} className="border-b hover:bg-gray-50">
                              <td className="p-2 font-medium">{booking.name}</td>
                              <td className="p-2">{booking.phone}</td>
                              <td className="p-2">{new Date(booking.date).toLocaleDateString()}</td>
                              <td className="p-2">{booking.timeSlot}</td>
                              <td className="p-2">
                                <Badge 
                                  variant={
                                    booking.status === 'confirmed' ? 'default' :
                                    booking.status === 'completed' ? 'secondary' :
                                    booking.status === 'cancelled' ? 'destructive' : 'outline'
                                  }
                                >
                                  {booking.status}
                                </Badge>
                              </td>
                              <td className="p-2 text-gray-600">
                                {new Date(booking.createdAt).toLocaleDateString()}
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  ) : (
                    <div className="text-center py-8 text-gray-500">
                      No bookings found for this campaign
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Campaign Funnel (if available) */}
              {campaignFunnel && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <TrendingUp className="w-4 h-4" />
                      Conversion Funnel
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {campaignFunnel.funnelStages?.map((stage: any, index: number) => (
                        <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded">
                          <span className="font-medium">{stage.stage}</span>
                          <div className="flex items-center gap-4">
                            <span className="text-blue-600 font-bold">{stage.sessions}</span>
                            <span className="text-gray-600">({stage.percentage}%)</span>
                            {stage.dropOff > 0 && (
                              <span className="text-red-600 text-sm">-{stage.dropOff}</span>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                    
                    {campaignFunnel.conversionRate && (
                      <div className="mt-4 p-3 bg-green-50 rounded">
                        <div className="text-center">
                          <div className="text-2xl font-bold text-green-600">
                            {campaignFunnel.conversionRate}%
                          </div>
                          <p className="text-sm text-gray-600">Overall Conversion Rate</p>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}