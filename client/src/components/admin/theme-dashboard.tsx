import { useState, useEffect } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { useAuth } from "@/hooks/use-auth";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { useToast } from "@/hooks/use-toast";
import { Palette, Eye, RotateCcw, Check, X, Calendar, MapPin, Phone } from "lucide-react";

interface ThemeConfig {
  // Background & Layout
  backgroundGradientStart: string;
  backgroundGradientEnd: string;
  backgroundGradientDirection: string;
  
  // Button Colors
  buttonInactiveColor: string;
  buttonActiveColor: string;
  buttonDisabledColor: string;
  buttonTextColor: string;
  
  // Text Colors
  primaryTextColor: string;
  secondaryTextColor: string;
  headingColor: string;
  accentTextColor: string;
  
  // Input & Form Colors
  inputBorderColor: string;
  inputFocusColor: string;
  inputBackgroundColor: string;
  
  // Brand Colors
  primaryBrandColor: string;
  secondaryBrandColor: string;
  
  // Status Colors
  successColor: string;
  errorColor: string;
  warningColor: string;
  infoColor: string;
}

const defaultTheme: ThemeConfig = {
  backgroundGradientStart: "#D4B9FC",
  backgroundGradientEnd: "#AD8FF7",
  backgroundGradientDirection: "135deg",
  buttonInactiveColor: "rgb(192, 161, 240)",
  buttonActiveColor: "rgb(170, 138, 219)",
  buttonDisabledColor: "#E5E7EB",
  buttonTextColor: "#FFFFFF",
  primaryTextColor: "#1F2937",
  secondaryTextColor: "#6B7280",
  headingColor: "#111827",
  accentTextColor: "#8B5CF6",
  inputBorderColor: "#D1D5DB",
  inputFocusColor: "#D4B9FC",
  inputBackgroundColor: "#FFFFFF",
  primaryBrandColor: "#D4B9FC",
  secondaryBrandColor: "#AD8FF7",
  successColor: "#10B981",
  errorColor: "#EF4444",
  warningColor: "#F59E0B",
  infoColor: "#3B82F6"
};

export default function ThemeDashboard() {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const { user } = useAuth();
  const [theme, setTheme] = useState<ThemeConfig>(defaultTheme);
  const [previewMode, setPreviewMode] = useState(false);

  const isSupport = user?.isSupport || false;
  const settingsEndpoint = isSupport ? "/api/support/settings" : "/api/admin/settings";

  const { data: settings = {}, isLoading } = useQuery<any>({
    queryKey: [settingsEndpoint],
  });

  const updateThemeMutation = useMutation({
    mutationFn: async (themeData: Partial<ThemeConfig>) => {
      const response = await apiRequest("PUT", settingsEndpoint, themeData);
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [settingsEndpoint] });
      toast({ title: "Theme updated successfully" });
    },
    onError: () => {
      toast({
        title: "Error updating theme",
        variant: "destructive",
      });
    },
  });

  // Load existing theme settings
  useEffect(() => {
    if (settings) {
      setTheme(prev => ({
        ...prev,
        backgroundGradientStart: settings.backgroundGradientStart || prev.backgroundGradientStart,
        backgroundGradientEnd: settings.backgroundGradientEnd || prev.backgroundGradientEnd,
        backgroundGradientDirection: settings.backgroundGradientDirection || prev.backgroundGradientDirection,
        buttonInactiveColor: settings.buttonInactiveColor || prev.buttonInactiveColor,
        buttonActiveColor: settings.buttonActiveColor || prev.buttonActiveColor,
        buttonDisabledColor: settings.buttonDisabledColor || prev.buttonDisabledColor,
        buttonTextColor: settings.buttonTextColor || prev.buttonTextColor,
        primaryTextColor: settings.primaryTextColor || prev.primaryTextColor,
        secondaryTextColor: settings.secondaryTextColor || prev.secondaryTextColor,
        headingColor: settings.headingColor || prev.headingColor,
        accentTextColor: settings.accentTextColor || prev.accentTextColor,
        inputBorderColor: settings.inputBorderColor || prev.inputBorderColor,
        inputFocusColor: settings.inputFocusColor || prev.inputFocusColor,
        inputBackgroundColor: settings.inputBackgroundColor || prev.inputBackgroundColor,
        primaryBrandColor: settings.primaryBrandColor || prev.primaryBrandColor,
        secondaryBrandColor: settings.secondaryBrandColor || prev.secondaryBrandColor,
        successColor: settings.successColor || prev.successColor,
        errorColor: settings.errorColor || prev.errorColor,
        warningColor: settings.warningColor || prev.warningColor,
        infoColor: settings.infoColor || prev.infoColor,
      }));
    }
  }, [settings]);

  // Apply theme to preview - real-time updates
  useEffect(() => {
    const root = document.documentElement;
    
    // Always apply theme changes for live preview
    root.style.setProperty('--theme-bg-start', theme.backgroundGradientStart);
    root.style.setProperty('--theme-bg-end', theme.backgroundGradientEnd);
    root.style.setProperty('--theme-bg-direction', theme.backgroundGradientDirection);
    root.style.setProperty('--theme-btn-inactive', theme.buttonInactiveColor);
    root.style.setProperty('--theme-btn-active', theme.buttonActiveColor);
    root.style.setProperty('--theme-btn-disabled', theme.buttonDisabledColor);
    root.style.setProperty('--theme-btn-text', theme.buttonTextColor);
    root.style.setProperty('--theme-primary-text', theme.primaryTextColor);
    root.style.setProperty('--theme-secondary-text', theme.secondaryTextColor);
    root.style.setProperty('--theme-heading', theme.headingColor);
    root.style.setProperty('--theme-accent', theme.accentTextColor);
    root.style.setProperty('--theme-input-border', theme.inputBorderColor);
    root.style.setProperty('--theme-input-focus', theme.inputFocusColor);
    root.style.setProperty('--theme-input-bg', theme.inputBackgroundColor);
    root.style.setProperty('--theme-brand-primary', theme.primaryBrandColor);
    root.style.setProperty('--theme-brand-secondary', theme.secondaryBrandColor);
    root.style.setProperty('--theme-success', theme.successColor);
    root.style.setProperty('--theme-error', theme.errorColor);
    root.style.setProperty('--theme-warning', theme.warningColor);
    root.style.setProperty('--theme-info', theme.infoColor);
  }, [theme]);

  const handleColorChange = (property: keyof ThemeConfig, value: string) => {
    setTheme(prev => ({
      ...prev,
      [property]: value
    }));
  };

  const handleSaveTheme = () => {
    updateThemeMutation.mutate(theme);
    // Apply theme immediately for live preview
    const root = document.documentElement;
    Object.entries(theme).forEach(([key, value]) => {
      const cssVar = key.replace(/([A-Z])/g, '-$1').toLowerCase();
      root.style.setProperty(`--theme-${cssVar}`, value);
    });
  };

  const handleResetTheme = () => {
    setTheme(defaultTheme);
  };

  const rgbToHex = (rgb: string): string => {
    if (rgb.startsWith('#')) return rgb;
    const match = rgb.match(/rgb\((\d+),\s*(\d+),\s*(\d+)\)/);
    if (match) {
      const r = parseInt(match[1]).toString(16).padStart(2, '0');
      const g = parseInt(match[2]).toString(16).padStart(2, '0');
      const b = parseInt(match[3]).toString(16).padStart(2, '0');
      return `#${r}${g}${b}`;
    }
    return rgb;
  };

  const hexToRgb = (hex: string): string => {
    if (hex.startsWith('rgb')) return hex;
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    if (result) {
      const r = parseInt(result[1], 16);
      const g = parseInt(result[2], 16);
      const b = parseInt(result[3], 16);
      return `rgb(${r}, ${g}, ${b})`;
    }
    return hex;
  };

  const ColorInput = ({ 
    label, 
    value, 
    onChange, 
    property 
  }: { 
    label: string; 
    value: string; 
    onChange: (property: keyof ThemeConfig, value: string) => void;
    property: keyof ThemeConfig;
  }) => (
    <div className="space-y-2">
      <Label className="text-sm font-medium">{label}</Label>
      <div className="flex gap-2">
        <div className="flex-1">
          <Input
            type="color"
            value={rgbToHex(value)}
            onChange={(e) => onChange(property, e.target.value)}
            className="h-10 w-full"
          />
        </div>
        <div className="flex-2">
          <Input
            type="text"
            value={value}
            onChange={(e) => onChange(property, e.target.value)}
            placeholder="#000000 or rgb(0,0,0)"
            className="text-xs"
          />
        </div>
      </div>
      <div 
        className="h-6 w-full rounded border-2 border-gray-300"
        style={{ backgroundColor: value }}
      />
    </div>
  );

  if (isLoading) {
    return <div className="text-center p-8">Loading theme settings...</div>;
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Palette className="h-6 w-6 text-purple-600" />
          <h1 className="text-2xl font-bold">Theme Dashboard</h1>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => setPreviewMode(!previewMode)}
            className="flex items-center gap-2"
          >
            <Eye className="h-4 w-4" />
            {previewMode ? "Exit Preview" : "Live Preview"}
          </Button>
          <Button
            variant="outline"
            onClick={handleResetTheme}
            className="flex items-center gap-2"
          >
            <RotateCcw className="h-4 w-4" />
            Reset to Default
          </Button>
          <Button
            onClick={handleSaveTheme}
            disabled={updateThemeMutation.isPending}
            className="btn-kult-active flex items-center gap-2"
          >
            <Check className="h-4 w-4" />
            {updateThemeMutation.isPending ? "Saving..." : "Save Theme"}
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Theme Controls */}
        <div className="lg:col-span-2 space-y-6">
          <Tabs defaultValue="background" className="w-full">
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="background">Background</TabsTrigger>
              <TabsTrigger value="buttons">Buttons</TabsTrigger>
              <TabsTrigger value="text">Text</TabsTrigger>
              <TabsTrigger value="inputs">Inputs</TabsTrigger>
              <TabsTrigger value="status">Status</TabsTrigger>
            </TabsList>

            <TabsContent value="background" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Background & Layout</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <ColorInput
                    label="Gradient Start Color"
                    value={theme.backgroundGradientStart}
                    onChange={handleColorChange}
                    property="backgroundGradientStart"
                  />
                  <ColorInput
                    label="Gradient End Color"
                    value={theme.backgroundGradientEnd}
                    onChange={handleColorChange}
                    property="backgroundGradientEnd"
                  />
                  <div className="space-y-2">
                    <Label>Gradient Direction</Label>
                    <Input
                      value={theme.backgroundGradientDirection}
                      onChange={(e) => handleColorChange('backgroundGradientDirection', e.target.value)}
                      placeholder="135deg, to right, etc."
                    />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="buttons" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Button Colors</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <ColorInput
                    label="Inactive Button Color"
                    value={theme.buttonInactiveColor}
                    onChange={handleColorChange}
                    property="buttonInactiveColor"
                  />
                  <ColorInput
                    label="Active Button Color"
                    value={theme.buttonActiveColor}
                    onChange={handleColorChange}
                    property="buttonActiveColor"
                  />
                  <ColorInput
                    label="Disabled Button Color"
                    value={theme.buttonDisabledColor}
                    onChange={handleColorChange}
                    property="buttonDisabledColor"
                  />
                  <ColorInput
                    label="Button Text Color"
                    value={theme.buttonTextColor}
                    onChange={handleColorChange}
                    property="buttonTextColor"
                  />
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="text" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Text Colors</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <ColorInput
                    label="Primary Text Color"
                    value={theme.primaryTextColor}
                    onChange={handleColorChange}
                    property="primaryTextColor"
                  />
                  <ColorInput
                    label="Secondary Text Color"
                    value={theme.secondaryTextColor}
                    onChange={handleColorChange}
                    property="secondaryTextColor"
                  />
                  <ColorInput
                    label="Heading Color"
                    value={theme.headingColor}
                    onChange={handleColorChange}
                    property="headingColor"
                  />
                  <ColorInput
                    label="Accent Text Color"
                    value={theme.accentTextColor}
                    onChange={handleColorChange}
                    property="accentTextColor"
                  />
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="inputs" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Input & Form Colors</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <ColorInput
                    label="Input Border Color"
                    value={theme.inputBorderColor}
                    onChange={handleColorChange}
                    property="inputBorderColor"
                  />
                  <ColorInput
                    label="Input Focus Color"
                    value={theme.inputFocusColor}
                    onChange={handleColorChange}
                    property="inputFocusColor"
                  />
                  <ColorInput
                    label="Input Background Color"
                    value={theme.inputBackgroundColor}
                    onChange={handleColorChange}
                    property="inputBackgroundColor"
                  />
                  <ColorInput
                    label="Primary Brand Color"
                    value={theme.primaryBrandColor}
                    onChange={handleColorChange}
                    property="primaryBrandColor"
                  />
                  <ColorInput
                    label="Secondary Brand Color"
                    value={theme.secondaryBrandColor}
                    onChange={handleColorChange}
                    property="secondaryBrandColor"
                  />
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="status" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Status Colors</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <ColorInput
                    label="Success Color"
                    value={theme.successColor}
                    onChange={handleColorChange}
                    property="successColor"
                  />
                  <ColorInput
                    label="Error Color"
                    value={theme.errorColor}
                    onChange={handleColorChange}
                    property="errorColor"
                  />
                  <ColorInput
                    label="Warning Color"
                    value={theme.warningColor}
                    onChange={handleColorChange}
                    property="warningColor"
                  />
                  <ColorInput
                    label="Info Color"
                    value={theme.infoColor}
                    onChange={handleColorChange}
                    property="infoColor"
                  />
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        {/* Live Preview */}
        <div className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Eye className="h-5 w-5" />
                Live Preview
              </CardTitle>
            </CardHeader>
            <CardContent>
              {/* Preview Container */}
              <div 
                className="p-6 rounded-lg border-2 border-gray-200 space-y-4"
                style={{
                  background: `linear-gradient(${theme.backgroundGradientDirection}, ${theme.backgroundGradientStart} 0%, ${theme.backgroundGradientEnd} 100%)`
                }}
              >
                {/* Heading Preview */}
                <h2 
                  className="text-xl font-bold"
                  style={{ color: theme.headingColor }}
                >
                  Where are you located?
                </h2>
                
                {/* Text Preview */}
                <p 
                  className="text-sm"
                  style={{ color: theme.secondaryTextColor }}
                >
                  Enter your postal code to check if we serve your area
                </p>

                {/* Input Preview */}
                <div className="space-y-3">
                  <input
                    type="text"
                    placeholder="Enter 6-digit postal code"
                    className="w-full px-4 py-3 rounded-2xl border-2 focus:outline-none transition-all"
                    style={{
                      backgroundColor: theme.inputBackgroundColor,
                      borderColor: theme.inputBorderColor,
                      color: theme.primaryTextColor,
                    }}
                    onFocus={(e) => {
                      e.target.style.borderColor = theme.inputFocusColor;
                    }}
                    onBlur={(e) => {
                      e.target.style.borderColor = theme.inputBorderColor;
                    }}
                  />

                  {/* Button Preview */}
                  <button
                    className="w-full py-3 rounded-2xl font-semibold transition-all"
                    style={{
                      backgroundColor: theme.buttonInactiveColor,
                      color: theme.buttonTextColor,
                      border: 'none'
                    }}
                  >
                    Check Availability
                  </button>

                  <button
                    className="w-full py-3 rounded-2xl font-semibold transition-all"
                    style={{
                      backgroundColor: theme.buttonActiveColor,
                      color: theme.buttonTextColor,
                      border: 'none'
                    }}
                  >
                    Continue to Details
                  </button>

                  <button
                    className="w-full py-3 rounded-2xl font-semibold transition-all opacity-60 cursor-not-allowed"
                    style={{
                      backgroundColor: theme.buttonDisabledColor,
                      color: '#9CA3AF',
                      border: 'none'
                    }}
                    disabled
                  >
                    Disabled Button
                  </button>
                </div>

                {/* Status Examples */}
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Check className="h-4 w-4" style={{ color: theme.successColor }} />
                    <span style={{ color: theme.successColor }} className="text-sm">
                      Success message
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <X className="h-4 w-4" style={{ color: theme.errorColor }} />
                    <span style={{ color: theme.errorColor }} className="text-sm">
                      Error message
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4" style={{ color: theme.warningColor }} />
                    <span style={{ color: theme.warningColor }} className="text-sm">
                      Warning message
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4" style={{ color: theme.infoColor }} />
                    <span style={{ color: theme.infoColor }} className="text-sm">
                      Info message
                    </span>
                  </div>
                </div>
              </div>

              {/* Color Values Display */}
              <div className="mt-4 p-3 bg-gray-50 rounded-lg">
                <h4 className="font-medium text-sm mb-2">Current Theme Values</h4>
                <div className="text-xs space-y-1 font-mono">
                  <div>Inactive: {theme.buttonInactiveColor}</div>
                  <div>Active: {theme.buttonActiveColor}</div>
                  <div>Gradient: {theme.backgroundGradientStart} → {theme.backgroundGradientEnd}</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}