import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { useAuth } from "@/hooks/use-auth";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useToast } from "@/hooks/use-toast";
import { Alarm<PERSON>lock, Bell, Clock } from "lucide-react";

export default function SettingsManagement() {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const { user } = useAuth();
  
  const isSupport = user?.isSupport || false;
  const settingsEndpoint = isSupport ? "/api/support/settings" : "/api/admin/settings";

  const { data: settings = {}, isLoading } = useQuery<any>({
    queryKey: [settingsEndpoint],
  });

  const updateSettingsMutation = useMutation({
    mutationFn: async (data: any) => {
      const response = await apiRequest("PUT", settingsEndpoint, data);
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [settingsEndpoint] });
      toast({ title: "Settings updated successfully" });
    },
    onError: () => {
      toast({
        title: "Error updating settings",
        variant: "destructive",
      });
    },
  });



  const handleTemplateSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const formData = new FormData(e.target as HTMLFormElement);
    
    // Build update object with only the fields present in this form
    const updateData: any = {};
    
    // Check which SMS template fields are present
    if (formData.has("confirmationSmsTemplate")) updateData.confirmationSmsTemplate = formData.get("confirmationSmsTemplate");
    if (formData.has("cancellationSmsTemplate")) updateData.cancellationSmsTemplate = formData.get("cancellationSmsTemplate");
    if (formData.has("rescheduleSmsTemplate")) updateData.rescheduleSmsTemplate = formData.get("rescheduleSmsTemplate");
    if (formData.has("reminderSmsTemplate")) updateData.reminderSmsTemplate = formData.get("reminderSmsTemplate");
    
    // Legacy template fields (for backward compatibility)
    if (formData.has("smsTemplate")) updateData.smsTemplate = formData.get("smsTemplate");
    
    updateSettingsMutation.mutate(updateData);
  };

  const handleTimingSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const formData = new FormData(e.target as HTMLFormElement);
    
    // Build update object with only the fields present in this form
    const updateData: any = {};
    
    // Master settings
    if (formData.has("remindersEnabled")) updateData.remindersEnabled = formData.get("remindersEnabled") === "on";
    
    // First reminder
    if (formData.has("firstReminderEnabled")) updateData.firstReminderEnabled = formData.get("firstReminderEnabled") === "on";
    if (formData.has("firstReminderHours")) updateData.firstReminderHours = parseInt(formData.get("firstReminderHours") as string);
    if (formData.has("firstReminderSms")) updateData.firstReminderSms = formData.get("firstReminderSms") === "on";
    
    // Second reminder
    if (formData.has("secondReminderEnabled")) updateData.secondReminderEnabled = formData.get("secondReminderEnabled") === "on";
    if (formData.has("secondReminderHours")) updateData.secondReminderHours = parseInt(formData.get("secondReminderHours") as string);
    if (formData.has("secondReminderSms")) updateData.secondReminderSms = formData.get("secondReminderSms") === "on";
    
    // Final reminder
    if (formData.has("finalReminderEnabled")) updateData.finalReminderEnabled = formData.get("finalReminderEnabled") === "on";
    if (formData.has("finalReminderHours")) updateData.finalReminderHours = parseInt(formData.get("finalReminderHours") as string);
    if (formData.has("finalReminderSms")) updateData.finalReminderSms = formData.get("finalReminderSms") === "on";
    
    updateSettingsMutation.mutate(updateData);
  };

  const handleRulesSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const formData = new FormData(e.target as HTMLFormElement);
    
    const updateData: any = {};
    
    // Booking rules
    if (formData.has("appointmentDuration")) updateData.appointmentDuration = parseInt(formData.get("appointmentDuration") as string);
    if (formData.has("gapBetweenAppointments")) updateData.gapBetweenAppointments = parseInt(formData.get("gapBetweenAppointments") as string);
    if (formData.has("shiftStartBuffer")) updateData.shiftStartBuffer = parseInt(formData.get("shiftStartBuffer") as string);
    if (formData.has("timeSlotInterval")) updateData.timeSlotInterval = parseInt(formData.get("timeSlotInterval") as string);
    if (formData.has("maxBookingDaysAhead")) updateData.maxBookingDaysAhead = parseInt(formData.get("maxBookingDaysAhead") as string);
    if (formData.has("minAdvanceHours")) updateData.minAdvanceHours = parseInt(formData.get("minAdvanceHours") as string);
    if (formData.has("maxBookingsPerRep")) updateData.maxBookingsPerRep = parseInt(formData.get("maxBookingsPerRep") as string);
    if (formData.has("workingDaysStart")) updateData.workingDaysStart = formData.get("workingDaysStart");
    if (formData.has("workingDaysEnd")) updateData.workingDaysEnd = formData.get("workingDaysEnd");
    if (formData.has("defaultShiftStart")) updateData.defaultShiftStart = formData.get("defaultShiftStart");
    if (formData.has("defaultShiftEnd")) updateData.defaultShiftEnd = formData.get("defaultShiftEnd");
    if (formData.has("shiftEndBuffer")) updateData.shiftEndBuffer = parseInt(formData.get("shiftEndBuffer") as string);
    
    updateSettingsMutation.mutate(updateData);
  };

  if (isLoading) {
    return <div className="text-center p-8">Loading settings...</div>;
  }

  return (
    <div className="space-y-6">
      <Tabs defaultValue="templates" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="templates">Notification Templates</TabsTrigger>
          <TabsTrigger value="timing">Notification Timing</TabsTrigger>
          <TabsTrigger value="rules">Business Rules</TabsTrigger>
        </TabsList>

        <TabsContent value="templates">
          <div className="space-y-6">
            {/* Confirmation Templates */}
            <Card>
              <CardHeader>
                <CardTitle>Confirmation Templates</CardTitle>
                <p className="text-sm text-gray-600">Sent when a booking is first created</p>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleTemplateSubmit} className="space-y-4">
                  <div>
                    <Label htmlFor="confirmationSmsTemplate">Confirmation SMS Template</Label>
                    <Textarea
                      id="confirmationSmsTemplate"
                      name="confirmationSmsTemplate"
                      defaultValue={settings?.confirmationSmsTemplate}
                      rows={3}
                      placeholder="Use {{name}}, {{date}}, {{time}} as placeholders"
                    />
                  </div>
                  <Button 
                    type="submit" 
                    className="btn-kult-active"
                    disabled={updateSettingsMutation.isPending}
                  >
                    {updateSettingsMutation.isPending ? "Saving..." : "Save Confirmation Template"}
                  </Button>
                </form>
              </CardContent>
            </Card>

            {/* Cancellation Templates */}
            <Card>
              <CardHeader>
                <CardTitle>Cancellation Templates</CardTitle>
                <p className="text-sm text-gray-600">Sent when a booking is cancelled</p>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleTemplateSubmit} className="space-y-4">
                  <div>
                    <Label htmlFor="cancellationSmsTemplate">Cancellation SMS Template</Label>
                    <Textarea
                      id="cancellationSmsTemplate"
                      name="cancellationSmsTemplate"
                      defaultValue={settings?.cancellationSmsTemplate}
                      rows={3}
                      placeholder="Use {{name}}, {{date}}, {{time}} as placeholders"
                    />
                  </div>
                  <Button 
                    type="submit" 
                    className="btn-kult-active"
                    disabled={updateSettingsMutation.isPending}
                  >
                    {updateSettingsMutation.isPending ? "Saving..." : "Save Cancellation Template"}
                  </Button>
                </form>
              </CardContent>
            </Card>

            {/* Reschedule Templates */}
            <Card>
              <CardHeader>
                <CardTitle>Reschedule Templates</CardTitle>
                <p className="text-sm text-gray-600">Sent when a booking is rescheduled</p>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleTemplateSubmit} className="space-y-4">
                  <div>
                    <Label htmlFor="rescheduleSmsTemplate">Reschedule SMS Template</Label>
                    <Textarea
                      id="rescheduleSmsTemplate"
                      name="rescheduleSmsTemplate"
                      defaultValue={settings?.rescheduleSmsTemplate}
                      rows={3}
                      placeholder="Use {{name}}, {{date}}, {{time}} as placeholders"
                    />
                  </div>
                  <Button 
                    type="submit" 
                    className="btn-kult-active"
                    disabled={updateSettingsMutation.isPending}
                  >
                    {updateSettingsMutation.isPending ? "Saving..." : "Save Reschedule Template"}
                  </Button>
                </form>
              </CardContent>
            </Card>

            {/* Reminder Templates */}
            <Card>
              <CardHeader>
                <CardTitle>Reminder Templates</CardTitle>
                <p className="text-sm text-gray-600">Sent as appointment reminders</p>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleTemplateSubmit} className="space-y-4">
                  <div>
                    <Label htmlFor="reminderSmsTemplate">Reminder SMS Template</Label>
                    <Textarea
                      id="reminderSmsTemplate"
                      name="reminderSmsTemplate"
                      defaultValue={settings?.reminderSmsTemplate}
                      rows={3}
                      placeholder="Use {{name}}, {{date}}, {{time}} as placeholders"
                    />
                  </div>
                  <Button 
                    type="submit" 
                    className="btn-kult-active"
                    disabled={updateSettingsMutation.isPending}
                  >
                    {updateSettingsMutation.isPending ? "Saving..." : "Save Reminder Template"}
                  </Button>
                </form>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="timing">
          <div className="space-y-6">
            {/* Overall Reminder Settings */}
            <Card>
              <CardHeader>
                <CardTitle>Master Reminder Settings</CardTitle>
                <p className="text-sm text-gray-600">Enable or disable all automatic reminders</p>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleTimingSubmit} className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="remindersEnabled"
                      name="remindersEnabled"
                      defaultChecked={settings?.remindersEnabled}
                    />
                    <Label htmlFor="remindersEnabled">Enable automatic reminders</Label>
                  </div>
                  <Button 
                    type="submit" 
                    className="bg-primary-lilac hover:bg-deep-lilac"
                    disabled={updateSettingsMutation.isPending}
                  >
                    {updateSettingsMutation.isPending ? "Saving..." : "Save Master Settings"}
                  </Button>
                </form>
              </CardContent>
            </Card>

            {/* First Reminder */}
            <Card>
              <CardHeader>
                <CardTitle>First Reminder</CardTitle>
                <p className="text-sm text-gray-600">Initial reminder notification</p>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleTimingSubmit} className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="firstReminderEnabled"
                      name="firstReminderEnabled"
                      defaultChecked={settings?.firstReminderEnabled}
                    />
                    <Label htmlFor="firstReminderEnabled">Enable first reminder</Label>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="firstReminderHours">Hours before appointment</Label>
                      <Input
                        id="firstReminderHours"
                        name="firstReminderHours"
                        type="number"
                        min="1"
                        max="168"
                        defaultValue={settings?.firstReminderHours || 24}
                        placeholder="24"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="firstReminderSms"
                        name="firstReminderSms"
                        defaultChecked={settings?.firstReminderSms}
                      />
                      <Label htmlFor="firstReminderSms">Enable SMS notifications</Label>
                    </div>
                  </div>

                  <Button 
                    type="submit" 
                    className="bg-primary-lilac hover:bg-deep-lilac"
                    disabled={updateSettingsMutation.isPending}
                  >
                    {updateSettingsMutation.isPending ? "Saving..." : "Save First Reminder"}
                  </Button>
                </form>
              </CardContent>
            </Card>

            {/* Second Reminder */}
            <Card>
              <CardHeader>
                <CardTitle>Second Reminder</CardTitle>
                <p className="text-sm text-gray-600">Follow-up reminder notification</p>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleTimingSubmit} className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="secondReminderEnabled"
                      name="secondReminderEnabled"
                      defaultChecked={settings?.secondReminderEnabled}
                    />
                    <Label htmlFor="secondReminderEnabled">Enable second reminder</Label>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="secondReminderHours">Hours before appointment</Label>
                      <Input
                        id="secondReminderHours"
                        name="secondReminderHours"
                        type="number"
                        min="1"
                        max="168"
                        defaultValue={settings?.secondReminderHours || 12}
                        placeholder="12"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="secondReminderSms"
                        name="secondReminderSms"
                        defaultChecked={settings?.secondReminderSms}
                      />
                      <Label htmlFor="secondReminderSms">Enable SMS notifications</Label>
                    </div>
                  </div>

                  <Button 
                    type="submit" 
                    className="bg-primary-lilac hover:bg-deep-lilac"
                    disabled={updateSettingsMutation.isPending}
                  >
                    {updateSettingsMutation.isPending ? "Saving..." : "Save Second Reminder"}
                  </Button>
                </form>
              </CardContent>
            </Card>

            {/* Final Reminder */}
            <Card>
              <CardHeader>
                <CardTitle>Final Reminder</CardTitle>
                <p className="text-sm text-gray-600">Last reminder before appointment</p>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleTimingSubmit} className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="finalReminderEnabled"
                      name="finalReminderEnabled"
                      defaultChecked={settings?.finalReminderEnabled}
                    />
                    <Label htmlFor="finalReminderEnabled">Enable final reminder</Label>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="finalReminderHours">Hours before appointment</Label>
                      <Input
                        id="finalReminderHours"
                        name="finalReminderHours"
                        type="number"
                        min="1"
                        max="24"
                        defaultValue={settings?.finalReminderHours || 3}
                        placeholder="3"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="finalReminderSms"
                        name="finalReminderSms"
                        defaultChecked={settings?.finalReminderSms}
                      />
                      <Label htmlFor="finalReminderSms">Enable SMS notifications</Label>
                    </div>
                  </div>

                  <Button 
                    type="submit" 
                    className="bg-primary-lilac hover:bg-deep-lilac"
                    disabled={updateSettingsMutation.isPending}
                  >
                    {updateSettingsMutation.isPending ? "Saving..." : "Save Final Reminder"}
                  </Button>
                </form>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="rules">
          <Card>
            <CardHeader>
              <CardTitle>Global Booking Rules</CardTitle>
              <p className="text-sm text-gray-600">Configure system-wide business rules for appointment scheduling</p>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleRulesSubmit} className="space-y-6">

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <Label htmlFor="appointmentDuration">Appointment Duration (minutes)</Label>
                    <Input
                      id="appointmentDuration"
                      name="appointmentDuration"
                      type="number"
                      min="30"
                      max="180"
                      defaultValue={settings?.appointmentDuration || 90}
                      placeholder="90"
                    />
                    <p className="text-xs text-gray-500 mt-1">How long each perfume trial appointment lasts</p>
                  </div>

                  <div>
                    <Label htmlFor="gapBetweenAppointments">Gap Between Appointments (minutes)</Label>
                    <Input
                      id="gapBetweenAppointments"
                      name="gapBetweenAppointments"
                      type="number"
                      min="0"
                      max="180"
                      defaultValue={settings?.gapBetweenAppointments || 60}
                      placeholder="60"
                    />
                    <p className="text-xs text-gray-500 mt-1">Minimum break time between appointments for each representative</p>
                  </div>

                  <div>
                    <Label htmlFor="shiftStartBuffer">Shift Start Buffer (minutes)</Label>
                    <Input
                      id="shiftStartBuffer"
                      name="shiftStartBuffer"
                      type="number"
                      min="0"
                      max="180"
                      defaultValue={settings?.shiftStartBuffer || 90}
                      placeholder="90"
                    />
                    <p className="text-xs text-gray-500 mt-1">How long after shift start before first appointment can be scheduled</p>
                  </div>

                  <div>
                    <Label htmlFor="timeSlotInterval">Time Slot Interval (minutes)</Label>
                    <Input
                      id="timeSlotInterval"
                      name="timeSlotInterval"
                      type="number"
                      min="15"
                      max="60"
                      defaultValue={settings?.timeSlotInterval || 30}
                      placeholder="30"
                    />
                    <p className="text-xs text-gray-500 mt-1">How often new time slots are available (e.g., every 30 minutes)</p>
                  </div>

                  <div>
                    <Label htmlFor="maxBookingDaysAhead">Max Booking Days Ahead</Label>
                    <Input
                      id="maxBookingDaysAhead"
                      name="maxBookingDaysAhead"
                      type="number"
                      min="1"
                      max="30"
                      defaultValue={settings?.maxBookingDaysAhead || 7}
                      placeholder="7"
                    />
                    <p className="text-xs text-gray-500 mt-1">How many days in advance customers can book appointments</p>
                  </div>

                  <div>
                    <Label htmlFor="minAdvanceHours">Minimum Advance Hours</Label>
                    <Input
                      id="minAdvanceHours"
                      name="minAdvanceHours"
                      type="number"
                      min="1"
                      max="48"
                      defaultValue={settings?.minAdvanceHours || 1}
                      placeholder="1"
                    />
                    <p className="text-xs text-gray-500 mt-1">How far in advance customers must book (e.g., 1 = can't book appointments starting in less than 1 hour)</p>
                  </div>

                  <div>
                    <Label htmlFor="shiftEndBuffer">Shift End Buffer (minutes)</Label>
                    <Input
                      id="shiftEndBuffer"
                      name="shiftEndBuffer"
                      type="number"
                      min="0"
                      max="120"
                      defaultValue={settings?.shiftEndBuffer || 30}
                      placeholder="30"
                    />
                    <p className="text-xs text-gray-500 mt-1">Last appointment must end this many minutes before shift ends</p>
                  </div>

                  <div>
                    <Label htmlFor="maxBookingsPerRep">Max Bookings per Rep per Day</Label>
                    <Input
                      id="maxBookingsPerRep"
                      name="maxBookingsPerRep"
                      type="number"
                      min="1"
                      max="20"
                      defaultValue={settings?.maxBookingsPerRep || 8}
                      placeholder="8"
                    />
                    <p className="text-xs text-gray-500 mt-1">Maximum appointments one representative can handle in a day</p>
                  </div>

                  <div>
                    <Label htmlFor="defaultShiftStart">Default Shift Start Time</Label>
                    <Input
                      id="defaultShiftStart"
                      name="defaultShiftStart"
                      type="time"
                      defaultValue={settings?.defaultShiftStart || "10:00"}
                    />
                    <p className="text-xs text-gray-500 mt-1">Default start time for new sales representatives</p>
                  </div>

                  <div>
                    <Label htmlFor="defaultShiftEnd">Default Shift End Time</Label>
                    <Input
                      id="defaultShiftEnd"
                      name="defaultShiftEnd"
                      type="time"
                      defaultValue={settings?.defaultShiftEnd || "20:00"}
                    />
                    <p className="text-xs text-gray-500 mt-1">Default end time for new sales representatives</p>
                  </div>
                </div>

                <div className="pt-6 border-t">
                  <Button 
                    type="submit" 
                    className="btn-kult-active w-full"
                    disabled={updateSettingsMutation.isPending}
                  >
                    {updateSettingsMutation.isPending ? "Saving New Settings..." : "Save New Settings"}
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
