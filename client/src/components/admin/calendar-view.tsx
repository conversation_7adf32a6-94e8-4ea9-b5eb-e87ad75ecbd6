import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Calendar } from "@/components/ui/calendar";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import { 
  format,
  startOfMonth, 
  endOfMonth, 
  addDays, 
  startOfWeek, 
  endOfWeek, 
  startOfDay,
  endOfDay,
  eachDayOfInterval,
  isSameDay,
  parseISO,
  addMinutes,
  setHours,
  setMinutes
} from "date-fns";
import { toApiDate, toDisplayDate, toShortDate } from "@shared/date-utils";
import { 
  ChevronLeft, 
  ChevronRight, 
  Plus, 
  Clock,
  CalendarPlus,
  Calendar as CalendarIcon,
  Users,
  Settings,
  User,
  Phone,
  MapPin,
  CheckCircle,
  XCircle,
  AlertCircle,
  Globe,
  TrendingUp,
  Play,
  Shield,
  CheckCircle2
} from "lucide-react";

type ViewMode = 'day' | 'week' | 'month';

interface NewAppointmentData {
  name: string;
  phone: string;
  address: string;
  postalCode: string;
  date: string;
  timeSlot: string;
}

interface BlockTimeData {
  date: string;
  startTime: string;
  endTime: string;
  reason: string;
  selectedReps: string[];
}

export default function CalendarView() {
  const [currentDate, setCurrentDate] = useState<Date>(new Date());
  const [viewMode, setViewMode] = useState<ViewMode>('week');
  const [selectedTimeSlot, setSelectedTimeSlot] = useState<{ date: Date; time: string } | null>(null);
  const [isNewAppointmentOpen, setIsNewAppointmentOpen] = useState(false);
  const [isBlockTimeOpen, setIsBlockTimeOpen] = useState(false);
  const [isTimeSlotActionOpen, setIsTimeSlotActionOpen] = useState(false);
  const [selectedBooking, setSelectedBooking] = useState<any>(null);
  const [selectedBookings, setSelectedBookings] = useState<any[]>([]);
  const [isBookingDetailsOpen, setIsBookingDetailsOpen] = useState(false);
  const [isMultipleBookingsOpen, setIsMultipleBookingsOpen] = useState(false);
  const [newAppointment, setNewAppointment] = useState<NewAppointmentData>({
    name: '',
    phone: '',
    address: '',
    postalCode: '',
    date: '',
    timeSlot: ''
  });
  const [blockTimeData, setBlockTimeData] = useState<BlockTimeData>({
    date: toApiDate(new Date()),
    startTime: '09:00',
    endTime: '12:00',
    reason: 'Blocked',
    selectedReps: []
  });

  // State for detailed booking view
  const [funnelJourney, setFunnelJourney] = useState<any>(null);
  const [funnelLoading, setFunnelLoading] = useState(false);

  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Utility function to format time to 12-hour format
  const formatTimeTo12Hour = (time24: string) => {
    try {
      if (!time24 || typeof time24 !== 'string') {
        return 'N/A';
      }
      
      const [hours, minutes] = time24.split(':').map(Number);
      
      // Validate parsed values
      if (isNaN(hours) || isNaN(minutes)) {
        return time24; // Return original if can't parse
      }
      
      const period = hours >= 12 ? 'PM' : 'AM';
      const hours12 = hours === 0 ? 12 : hours > 12 ? hours - 12 : hours;
      return `${hours12}:${minutes.toString().padStart(2, '0')} ${period}`;
    } catch (error) {
      console.error('Error formatting time:', error, time24);
      return time24 || 'N/A';
    }
  };

  // Status badge component
  const getStatusBadge = (status: string) => {
    try {
      const statusConfig = {
        confirmed: { color: 'bg-green-100 text-green-800', label: 'Confirmed' },
        cancelled: { color: 'bg-red-100 text-red-800', label: 'Cancelled' },
        completed: { color: 'bg-blue-100 text-blue-800', label: 'Completed' },
        pending: { color: 'bg-yellow-100 text-yellow-800', label: 'Pending' },
      };
      
      const safeStatus = status && typeof status === 'string' ? status.toLowerCase() : 'pending';
      const config = statusConfig[safeStatus as keyof typeof statusConfig] || statusConfig.pending;
      
      return (
        <span className={`px-2 py-1 rounded-full text-xs font-medium ${config.color}`}>
          {config.label}
        </span>
      );
    } catch (error) {
      console.error('Error in getStatusBadge:', error);
      return (
        <span className="px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
          Unknown
        </span>
      );
    }
  };

  // Get date range based on view mode
  const getDateRange = () => {
    switch (viewMode) {
      case 'day':
        return { 
          start: startOfDay(currentDate), 
          end: endOfDay(currentDate)
        };
      case 'week':
        return { 
          start: startOfWeek(currentDate, { weekStartsOn: 1 }), 
          end: endOfWeek(currentDate, { weekStartsOn: 1 })
        };
      case 'month':
        return { 
          start: startOfMonth(currentDate), 
          end: endOfMonth(currentDate)
        };
    }
  };

  const { start: rangeStart, end: rangeEnd } = getDateRange();

  const { data: bookings, isLoading } = useQuery({
    queryKey: ["/api/admin/bookings/range", rangeStart.toISOString(), rangeEnd.toISOString()],
    queryFn: async () => {
      const response = await fetch(
        `/api/admin/bookings/range?start=${rangeStart.toISOString()}&end=${rangeEnd.toISOString()}&status=all`,
        {
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
      if (!response.ok) {
        throw new Error('Failed to fetch bookings');
      }
      return response.json();
    },
  });

  const { data: salesReps } = useQuery({
    queryKey: ["/api/admin/reps"],
    queryFn: async () => {
      const response = await fetch("/api/admin/reps", {
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      if (!response.ok) {
        throw new Error('Failed to fetch sales reps');
      }
      return response.json();
    },
  });

  const { data: postalCodes } = useQuery({
    queryKey: ["/api/admin/postal-codes"],
    queryFn: async () => {
      const response = await fetch("/api/admin/postal-codes", {
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      if (!response.ok) {
        throw new Error('Failed to fetch postal codes');
      }
      return response.json();
    },
  });

  const { data: blockedSlots } = useQuery({
    queryKey: ["/api/admin/blocked-slots", rangeStart.toISOString(), rangeEnd.toISOString()],
    queryFn: async () => {
      const response = await fetch(
        `/api/admin/blocked-slots?start=${rangeStart.toISOString()}&end=${rangeEnd.toISOString()}`,
        {
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
      if (!response.ok) {
        throw new Error('Failed to fetch blocked slots');
      }
      return response.json();
    },
  });

  const createAppointmentMutation = useMutation({
    mutationFn: async (data: NewAppointmentData) => {
      // STRICT FRONTEND VALIDATION: Enforce business rules before API call
      if (data.date && data.timeSlot) {
        const selectedDate = new Date(data.date);
        const slotBookings = getBookingsForTimeSlot(selectedDate, data.timeSlot);
        const isBlocked = isTimeSlotBlocked(selectedDate, data.timeSlot);
        
        if (slotBookings.length > 0 || isBlocked) {
          throw new Error("This time slot is no longer available due to business rules (60-minute gap between appointments, shift hours, or existing bookings). Please select a different time.");
        }
        
        // Double-check with backend availability API
        const availabilityResponse = await fetch(`/api/availability/${data.date}`, {
          credentials: 'include',
        });
        const availabilityData = await availabilityResponse.json();
        
        if (!availabilityData.available || !availabilityData.timeSlots.includes(data.timeSlot)) {
          throw new Error(`Time slot ${data.timeSlot} violates business rules. Available slots: ${availabilityData.timeSlots?.join(', ')}`);
        }
      }
      
      const response = await apiRequest("POST", "/api/bookings", data);
      return response.json();
    },
    onSuccess: () => {
      toast({
        title: "Appointment Created",
        description: "New appointment has been successfully created.",
      });
      setIsNewAppointmentOpen(false);
      setNewAppointment({
        name: '',
        phone: '',
        address: '',
        postalCode: '',
        date: '',
        timeSlot: ''
      });
      queryClient.invalidateQueries({ queryKey: ["/api/admin/bookings/range"] });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to create appointment.",
        variant: "destructive",
      });
    },
  });

  const blockTimeMutation = useMutation({
    mutationFn: async (data: BlockTimeData) => {
      const response = await apiRequest("POST", "/api/admin/blocked-slots", {
        ...data,
        date: new Date(data.date).toISOString(),
        createdBy: "admin",
        selectedReps: data.selectedReps.length > 0 ? JSON.stringify(data.selectedReps) : null
      });
      return response.json();
    },
    onSuccess: () => {
      toast({
        title: "Time Blocked",
        description: "Selected time range has been blocked successfully.",
      });
      setIsBlockTimeOpen(false);
      setBlockTimeData({
        date: toApiDate(new Date()),
        startTime: '09:00',
        endTime: '12:00',
        reason: 'Blocked',
        selectedReps: []
      });
      queryClient.invalidateQueries({ queryKey: ["/api/admin/blocked-slots"] });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to block time.",
        variant: "destructive",
      });
    },
  });

  const navigate = (direction: 'prev' | 'next') => {
    const amount = viewMode === 'day' ? 1 : viewMode === 'week' ? 7 : 30;
    setCurrentDate(prev => 
      direction === 'next' 
        ? addDays(prev, amount)
        : addDays(prev, -amount)
    );
  };

  const getBookingsForDate = (date: Date) => {
    if (!bookings) return [];
    return bookings.filter((booking: any) => {
      const bookingDate = new Date(booking.date);
      return isSameDay(bookingDate, date);
    });
  };

  const getBookingsForTimeSlot = (date: Date, time: string) => {
    const dayBookings = getBookingsForDate(date);
    return dayBookings.filter((booking: any) => convertTo24Hour(booking.timeSlot) === time);
  };

  // Convert AM/PM time to 24-hour format for comparison
  const convertTo24Hour = (time12h: string): string => {
    if (!time12h.includes('AM') && !time12h.includes('PM')) {
      return time12h; // Already in 24-hour format
    }
    
    const [time, period] = time12h.split(' ');
    const [hours, minutes] = time.split(':');
    let hour = parseInt(hours);
    
    if (period === 'AM' && hour === 12) {
      hour = 0;
    } else if (period === 'PM' && hour !== 12) {
      hour += 12;
    }
    
    return `${hour.toString().padStart(2, '0')}:${minutes}`;
  };

  const getBlockedSlotsForDate = (date: Date) => {
    if (!blockedSlots) return [];
    return blockedSlots.filter((blocked: any) => {
      const blockedDate = new Date(blocked.date);
      return isSameDay(blockedDate, date);
    });
  };

  const isTimeSlotBlocked = (date: Date, time: string, forRep?: string) => {
    const blockedForDate = getBlockedSlotsForDate(date);
    return blockedForDate.some((blocked: any) => {
      const slotTime = time;
      const timeInRange = slotTime >= blocked.startTime && slotTime < blocked.endTime;
      
      if (!timeInRange) return false;
      
      // If no specific rep selected, or blocked for all reps (selectedReps is null/empty)
      if (!blocked.selectedReps || !forRep) {
        return !blocked.selectedReps; // Block if selectedReps is null (blocks all)
      }
      
      // Check if this specific rep is in the blocked list
      try {
        const blockedReps = JSON.parse(blocked.selectedReps);
        return blockedReps.includes(forRep);
      } catch {
        return !blocked.selectedReps; // If parsing fails, treat as "block all"
      }
    });
  };

  const generateTimeSlots = () => {
    const slots = [];
    for (let hour = 9; hour <= 22; hour++) {
      for (let minute = 0; minute < 60; minute += 30) {
        const time = setMinutes(setHours(new Date(), hour), minute);
        slots.push(format(time, 'HH:mm'));
      }
    }
    return slots;
  };

  const timeSlots = generateTimeSlots();

  // Get available time slots for the new appointment form date
  const { data: newAppointmentAvailability } = useQuery<{
    available: boolean;
    timeSlots: string[];
  }>({
    queryKey: ['/api/availability', newAppointment.date],
    queryFn: async () => {
      if (!newAppointment.date) return { available: false, timeSlots: [] };
      const response = await fetch(`/api/availability/${newAppointment.date}`, {
        credentials: 'include',
      });
      if (!response.ok) {
        throw new Error('Failed to fetch availability');
      }
      return response.json();
    },
    enabled: !!newAppointment.date,
    staleTime: 30000, // Cache for 30 seconds
  });

  const getAvailableTimeSlotsForDate = (date: string): string[] => {
    if (!date) return [];
    
    // Return slots if we have data for the requested date
    if (date === newAppointment.date && newAppointmentAvailability) {
      return newAppointmentAvailability.timeSlots || [];
    }
    
    return [];
  };

  const handleTimeSlotClick = (date: Date, time: string) => {
    // Check if this time slot is available before allowing admin to create appointment
    const slotBookings = getBookingsForTimeSlot(date, time);
    const isBlocked = isTimeSlotBlocked(date, time);
    
    if (slotBookings.length > 0 || isBlocked) {
      toast({
        title: "Time Slot Unavailable",
        description: "This time slot is already booked or blocked. Please select an available slot.",
        variant: "destructive",
      });
      return;
    }

    setSelectedTimeSlot({ date, time });
    setNewAppointment(prev => ({
      ...prev,
      date: format(date, 'yyyy-MM-dd'),
      timeSlot: time
    }));
    setBlockTimeData(prev => ({
      ...prev,
      date: format(date, 'yyyy-MM-dd'),
      startTime: time,
      endTime: time // Will be adjusted in the block time dialog
    }));
    setIsTimeSlotActionOpen(true);
  };

  const handleCreateBookingAction = () => {
    setIsTimeSlotActionOpen(false);
    setIsNewAppointmentOpen(true);
  };

  const handleBlockTimeAction = async () => {
    setIsTimeSlotActionOpen(false);
    
    if (!selectedTimeSlot) return;
    
    try {
      // Direct single-slot blocking without form
      const blockData = {
        date: format(selectedTimeSlot.date, 'yyyy-MM-dd'),
        startTime: selectedTimeSlot.time,
        endTime: selectedTimeSlot.time, // Block just this single time slot
        reason: 'Blocked',
        selectedReps: [] // Block for all reps by default
      };
      
      await blockTimeMutation.mutateAsync(blockData);
      toast({
        title: "Time Blocked",
        description: `${formatTimeTo12Hour(selectedTimeSlot.time)} on ${format(selectedTimeSlot.date, 'MMM d, yyyy')} has been blocked.`,
      });
    } catch (error) {
      console.error('Error blocking time slot:', error);
      toast({
        title: "Error",
        description: "Failed to block time slot. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleBookingClick = (booking: any, e: React.MouseEvent) => {
    try {
      e.stopPropagation();
      
      // Validate booking object
      if (!booking || !booking.id) {
        console.error('Invalid booking object:', booking);
        toast({
          title: "Error",
          description: "Invalid booking data",
          variant: "destructive"
        });
        return;
      }

      setSelectedBooking(booking);
      setIsBookingDetailsOpen(true);
      
      // Load funnel journey if session data exists
      if (booking.sessionId) {
        setFunnelLoading(true);
        setFunnelJourney(null); // Clear previous data
        
        fetch(`/api/admin/bookings/${booking.id}/funnel-journey`)
          .then(res => {
            if (!res.ok) {
              throw new Error(`HTTP ${res.status}: ${res.statusText}`);
            }
            return res.json();
          })
          .then(data => {
            // Validate response data
            if (data && typeof data === 'object') {
              setFunnelJourney(data);
            } else {
              console.warn('Invalid funnel journey data:', data);
              setFunnelJourney(null);
            }
          })
          .catch(error => {
            console.error('Error loading funnel journey:', error);
            setFunnelJourney(null);
            // Don't show error toast for funnel journey failures as it's optional
          })
          .finally(() => {
            setFunnelLoading(false);
          });
      } else {
        setFunnelJourney(null);
        setFunnelLoading(false);
      }
    } catch (error) {
      console.error('Error in handleBookingClick:', error);
      toast({
        title: "Error",
        description: "Failed to open booking details",
        variant: "destructive"
      });
    }
  };

  const handleMultipleBookingsClick = (bookings: any[], e: React.MouseEvent) => {
    e.stopPropagation();
    if (bookings.length === 1) {
      handleBookingClick(bookings[0], e);
    } else {
      setSelectedBookings(bookings);
      setIsMultipleBookingsOpen(true);
    }
  };

  const handleCreateAppointment = () => {
    if (!newAppointment.name || !newAppointment.phone || !newAppointment.address || 
        !newAppointment.postalCode || !newAppointment.date || !newAppointment.timeSlot) {
      toast({
        title: "Missing Information",
        description: "Please fill in all required fields.",
        variant: "destructive",
      });
      return;
    }

    // Validate time slot availability before creating appointment
    const selectedDate = new Date(newAppointment.date);
    const slotBookings = getBookingsForTimeSlot(selectedDate, newAppointment.timeSlot);
    const isBlocked = isTimeSlotBlocked(selectedDate, newAppointment.timeSlot);
    
    if (slotBookings.length > 0 || isBlocked) {
      toast({
        title: "Time Slot Unavailable",
        description: "This time slot is no longer available. Please select a different time.",
        variant: "destructive",
      });
      return;
    }

    // Format phone number
    let formattedPhone = newAppointment.phone;
    if (!formattedPhone.startsWith('+91')) {
      formattedPhone = '+91' + formattedPhone.replace(/^0+/, '');
    }

    createAppointmentMutation.mutate({
      ...newAppointment,
      phone: formattedPhone
    });
  };

  const renderWeekView = () => {
    const weekStart = startOfWeek(currentDate, { weekStartsOn: 1 });
    const weekEnd = endOfWeek(currentDate, { weekStartsOn: 1 });
    const weekDays = eachDayOfInterval({
      start: weekStart,
      end: weekEnd
    });

    return (
      <div className="flex-1 bg-white">
        <div className="border-b p-4">
          <h3 className="text-lg font-semibold">
            {format(weekStart, 'MMM d')} - {format(weekEnd, 'MMM d, yyyy')}
          </h3>
        </div>
        
        {/* Week header */}
        <div className="grid grid-cols-8 border-b">
          <div className="p-4 text-sm font-medium text-gray-500 border-r">Time</div>
          {weekDays.map((day) => (
            <div key={day.toISOString()} className="p-4 text-center border-r last:border-r-0">
              <div className="text-sm font-medium text-gray-900">
                {format(day, 'EEE')}
              </div>
              <div className={`text-lg font-semibold mt-1 ${
                isSameDay(day, new Date()) ? 'text-purple-600' : 'text-gray-900'
              }`}>
                {format(day, 'd')}
              </div>
            </div>
          ))}
        </div>

        {/* Time slots grid */}
        <div className="overflow-y-auto" style={{ height: 'calc(100vh - 280px)' }}>
          {timeSlots.map((time) => (
            <div key={time} className="grid grid-cols-8 border-b border-gray-100 hover:bg-gray-50">
              <div className="p-3 text-xs text-gray-500 border-r font-medium">
                {format(parseISO(`2000-01-01T${time}:00`), 'h:mm a')}
              </div>
              {weekDays.map((day) => {
                const slotBookings = getBookingsForTimeSlot(day, time);
                const isBlocked = isTimeSlotBlocked(day, time);
                
                return (
                  <div 
                    key={`${day.toISOString()}-${time}`}
                    className={`p-1 border-r last:border-r-0 min-h-[60px] relative cursor-pointer ${
                      isBlocked ? 'bg-red-100' : 'hover:bg-purple-50'
                    }`}
                    onClick={() => slotBookings.length === 0 && !isBlocked && handleTimeSlotClick(day, time)}
                  >
                    {slotBookings.length > 0 ? (
                      (() => {
                        // Get status-based colors for the first booking (primary display)
                        const getStatusColors = (status: string) => {
                          switch (status) {
                            case 'confirmed':
                              return {
                                bg: 'bg-green-200',
                                text: 'text-green-800',
                                hover: 'hover:bg-green-300',
                                secondary: 'text-green-600'
                              };
                            case 'cancelled':
                              return {
                                bg: 'bg-red-200',
                                text: 'text-red-800',
                                hover: 'hover:bg-red-300',
                                secondary: 'text-red-600'
                              };
                            case 'completed':
                              return {
                                bg: 'bg-blue-200',
                                text: 'text-blue-800',
                                hover: 'hover:bg-blue-300',
                                secondary: 'text-blue-600'
                              };
                            default: // pending or any other status
                              return {
                                bg: 'bg-yellow-200',
                                text: 'text-yellow-800',
                                hover: 'hover:bg-yellow-300',
                                secondary: 'text-yellow-600'
                              };
                          }
                        };
                        
                        const colors = getStatusColors(slotBookings[0].status);
                        
                        return (
                          <div 
                            className={`${colors.bg} ${colors.text} p-2 rounded text-xs font-medium h-full cursor-pointer ${colors.hover} transition-colors`}
                            onClick={(e) => handleMultipleBookingsClick(slotBookings, e)}
                          >
                            <div className="font-semibold truncate">{slotBookings[0].name}</div>
                            <div className={`${colors.secondary} truncate`}>{slotBookings[0].phone}</div>
                            <div className={`${colors.secondary} text-xs capitalize`}>{slotBookings[0].status}</div>
                            {slotBookings.length > 1 && (
                              <div className={`${colors.secondary} text-xs mt-1`}>
                                +{slotBookings.length - 1} more
                              </div>
                            )}
                          </div>
                        );
                      })()
                    ) : isBlocked ? (
                      <div className="bg-red-200 text-red-800 p-2 rounded text-xs font-medium h-full">
                        <div className="font-semibold">Blocked</div>
                        <div className="text-red-600">Unavailable</div>
                      </div>
                    ) : (
                      <div className="h-full flex items-center justify-center text-gray-300 hover:text-gray-500">
                        <Plus className="h-4 w-4" />
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          ))}
        </div>
      </div>
    );
  };

  const renderDayView = () => {
    const dayBookings = getBookingsForDate(currentDate);
    
    // Get all sales reps from the reps query, not just from bookings
    const allReps = salesReps || [];
    const repNames = allReps.map((rep: any) => rep.name);
    
    // Get unique sales reps that have bookings for the day
    const repsWithBookings = Array.from(new Set(dayBookings.map((b: any) => b.repAssigned).filter(Boolean)));
    
    // Combine all available reps with those that have bookings (avoiding duplicates)
    const allRepNames = Array.from(new Set([...repNames, ...repsWithBookings]));
    
    // Add columns for each rep plus unassigned and available slots
    const columns: string[] = [...allRepNames, 'Unassigned', 'Available'];

    // Calculate appointment duration blocks (90 minutes each)
    const getAppointmentDuration = () => 90; // minutes
    const slotsPerAppointment = Math.ceil(getAppointmentDuration() / 30); // 30-minute slots

    return (
      <div className="flex-1 bg-white">
        <div className="border-b p-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">
              {format(currentDate, 'EEEE, MMMM d, yyyy')}
            </h3>
            {/* Legend for time slot availability */}
            <div className="flex items-center gap-4 text-xs">
              <div className="flex items-center gap-1">
                <div className="w-3 h-3 bg-purple-50 border rounded"></div>
                <span>Available</span>
              </div>
              <div className="flex items-center gap-1">
                <div className="w-3 h-3 bg-gray-50 border rounded"></div>
                <span>Booked</span>
              </div>
              <div className="flex items-center gap-1">
                <div className="w-3 h-3 bg-red-50 border rounded"></div>
                <span>Blocked</span>
              </div>
            </div>
          </div>
        </div>
        
        {/* Calendar header with rep columns */}
        <div className={`grid border-b bg-gray-50`} style={{ gridTemplateColumns: `100px repeat(${columns.length}, 1fr)` }}>
          <div className="p-3 text-sm font-medium text-gray-500 border-r">Time</div>
          {columns.map((rep: string, index: number) => (
            <div key={rep} className="p-3 text-center border-r last:border-r-0">
              <div className="text-sm font-medium text-gray-900">
                {rep === 'Add New' ? 'Available' : rep}
              </div>
              {rep !== 'Add New' && rep !== 'Available' && (
                <div className="text-xs text-gray-500 mt-1">
                  {dayBookings.filter((b: any) => (b.repAssigned || 'Unassigned') === rep).length} appointments
                </div>
              )}
            </div>
          ))}
        </div>
        
        <div className="overflow-y-auto" style={{ height: 'calc(100vh - 350px)' }}>
          {timeSlots.map((time) => {
            const slotBookings = getBookingsForTimeSlot(currentDate, time);
            const isBlocked = isTimeSlotBlocked(currentDate, time);
            
            return (
              <div 
                key={time}
                className={`grid border-b border-gray-100 hover:bg-gray-50 min-h-[70px]`}
                style={{ gridTemplateColumns: `100px repeat(${columns.length}, 1fr)` }}
              >
                <div className="p-3 text-xs text-gray-500 border-r font-medium">
                  {format(parseISO(`2000-01-01T${time}:00`), 'h:mm a')}
                </div>
                
                {columns.map((rep: string) => {
                  const repBookings = slotBookings.filter((b: any) => {
                    if (rep === 'Unassigned') {
                      return !b.repAssigned || b.repAssigned === 'Unassigned';
                    }
                    return b.repAssigned === rep;
                  });
                  
                  // Check if this specific rep is blocked (or all reps are blocked)
                  const isRepBlocked = rep === 'Add New' ? isBlocked : isTimeSlotBlocked(currentDate, time, rep);
                  const canAddAppointment = rep === 'Add New' || (repBookings.length === 0 && !isRepBlocked);
                  
                  return (
                    <div 
                      key={`${rep}-${time}`}
                      className={`p-2 border-r last:border-r-0 relative ${
                        isRepBlocked ? 'bg-red-50 cursor-not-allowed' : 
                        canAddAppointment ? 'hover:bg-purple-50 cursor-pointer' : 'bg-gray-50 cursor-not-allowed'
                      }`}
                      onClick={() => canAddAppointment && !isRepBlocked && handleTimeSlotClick(currentDate, time)}
                    >
                      {repBookings.length > 0 ? (
                        <div className="space-y-1">
                          {repBookings.map((booking: any, index: number) => {
                            // Get status-based colors matching the week view exactly
                            const getStatusColors = (status: string) => {
                              switch (status) {
                                case 'confirmed':
                                  return {
                                    bg: 'bg-green-200',
                                    text: 'text-green-800',
                                    hover: 'hover:bg-green-300',
                                    secondary: 'text-green-600'
                                  };
                                case 'cancelled':
                                  return {
                                    bg: 'bg-red-200',
                                    text: 'text-red-800',
                                    hover: 'hover:bg-red-300',
                                    secondary: 'text-red-600'
                                  };
                                case 'completed':
                                  return {
                                    bg: 'bg-blue-200',
                                    text: 'text-blue-800',
                                    hover: 'hover:bg-blue-300',
                                    secondary: 'text-blue-600'
                                  };
                                default: // pending or any other status
                                  return {
                                    bg: 'bg-yellow-200',
                                    text: 'text-yellow-800',
                                    hover: 'hover:bg-yellow-300',
                                    secondary: 'text-yellow-600'
                                  };
                              }
                            };
                            
                            const colors = getStatusColors(booking.status);
                            
                            return (
                              <div 
                                key={booking.id}
                                className={`${colors.bg} ${colors.text} p-2 rounded text-xs font-medium cursor-pointer ${colors.hover} transition-colors`}
                                onClick={(e) => handleBookingClick(booking, e)}
                              >
                                <div className="font-semibold truncate">{booking.name}</div>
                                <div className={`${colors.secondary} truncate`}>{booking.phone}</div>
                                <div className={`${colors.secondary} text-xs truncate`}>{booking.postalCode}</div>
                                <div className={`${colors.secondary} text-xs capitalize mt-1`}>{booking.status}</div>
                              </div>
                            );
                          })}
                        </div>
                      ) : isRepBlocked ? (
                        <div className="bg-red-100 border-2 border-red-400 p-2 rounded text-xs">
                          <div className="font-semibold text-red-900 flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            Blocked
                          </div>
                          {/* Show blocked slot details if available */}
                          {(() => {
                            const blockedSlot = getBlockedSlotsForDate(currentDate).find((blocked: any) => {
                              const timeInRange = time >= blocked.startTime && time < blocked.endTime;
                              if (!timeInRange) return false;
                              
                              if (!blocked.selectedReps || rep === 'Add New') {
                                return !blocked.selectedReps;
                              }
                              
                              try {
                                const blockedReps = JSON.parse(blocked.selectedReps);
                                return blockedReps.includes(rep);
                              } catch {
                                return !blocked.selectedReps;
                              }
                            });
                            
                            return blockedSlot ? (
                              <div className="text-red-700 text-xs mt-1">
                                {blockedSlot.reason}
                              </div>
                            ) : null;
                          })()}
                        </div>
                      ) : canAddAppointment ? (
                        <div className="h-full flex items-center justify-center text-gray-300 hover:text-gray-500">
                          <Plus className="h-4 w-4" />
                        </div>
                      ) : null}
                    </div>
                  );
                })}
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  if (isLoading) {
    return <div className="text-center p-8">Loading calendar...</div>;
  }

  return (
    <div className="h-full flex flex-col">
      {/* Calendar Header */}
      <div className="bg-white border-b p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <h2 className="text-xl font-semibold text-gray-900">
              {format(currentDate, viewMode === 'month' ? 'MMMM yyyy' : 'MMMM d, yyyy')}
            </h2>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => navigate('prev')}
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentDate(new Date())}
              >
                Today
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => navigate('next')}
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>

          <div className="flex items-center space-x-4">
            {/* Status Legend */}
            <div className="flex items-center space-x-3 text-xs">
              <div className="flex items-center space-x-1">
                <div className="w-3 h-3 bg-green-200 rounded"></div>
                <span className="text-gray-600">Confirmed</span>
              </div>
              <div className="flex items-center space-x-1">
                <div className="w-3 h-3 bg-yellow-200 rounded"></div>
                <span className="text-gray-600">Pending</span>
              </div>
              <div className="flex items-center space-x-1">
                <div className="w-3 h-3 bg-blue-200 rounded"></div>
                <span className="text-gray-600">Completed</span>
              </div>
              <div className="flex items-center space-x-1">
                <div className="w-3 h-3 bg-red-200 rounded"></div>
                <span className="text-gray-600">Cancelled</span>
              </div>
            </div>

            {/* View Toggle */}
            <div className="flex bg-gray-100 rounded-lg p-1">
              {(['day', 'week'] as const).map((mode) => (
                <Button
                  key={mode}
                  variant={viewMode === mode ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setViewMode(mode)}
                  className="capitalize"
                >
                  {mode}
                </Button>
              ))}
            </div>

            {/* Block Time */}
            <Dialog open={isBlockTimeOpen} onOpenChange={setIsBlockTimeOpen}>
              <DialogTrigger asChild>
                <Button variant="outline" className="border-red-300 text-red-600 hover:bg-red-50">
                  <Clock className="h-4 w-4 mr-2" />
                  Block Time
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-md">
                <DialogHeader>
                  <DialogTitle>Block Time Range</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="blockDate">Date</Label>
                    <Input
                      id="blockDate"
                      type="date"
                      value={blockTimeData.date}
                      onChange={(e) => setBlockTimeData(prev => ({ ...prev, date: e.target.value }))}
                    />
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="startTime">Start Time</Label>
                      <Select 
                        value={blockTimeData.startTime} 
                        onValueChange={(value) => setBlockTimeData(prev => ({ ...prev, startTime: value }))}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Start time" />
                        </SelectTrigger>
                        <SelectContent>
                          {timeSlots.map((time) => (
                            <SelectItem key={time} value={time}>
                              {format(parseISO(`2000-01-01T${time}:00`), 'h:mm a')}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div>
                      <Label htmlFor="endTime">End Time</Label>
                      <Select 
                        value={blockTimeData.endTime} 
                        onValueChange={(value) => setBlockTimeData(prev => ({ ...prev, endTime: value }))}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="End time" />
                        </SelectTrigger>
                        <SelectContent>
                          {timeSlots.map((time) => (
                            <SelectItem key={time} value={time}>
                              {format(parseISO(`2000-01-01T${time}:00`), 'h:mm a')}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  
                  <div>
                    <Label htmlFor="reason">Reason</Label>
                    <Input
                      id="reason"
                      value={blockTimeData.reason}
                      onChange={(e) => setBlockTimeData(prev => ({ ...prev, reason: e.target.value }))}
                      placeholder="Reason for blocking"
                    />
                  </div>

                  <div>
                    <Label>Block for Sales Representatives</Label>
                    <div className="space-y-3 mt-2">
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="all-reps"
                          checked={blockTimeData.selectedReps.length === 0}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              setBlockTimeData(prev => ({ ...prev, selectedReps: [] }));
                            }
                          }}
                        />
                        <Label htmlFor="all-reps" className="text-sm font-medium">
                          Block for all representatives (Default)
                        </Label>
                      </div>
                      
                      {salesReps && salesReps.length > 0 && (
                        <div className="pl-6 space-y-2">
                          <div className="text-xs text-gray-500 mb-2">Or select specific representatives:</div>
                          {salesReps.map((rep: any) => (
                            <div key={rep.id} className="flex items-center space-x-2">
                              <Checkbox
                                id={`rep-${rep.id}`}
                                checked={blockTimeData.selectedReps.includes(rep.name)}
                                onCheckedChange={(checked) => {
                                  if (checked) {
                                    setBlockTimeData(prev => ({
                                      ...prev,
                                      selectedReps: [...prev.selectedReps, rep.name]
                                    }));
                                  } else {
                                    setBlockTimeData(prev => ({
                                      ...prev,
                                      selectedReps: prev.selectedReps.filter(name => name !== rep.name)
                                    }));
                                  }
                                }}
                              />
                              <Label htmlFor={`rep-${rep.id}`} className="text-sm">
                                {rep.name}
                              </Label>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex justify-end space-x-2 pt-4">
                    <Button
                      variant="outline"
                      onClick={() => setIsBlockTimeOpen(false)}
                    >
                      Cancel
                    </Button>
                    <Button
                      className="bg-red-600 hover:bg-red-700"
                      onClick={() => blockTimeMutation.mutate(blockTimeData)}
                      disabled={blockTimeMutation.isPending}
                    >
                      {blockTimeMutation.isPending ? "Blocking..." : "Block Time"}
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>

            {/* Add Appointment */}
            <Dialog open={isNewAppointmentOpen} onOpenChange={setIsNewAppointmentOpen}>
              <DialogTrigger asChild>
                <Button className="bg-purple-600 hover:bg-purple-700">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Appointment
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-md">
                <DialogHeader>
                  <DialogTitle>Create New Appointment</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="name">Customer Name</Label>
                    <Input
                      id="name"
                      value={newAppointment.name}
                      onChange={(e) => setNewAppointment(prev => ({ ...prev, name: e.target.value }))}
                      placeholder="Enter customer name"
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="phone">Phone Number</Label>
                    <Input
                      id="phone"
                      value={newAppointment.phone}
                      onChange={(e) => setNewAppointment(prev => ({ ...prev, phone: e.target.value }))}
                      placeholder="Enter phone number"
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="address">Address</Label>
                    <Input
                      id="address"
                      value={newAppointment.address}
                      onChange={(e) => setNewAppointment(prev => ({ ...prev, address: e.target.value }))}
                      placeholder="Enter full address"
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="postalCode">Postal Code</Label>
                    <Select 
                      value={newAppointment.postalCode} 
                      onValueChange={(value) => setNewAppointment(prev => ({ ...prev, postalCode: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select postal code" />
                      </SelectTrigger>
                      <SelectContent>
                        {postalCodes?.filter((pc: any) => pc.isActive).map((pc: any) => (
                          <SelectItem key={pc.id} value={pc.code}>
                            {pc.code} - {pc.city}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div>
                    <Label htmlFor="date">Date</Label>
                    <Input
                      id="date"
                      type="date"
                      value={newAppointment.date}
                      onChange={(e) => setNewAppointment(prev => ({ 
                        ...prev, 
                        date: e.target.value, 
                        timeSlot: '' // Clear time slot when date changes
                      }))}
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="timeSlot">Time Slot</Label>
                    <Select 
                      value={newAppointment.timeSlot} 
                      onValueChange={(value) => setNewAppointment(prev => ({ ...prev, timeSlot: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select time slot" />
                      </SelectTrigger>
                      <SelectContent>
                        {newAppointment.date ? (
                          newAppointmentAvailability?.timeSlots && newAppointmentAvailability.timeSlots.length > 0 ? (
                            newAppointmentAvailability.timeSlots.map((time) => (
                              <SelectItem key={time} value={time}>
                                {time}
                              </SelectItem>
                            ))
                          ) : (
                            <SelectItem value="no-slots" disabled>
                              No available time slots for this date
                            </SelectItem>
                          )
                        ) : (
                          <SelectItem value="select-date" disabled>
                            Please select a date first
                          </SelectItem>
                        )}
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="flex justify-end space-x-2 pt-4">
                    <Button
                      variant="outline"
                      onClick={() => setIsNewAppointmentOpen(false)}
                    >
                      Cancel
                    </Button>
                    <Button
                      onClick={handleCreateAppointment}
                      disabled={createAppointmentMutation.isPending}
                    >
                      {createAppointmentMutation.isPending ? "Creating..." : "Create Appointment"}
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </div>
      </div>

      {/* Calendar Content */}
      {viewMode === 'week' && renderWeekView()}
      {viewMode === 'day' && renderDayView()}

      {/* Booking Details Dialog */}
      <Dialog open={isBookingDetailsOpen} onOpenChange={setIsBookingDetailsOpen}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Booking Details</DialogTitle>
            <DialogDescription>
              View complete booking information including customer details, appointment time, and assigned representative.
            </DialogDescription>
          </DialogHeader>
          {selectedBooking && (
            <div className="space-y-4">
              <div className="flex items-center gap-3">
                <User className="h-5 w-5 text-gray-500" />
                <div>
                  <div className="font-medium">{selectedBooking.name || 'N/A'}</div>
                  <div className="text-sm text-gray-500">{selectedBooking.phone || 'N/A'}</div>
                  {selectedBooking.email && (
                    <div className="text-sm text-gray-500">{selectedBooking.email}</div>
                  )}
                </div>
              </div>

              <div className="flex items-center gap-3">
                <CalendarIcon className="h-5 w-5 text-gray-500" />
                <div>
                  <div className="font-medium">
                    {selectedBooking.date ? format(new Date(selectedBooking.date), "EEEE, MMMM d, yyyy") : 'Invalid date'}
                  </div>
                  <div className="text-sm text-gray-500">
                    {selectedBooking.timeSlot ? formatTimeTo12Hour(selectedBooking.timeSlot) : 'N/A'}
                  </div>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <MapPin className="h-5 w-5 text-gray-500 mt-0.5" />
                <div>
                  <div className="font-medium">Address</div>
                  <div className="text-sm text-gray-500">{selectedBooking.address}</div>
                  <div className="text-sm text-gray-500">PIN: {selectedBooking.postalCode}</div>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <User className="h-5 w-5 text-gray-500" />
                <div>
                  <div className="font-medium">Assigned Rep</div>
                  <div className="text-sm text-gray-500">
                    {selectedBooking.repAssigned || "Not assigned"}
                  </div>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <div className="h-5 w-5" />
                <div>
                  <div className="font-medium">Status</div>
                  <div className="text-sm">{getStatusBadge(selectedBooking.status)}</div>
                </div>
              </div>

              {/* Traffic Source */}
              {selectedBooking.trafficSource && (
                <div className="flex items-start gap-3">
                  <Globe className="h-5 w-5 text-gray-500 mt-0.5 flex-shrink-0" />
                  <div className="min-w-0 flex-1 overflow-hidden">
                    <div className="font-medium">Traffic Source</div>
                    <div className="text-sm text-gray-500 break-all whitespace-pre-wrap leading-relaxed">
                      {selectedBooking.trafficSource}
                    </div>
                    {selectedBooking.utmCampaign && (
                      <div className="text-xs text-gray-400 break-all whitespace-pre-wrap leading-relaxed mt-1">
                        Campaign: {selectedBooking.utmCampaign}
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* User Journey Timeline - Show if session data exists */}
              {selectedBooking.sessionId && (
                <div className="border-t pt-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center gap-2">
                      <TrendingUp className="h-5 w-5 text-blue-500" />
                      <div className="font-medium">Customer Journey</div>
                    </div>
                    {funnelJourney && (
                      <div className="text-xs text-gray-500">
                        {(funnelJourney.totalTimeMinutes * 60 * 1000).toLocaleString()}ms total • {funnelJourney.stepsCompleted}/{funnelJourney.totalSteps} steps
                      </div>
                    )}
                  </div>
                  
                  {funnelLoading ? (
                    <div className="flex items-center gap-2 text-sm text-gray-500">
                      <div className="animate-spin h-4 w-4 border-2 border-gray-300 border-t-blue-500 rounded-full"></div>
                      Loading journey...
                    </div>
                  ) : funnelJourney && funnelJourney.funnelSteps && Array.isArray(funnelJourney.funnelSteps) ? (
                    <div className="space-y-2 max-h-48 overflow-y-auto">
                      {funnelJourney.funnelSteps.map((step: any, index: number) => {
                        if (!step || typeof step !== 'object') {
                          return null;
                        }
                        
                        const IconComponent = step.icon === 'Globe' ? Globe : 
                                            step.icon === 'Play' ? Play :
                                            step.icon === 'MapPin' ? MapPin :
                                            step.icon === 'Calendar' ? CalendarIcon :
                                            step.icon === 'Clock' ? Clock :
                                            step.icon === 'Shield' ? Shield :
                                            step.icon === 'CheckCircle' ? CheckCircle :
                                            step.icon === 'CheckCircle2' ? CheckCircle2 : Globe;
                        
                        return (
                          <div key={index} className={`flex items-center gap-3 py-2 px-3 rounded-lg ${
                            step.completed ? 'bg-green-50 border border-green-200' : 'bg-gray-50 border border-gray-200'
                          }`}>
                            <IconComponent className={`h-4 w-4 ${
                              step.completed ? 'text-green-600' : 'text-gray-400'
                            }`} />
                            <div className="flex-1">
                              <div className={`text-sm font-medium ${
                                step.completed ? 'text-green-800' : 'text-gray-500'
                              }`}>
                                {step.step}
                              </div>
                              {step.completed && step.timeFromPrevious !== null && (
                                <div className="text-xs text-green-600">
                                  {(step.timeFromPrevious * 1000).toLocaleString()}ms
                                  {step.totalTimeFromStart !== null && (
                                    <span className="text-gray-500 ml-1">
                                      (total: {(step.totalTimeFromStart * 1000).toLocaleString()}ms)
                                    </span>
                                  )}
                                </div>
                              )}
                            </div>
                          </div>
                        );
                      })}
                      
                      {/* Journey Summary */}
                      <div className="mt-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                        <div className="text-sm font-medium text-blue-800 mb-1">Journey Summary</div>
                        <div className="text-xs text-blue-600 space-y-1">
                          <div>Total events: {funnelJourney.eventCount || 0}</div>
                          <div>Conversion rate: {funnelJourney.conversionRate || 0}%</div>
                          <div>Average step time: {funnelJourney.averageStepTime || 0}s</div>
                          <div>Source: {funnelJourney.traffic_source || 'Unknown'}</div>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="text-sm text-gray-500">
                      No journey data available
                    </div>
                  )}
                </div>
              )}

              <div className="flex items-center gap-3">
                <Clock className="h-5 w-5 text-gray-500" />
                <div>
                  <div className="font-medium">Created</div>
                  <div className="text-sm text-gray-500">
                    {format(new Date(selectedBooking.createdAt), "MMM d, yyyy 'at' h:mm a")}
                  </div>
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Multiple Bookings Dialog */}
      <Dialog open={isMultipleBookingsOpen} onOpenChange={setIsMultipleBookingsOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Multiple Appointments</DialogTitle>
          </DialogHeader>
          <div className="space-y-4 max-h-96 overflow-y-auto">
            {selectedBookings.map((booking: any) => (
              <div 
                key={booking.id} 
                className="border rounded-lg p-4 hover:bg-gray-50 cursor-pointer"
                onClick={() => {
                  setIsMultipleBookingsOpen(false);
                  setSelectedBooking(booking);
                  setIsBookingDetailsOpen(true);
                }}
              >
                <div className="flex items-start justify-between">
                  <div className="space-y-2">
                    <div className="flex items-center space-x-4">
                      <div>
                        <div className="font-medium text-lg">{booking.name}</div>
                        <div className="text-sm text-gray-600">{booking.phone}</div>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div className="flex items-center space-x-2">
                        <CalendarIcon className="h-4 w-4 text-gray-400" />
                        <span>{format(new Date(booking.date), 'MMM d, yyyy')}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Clock className="h-4 w-4 text-gray-400" />
                        <span>{booking.timeSlot}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <MapPin className="h-4 w-4 text-gray-400" />
                        <span>{booking.postalCode}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <User className="h-4 w-4 text-gray-400" />
                        <span>{booking.repAssigned || 'Unassigned'}</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    {booking.status === 'confirmed' && (
                      <CheckCircle className="h-4 w-4 text-green-500" />
                    )}
                    {booking.status === 'cancelled' && (
                      <XCircle className="h-4 w-4 text-red-500" />
                    )}
                    {booking.status === 'pending' && (
                      <AlertCircle className="h-4 w-4 text-yellow-500" />
                    )}
                    <div className="text-right">
                      <div className="font-medium capitalize text-sm">{booking.status}</div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
          <div className="flex justify-end">
            <Button
              variant="outline"
              onClick={() => setIsMultipleBookingsOpen(false)}
            >
              Close
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Time Slot Action Dialog */}
      <Dialog open={isTimeSlotActionOpen} onOpenChange={setIsTimeSlotActionOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Select Action</DialogTitle>
            <DialogDescription>
              What would you like to do with this time slot?
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="text-sm text-gray-600 bg-gray-50 p-3 rounded">
              <div><strong>Date:</strong> {selectedTimeSlot ? format(selectedTimeSlot.date, "EEEE, MMMM d, yyyy") : ''}</div>
              <div><strong>Time:</strong> {selectedTimeSlot ? formatTimeTo12Hour(selectedTimeSlot.time) : ''}</div>
            </div>
            
            <div className="space-y-3">
              <Button
                onClick={handleCreateBookingAction}
                className="w-full flex items-center gap-2 bg-green-600 hover:bg-green-700 text-white"
              >
                <CalendarPlus className="h-4 w-4" />
                Create New Booking
              </Button>
              
              <Button
                onClick={handleBlockTimeAction}
                className="w-full flex items-center gap-2 bg-red-600 hover:bg-red-700 text-white"
              >
                <Clock className="h-4 w-4" />
                Block This Slot
              </Button>
            </div>
            
            <Button
              variant="outline"
              onClick={() => setIsTimeSlotActionOpen(false)}
              className="w-full"
            >
              Cancel
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
