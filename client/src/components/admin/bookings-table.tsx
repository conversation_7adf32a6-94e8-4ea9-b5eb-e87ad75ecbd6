import { useState, useEffect } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { useAuth } from "@/hooks/use-auth";
import { useAdminWebSocket } from "@/hooks/use-admin-websocket";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { subDays, startOfWeek, startOfMonth, startOfYear } from "date-fns";
import { toShortDate, toApiDate, formatDate } from "@shared/date-utils";
import { Eye, Edit, Trash2, Calendar, Clock, User, MapPin, Phone, Mail, AlarmClock, Download, PhoneCall, CheckCircle2, History, ChevronLeft, ChevronRight, ArrowUpDown, Globe, TrendingUp, Play, Shield, CheckCircle, X } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { BookingHistoryModal } from "./booking-history-modal";

// Helper function to convert any time format to 12-hour format
function formatTimeTo12Hour(timeString: string): string {
  if (!timeString) return timeString;
  
  // If already in 12-hour format (contains AM/PM), return as is
  if (timeString.includes('AM') || timeString.includes('PM')) {
    return timeString;
  }
  
  // If in 24-hour format (e.g., "15:30"), convert to 12-hour
  const timeMatch = timeString.match(/^(\d{1,2}):(\d{2})$/);
  if (timeMatch) {
    let hour = parseInt(timeMatch[1]);
    const minute = timeMatch[2];
    
    const period = hour >= 12 ? 'PM' : 'AM';
    if (hour === 0) {
      hour = 12;
    } else if (hour > 12) {
      hour = hour - 12;
    }
    
    return `${hour}:${minute} ${period}`;
  }
  
  return timeString;
}

interface Booking {
  id: number;
  name: string;
  phone: string;
  email: string | null;
  address: string;
  postalCode: string;
  date: string;
  timeSlot: string;
  repAssigned: string | null;
  status: string;
  service: string;
  appointmentCompleted: boolean;
  customerSatisfaction: number | null;
  feedbackNotes: string | null;
  purchaseAmount: number | null;
  orderId: string | null;
  paymentStatus: string | null;
  paymentMethod: string | null;
  createdAt: string;
  trafficSource?: string;
  utmCampaign?: string;
  sessionId?: string;
}

interface FunnelStep {
  step: string;
  icon: string;
  timestamp: string | null;
  timeFromPrevious: number | null;
  totalTimeFromStart: number | null;
  eventData: any;
  completed: boolean;
}

interface FunnelJourney {
  bookingId: number;
  sessionId: string;
  journeyStarted: string;
  journeyCompleted: string;
  totalTimeMinutes: number;
  stepsCompleted: number;
  totalSteps: number;
  conversionRate: number;
  averageStepTime: number;
  finalOutcome: string;
  funnelSteps: FunnelStep[];
  eventCount: number;
  traffic_source: string;
}

interface BookingsTableProps {
  readOnly?: boolean;
}

export default function BookingsTable({ readOnly = false }: BookingsTableProps) {
  const [viewingBooking, setViewingBooking] = useState<Booking | null>(null);
  const [editingBooking, setEditingBooking] = useState<Booking | null>(null);
  const [completingBooking, setCompletingBooking] = useState<Booking | null>(null);
  const [historyPhoneNumber, setHistoryPhoneNumber] = useState<string>("");
  const [showHistoryModal, setShowHistoryModal] = useState(false);
  const [exportFilter, setExportFilter] = useState<string>("all");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [dateFilter, setDateFilter] = useState<string>("all");
  const [currentDate, setCurrentDate] = useState<string>("");
  const [sortOrder, setSortOrder] = useState<string>("");
  const [createdSortOrder, setCreatedSortOrder] = useState<string>("createdDesc");
  const [editFormData, setEditFormData] = useState({
    name: "",
    phone: "",
    email: "",
    address: "",
    date: "",
    timeSlot: "",
    status: "",
    repAssigned: "",
    customerSatisfaction: "",
    feedbackNotes: "",
    orderId: ""
  });
  
  const [completeFormData, setCompleteFormData] = useState({
    transactionMade: false,
    purchaseAmount: "",
    orderId: "",
    customerSatisfaction: "",
    feedbackNotes: ""
  });
  
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const { user } = useAuth();
  
  // WebSocket connection for real-time updates
  useAdminWebSocket();

  // Reset currentDate when switching away from today filter
  useEffect(() => {
    if (dateFilter !== 'today') {
      setCurrentDate('');
    } else if (dateFilter === 'today' && !currentDate) {
      // Set current date when switching to today filter
      setCurrentDate(toApiDate(new Date()));
    }
  }, [dateFilter]);
  
  const isSupport = user?.isSupport || false;
  const bookingsEndpoint = isSupport ? "/api/support/bookings" : "/api/admin/bookings";
  const repsEndpoint = "/api/admin/reps"; // Only admin can access reps

  const { data: bookings = [], isLoading } = useQuery<Booking[]>({
    queryKey: [bookingsEndpoint, { 
      dateFilter: dateFilter !== "all" ? dateFilter : undefined,
      date: currentDate || undefined,
      sort: sortOrder || createdSortOrder,
      status: statusFilter !== "all" ? statusFilter : undefined
    }],
    queryFn: async () => {
      const params = new URLSearchParams();
      
      if (dateFilter !== "all") {
        params.append('dateFilter', dateFilter);
      }
      
      if (currentDate) {
        params.append('date', currentDate);
      }
      
      if (sortOrder) {
        params.append('sort', sortOrder);
      } else if (createdSortOrder) {
        params.append('sort', createdSortOrder);
      }
      
      if (statusFilter !== "all") {
        params.append('status', statusFilter);
      }
      
      const url = `${bookingsEndpoint}${params.toString() ? '?' + params.toString() : ''}`;
      console.log('🔍 Bookings API request:', { statusFilter, url, params: params.toString() });
      
      const response = await fetch(url, {
        credentials: 'include',
      });
      
      if (!response.ok) {
        throw new Error('Failed to fetch bookings');
      }
      
      const data = await response.json();
      console.log('🔍 Bookings API response:', { count: data.length, statusFilter, sample: data.slice(0, 2) });
      return data;
    },
  });

  const { data: reps = [] } = useQuery<any[]>({
    queryKey: [repsEndpoint],
    enabled: !isSupport, // Only fetch reps for admin users
  });

  // Funnel journey query for selected booking
  const { data: funnelJourney, isLoading: funnelLoading } = useQuery<FunnelJourney>({
    queryKey: ['/api/admin/bookings', viewingBooking?.id, 'funnel-journey'],
    queryFn: async () => {
      if (!viewingBooking?.id) throw new Error('No booking selected');
      
      const response = await fetch(`/api/admin/bookings/${viewingBooking.id}/funnel-journey`, {
        credentials: 'include',
      });
      
      if (!response.ok) {
        throw new Error('Failed to fetch funnel journey');
      }
      
      return response.json();
    },
    enabled: !!viewingBooking?.id, // Only fetch when viewing a booking
  });

  const updateBookingMutation = useMutation({
    mutationFn: async ({ id, ...data }: any) => {
      const updateEndpoint = isSupport ? `/api/support/bookings/${id}` : `/api/admin/bookings/${id}`;
      const response = await apiRequest("PUT", updateEndpoint, data);
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [bookingsEndpoint] });
      setEditingBooking(null);
      resetEditForm();
      toast({ title: "Booking updated successfully" });
    },
    onError: () => {
      toast({ title: "Failed to update booking", variant: "destructive" });
    }
  });

  const deleteBookingMutation = useMutation({
    mutationFn: async (id: number) => {
      // Only admin can delete bookings
      const response = await apiRequest("DELETE", `/api/admin/bookings/${id}`);
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [bookingsEndpoint] });
      toast({ title: "Booking cancelled successfully" });
    },
    onError: () => {
      toast({ title: "Failed to cancel booking", variant: "destructive" });
    }
  });

  const sendReminderMutation = useMutation({
    mutationFn: async (id: number) => {
      const response = await apiRequest("POST", `/api/bookings/${id}/reminder`);
      return response.json();
    },
    onSuccess: () => {
      toast({ title: "WhatsApp reminder sent successfully" });
    },
    onError: () => {
      toast({ title: "Failed to send reminder", variant: "destructive" });
    }
  });

  const sendCxFollowupMutation = useMutation({
    mutationFn: async (id: number) => {
      const response = await apiRequest("POST", `/api/bookings/${id}/cx-followup`);
      return response.json();
    },
    onSuccess: () => {
      toast({ title: "Customer follow-up message sent successfully" });
    },
    onError: () => {
      toast({ title: "Failed to send follow-up message", variant: "destructive" });
    }
  });

  const completeBookingMutation = useMutation({
    mutationFn: async (data: { id: number; completionData: any }) => {
      const response = await apiRequest("PUT", `/api/admin/bookings/${data.id}/complete`, data.completionData);
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [bookingsEndpoint] });
      toast({ title: "Booking completed successfully" });
      setCompletingBooking(null);
      setCompleteFormData({
        transactionMade: false,
        purchaseAmount: "",
        orderId: "",
        customerSatisfaction: "",
        feedbackNotes: ""
      });
    },
    onError: () => {
      toast({ title: "Failed to complete booking", variant: "destructive" });
    }
  });

  // Export CSV function
  const handleExportCSV = async () => {
    try {
      const params = new URLSearchParams();
      if (dateFilter !== "all") params.append("dateFilter", dateFilter);
      if (currentDate) params.append("date", currentDate);
      if (sortOrder) {
        params.append("sort", sortOrder);
      } else if (createdSortOrder) {
        params.append("sort", createdSortOrder);
      }
      if (statusFilter !== "all") params.append("status", statusFilter);
      
      const url = `/api/admin/bookings/export?${params.toString()}`;
      const response = await fetch(url, {
        credentials: 'include'
      });
      
      if (response.ok) {
        const blob = await response.blob();
        const downloadUrl = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = downloadUrl;
        link.download = `bookings_${toApiDate(new Date())}.csv`;
        document.body.appendChild(link);
        link.click();
        link.remove();
        window.URL.revokeObjectURL(downloadUrl);
        toast({ title: "CSV exported successfully" });
      } else {
        throw new Error('Export failed');
      }
    } catch (error) {
      toast({ title: "Failed to export CSV", variant: "destructive" });
    }
  };

  // Date navigation functions
  const navigateDate = (direction: 'prev' | 'next') => {
    let current;
    if (currentDate) {
      current = new Date(currentDate);
    } else {
      current = new Date();
      // Initialize currentDate if not set
      setCurrentDate(toApiDate(current));
    }
    
    const newDate = new Date(current);
    newDate.setDate(current.getDate() + (direction === 'next' ? 1 : -1));
    setCurrentDate(toApiDate(newDate));
  };

  // Toggle sort order for date/time
  const toggleSortOrder = () => {
    setSortOrder(prev => prev === 'dateDesc' ? 'dateAsc' : 'dateDesc');
    setCreatedSortOrder(""); // Clear created sort when date sort is used
  };

  // Toggle sort order for created column
  const toggleCreatedSortOrder = () => {
    setCreatedSortOrder(prev => prev === 'createdDesc' ? 'createdAsc' : 'createdDesc');
    setSortOrder(""); // Clear date sort when created sort is used
  };

  const resetEditForm = () => {
    setEditFormData({
      name: "",
      phone: "",
      email: "",
      address: "",
      date: "",
      timeSlot: "",
      status: "",
      repAssigned: "unassigned",
      customerSatisfaction: "",
      feedbackNotes: "",
      orderId: ""
    });
  };

  const handleView = (booking: Booking) => {
    setViewingBooking(booking);
  };

  const handleEdit = (booking: Booking) => {
    setEditingBooking(booking);
    setEditFormData({
      name: booking.name,
      phone: booking.phone,
      email: booking.email || "",
      address: booking.address,
      date: booking.date.split('T')[0], // Convert to YYYY-MM-DD format
      timeSlot: booking.timeSlot,
      status: booking.status,
      repAssigned: booking.repAssigned || "unassigned",
      customerSatisfaction: (booking as any).customerSatisfaction?.toString() || "",
      feedbackNotes: (booking as any).feedbackNotes || "",
      orderId: (booking as any).orderId || ""
    });
  };

  const handleDelete = (booking: Booking) => {
    if (confirm(`Are you sure you want to cancel ${booking.name}'s appointment?`)) {
      deleteBookingMutation.mutate(booking.id);
    }
  };

  const handleSendReminder = (booking: Booking) => {
    if (confirm(`Send WhatsApp reminder to ${booking.name} for their appointment on ${booking.date}?`)) {
      sendReminderMutation.mutate(booking.id);
    }
  };

  const handleCxFollowup = (booking: Booking) => {
    if (confirm(`Send "Tried Reaching" follow-up message to ${booking.name}?`)) {
      sendCxFollowupMutation.mutate(booking.id);
    }
  };

  const handleComplete = (booking: Booking) => {
    setCompletingBooking(booking);
    setCompleteFormData({
      transactionMade: false,
      purchaseAmount: "",
      orderId: "",
      customerSatisfaction: "",
      feedbackNotes: ""
    });
  };

  const handleViewHistory = (booking: Booking) => {
    setHistoryPhoneNumber(booking.phone);
    setShowHistoryModal(true);
  };

  const handleEditSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!editingBooking) return;

    // Check if date or time slot changed and validate availability
    const dateChanged = editFormData.date !== editingBooking.date.split('T')[0];
    const timeChanged = editFormData.timeSlot !== editingBooking.timeSlot;
    
    if (dateChanged || timeChanged) {
      // Need to validate availability using the API (excluding current booking)
      const checkAvailability = async () => {
        try {
          const response = await fetch(`/api/availability/${editFormData.date}?excludeBooking=${editingBooking.id}`, {
            credentials: 'include',
          });
          const availabilityData = await response.json();
          
          console.log('🔍 Availability check debug:', {
            date: editFormData.date,
            timeSlot: editFormData.timeSlot,
            excludeBooking: editingBooking.id,
            availabilityData,
            timeSlotExists: availabilityData.timeSlots?.includes(editFormData.timeSlot)
          });
          
          if (!availabilityData.available || !availabilityData.timeSlots.includes(editFormData.timeSlot)) {
            toast({
              title: "Business Rule Violation",
              description: `The selected time slot "${editFormData.timeSlot}" is not available due to business rules (60-minute gap between appointments, shift hours, or rep conflicts). Available slots: ${availabilityData.timeSlots?.join(', ')}`,
              variant: "destructive",
            });
            // CRITICAL FIX: Do NOT proceed with update if validation fails
            return;
          }
          
          // If available, proceed with update
          proceedWithUpdate();
        } catch (error) {
          console.error('Error checking availability:', error);
          // STRICT ENFORCEMENT: Do not allow admin to bypass if validation fails
          toast({
            title: "Validation Error",
            description: "Could not verify time slot availability. Please try again or contact support.",
            variant: "destructive",
          });
          // Do NOT proceed with update if availability check fails
          return;
        }
      };
      
      checkAvailability();
    } else {
      // No date/time changes, proceed directly
      proceedWithUpdate();
    }
    
    function proceedWithUpdate() {
      if (!editingBooking) return;
      
      const updates = {
        name: editFormData.name,
        phone: editFormData.phone,
        email: editFormData.email || null,
        address: editFormData.address,
        date: editFormData.date,
        timeSlot: editFormData.timeSlot,
        status: editFormData.status,
        repAssigned: editFormData.repAssigned === "unassigned" ? null : editFormData.repAssigned,
        customerSatisfaction: editFormData.customerSatisfaction ? parseInt(editFormData.customerSatisfaction) : null,
        feedbackNotes: editFormData.feedbackNotes || null,
        orderId: editFormData.orderId || null
      };

      updateBookingMutation.mutate({
        id: editingBooking.id,
        ...updates
      });
    }
  };

  const handleCompleteSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!completingBooking) return;

    const completionData = {
      appointmentCompleted: true,
      customerSatisfaction: completeFormData.customerSatisfaction ? parseInt(completeFormData.customerSatisfaction) : null,
      feedbackNotes: completeFormData.feedbackNotes || null,
      purchaseAmount: completeFormData.transactionMade && completeFormData.purchaseAmount ? 
        Math.round(parseFloat(completeFormData.purchaseAmount) * 100) : null, // Convert to paise
      orderId: completeFormData.transactionMade ? completeFormData.orderId || null : null,
      paymentStatus: completeFormData.transactionMade ? "completed" : null,
      status: "completed"
    };

    completeBookingMutation.mutate({
      id: completingBooking.id,
      completionData
    });
  };

  const getFilteredBookings = () => {
    if (!bookings) return [];
    
    const now = new Date();
    let filteredByDate = bookings;
    
    // First filter by date
    switch (exportFilter) {
      case 'daily':
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);
        filteredByDate = bookings.filter(b => {
          const bookingDate = new Date(b.date);
          return bookingDate >= today && bookingDate < tomorrow;
        });
        break;
      
      case 'weekly':
        const weekStart = startOfWeek(now, { weekStartsOn: 1 }); // Monday start
        const weekEnd = new Date(weekStart);
        weekEnd.setDate(weekEnd.getDate() + 7);
        filteredByDate = bookings.filter(b => {
          const bookingDate = new Date(b.date);
          return bookingDate >= weekStart && bookingDate < weekEnd;
        });
        break;
      
      case 'monthly':
        const monthStart = startOfMonth(now);
        const monthEnd = new Date(monthStart);
        monthEnd.setMonth(monthEnd.getMonth() + 1);
        filteredByDate = bookings.filter(b => {
          const bookingDate = new Date(b.date);
          return bookingDate >= monthStart && bookingDate < monthEnd;
        });
        break;
      
      case 'all':
      default:
        filteredByDate = bookings;
        break;
    }
    
    // Then filter by status
    return filteredByDate.filter(b => 
      statusFilter === "all" || b.status === statusFilter
    );
  };

  const exportToCSV = () => {
    const filteredBookings = getFilteredBookings();
    if (filteredBookings.length === 0) {
      toast({ title: "No bookings to export for selected filter", variant: "destructive" });
      return;
    }

    const headers = [
      'Booking ID',
      'Customer Name', 
      'Phone',
      'Email',
      'Address',
      'Postal Code',
      'Date',
      'Time Slot',
      'Rep Assigned',
      'Status',
      'Service',
      'Created At'
    ];

    const csvData = filteredBookings.map(booking => [
      booking.id,
      booking.name,
      booking.phone,
      booking.email || '',
      booking.address,
      booking.postalCode,
      toApiDate(booking.date),
      booking.timeSlot,
      booking.repAssigned || 'Not assigned',
      booking.status,
      booking.service,
      formatDate((booking as any).createdAt || booking.date, 'TIMESTAMP')
    ]);

    const csvContent = [headers, ...csvData]
      .map(row => row.map(field => `"${field}"`).join(','))
      .join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `kult-bookings-${exportFilter}-${toApiDate(new Date())}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    toast({ title: `Exported ${filteredBookings.length} bookings successfully` });
  };



  if (isLoading) {
    return <div className="text-center p-8">Loading bookings...</div>;
  }

  const getStatusBadge = (status: string) => {
    const variants = {
      confirmed: "default",
      cancelled: "destructive",
      completed: "secondary",
    } as const;

    return (
      <Badge variant={variants[status as keyof typeof variants] || "default"}>
        {status}
      </Badge>
    );
  };

  return (
    <div className="space-y-4">
      {/* Enhanced Filtering Controls */}
      <div className="bg-white p-4 rounded-lg border shadow-sm">
        <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between">
          <h3 className="text-lg font-semibold">All Bookings</h3>
          
          {/* Filter Controls */}
          <div className="flex flex-wrap gap-3 items-center">
            {/* Date Filter Dropdown */}
            <div className="flex items-center gap-2">
              <Label className="text-sm font-medium">Date Filter:</Label>
              <Select value={dateFilter} onValueChange={setDateFilter}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Time</SelectItem>
                  <SelectItem value="today">Today</SelectItem>
                  <SelectItem value="week">This Week</SelectItem>
                  <SelectItem value="month">This Month</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Date Navigation (when Today filter is active) */}
            {dateFilter === 'today' && (
              <div className="flex items-center gap-1">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => navigateDate('prev')}
                  className="p-2"
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                <span className="text-sm font-medium px-3">
                  {currentDate ? toShortDate(currentDate) : toShortDate(new Date())}
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => navigateDate('next')}
                  className="p-2"
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            )}

            {/* Status Filter */}
            <div className="flex items-center gap-2">
              <Label className="text-sm font-medium">Status:</Label>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-48">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="confirmed">Confirmed</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="pending_reassignment">Pending Reassignment</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Export CSV Button */}
            <Button 
              onClick={handleExportCSV}
              variant="outline"
              className="flex items-center gap-2"
            >
              <Download className="h-4 w-4" />
              Export CSV
            </Button>
          </div>
        </div>
      </div>

      <div className="border rounded-lg overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Customer</TableHead>
              <TableHead>
                <Button
                  variant="ghost"
                  onClick={toggleSortOrder}
                  className="h-auto p-0 font-semibold hover:bg-transparent"
                >
                  Date & Time
                  <ArrowUpDown className="ml-2 h-4 w-4" />
                </Button>
              </TableHead>
              <TableHead>Address</TableHead>
              <TableHead>Rep Assigned</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>UTM Campaign</TableHead>
              <TableHead>
                <Button
                  variant="ghost"
                  onClick={toggleCreatedSortOrder}
                  className="h-auto p-0 font-semibold hover:bg-transparent"
                >
                  Created
                  <ArrowUpDown className="ml-2 h-4 w-4" />
                </Button>
              </TableHead>
              <TableHead>Session ID</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {bookings.map((booking: any) => (
              <TableRow key={booking.id}>
                <TableCell>
                  <div>
                    <div className="font-medium">{booking.name}</div>
                    <div className="text-sm text-gray-500">{booking.phone}</div>
                    <div className="text-sm text-gray-500">PIN: {booking.postalCode}</div>
                  </div>
                </TableCell>
                <TableCell>
                  <div>
                    <div className="font-medium">
                      {toShortDate(booking.date)}
                    </div>
                    <div className="text-sm text-gray-500">{formatTimeTo12Hour(booking.timeSlot)}</div>
                  </div>
                </TableCell>
                <TableCell className="max-w-xs truncate">
                  {booking.address}
                </TableCell>
                <TableCell>{booking.repAssigned || "Not assigned"}</TableCell>
                <TableCell>{getStatusBadge(booking.status)}</TableCell>
                <TableCell>
                  <div className="text-xs max-w-[120px]">
                    {booking.utmCampaign ? (
                      <div>
                        <div className="font-medium text-blue-600 truncate" title={booking.utmCampaign}>
                          {booking.utmCampaign}
                        </div>
                        <div className="text-gray-500 truncate" title={`${booking.utmSource || 'unknown'}/${booking.utmMedium || 'unknown'}`}>
                          {booking.utmSource || 'unknown'}/{booking.utmMedium || 'unknown'}
                        </div>
                      </div>
                    ) : (
                      <span className="text-gray-400">Unknown</span>
                    )}
                  </div>
                </TableCell>
                <TableCell>
                  <div className="text-sm">
                    <div>{toShortDate(booking.createdAt)}</div>
                    <div className="text-gray-500">{formatDate(booking.createdAt, "TIME_24H")}</div>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="text-xs font-mono text-gray-500 max-w-[100px] truncate" title={booking.sessionId || 'No session data'}>
                    {booking.sessionId ? booking.sessionId.replace('session_', '') : 'Direct'}
                  </div>
                </TableCell>
                <TableCell>
                  <div className="flex space-x-1">
                    <Button 
                      variant="ghost" 
                      size="sm"
                      onClick={() => handleView(booking)}
                      title="View booking details"
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                    <Button 
                      variant="ghost" 
                      size="sm"
                      onClick={() => handleViewHistory(booking)}
                      title="View booking history"
                      className="text-purple-600 hover:text-purple-700"
                    >
                      <History className="h-4 w-4" />
                    </Button>
                    {!readOnly && (
                      <Button 
                        variant="ghost" 
                        size="sm"
                        onClick={() => handleEdit(booking)}
                        title="Edit booking"
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                    )}
                    {!readOnly && (
                      <Button 
                        variant="ghost" 
                        size="sm"
                        onClick={() => handleSendReminder(booking)}
                        disabled={sendReminderMutation.isPending}
                        className="text-blue-600 hover:text-blue-700"
                        title="Send WhatsApp reminder"
                      >
                        <AlarmClock className="h-4 w-4" />
                      </Button>
                    )}
                    {!readOnly && (
                      <Button 
                        variant="ghost" 
                        size="sm"
                        onClick={() => handleCxFollowup(booking)}
                        disabled={sendCxFollowupMutation.isPending}
                        className="text-orange-600 hover:text-orange-700"
                        title="Send 'Tried Reaching' follow-up"
                      >
                        <PhoneCall className="h-4 w-4" />
                      </Button>
                    )}
                    {!readOnly && booking.status === "confirmed" && (
                      <Button 
                        variant="ghost" 
                        size="sm"
                        onClick={() => handleComplete(booking)}
                        disabled={completeBookingMutation.isPending}
                        className="text-green-600 hover:text-green-700"
                        title="Complete booking"
                      >
                        <CheckCircle2 className="h-4 w-4" />
                      </Button>
                    )}
                    {!readOnly && (
                      <Button 
                        variant="ghost" 
                        size="sm"
                        onClick={() => handleDelete(booking)}
                        disabled={deleteBookingMutation.isPending}
                        className="text-red-600 hover:text-red-700"
                        title="Delete booking"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {(!bookings || bookings.length === 0) && (
        <div className="text-center p-8 text-gray-500">
          No bookings found. Create your first booking to get started.
        </div>
      )}

      {/* View Booking Dialog */}
      <Dialog open={!!viewingBooking} onOpenChange={() => setViewingBooking(null)}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Booking Details</DialogTitle>
            <DialogDescription>
              View complete booking information including customer details, appointment time, and assigned representative.
            </DialogDescription>
          </DialogHeader>
          {viewingBooking && (
            <div className="space-y-4">
              <div className="flex items-center gap-3">
                <User className="h-5 w-5 text-gray-500" />
                <div>
                  <div className="font-medium">{viewingBooking.name}</div>
                  <div className="text-sm text-gray-500">{viewingBooking.phone}</div>
                  {viewingBooking.email && (
                    <div className="text-sm text-gray-500">{viewingBooking.email}</div>
                  )}
                </div>
              </div>

              <div className="flex items-center gap-3">
                <Calendar className="h-5 w-5 text-gray-500" />
                <div>
                  <div className="font-medium">
                    {formatDate(viewingBooking.date, "DISPLAY_DATE")}
                  </div>
                  <div className="text-sm text-gray-500">{formatTimeTo12Hour(viewingBooking.timeSlot)}</div>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <MapPin className="h-5 w-5 text-gray-500 mt-0.5" />
                <div>
                  <div className="font-medium">Address</div>
                  <div className="text-sm text-gray-500">{viewingBooking.address}</div>
                  <div className="text-sm text-gray-500">PIN: {viewingBooking.postalCode}</div>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <User className="h-5 w-5 text-gray-500" />
                <div>
                  <div className="font-medium">Assigned Rep</div>
                  <div className="text-sm text-gray-500">
                    {viewingBooking.repAssigned || "Not assigned"}
                  </div>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <div className="h-5 w-5" />
                <div>
                  <div className="font-medium">Status</div>
                  <div className="text-sm">{getStatusBadge(viewingBooking.status)}</div>
                </div>
              </div>

              {/* Traffic Source */}
              {viewingBooking.trafficSource && (
                <div className="flex items-start gap-3">
                  <Globe className="h-5 w-5 text-gray-500 mt-0.5 flex-shrink-0" />
                  <div className="min-w-0 flex-1 overflow-hidden">
                    <div className="font-medium">Traffic Source</div>
                    <div className="text-sm text-gray-500 break-all whitespace-pre-wrap leading-relaxed">
                      {viewingBooking.trafficSource}
                    </div>
                    {viewingBooking.utmCampaign && (
                      <div className="text-xs text-gray-400 break-all whitespace-pre-wrap leading-relaxed mt-1">
                        Campaign: {viewingBooking.utmCampaign}
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* User Journey Timeline - Show if session data exists */}
              {viewingBooking.sessionId && (
                <div className="border-t pt-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center gap-2">
                      <TrendingUp className="h-5 w-5 text-blue-500" />
                      <div className="font-medium">Customer Journey</div>
                    </div>
                    {funnelJourney && (
                      <div className="text-xs text-gray-500">
                        {(funnelJourney.totalTimeMinutes * 60 * 1000).toLocaleString()}ms total • {funnelJourney.stepsCompleted}/{funnelJourney.totalSteps} steps
                      </div>
                    )}
                  </div>
                  
                  {funnelLoading ? (
                    <div className="flex items-center gap-2 text-sm text-gray-500">
                      <div className="animate-spin h-4 w-4 border-2 border-gray-300 border-t-blue-500 rounded-full"></div>
                      Loading journey...
                    </div>
                  ) : funnelJourney ? (
                    <div className="space-y-2 max-h-48 overflow-y-auto">
                      {funnelJourney.funnelSteps.map((step, index) => {
                        const IconComponent = step.icon === 'Globe' ? Globe : 
                                            step.icon === 'Play' ? Play :
                                            step.icon === 'MapPin' ? MapPin :
                                            step.icon === 'Calendar' ? Calendar :
                                            step.icon === 'Clock' ? Clock :
                                            step.icon === 'Shield' ? Shield :
                                            step.icon === 'CheckCircle' ? CheckCircle :
                                            step.icon === 'CheckCircle2' ? CheckCircle2 : Globe;
                        
                        return (
                          <div key={index} className={`flex items-center gap-3 py-2 px-3 rounded-lg ${
                            step.completed ? 'bg-green-50 border border-green-200' : 'bg-gray-50 border border-gray-200'
                          }`}>
                            <IconComponent className={`h-4 w-4 ${
                              step.completed ? 'text-green-600' : 'text-gray-400'
                            }`} />
                            <div className="flex-1">
                              <div className={`text-sm font-medium ${
                                step.completed ? 'text-green-800' : 'text-gray-500'
                              }`}>
                                {step.step}
                              </div>
                              {step.completed && step.timeFromPrevious !== null && (
                                <div className="text-xs text-green-600">
                                  {(step.timeFromPrevious * 1000).toLocaleString()}ms
                                  {step.totalTimeFromStart !== null && (
                                    <span className="text-gray-500 ml-1">
                                      (total: {(step.totalTimeFromStart * 1000).toLocaleString()}ms)
                                    </span>
                                  )}
                                </div>
                              )}
                            </div>
                          </div>
                        );
                      })}
                      
                      {/* Journey Summary */}
                      <div className="mt-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                        <div className="text-sm font-medium text-blue-800 mb-1">Journey Summary</div>
                        <div className="text-xs text-blue-600 space-y-1">
                          <div>Total events: {funnelJourney.eventCount}</div>
                          <div>Conversion rate: {funnelJourney.conversionRate}%</div>
                          <div>Average step time: {funnelJourney.averageStepTime}s</div>
                          <div>Source: {funnelJourney.traffic_source}</div>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="text-sm text-gray-500">
                      No journey data available
                    </div>
                  )}
                </div>
              )}

              <div className="flex items-center gap-3">
                <Clock className="h-5 w-5 text-gray-500" />
                <div>
                  <div className="font-medium">Created</div>
                  <div className="text-sm text-gray-500">
                    {formatDate(viewingBooking.createdAt, "DATETIME_DETAILED")}
                  </div>
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Edit Booking Dialog */}
      <Dialog open={!!editingBooking} onOpenChange={() => setEditingBooking(null)}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Edit Booking</DialogTitle>
            <DialogDescription>
              Modify booking details including customer information, appointment time, assigned representative, and status.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleEditSubmit} className="space-y-4">
            <div>
              <Label htmlFor="edit-name">Customer Name</Label>
              <Input
                id="edit-name"
                value={editFormData.name}
                onChange={(e) => setEditFormData(prev => ({ ...prev, name: e.target.value }))}
                required
              />
            </div>

            <div>
              <Label htmlFor="edit-phone">Phone</Label>
              <Input
                id="edit-phone"
                value={editFormData.phone}
                onChange={(e) => setEditFormData(prev => ({ ...prev, phone: e.target.value }))}
                required
              />
            </div>

            <div>
              <Label htmlFor="edit-email">Email</Label>
              <Input
                id="edit-email"
                type="email"
                value={editFormData.email}
                onChange={(e) => setEditFormData(prev => ({ ...prev, email: e.target.value }))}
              />
            </div>

            <div>
              <Label htmlFor="edit-address">Address</Label>
              <Textarea
                id="edit-address"
                value={editFormData.address}
                onChange={(e) => setEditFormData(prev => ({ ...prev, address: e.target.value }))}
                required
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="edit-date">Date</Label>
                <Input
                  id="edit-date"
                  type="date"
                  value={editFormData.date}
                  onChange={(e) => setEditFormData(prev => ({ ...prev, date: e.target.value }))}
                  required
                />
              </div>
              <div>
                <Label htmlFor="edit-time">Time</Label>
                <Input
                  id="edit-time"
                  value={editFormData.timeSlot}
                  onChange={(e) => setEditFormData(prev => ({ ...prev, timeSlot: e.target.value }))}
                  placeholder="14:00"
                  required
                />
              </div>
            </div>

            <div>
              <Label htmlFor="edit-rep">Assigned Rep</Label>
              <Select 
                value={editFormData.repAssigned} 
                onValueChange={(value) => setEditFormData(prev => ({ ...prev, repAssigned: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select a representative" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="unassigned">Unassigned</SelectItem>
                  {reps?.map((rep: any) => (
                    <SelectItem key={rep.id} value={rep.name}>
                      {rep.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="edit-status">Status</Label>
              <Select 
                value={editFormData.status} 
                onValueChange={(value) => setEditFormData(prev => ({ ...prev, status: value }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="confirmed">Confirmed</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                  <SelectItem value="pending_reassignment">Pending Reassignment</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="edit-satisfaction">Satisfaction (1-5)</Label>
                <Input
                  id="edit-satisfaction"
                  type="number"
                  min="1"
                  max="5"
                  value={editFormData.customerSatisfaction}
                  onChange={(e) => setEditFormData(prev => ({ ...prev, customerSatisfaction: e.target.value }))}
                  placeholder="1-5 rating"
                />
              </div>
              <div>
                <Label htmlFor="edit-transaction">Transaction ID</Label>
                <Input
                  id="edit-transaction"
                  value={editFormData.orderId}
                  onChange={(e) => setEditFormData(prev => ({ ...prev, orderId: e.target.value }))}
                  placeholder="Enter if purchase happened"
                />
              </div>
            </div>

            <div>
              <Label htmlFor="edit-feedback">Feedback Notes</Label>
              <Textarea
                id="edit-feedback"
                value={editFormData.feedbackNotes}
                onChange={(e) => setEditFormData(prev => ({ ...prev, feedbackNotes: e.target.value }))}
                placeholder="Customer feedback or notes"
              />
            </div>

            <div className="flex gap-2 pt-4">
              <Button 
                type="submit" 
                className="flex-1 bg-purple-600 hover:bg-purple-700 text-white"
                disabled={updateBookingMutation.isPending}
              >
                {updateBookingMutation.isPending ? "Updating..." : "Update Booking"}
              </Button>
              <Button 
                type="button" 
                variant="outline" 
                onClick={() => setEditingBooking(null)}
              >
                Close
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>

      {/* Complete Booking Dialog */}
      <Dialog open={!!completingBooking} onOpenChange={() => setCompletingBooking(null)}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Complete Booking</DialogTitle>
            <DialogDescription>
              Mark this appointment as completed and record transaction details.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleCompleteSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label>Transaction Made?</Label>
              <div className="flex space-x-4">
                <label className="flex items-center space-x-2">
                  <input
                    type="radio"
                    checked={!completeFormData.transactionMade}
                    onChange={() => setCompleteFormData(prev => ({ ...prev, transactionMade: false }))}
                  />
                  <span>No</span>
                </label>
                <label className="flex items-center space-x-2">
                  <input
                    type="radio"
                    checked={completeFormData.transactionMade}
                    onChange={() => setCompleteFormData(prev => ({ ...prev, transactionMade: true }))}
                  />
                  <span>Yes</span>
                </label>
              </div>
            </div>

            {completeFormData.transactionMade && (
              <>
                <div className="space-y-2">
                  <Label htmlFor="purchaseAmount">Purchase Amount (₹)</Label>
                  <Input
                    id="purchaseAmount"
                    type="number"
                    step="0.01"
                    placeholder="0.00"
                    value={completeFormData.purchaseAmount}
                    onChange={(e) => setCompleteFormData(prev => ({ ...prev, purchaseAmount: e.target.value }))}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="orderId">Order ID (Optional)</Label>
                  <Input
                    id="orderId"
                    placeholder="Enter order ID"
                    value={completeFormData.orderId}
                    onChange={(e) => setCompleteFormData(prev => ({ ...prev, orderId: e.target.value }))}
                  />
                </div>
              </>
            )}

            <div className="space-y-2">
              <Label htmlFor="customerSatisfaction">Customer Satisfaction (1-10)</Label>
              <Select
                value={completeFormData.customerSatisfaction}
                onValueChange={(value) => setCompleteFormData(prev => ({ ...prev, customerSatisfaction: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Rate satisfaction" />
                </SelectTrigger>
                <SelectContent>
                  {Array.from({ length: 10 }, (_, i) => i + 1).map(num => (
                    <SelectItem key={num} value={num.toString()}>{num}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="feedbackNotes">Notes (Optional)</Label>
              <Textarea
                id="feedbackNotes"
                placeholder="Additional feedback or notes..."
                value={completeFormData.feedbackNotes}
                onChange={(e) => setCompleteFormData(prev => ({ ...prev, feedbackNotes: e.target.value }))}
                rows={3}
              />
            </div>

            <div className="flex justify-end space-x-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => setCompletingBooking(null)}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={completeBookingMutation.isPending}
              >
                {completeBookingMutation.isPending ? "Completing..." : "Complete Booking"}
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>

      {/* Booking History Modal */}
      <BookingHistoryModal
        isOpen={showHistoryModal}
        onClose={() => setShowHistoryModal(false)}
        phoneNumber={historyPhoneNumber}
      />
    </div>
  );
}
