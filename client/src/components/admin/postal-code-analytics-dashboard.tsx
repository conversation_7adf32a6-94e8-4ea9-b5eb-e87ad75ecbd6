import { useState, useEffect } from "react";
import { useQ<PERSON>y, useMutation, useQueryClient } from "@tanstack/react-query";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { MapPin, Calendar, TrendingUp, Users, Download, Plus, AlertCircle, CheckCircle, Eye, ChevronLeft, ChevronRight } from "lucide-react";
import { format } from "date-fns";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>sponsive<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Bar, <PERSON>xis, <PERSON><PERSON><PERSON>, <PERSON>tesianGrid, <PERSON>lt<PERSON>, <PERSON><PERSON>hart, Line } from "recharts";
import { apiRequest } from "@/lib/queryClient";

interface PostalCodeAttempt {
  session_id: string;
  postal_code: string;
  timestamp: string;
  entry_page: string;
  is_valid: boolean;
  matched_service_area: string | null;
  final_code: string | null;
  booking_id: number | null;
  customer_name: string | null;
  rep_assigned: string | null;
  booking_status: string | null;
  outcome: string;
}

interface FunnelStats {
  total_invalid_entries: number;
  corrected_entries: number;
  led_to_bookings: number;
  dropped_sessions: number;
  correction_rate: number;
  conversion_rate: number;
  drop_off_rate: number;
}

interface FrequentInvalidCode {
  postal_code: string;
  attempt_count: number;
  unique_sessions: number;
  first_attempt: string;
  last_attempt: string;
}

interface PostalDashboardData {
  sessions: PostalCodeAttempt[];
  funnelStats: FunnelStats;
  frequentInvalidCodes: FrequentInvalidCode[];
}

interface ServiceAreaForm {
  postalCode: string;
  city: string;
  state: string;
  addedBy: string;
}

const COLORS = ['#8B5CF6', '#06B6D4', '#10B981', '#F59E0B', '#EF4444'];

export default function PostalCodeAnalyticsDashboard() {
  const [dateRange, setDateRange] = useState({
    startDate: '2025-06-01', // Show all historical data from webapp beginning
    endDate: new Date().toISOString().split('T')[0]
  });
  const [filters, setFilters] = useState({
    outcomeFilter: 'all',
    validityFilter: 'all'
  });
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(100); // 100 entries per page
  const [showAddServiceArea, setShowAddServiceArea] = useState(false);
  const [showInvalidCodesModal, setShowInvalidCodesModal] = useState(false);
  const [serviceAreaForm, setServiceAreaForm] = useState<ServiceAreaForm>({
    postalCode: '',
    city: '',
    state: '',
    addedBy: 'admin'
  });

  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch postal code analytics data
  const { data: dashboardData, isLoading, refetch } = useQuery<PostalDashboardData>({
    queryKey: ['/api/analytics/postal-dashboard', dateRange, filters],
    queryFn: async () => {
      const params = new URLSearchParams({
        startDate: dateRange.startDate,
        endDate: dateRange.endDate,
        outcomeFilter: filters.outcomeFilter,
        validityFilter: filters.validityFilter
      });
      const response = await fetch(`/api/analytics/postal-dashboard?${params}`);
      if (!response.ok) throw new Error('Failed to fetch postal dashboard data');
      return response.json();
    }
  });

  // Reset pagination when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [filters, dateRange]);

  // Add new service area mutation
  const addServiceAreaMutation = useMutation({
    mutationFn: async (data: ServiceAreaForm) => {
      return await apiRequest('POST', '/api/service-areas/add', data);
    },
    onSuccess: () => {
      toast({
        title: "Service Area Added",
        description: `Postal code ${serviceAreaForm.postalCode} added successfully`,
      });
      setShowAddServiceArea(false);
      setServiceAreaForm({ postalCode: '', city: '', state: '', addedBy: 'admin' });
      refetch();
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to add service area",
        variant: "destructive",
      });
    }
  });

  // Export data function
  const handleExport = async () => {
    try {
      const params = new URLSearchParams({
        startDate: dateRange.startDate,
        endDate: dateRange.endDate
      });
      
      const response = await fetch(`/api/analytics/postal-export?${params}`);
      if (!response.ok) throw new Error('Export failed');
      
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `postal_code_analytics_${dateRange.startDate}_to_${dateRange.endDate}.csv`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);
      
      toast({
        title: "Export Complete",
        description: "Postal code analytics data has been downloaded",
      });
    } catch (error) {
      toast({
        title: "Export Failed",
        description: "Could not export postal code data",
        variant: "destructive",
      });
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  const funnelStats = dashboardData?.funnelStats || {};
  const sessions = dashboardData?.sessions || [];
  const frequentInvalidCodes = dashboardData?.frequentInvalidCodes || [];

  // Prepare chart data
  const outcomeData = [
    { name: 'Booked', value: (funnelStats as any)?.led_to_bookings || 0, color: '#10B981' },
    { name: 'Dropped', value: (funnelStats as any)?.dropped_sessions || 0, color: '#EF4444' }
  ];

  const validityData = sessions.reduce((acc, session) => {
    const validity = session.is_valid ? 'Valid' : 'Invalid';
    acc[validity] = (acc[validity] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  const validityChartData = Object.entries(validityData).map(([name, value]) => ({
    name,
    value,
    color: name === 'Valid' ? '#10B981' : '#EF4444'
  }));

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold">Postal Code Analytics</h2>
          <p className="text-gray-600">Track postal code entries and service area expansion opportunities</p>
        </div>
        <div className="flex gap-2">
          <Button onClick={handleExport} variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export Data
          </Button>
          <Dialog open={showAddServiceArea} onOpenChange={setShowAddServiceArea}>
            <DialogTrigger asChild>
              <Button size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Add Service Area
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Add New Service Area</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="postalCode">Postal Code</Label>
                  <Input
                    id="postalCode"
                    value={serviceAreaForm.postalCode}
                    onChange={(e) => setServiceAreaForm(prev => ({ ...prev, postalCode: e.target.value }))}
                    placeholder="110001"
                  />
                </div>
                <div>
                  <Label htmlFor="city">City</Label>
                  <Input
                    id="city"
                    value={serviceAreaForm.city}
                    onChange={(e) => setServiceAreaForm(prev => ({ ...prev, city: e.target.value }))}
                    placeholder="New Delhi"
                  />
                </div>
                <div>
                  <Label htmlFor="state">State</Label>
                  <Input
                    id="state"
                    value={serviceAreaForm.state}
                    onChange={(e) => setServiceAreaForm(prev => ({ ...prev, state: e.target.value }))}
                    placeholder="Delhi"
                  />
                </div>
                <Button 
                  onClick={() => addServiceAreaMutation.mutate(serviceAreaForm)}
                  disabled={!serviceAreaForm.postalCode || !serviceAreaForm.city || !serviceAreaForm.state || addServiceAreaMutation.isPending}
                  className="w-full"
                >
                  {addServiceAreaMutation.isPending ? "Adding..." : "Add Service Area"}
                </Button>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <Label>Start Date</Label>
              <Input
                type="date"
                value={dateRange.startDate}
                onChange={(e) => setDateRange(prev => ({ ...prev, startDate: e.target.value }))}
              />
            </div>
            <div>
              <Label>End Date</Label>
              <Input
                type="date"
                value={dateRange.endDate}
                onChange={(e) => setDateRange(prev => ({ ...prev, endDate: e.target.value }))}
              />
            </div>
            <div>
              <Label>Outcome</Label>
              <Select value={filters.outcomeFilter} onValueChange={(value) => setFilters(prev => ({ ...prev, outcomeFilter: value }))}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Outcomes</SelectItem>
                  <SelectItem value="booked">Booked</SelectItem>
                  <SelectItem value="dropped">Dropped</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label>Validity</Label>
              <Select value={filters.validityFilter} onValueChange={(value) => setFilters(prev => ({ ...prev, validityFilter: value }))}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Codes</SelectItem>
                  <SelectItem value="valid">Valid Codes Only</SelectItem>
                  <SelectItem value="invalid">Invalid Codes Only</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Entries</CardTitle>
            <MapPin className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{sessions.length}</div>
            <p className="text-xs text-gray-600">Postal code attempts</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Invalid Codes</CardTitle>
            <AlertCircle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{(funnelStats as any)?.invalid_codes || 0}</div>
            <p className="text-xs text-gray-600">
              {sessions.length > 0 ? Math.round(((funnelStats as any)?.invalid_codes || 0) / sessions.length * 100) : 0}% of total
            </p>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-lg transition-shadow" onClick={() => {
          setFilters(prev => ({ ...prev, validityFilter: 'invalid' }));
          setShowInvalidCodesModal(true);
        }}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Wrong Codes</CardTitle>
            <Eye className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{frequentInvalidCodes.length}</div>
            <p className="text-xs text-gray-600">Click to view all invalid codes</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Conversion Rate</CardTitle>
            <TrendingUp className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{(funnelStats as any)?.conversion_rate || 0}%</div>
            <p className="text-xs text-gray-600">Entries to bookings</p>
          </CardContent>
        </Card>
      </div>

      {/* Charts Row */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Outcome Distribution */}
        <Card>
          <CardHeader>
            <CardTitle>Booking Outcomes</CardTitle>
            <CardDescription>Distribution of postal code entry outcomes</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={outcomeData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {outcomeData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Validity Distribution */}
        <Card>
          <CardHeader>
            <CardTitle>Code Validity</CardTitle>
            <CardDescription>Valid vs invalid postal code entries</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={validityChartData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {validityChartData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* All Postal Code Attempts Table */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>All Postal Code Attempts</CardTitle>
              <CardDescription>Complete historical data - {sessions.length} total entries</CardDescription>
            </div>
            <div className="text-sm text-gray-600">
              Showing {Math.min((currentPage - 1) * pageSize + 1, sessions.length)} - {Math.min(currentPage * pageSize, sessions.length)} of {sessions.length}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full border-collapse">
              <thead>
                <tr className="border-b">
                  <th className="text-left p-2 font-medium">Session ID</th>
                  <th className="text-left p-2 font-medium">Postal Code</th>
                  <th className="text-left p-2 font-medium">Time</th>
                  <th className="text-left p-2 font-medium">Valid</th>
                  <th className="text-left p-2 font-medium">Service Area</th>
                  <th className="text-left p-2 font-medium">Outcome</th>
                  <th className="text-left p-2 font-medium">Customer</th>
                </tr>
              </thead>
              <tbody>
                {sessions.slice((currentPage - 1) * pageSize, currentPage * pageSize).map((session) => (
                  <tr key={session.session_id} className="border-b hover:bg-gray-50">
                    <td className="p-2 font-mono text-sm">{session.session_id.slice(0, 8)}...</td>
                    <td className="p-2 font-medium">{session.postal_code}</td>
                    <td className="p-2 text-sm">{format(new Date(session.timestamp), 'MMM d, HH:mm')}</td>
                    <td className="p-2">
                      {session.is_valid ? (
                        <Badge variant="default" className="bg-green-100 text-green-800">
                          <CheckCircle className="h-3 w-3 mr-1" />
                          Valid
                        </Badge>
                      ) : (
                        <Badge variant="destructive" className="bg-red-100 text-red-800">
                          <AlertCircle className="h-3 w-3 mr-1" />
                          Invalid
                        </Badge>
                      )}
                    </td>
                    <td className="p-2 text-sm">{session.matched_service_area || '-'}</td>
                    <td className="p-2">
                      <Badge variant={session.outcome === 'Booked' ? 'default' : 'secondary'}>
                        {session.outcome}
                      </Badge>
                    </td>
                    <td className="p-2 text-sm">{session.customer_name || '-'}</td>
                  </tr>
                ))}
              </tbody>
            </table>
            {/* Pagination Controls */}
            {sessions.length > pageSize && (
              <div className="flex justify-between items-center mt-4 pt-4 border-t">
                <div className="text-sm text-gray-600">
                  Page {currentPage} of {Math.ceil(sessions.length / pageSize)}
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                    disabled={currentPage === 1}
                  >
                    ← Previous
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(prev => Math.min(Math.ceil(sessions.length / pageSize), prev + 1))}
                    disabled={currentPage === Math.ceil(sessions.length / pageSize)}
                  >
                    Next →
                  </Button>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Invalid Codes Modal */}
      <Dialog open={showInvalidCodesModal} onOpenChange={setShowInvalidCodesModal}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>Frequently Entered Invalid Postal Codes</DialogTitle>
          </DialogHeader>
          <div className="overflow-x-auto max-h-96">
            <table className="w-full border-collapse">
              <thead>
                <tr className="border-b">
                  <th className="text-left p-2 font-medium">Postal Code</th>
                  <th className="text-left p-2 font-medium">Attempt Count</th>
                  <th className="text-left p-2 font-medium">Unique Sessions</th>
                  <th className="text-left p-2 font-medium">First Attempt</th>
                  <th className="text-left p-2 font-medium">Last Attempt</th>
                  <th className="text-left p-2 font-medium">Action</th>
                </tr>
              </thead>
              <tbody>
                {frequentInvalidCodes.map((code) => (
                  <tr key={code.postal_code} className="border-b hover:bg-gray-50">
                    <td className="p-2 font-medium">{code.postal_code}</td>
                    <td className="p-2">{code.attempt_count}</td>
                    <td className="p-2">{code.unique_sessions}</td>
                    <td className="p-2 text-sm">{format(new Date(code.first_attempt), 'MMM d, yyyy')}</td>
                    <td className="p-2 text-sm">{format(new Date(code.last_attempt), 'MMM d, yyyy')}</td>
                    <td className="p-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => {
                          setServiceAreaForm(prev => ({ ...prev, postalCode: code.postal_code }));
                          setShowInvalidCodesModal(false);
                          setShowAddServiceArea(true);
                        }}
                      >
                        <Plus className="h-3 w-3 mr-1" />
                        Add to Service Area
                      </Button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}