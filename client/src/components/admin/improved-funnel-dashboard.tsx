import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line, PieChart, Pie, Cell } from 'recharts';
import { TrendingUp, TrendingDown, Filter, Download, Calendar, MapPin, Video, Image, Users, Target } from 'lucide-react';

interface CampaignFunnelData {
  campaignName: string;
  utm_source: string;
  utm_medium: string;
  utm_campaign: string;
  meta_parsed?: {
    creativeType: string | null;
    brand: string | null;
    product: string | null;
    offer: string | null;
    location: string | null;
    audience: string | null;
  };
  funnel: {
    sessionsStarted: number;
    viewedHomepage: number;
    reachedCalendar: number;
    filledAddress: number;
    otpEntered: number;
    bookingSubmitted: number;
    bookingConfirmed: number;
    trialHappened?: number;
    orderPlaced?: number;
  };
  metrics: {
    conversionRate: number;
    bookingRate: number;
    bounceRate: number;
    avgSteps: number;
    avgDuration: number;
  };
  firstSeen: string;
  lastSeen: string;
  activeConversionWindow: boolean;
}

interface FilterState {
  dateRange: string;
  campaign: string;
  location: string;
  creativeType: string;
  source: string;
  search: string;
  cleanDataOnly: boolean;
}

export default function ImprovedFunnelDashboard() {
  const [filters, setFilters] = useState<FilterState>({
    dateRange: '7d',
    campaign: 'all',
    location: 'all',
    creativeType: 'all',
    source: 'all',
    search: '',
    cleanDataOnly: true
  });

  const [selectedTab, setSelectedTab] = useState('overview');

  // Fetch comprehensive funnel data
  const { data: funnelData, isLoading, refetch } = useQuery({
    queryKey: ['/api/analytics/comprehensive-funnel', filters],
    queryFn: async () => {
      const params = new URLSearchParams({
        period: filters.dateRange,
        campaign: filters.campaign,
        location: filters.location,
        creativeType: filters.creativeType,
        source: filters.source,
        search: filters.search,
        cleanDataOnly: filters.cleanDataOnly.toString()
      });
      
      const response = await fetch(`/api/analytics/comprehensive-funnel?${params}`);
      if (!response.ok) throw new Error('Failed to fetch funnel data');
      return response.json();
    }
  });

  // Filter campaigns based on search and filters
  const filteredCampaigns = funnelData?.campaigns?.filter((campaign: CampaignFunnelData) => {
    if (filters.search && !campaign.campaignName.toLowerCase().includes(filters.search.toLowerCase())) {
      return false;
    }
    return true;
  }) || [];

  // Calculate aggregate metrics
  const aggregateMetrics = filteredCampaigns.reduce((acc, campaign) => {
    acc.totalSessions += campaign.funnel.sessionsStarted;
    acc.totalBookings += campaign.funnel.bookingConfirmed;
    acc.totalCampaigns += 1;
    return acc;
  }, { totalSessions: 0, totalBookings: 0, totalCampaigns: 0 });

  const overallConversionRate = aggregateMetrics.totalSessions > 0 
    ? (aggregateMetrics.totalBookings / aggregateMetrics.totalSessions * 100).toFixed(2)
    : '0.00';

  // Get unique filter options
  const uniqueLocations = [...new Set(filteredCampaigns
    .map(c => c.meta_parsed?.location)
    .filter(Boolean))];
  
  const uniqueCreativeTypes = [...new Set(filteredCampaigns
    .map(c => c.meta_parsed?.creativeType)
    .filter(Boolean))];

  const uniqueSources = [...new Set(filteredCampaigns.map(c => c.utm_source))];

  const exportCampaignCSV = (campaign: CampaignFunnelData) => {
    const csvData = [
      ['Funnel Stage', 'Users', 'Conversion Rate'],
      ['Sessions Started', campaign.funnel.sessionsStarted, '100%'],
      ['Viewed Homepage', campaign.funnel.viewedHomepage, `${(campaign.funnel.viewedHomepage / campaign.funnel.sessionsStarted * 100).toFixed(1)}%`],
      ['Reached Calendar', campaign.funnel.reachedCalendar, `${(campaign.funnel.reachedCalendar / campaign.funnel.sessionsStarted * 100).toFixed(1)}%`],
      ['Filled Address', campaign.funnel.filledAddress, `${(campaign.funnel.filledAddress / campaign.funnel.sessionsStarted * 100).toFixed(1)}%`],
      ['OTP Entered', campaign.funnel.otpEntered, `${(campaign.funnel.otpEntered / campaign.funnel.sessionsStarted * 100).toFixed(1)}%`],
      ['Booking Submitted', campaign.funnel.bookingSubmitted, `${(campaign.funnel.bookingSubmitted / campaign.funnel.sessionsStarted * 100).toFixed(1)}%`],
      ['Booking Confirmed', campaign.funnel.bookingConfirmed, `${campaign.metrics.conversionRate.toFixed(1)}%`]
    ];

    const csvContent = csvData.map(row => row.join(',')).join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${campaign.campaignName.replace(/[^a-zA-Z0-9]/g, '_')}_funnel_data.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  const CampaignFunnelCard = ({ campaign }: { campaign: CampaignFunnelData }) => {
    const funnelSteps = [
      { name: 'Sessions Started', value: campaign.funnel.sessionsStarted, color: '#8884d8' },
      { name: 'Viewed Homepage', value: campaign.funnel.viewedHomepage, color: '#82ca9d' },
      { name: 'Reached Calendar', value: campaign.funnel.reachedCalendar, color: '#ffc658' },
      { name: 'Filled Address', value: campaign.funnel.filledAddress, color: '#ff7c7c' },
      { name: 'OTP Entered', value: campaign.funnel.otpEntered, color: '#8dd1e1' },
      { name: 'Booking Submitted', value: campaign.funnel.bookingSubmitted, color: '#d084d0' },
      { name: 'Booking Confirmed', value: campaign.funnel.bookingConfirmed, color: '#87d068' }
    ];

    return (
      <Card className="mb-4">
        <CardHeader>
          <div className="flex justify-between items-start">
            <div>
              <CardTitle className="text-lg">{campaign.campaignName}</CardTitle>
              <CardDescription>
                Source: {campaign.utm_source} | Medium: {campaign.utm_medium}
              </CardDescription>
              {campaign.meta_parsed && (
                <div className="flex gap-2 mt-2">
                  {campaign.meta_parsed.location && (
                    <Badge variant="outline">
                      <MapPin className="w-3 h-3 mr-1" />
                      {campaign.meta_parsed.location}
                    </Badge>
                  )}
                  {campaign.meta_parsed.audience && (
                    <Badge variant="outline">
                      <Users className="w-3 h-3 mr-1" />
                      {campaign.meta_parsed.audience}
                    </Badge>
                  )}
                  {campaign.meta_parsed.creativeType && (
                    <Badge variant="outline">
                      {campaign.meta_parsed.creativeType === 'Video' ? (
                        <Video className="w-3 h-3 mr-1" />
                      ) : (
                        <Image className="w-3 h-3 mr-1" />
                      )}
                      {campaign.meta_parsed.creativeType}
                    </Badge>
                  )}
                </div>
              )}
            </div>
            <div className="text-right">
              <div className="text-2xl font-bold text-green-600">
                {campaign.metrics.conversionRate.toFixed(1)}%
              </div>
              <div className="text-sm text-gray-500">Conversion Rate</div>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
            <div>
              <div className="text-lg font-semibold">{campaign.funnel.sessionsStarted}</div>
              <div className="text-sm text-gray-500">Sessions</div>
            </div>
            <div>
              <div className="text-lg font-semibold">{campaign.funnel.bookingConfirmed}</div>
              <div className="text-sm text-gray-500">Confirmed</div>
            </div>
            <div>
              <div className="text-lg font-semibold">{campaign.metrics.avgSteps.toFixed(1)}</div>
              <div className="text-sm text-gray-500">Avg Steps</div>
            </div>
            <div>
              <div className="text-lg font-semibold">{campaign.metrics.bounceRate.toFixed(1)}%</div>
              <div className="text-sm text-gray-500">Bounce Rate</div>
            </div>
          </div>

          <div className="mb-4">
            <h4 className="text-sm font-medium mb-2">Conversion Funnel</h4>
            <ResponsiveContainer width="100%" height={200}>
              <BarChart data={funnelSteps} layout="horizontal">
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis type="number" />
                <YAxis dataKey="name" type="category" width={100} />
                <Tooltip />
                <Bar dataKey="value" fill="#8884d8" />
              </BarChart>
            </ResponsiveContainer>
          </div>

          <div className="flex justify-between items-center">
            <div className="text-xs text-gray-500">
              Active: {new Date(campaign.firstSeen).toLocaleDateString()} - {new Date(campaign.lastSeen).toLocaleDateString()}
            </div>
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => exportCampaignCSV(campaign)}
            >
              <Download className="w-4 h-4 mr-1" />
              Export CSV
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  };

  if (isLoading) {
    return <div className="p-6">Loading comprehensive funnel data...</div>;
  }

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold">Campaign Conversion Funnels</h1>
          <p className="text-gray-600">Detailed funnel analysis by campaign with clean data filtering</p>
        </div>
        <div className="flex gap-2">
          <Button 
            variant="outline" 
            onClick={() => setFilters(prev => ({ ...prev, cleanDataOnly: !prev.cleanDataOnly }))}
            className={filters.cleanDataOnly ? 'bg-green-50 border-green-200' : ''}
          >
            <Filter className="w-4 h-4 mr-1" />
            {filters.cleanDataOnly ? 'Clean Data Only' : 'All Data'}
          </Button>
          <Button variant="outline" onClick={() => refetch()}>
            Refresh Data
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="text-lg">Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Select value={filters.dateRange} onValueChange={(value) => setFilters(prev => ({ ...prev, dateRange: value }))}>
              <SelectTrigger>
                <SelectValue placeholder="Date Range" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="1d">Last 24 Hours</SelectItem>
                <SelectItem value="7d">Last 7 Days</SelectItem>
                <SelectItem value="30d">Last 30 Days</SelectItem>
                <SelectItem value="90d">Last 90 Days</SelectItem>
              </SelectContent>
            </Select>

            <Select value={filters.source} onValueChange={(value) => setFilters(prev => ({ ...prev, source: value }))}>
              <SelectTrigger>
                <SelectValue placeholder="Source" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Sources</SelectItem>
                {uniqueSources.map(source => (
                  <SelectItem key={source} value={source}>{source}</SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={filters.location} onValueChange={(value) => setFilters(prev => ({ ...prev, location: value }))}>
              <SelectTrigger>
                <SelectValue placeholder="Location" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Locations</SelectItem>
                {uniqueLocations.map(location => (
                  <SelectItem key={location} value={location}>{location}</SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={filters.creativeType} onValueChange={(value) => setFilters(prev => ({ ...prev, creativeType: value }))}>
              <SelectTrigger>
                <SelectValue placeholder="Creative Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                {uniqueCreativeTypes.map(type => (
                  <SelectItem key={type} value={type}>{type}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          <div className="mt-4">
            <Input
              placeholder="Search campaigns..."
              value={filters.search}
              onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
              className="max-w-md"
            />
          </div>
        </CardContent>
      </Card>

      {/* Overview Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold">{aggregateMetrics.totalCampaigns}</div>
            <div className="text-sm text-gray-500">Active Campaigns</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold">{aggregateMetrics.totalSessions.toLocaleString()}</div>
            <div className="text-sm text-gray-500">Total Sessions</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold">{aggregateMetrics.totalBookings}</div>
            <div className="text-sm text-gray-500">Total Conversions</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-green-600">{overallConversionRate}%</div>
            <div className="text-sm text-gray-500">Overall Conversion Rate</div>
          </CardContent>
        </Card>
      </div>

      {/* Campaign Funnels */}
      <div className="space-y-4">
        <h2 className="text-xl font-semibold">Campaign Performance</h2>
        {filteredCampaigns.length === 0 ? (
          <Card>
            <CardContent className="p-8 text-center">
              <div className="text-gray-500">No campaigns found with current filters</div>
            </CardContent>
          </Card>
        ) : (
          filteredCampaigns.map((campaign, index) => (
            <CampaignFunnelCard key={index} campaign={campaign} />
          ))
        )}
      </div>
    </div>
  );
}