import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { 
  Clock, 
  User, 
  Phone, 
  Calendar, 
  MapPin, 
  AlertCircle, 
  CheckCircle, 
  X, 
  Edit,
  History,
  UserCheck,
  Shield
} from 'lucide-react';
import { format } from 'date-fns';

interface BookingHistoryEntry {
  id: number;
  bookingId: number;
  phoneNumber: string;
  action: 'created' | 'rescheduled' | 'cancelled' | 'completed';
  actionBy: 'customer' | 'admin' | 'system';
  adminUserId?: number;
  adminUserName?: string;
  previousValues?: string;
  newValues?: string;
  reason?: string;
  ipAddress?: string;
  userAgent?: string;
  timestamp: string;
}

interface BookingHistoryModalProps {
  isOpen: boolean;
  onClose: () => void;
  phoneNumber: string;
}

export function BookingHistoryModal({ isOpen, onClose, phoneNumber }: BookingHistoryModalProps) {
  const { data: history = [], isLoading } = useQuery<BookingHistoryEntry[]>({
    queryKey: [`/api/admin/booking-history/${phoneNumber}`],
    enabled: isOpen && !!phoneNumber,
  });

  const getActionIcon = (action: string) => {
    switch (action) {
      case 'created':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'rescheduled':
        return <Edit className="h-4 w-4 text-blue-600" />;
      case 'cancelled':
        return <X className="h-4 w-4 text-red-600" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      default:
        return <History className="h-4 w-4 text-gray-600" />;
    }
  };

  const getActionBadgeVariant = (action: string) => {
    switch (action) {
      case 'created':
        return 'default';
      case 'rescheduled':
        return 'secondary';
      case 'cancelled':
        return 'destructive';
      case 'completed':
        return 'default';
      default:
        return 'outline';
    }
  };

  const getActionByIcon = (actionBy: string) => {
    switch (actionBy) {
      case 'customer':
        return <User className="h-4 w-4 text-blue-600" />;
      case 'admin':
        return <Shield className="h-4 w-4 text-purple-600" />;
      case 'system':
        return <UserCheck className="h-4 w-4 text-gray-600" />;
      default:
        return <User className="h-4 w-4 text-gray-600" />;
    }
  };

  const renderBookingDetails = (valuesString: string | null) => {
    if (!valuesString) return null;
    
    try {
      const values = JSON.parse(valuesString);
      return (
        <div className="space-y-2 text-sm">
          {values.name && (
            <div className="flex items-center gap-2">
              <User className="h-3 w-3 text-gray-500" />
              <span>{values.name}</span>
            </div>
          )}
          {values.date && (
            <div className="flex items-center gap-2">
              <Calendar className="h-3 w-3 text-gray-500" />
              <span>{format(new Date(values.date), 'PPP')}</span>
            </div>
          )}
          {values.timeSlot && (
            <div className="flex items-center gap-2">
              <Clock className="h-3 w-3 text-gray-500" />
              <span>{values.timeSlot}</span>
            </div>
          )}
          {values.address && (
            <div className="flex items-center gap-2">
              <MapPin className="h-3 w-3 text-gray-500" />
              <span>{values.address}</span>
            </div>
          )}
          {values.status && (
            <div className="flex items-center gap-2">
              <AlertCircle className="h-3 w-3 text-gray-500" />
              <Badge variant="outline" className="text-xs">
                {values.status}
              </Badge>
            </div>
          )}
        </div>
      );
    } catch (e) {
      return <span className="text-xs text-gray-500">Invalid data format</span>;
    }
  };

  const renderChanges = (previousValues: string | null, newValues: string | null) => {
    if (!previousValues || !newValues) return null;

    try {
      const prev = JSON.parse(previousValues);
      const current = JSON.parse(newValues);
      const changes = [];

      // Compare key fields
      if (prev.date !== current.date) {
        changes.push({
          field: 'Date',
          from: format(new Date(prev.date), 'PPP'),
          to: format(new Date(current.date), 'PPP')
        });
      }

      if (prev.timeSlot !== current.timeSlot) {
        changes.push({
          field: 'Time',
          from: prev.timeSlot,
          to: current.timeSlot
        });
      }

      if (prev.status !== current.status) {
        changes.push({
          field: 'Status',
          from: prev.status,
          to: current.status
        });
      }

      if (prev.address !== current.address) {
        changes.push({
          field: 'Address',
          from: prev.address,
          to: current.address
        });
      }

      return changes.length > 0 ? (
        <div className="space-y-2">
          <h5 className="text-xs font-medium text-gray-700">Changes:</h5>
          {changes.map((change, idx) => (
            <div key={idx} className="text-xs space-y-1">
              <div className="font-medium text-gray-600">{change.field}:</div>
              <div className="flex items-center gap-2">
                <span className="text-red-600 line-through">{change.from}</span>
                <span className="text-gray-400">→</span>
                <span className="text-green-600">{change.to}</span>
              </div>
            </div>
          ))}
        </div>
      ) : null;
    } catch (e) {
      return null;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <History className="h-5 w-5" />
            Booking History for {phoneNumber}
          </DialogTitle>
        </DialogHeader>

        <ScrollArea className="h-[600px] pr-4">
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
            </div>
          ) : !history || !Array.isArray(history) || history.length === 0 ? (
            <div className="text-center py-8">
              <History className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600">No booking history found for this phone number.</p>
            </div>
          ) : (
            <div className="space-y-4">
              {history.map((entry: BookingHistoryEntry, index: number) => (
                <Card key={entry.id} className="border-l-4 border-l-purple-500">
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        {getActionIcon(entry.action)}
                        <div>
                          <CardTitle className="text-base capitalize">
                            {entry.action} Booking
                          </CardTitle>
                          <div className="flex items-center gap-2 text-sm text-gray-700 bg-gray-100 px-2 py-1 rounded">
                            <Clock className="h-3 w-3" />
                            <span className="font-medium">
                              {format(new Date(entry.timestamp), 'PPP')} at {format(new Date(entry.timestamp), 'p')}
                            </span>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant={getActionBadgeVariant(entry.action)}>
                          {entry.action}
                        </Badge>
                        <div className="flex items-center gap-1 text-xs text-gray-600">
                          {getActionByIcon(entry.actionBy)}
                          <span className="capitalize">{entry.actionBy}</span>
                        </div>
                      </div>
                    </div>
                  </CardHeader>
                  
                  <CardContent className="space-y-4">
                    {/* Action performed by information */}
                    <div className="flex items-center gap-2 text-sm">
                      {entry.actionBy === 'customer' ? (
                        <div className="flex items-center gap-2 text-blue-700 bg-blue-50 px-3 py-1 rounded-full">
                          <User className="h-3 w-3" />
                          <span className="font-medium">Customer Action</span>
                        </div>
                      ) : entry.actionBy === 'admin' ? (
                        <div className="flex items-center gap-2 text-purple-700 bg-purple-50 px-3 py-1 rounded-full">
                          <Shield className="h-3 w-3" />
                          <span className="font-medium">
                            Admin Action {entry.adminUserName ? `by ${entry.adminUserName}` : '(Admin user not recorded)'}
                          </span>
                        </div>
                      ) : (
                        <div className="flex items-center gap-2 text-gray-700 bg-gray-50 px-3 py-1 rounded-full">
                          <UserCheck className="h-3 w-3" />
                          <span className="font-medium">System Action</span>
                        </div>
                      )}
                    </div>

                    {entry.reason && (
                      <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                        <div className="flex items-center gap-2 text-sm">
                          <AlertCircle className="h-4 w-4 text-yellow-600" />
                          <span className="font-medium text-yellow-800">Reason:</span>
                          <span className="text-yellow-700">{entry.reason}</span>
                        </div>
                      </div>
                    )}

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {entry.previousValues && (
                        <div className="space-y-2">
                          <h5 className="text-sm font-medium text-gray-700">Previous Values:</h5>
                          <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                            {renderBookingDetails(entry.previousValues)}
                          </div>
                        </div>
                      )}

                      {entry.newValues && (
                        <div className="space-y-2">
                          <h5 className="text-sm font-medium text-gray-700">New Values:</h5>
                          <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
                            {renderBookingDetails(entry.newValues)}
                          </div>
                        </div>
                      )}
                    </div>

                    {entry.previousValues && entry.newValues && (
                      <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                        {renderChanges(entry.previousValues, entry.newValues)}
                      </div>
                    )}

                    {entry.bookingId && (
                      <div className="text-xs text-gray-500">
                        Booking ID: {entry.bookingId}
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </ScrollArea>

        <div className="flex justify-end pt-4 border-t">
          <Button onClick={onClose} variant="outline">
            Close
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}