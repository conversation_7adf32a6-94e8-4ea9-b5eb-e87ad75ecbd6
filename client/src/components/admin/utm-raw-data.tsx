import { useQuery } from "@tanstack/react-query";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Progress } from "@/components/ui/progress";
import { RefreshCw, Database, Activity, ExternalLink, TrendingUp, Users, MousePointer, Target, CheckCircle, BarChart3 } from "lucide-react";
import { useState } from 'react';
import { useToast } from "@/hooks/use-toast";

export default function UTMRawData() {
  const [debugMode, setDebugMode] = useState(true);
  const [selectedSource, setSelectedSource] = useState<{source: string, campaign: string} | null>(null);
  const [funnelDialogOpen, setFunnelDialogOpen] = useState(false);
  const { toast } = useToast();

  // Direct UTM data query
  const { data: utmDirectResponse, isLoading: directLoading, refetch: refetchDirect } = useQuery({
    queryKey: ['/api/admin/analytics/utm-direct', debugMode],
    queryFn: async () => {
      const url = `/api/admin/analytics/utm-direct${debugMode ? '?debug=true' : ''}`;
      const response = await fetch(url, { credentials: 'include' });
      if (!response.ok) throw new Error('Failed to fetch direct UTM data');
      return response.json();
    },
    refetchInterval: 10000, // Auto-refresh every 10 seconds for real-time updates
    refetchOnWindowFocus: true,
    staleTime: 5000,
  });

  // UTM Source Funnel query - only when a source is selected
  const { data: funnelData, isLoading: funnelLoading } = useQuery({
    queryKey: ['/api/admin/analytics/utm-source-funnel', selectedSource?.source, selectedSource?.campaign],
    queryFn: async () => {
      if (!selectedSource) return null;
      const url = `/api/admin/analytics/utm-source-funnel/${encodeURIComponent(selectedSource.source)}/${encodeURIComponent(selectedSource.campaign)}?debug=true`;
      const response = await fetch(url, { credentials: 'include' });
      if (!response.ok) throw new Error('Failed to fetch funnel data');
      return response.json();
    },
    enabled: !!selectedSource,
    refetchInterval: 10000,
  });

  const utmData = utmDirectResponse?.data || [];
  const debugInfo = utmDirectResponse?.debug;

  // Handle source click to show funnel
  const handleSourceClick = (source: string, campaign: string) => {
    setSelectedSource({ source, campaign });
    setFunnelDialogOpen(true);
  };

  // Enhanced refresh with user feedback
  const handleRefresh = async () => {
    try {
      await refetchDirect();
      toast({
        title: "Data refreshed successfully",
        description: "UTM analytics data has been updated with the latest information.",
      });
    } catch (error) {
      toast({
        title: "Refresh failed",
        description: "Unable to refresh data. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Create a clickable link for each UTM source
  const createUtmLink = (source: string, medium: string, campaign: string, content: string, term: string) => {
    const baseUrl = 'https://perfumestrial.kult.app';
    const params = new URLSearchParams();
    if (source && source !== 'Unknown') params.append('utm_source', source);
    if (medium && medium !== 'Unknown') params.append('utm_medium', medium);
    if (campaign && campaign !== 'Unknown') params.append('utm_campaign', campaign);
    if (content) params.append('utm_content', content);
    if (term) params.append('utm_term', term);
    
    return params.toString() ? `${baseUrl}?${params.toString()}` : baseUrl;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <BarChart3 className="w-8 h-8 text-purple-600" />
            Real-Time UTM Analytics
          </h1>
          <p className="text-muted-foreground">
            Interactive campaign tracking with live conversion funnels - click any source to analyze
          </p>
          <div className="mt-2 flex items-center gap-2">
            <Badge variant="secondary" className="bg-green-100 text-green-800">
              <Activity className="w-3 h-3 mr-1 animate-pulse" />
              Live Updates: 10s
            </Badge>
            <Badge variant="outline" className="bg-blue-50 text-blue-700">
              <Database className="w-3 h-3 mr-1" />
              Today's Data
            </Badge>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={directLoading}
            className="bg-purple-50 hover:bg-purple-100 border-purple-200"
          >
            <RefreshCw className={`w-4 h-4 mr-2 ${directLoading ? 'animate-spin' : ''}`} />
            Refresh Now
          </Button>
        </div>
      </div>

      {/* Debug Information */}
      {debugInfo && (
        <Card className="border-blue-200 bg-blue-50">
          <CardHeader>
            <CardTitle className="text-blue-800 flex items-center gap-2">
              <Database className="w-5 h-5" />
              Database Query Results
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div>
                <div className="text-sm text-blue-700 font-medium">Total Results</div>
                <div className="text-lg font-bold text-blue-900">{debugInfo.totalResults}</div>
              </div>
              <div>
                <div className="text-sm text-blue-700 font-medium">Meta Campaigns</div>
                <div className="text-lg font-bold text-blue-900">{debugInfo.metaCampaigns}</div>
              </div>
              <div>
                <div className="text-sm text-blue-700 font-medium">App Campaigns</div>
                <div className="text-lg font-bold text-blue-900">{debugInfo.appCampaigns}</div>
              </div>
              <div>
                <div className="text-sm text-blue-700 font-medium">Query Method</div>
                <div className="text-xs text-blue-600">{debugInfo.queryMethod}</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Interactive UTM Traffic Sources */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MousePointer className="w-5 h-5 text-purple-600" />
            Interactive Traffic Sources
          </CardTitle>
          <CardDescription>
            Click any traffic source to view detailed conversion funnel analysis
          </CardDescription>
        </CardHeader>
        <CardContent>
          {directLoading ? (
            <div className="text-center py-12">
              <RefreshCw className="w-12 h-12 animate-spin mx-auto mb-4 text-purple-600" />
              <p className="text-lg">Loading real-time traffic data...</p>
            </div>
          ) : utmData.length === 0 ? (
            <div className="text-center py-12">
              <Target className="w-12 h-12 mx-auto mb-4 text-gray-400" />
              <p className="text-lg text-gray-500">No UTM campaigns active</p>
              <p className="text-sm text-gray-400">Start your campaigns to see real-time analytics</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {utmData
                .sort((a: any, b: any) => {
                  // Sort by conversion rate (highest to lowest), then by page views
                  const aConversionRate = a.conversionRate || 0;
                  const bConversionRate = b.conversionRate || 0;
                  if (aConversionRate !== bConversionRate) {
                    return bConversionRate - aConversionRate;
                  }
                  return (b.unique_sessions || 0) - (a.unique_sessions || 0);
                })
                .map((row: any, index: number) => {
                const utmLink = createUtmLink(row.utm_source, row.utm_medium, row.utm_campaign, row.utm_content, row.utm_term);
                return (
                  <Card 
                    key={index} 
                    className="cursor-pointer hover:shadow-lg transition-all duration-300 hover:scale-105 border-l-4 border-l-purple-500 bg-gradient-to-br from-white to-purple-50"
                    onClick={() => handleSourceClick(row.utm_source, row.utm_campaign)}
                  >
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <Badge variant={row.isMetaAd ? "default" : row.isAppCampaign ? "secondary" : "outline"} className="text-xs">
                              {row.isMetaAd ? "Meta Ad" : row.isAppCampaign ? "App Campaign" : "Direct"}
                            </Badge>
                            <Badge variant="outline" className="text-xs bg-green-50 text-green-700">
                              Live
                            </Badge>
                          </div>
                          <h3 className="font-bold text-lg text-purple-900">{row.utm_source}</h3>
                          <p className="text-xs text-gray-600 line-clamp-2" title={row.utm_campaign}>
                            {row.utm_campaign}
                          </p>
                        </div>
                        <TrendingUp className="w-5 h-5 text-purple-600" />
                      </div>
                      
                      <div className="grid grid-cols-2 gap-4 mb-4">
                        <div className="text-center">
                          <div className="text-2xl font-bold text-purple-700">{row.sessions}</div>
                          <div className="text-xs text-gray-500 flex items-center justify-center gap-1">
                            <Users className="w-3 h-3" />
                            Sessions
                          </div>
                        </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold text-green-600">{row.total_events}</div>
                          <div className="text-xs text-gray-500 flex items-center justify-center gap-1">
                            <Activity className="w-3 h-3" />
                            Events
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex items-center justify-between text-xs text-gray-500 mb-3">
                        <span>Last activity: {new Date(row.last_seen).toLocaleTimeString()}</span>
                        <span className="flex items-center gap-1">
                          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                          Active
                        </span>
                      </div>
                      
                      <div className="flex gap-2">
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            window.open(utmLink, '_blank');
                          }}
                          className="flex items-center gap-1 flex-1"
                        >
                          <ExternalLink className="w-3 h-3" />
                          Test Link
                        </Button>
                        <Button 
                          size="sm"
                          onClick={() => handleSourceClick(row.utm_source, row.utm_campaign)}
                          className="flex items-center gap-1 flex-1 bg-purple-600 hover:bg-purple-700"
                        >
                          <BarChart3 className="w-3 h-3" />
                          View Funnel
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Meta Campaigns</p>
                <p className="text-2xl font-bold text-blue-600">
                  {utmData.filter((d: any) => d.isMetaAd).length}
                </p>
              </div>
              <Badge variant="default">Meta</Badge>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">App Campaigns</p>
                <p className="text-2xl font-bold text-green-600">
                  {utmData.filter((d: any) => d.isAppCampaign).length}
                </p>
              </div>
              <Badge variant="secondary">App</Badge>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Sources</p>
                <p className="text-2xl font-bold text-purple-600">
                  {utmData.length}
                </p>
              </div>
              <Badge variant="outline">All</Badge>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Conversion Funnel Dialog */}
      <Dialog open={funnelDialogOpen} onOpenChange={setFunnelDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <TrendingUp className="w-6 h-6 text-purple-600" />
              Conversion Funnel Analysis
            </DialogTitle>
            <DialogDescription>
              {selectedSource && (
                <div className="flex items-center gap-2 mt-2">
                  <Badge variant="outline" className="bg-purple-50 text-purple-700">
                    {selectedSource.source}
                  </Badge>
                  <span className="text-sm text-gray-600 truncate max-w-96" title={selectedSource.campaign}>
                    {selectedSource.campaign}
                  </span>
                </div>
              )}
            </DialogDescription>
          </DialogHeader>
          
          {funnelLoading ? (
            <div className="text-center py-12">
              <RefreshCw className="w-8 h-8 animate-spin mx-auto mb-4 text-purple-600" />
              <p>Loading conversion funnel data...</p>
            </div>
          ) : funnelData ? (
            <div className="space-y-6">
              {/* Summary Cards */}
              <div className="grid grid-cols-3 gap-4">
                <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
                  <CardContent className="p-4 text-center">
                    <Users className="w-8 h-8 mx-auto mb-2 text-blue-600" />
                    <div className="text-2xl font-bold text-blue-700">{funnelData.totalSessions}</div>
                    <div className="text-sm text-blue-600">Total Sessions</div>
                  </CardContent>
                </Card>
                
                <Card className="bg-gradient-to-br from-green-50 to-green-100 border-green-200">
                  <CardContent className="p-4 text-center">
                    <CheckCircle className="w-8 h-8 mx-auto mb-2 text-green-600" />
                    <div className="text-2xl font-bold text-green-700">{funnelData.completedBookings}</div>
                    <div className="text-sm text-green-600">Completed Bookings</div>
                  </CardContent>
                </Card>
                
                <Card className="bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200">
                  <CardContent className="p-4 text-center">
                    <Target className="w-8 h-8 mx-auto mb-2 text-purple-600" />
                    <div className="text-2xl font-bold text-purple-700">{funnelData.conversionRate}%</div>
                    <div className="text-sm text-purple-600">Conversion Rate</div>
                  </CardContent>
                </Card>
              </div>
              
              {/* Funnel Steps */}
              <Card>
                <CardHeader>
                  <CardTitle>Conversion Funnel Steps</CardTitle>
                  <CardDescription>User journey progression through booking flow</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {funnelData.funnel.map((step: any, index: number) => {
                      const percentage = funnelData.totalSessions > 0 ? (step.sessions / funnelData.totalSessions * 100).toFixed(1) : '0.0';
                      const stepNames = {
                        'page_view': 'Landing Page View',
                        'form_start': 'Started Booking Form',
                        'time_slot_selected': 'Selected Time Slot',
                        'otp_requested': 'Requested OTP',
                        'otp_verified': 'Verified Phone',
                        'booking_complete': 'Completed Booking'
                      };
                      const stepName = stepNames[step.step as keyof typeof stepNames] || step.step;
                      
                      return (
                        <div key={index} className="relative">
                          <div className="flex items-center justify-between mb-2">
                            <div className="flex items-center gap-3">
                              <div className="w-8 h-8 rounded-full bg-purple-600 text-white flex items-center justify-center text-sm font-bold">
                                {index + 1}
                              </div>
                              <div>
                                <div className="font-medium">{stepName}</div>
                                <div className="text-sm text-gray-500">{step.sessions} sessions • {step.events} events</div>
                              </div>
                            </div>
                            <div className="text-right">
                              <div className="text-lg font-bold text-purple-700">{percentage}%</div>
                              <div className="text-xs text-gray-500">of total</div>
                            </div>
                          </div>
                          <Progress value={parseFloat(percentage)} className="h-3" />
                          {step.dropOffRate > 0 && (
                            <div className="text-xs text-red-600 mt-1">
                              Drop-off: {step.dropOffRate}%
                            </div>
                          )}
                        </div>
                      );
                    })}
                  </div>
                </CardContent>
              </Card>
              
              {/* Debug Info */}
              {funnelData.debug && (
                <Card className="bg-gray-50">
                  <CardHeader>
                    <CardTitle className="text-sm">Debug Information</CardTitle>
                  </CardHeader>
                  <CardContent className="text-xs">
                    <pre className="whitespace-pre-wrap">{JSON.stringify(funnelData.debug, null, 2)}</pre>
                  </CardContent>
                </Card>
              )}
            </div>
          ) : (
            <div className="text-center py-8">
              <p className="text-gray-500">No funnel data available</p>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}