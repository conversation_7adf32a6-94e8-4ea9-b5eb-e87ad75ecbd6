import { useQuery } from "@tanstack/react-query";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { RefreshCw, TrendingUp, TrendingDown, Activity, Eye, MousePointer, Target, Globe, Smartphone, Users, ExternalLink, Copy } from "lucide-react";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { useToast } from "@/hooks/use-toast";
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar, Pie<PERSON>hart, Pie, Cell } from 'recharts';
import { useState } from 'react';

interface UTMSource {
  utm_source: string;
  utm_medium: string;
  utm_campaign: string;
  utm_term: string;
  utm_content: string;
  sessions: number;
  conversions: number;
  conversionRate: number;
  performance: 'High' | 'Medium' | 'Low';
  isMetaAd: boolean;
  isGoogleAd: boolean;
  adType: string;
  isActive: boolean;
  lastActivity: string;
}

interface UTMCampaign {
  campaign: string;
  source: string;
  medium: string;
  sessions: number;
  conversions: number;
  clicks: number;
  conversionRate: number;
  status: 'Active' | 'Paused';
  firstSeen: string;
  lastSeen: string;
  isActive: boolean;
}

interface UTMFunnel {
  funnelSteps: Array<{
    step: string;
    count: number;
    percentage: number;
  }>;
  totalSessions: number;
  completionRate: number;
}

const COLORS = ['#8884d8', '#82ca9d', '#ffc658', '#ff7300', '#00ff88', '#8dd1e1'];

// Helper function to generate full UTM URL
const generateUTMUrl = (baseUrl: string, utmParams: Partial<UTMSource>) => {
  const url = new URL(baseUrl);
  const params = url.searchParams;
  
  if (utmParams.utm_source && utmParams.utm_source !== 'Direct') {
    params.set('utm_source', utmParams.utm_source);
  }
  if (utmParams.utm_medium && utmParams.utm_medium !== 'None') {
    params.set('utm_medium', utmParams.utm_medium);
  }
  if (utmParams.utm_campaign && utmParams.utm_campaign !== 'None') {
    params.set('utm_campaign', utmParams.utm_campaign);
  }
  if (utmParams.utm_content) {
    params.set('utm_content', utmParams.utm_content);
  }
  if (utmParams.utm_term) {
    params.set('utm_term', utmParams.utm_term);
  }
  
  return url.toString();
};

// Component for clickable UTM campaign
function ClickableUTMCampaign({ campaign }: { campaign: UTMCampaign }) {
  const { toast } = useToast();
  const baseUrl = window.location.origin;
  
  // Create UTM source object for URL generation
  const utmParams = {
    utm_source: campaign.source,
    utm_medium: campaign.medium,
    utm_campaign: campaign.campaign,
    utm_content: '',
    utm_term: ''
  };
  
  const fullUrl = generateUTMUrl(baseUrl, utmParams);
  
  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast({
        description: "Campaign URL copied to clipboard!",
      });
    } catch (err) {
      toast({
        description: "Failed to copy URL",
        variant: "destructive",
      });
    }
  };
  
  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="ghost" className="p-0 h-auto justify-start">
          <div className="text-left">
            <h3 className="font-semibold flex items-center gap-1">
              {campaign.campaign} <ExternalLink className="w-3 h-3" />
            </h3>
            <p className="text-sm text-gray-600">{campaign.source} / {campaign.medium}</p>
          </div>
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Campaign Details - {campaign.campaign}</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium text-gray-600">Campaign Name</label>
              <p className="font-mono text-sm bg-gray-50 p-2 rounded break-all">{campaign.campaign}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-600">Source / Medium</label>
              <p className="font-mono text-sm bg-gray-50 p-2 rounded">{campaign.source} / {campaign.medium}</p>
            </div>
          </div>
          
          <div>
            <label className="text-sm font-medium text-gray-600">Campaign UTM URL</label>
            <div className="flex items-center gap-2 mt-1">
              <code className="flex-1 text-xs bg-gray-50 p-3 rounded border break-all">
                {fullUrl}
              </code>
              <Button
                variant="outline"
                size="sm"
                onClick={() => copyToClipboard(fullUrl)}
              >
                <Copy className="w-4 h-4" />
              </Button>
            </div>
          </div>
          
          <div className="grid grid-cols-4 gap-4 pt-4 border-t">
            <div className="text-center">
              <p className="text-2xl font-bold text-blue-600">{campaign.sessions}</p>
              <p className="text-sm text-gray-600">Sessions</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-green-600">{campaign.conversions}</p>
              <p className="text-sm text-gray-600">Conversions</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-purple-600">{campaign.conversionRate}%</p>
              <p className="text-sm text-gray-600">Conv. Rate</p>
            </div>
            <div className="text-center">
              <p className="text-lg font-bold text-orange-600">
                {new Date(campaign.lastSeen).toLocaleDateString()}
              </p>
              <p className="text-sm text-gray-600">Last Activity</p>
            </div>
          </div>
          
          <div className="pt-4 border-t">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Campaign Status</span>
              <Badge variant={campaign.status === 'Active' ? "default" : "secondary"}>
                {campaign.status}
              </Badge>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

// Component for clickable UTM source
function ClickableUTMSource({ source }: { source: UTMSource }) {
  const { toast } = useToast();
  const baseUrl = window.location.origin;
  const fullUrl = generateUTMUrl(baseUrl, source);
  
  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast({
        description: "URL copied to clipboard!",
      });
    } catch (err) {
      toast({
        description: "Failed to copy URL",
        variant: "destructive",
      });
    }
  };
  
  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="ghost" className="p-0 h-auto justify-start">
          <div className="text-left">
            <div className="font-medium flex items-center gap-1">
              {source.utm_source} <ExternalLink className="w-3 h-3" />
            </div>
            <div className="text-xs text-gray-500">{source.utm_medium}</div>
          </div>
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>UTM Source Details - {source.utm_source}</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium text-gray-600">Source</label>
              <p className="font-mono text-sm bg-gray-50 p-2 rounded">{source.utm_source}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-600">Medium</label>
              <p className="font-mono text-sm bg-gray-50 p-2 rounded">{source.utm_medium}</p>
            </div>
            <div className="col-span-2">
              <label className="text-sm font-medium text-gray-600">Campaign</label>
              <p className="font-mono text-sm bg-gray-50 p-2 rounded break-all">{source.utm_campaign}</p>
            </div>
            {source.utm_content && (
              <div>
                <label className="text-sm font-medium text-gray-600">Content</label>
                <p className="font-mono text-sm bg-gray-50 p-2 rounded break-all">{source.utm_content}</p>
              </div>
            )}
            {source.utm_term && (
              <div>
                <label className="text-sm font-medium text-gray-600">Term</label>
                <p className="font-mono text-sm bg-gray-50 p-2 rounded break-all">{source.utm_term}</p>
              </div>
            )}
          </div>
          
          <div>
            <label className="text-sm font-medium text-gray-600">Full UTM URL</label>
            <div className="flex items-center gap-2 mt-1">
              <code className="flex-1 text-xs bg-gray-50 p-3 rounded border break-all">
                {fullUrl}
              </code>
              <Button
                variant="outline"
                size="sm"
                onClick={() => copyToClipboard(fullUrl)}
              >
                <Copy className="w-4 h-4" />
              </Button>
            </div>
          </div>
          
          <div className="grid grid-cols-3 gap-4 pt-4 border-t">
            <div className="text-center">
              <p className="text-2xl font-bold text-blue-600">{source.sessions}</p>
              <p className="text-sm text-gray-600">Sessions</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-green-600">{source.conversions}</p>
              <p className="text-sm text-gray-600">Conversions</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-purple-600">{source.conversionRate}%</p>
              <p className="text-sm text-gray-600">Conv. Rate</p>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

export default function EnhancedUTMDashboard() {
  const [dateRange, setDateRange] = useState(30);
  const [autoRefresh, setAutoRefresh] = useState(true); // Enable auto-refresh by default
  const [selectedSource, setSelectedSource] = useState<string | null>(null);
  const [realTimeData, setRealTimeData] = useState<any[]>([]);
  const [debugMode, setDebugMode] = useState(false);
  const { toast } = useToast();

  const startDate = new Date(Date.now() - dateRange * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
  const endDate = new Date().toISOString().split('T')[0];

  // Fetch UTM Sources with real-time updates and debug support
  const { data: utmSourcesResponse, isLoading: sourcesLoading, refetch: refetchSources } = useQuery({
    queryKey: ['/api/admin/analytics/utm-sources', startDate, endDate, debugMode],
    queryFn: async () => {
      const url = `/api/admin/analytics/utm-sources?startDate=${startDate}&endDate=${endDate}${debugMode ? '&debug=true' : ''}`;
      const response = await fetch(url, { credentials: 'include' });
      if (!response.ok) throw new Error('Failed to fetch UTM sources');
      return response.json();
    },
    refetchInterval: autoRefresh ? 15000 : false, // Auto-refresh every 15 seconds for real-time
    refetchOnWindowFocus: true,
    staleTime: 10000, // Consider data stale after 10 seconds
  });

  // Extract the actual data (handle both debug and non-debug responses)
  const utmSources = debugMode && utmSourcesResponse?.data ? utmSourcesResponse.data : utmSourcesResponse;

  // Fetch UTM Campaigns with enhanced filtering and debug support
  const { data: utmCampaignsResponse, isLoading: campaignsLoading, refetch: refetchCampaigns } = useQuery({
    queryKey: ['/api/admin/analytics/utm-campaigns', startDate, endDate, selectedSource, debugMode],
    queryFn: async () => {
      const params = new URLSearchParams({
        startDate,
        endDate,
        ...(selectedSource && { source: selectedSource }),
        ...(debugMode && { debug: 'true' })
      });
      const response = await fetch(`/api/admin/analytics/utm-campaigns?${params}`, { credentials: 'include' });
      if (!response.ok) throw new Error('Failed to fetch UTM campaigns');
      return response.json();
    },
    refetchInterval: autoRefresh ? 15000 : false,
    refetchOnWindowFocus: true,
    staleTime: 10000,
  });

  // Extract the actual campaigns data (handle both debug and non-debug responses)
  const utmCampaigns = debugMode && utmCampaignsResponse?.data ? utmCampaignsResponse.data : utmCampaignsResponse;

  // Fetch Conversion Funnel
  const { data: conversionFunnel, isLoading: funnelLoading, refetch: refetchFunnel } = useQuery({
    queryKey: ['/api/admin/analytics/utm-funnel', startDate, endDate],
    refetchInterval: autoRefresh ? 15000 : false,
    refetchOnWindowFocus: true,
    staleTime: 10000,
  });

  // Fetch real-time hourly data for trend graphs
  const { data: hourlyTrends } = useQuery({
    queryKey: ['/api/admin/analytics/utm-hourly-trends', startDate, endDate],
    refetchInterval: autoRefresh ? 60000 : false, // Every minute for hourly trends
    refetchOnWindowFocus: true,
  });

  const refreshAll = async () => {
    try {
      await Promise.all([
        refetchSources(),
        refetchCampaigns(),
        refetchFunnel()
      ]);
      toast({
        title: "Data refreshed successfully",
        description: "UTM analytics data has been updated with the latest information.",
      });
    } catch (error) {
      toast({
        title: "Refresh failed",
        description: "Unable to refresh data. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Calculate summary metrics
  const totalSessions = (utmSources as UTMSource[])?.reduce((sum: number, source: UTMSource) => sum + source.sessions, 0) || 0;
  const totalConversions = (utmSources as UTMSource[])?.reduce((sum: number, source: UTMSource) => sum + source.conversions, 0) || 0;
  const overallConversionRate = totalSessions > 0 ? (totalConversions / totalSessions * 100).toFixed(2) : '0.00';
  const activeCampaigns = (utmCampaigns as UTMCampaign[])?.filter((c: UTMCampaign) => c.isActive).length || 0;

  // Meta ads analysis
  const metaSources = (utmSources as UTMSource[])?.filter((s: UTMSource) => s.isMetaAd) || [];
  const metaSessions = metaSources.reduce((sum, source) => sum + source.sessions, 0);
  const metaConversions = metaSources.reduce((sum, source) => sum + source.conversions, 0);
  const metaConversionRate = metaSessions > 0 ? (metaConversions / metaSessions * 100).toFixed(2) : '0.00';

  // Prepare chart data
  const sourceChartData = (utmSources as UTMSource[])?.slice(0, 10).map((source: UTMSource) => ({
    name: `${source.utm_source}/${source.utm_medium}`,
    sessions: source.sessions,
    conversions: source.conversions,
    conversionRate: source.conversionRate
  })) || [];

  const performanceData = [
    { name: 'High', value: (utmSources as UTMSource[])?.filter((s: UTMSource) => s.performance === 'High').length || 0 },
    { name: 'Medium', value: (utmSources as UTMSource[])?.filter((s: UTMSource) => s.performance === 'Medium').length || 0 },
    { name: 'Low', value: (utmSources as UTMSource[])?.filter((s: UTMSource) => s.performance === 'Low').length || 0 }
  ];

  return (
    <div className="space-y-6">
      {/* Header Controls */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Real-Time UTM Campaign Analytics</h2>
          <p className="text-muted-foreground">
            Live tracking and analysis of all UTM campaigns and traffic sources
          </p>
          <div className="mt-2">
            <Badge variant="secondary" className="bg-green-100 text-green-800">
              📊 Showing ALL Incoming Traffic
            </Badge>
          </div>
        </div>
        <div className="flex items-center gap-3">
          <select
            value={dateRange}
            onChange={(e) => setDateRange(Number(e.target.value))}
            className="px-3 py-2 border rounded-md"
          >
            <option value={7}>Last 7 days</option>
            <option value={14}>Last 14 days</option>
            <option value={30}>Last 30 days</option>
            <option value={90}>Last 90 days</option>
          </select>
          
          <Button
            variant={autoRefresh ? "default" : "outline"}
            size="sm"
            onClick={() => setAutoRefresh(!autoRefresh)}
          >
            <Activity className="w-4 h-4 mr-2" />
            Live Updates {autoRefresh && "(15s)"}
          </Button>
          
          <Button
            variant={debugMode ? "destructive" : "outline"}
            size="sm"
            onClick={() => setDebugMode(!debugMode)}
          >
            <Eye className="w-4 h-4 mr-2" />
            Debug {debugMode && "ON"}
          </Button>
          
          <Button size="sm" onClick={refreshAll} disabled={sourcesLoading}>
            <RefreshCw className={`w-4 h-4 mr-2 ${sourcesLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Sessions</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalSessions.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              Last {dateRange} days
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Conversions</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalConversions}</div>
            <p className="text-xs text-muted-foreground">
              {overallConversionRate}% conversion rate
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Campaigns</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{activeCampaigns}</div>
            <p className="text-xs text-muted-foreground">
              Running right now
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Meta Ads Performance</CardTitle>
            <Smartphone className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metaConversionRate}%</div>
            <p className="text-xs text-muted-foreground">
              {metaSessions} sessions, {metaConversions} conversions
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Debug Information Panel */}
      {debugMode && utmSourcesResponse?.debug && (
        <Card className="border-yellow-200 bg-yellow-50">
          <CardHeader>
            <CardTitle className="text-yellow-800 flex items-center gap-2">
              <Eye className="w-5 h-5" />
              Debug Information - Real-time Data Verification
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div>
                <div className="text-sm text-yellow-700 font-medium">Date Range</div>
                <div className="text-xs text-yellow-600">
                  {new Date(utmSourcesResponse.debug.dateRange.from).toLocaleDateString()} - {new Date(utmSourcesResponse.debug.dateRange.to).toLocaleDateString()}
                </div>
              </div>
              <div>
                <div className="text-sm text-yellow-700 font-medium">Events Processed</div>
                <div className="text-xs text-yellow-600">{utmSourcesResponse.debug.totalEventsProcessed}</div>
              </div>
              <div>
                <div className="text-sm text-yellow-700 font-medium">Sessions Analyzed</div>
                <div className="text-xs text-yellow-600">{utmSourcesResponse.debug.totalSessionsProcessed}</div>
              </div>
              <div>
                <div className="text-sm text-yellow-700 font-medium">Last Updated</div>
                <div className="text-xs text-yellow-600">
                  {new Date(utmSourcesResponse.debug.lastUpdated).toLocaleTimeString()}
                </div>
              </div>
            </div>
            <div>
              <div className="text-sm text-yellow-700 font-medium mb-2">Case Normalization</div>
              <Badge variant="outline" className="text-green-700 border-green-300">
                ✓ Applied - "direct" → "Direct", "none" → "None"
              </Badge>
            </div>
            {utmSourcesResponse.debug.rawEventSample?.length > 0 && (
              <div>
                <div className="text-sm text-yellow-700 font-medium mb-2">Raw Event Sample (First 5)</div>
                <div className="bg-white p-2 rounded text-xs max-h-32 overflow-y-auto">
                  <pre>{JSON.stringify(utmSourcesResponse.debug.rawEventSample.slice(0, 5), null, 2)}</pre>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Main Analytics Tabs */}
      <Tabs defaultValue="sources" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="sources">Traffic Sources</TabsTrigger>
          <TabsTrigger value="campaigns">Campaign Analysis</TabsTrigger>
          <TabsTrigger value="funnel">Conversion Funnel</TabsTrigger>
          <TabsTrigger value="realtime">Real-Time</TabsTrigger>
        </TabsList>

        {/* Traffic Sources Tab */}
        <TabsContent value="sources" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Sources Performance Chart */}
            <Card>
              <CardHeader>
                <CardTitle>Top Traffic Sources</CardTitle>
                <CardDescription>Sessions and conversions by source</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={sourceChartData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="sessions" fill="#8884d8" name="Sessions" />
                    <Bar dataKey="conversions" fill="#82ca9d" name="Conversions" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Performance Distribution */}
            <Card>
              <CardHeader>
                <CardTitle>Campaign Performance Distribution</CardTitle>
                <CardDescription>High, Medium, Low performing sources</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={performanceData}
                      cx="50%"
                      cy="50%"
                      outerRadius={80}
                      dataKey="value"
                    >
                      {performanceData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>

          {/* Real-time Hourly Trends */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="w-5 h-5 text-green-500" />
                Real-time Traffic Trends
                {autoRefresh && <Badge variant="secondary" className="text-xs">Live</Badge>}
              </CardTitle>
              <CardDescription>Hourly session distribution for the last 24 hours</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={hourlyTrends || []}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis 
                    dataKey="hour" 
                    tick={{ fontSize: 12 }}
                    tickFormatter={(value) => `${value}:00`}
                  />
                  <YAxis tick={{ fontSize: 12 }} />
                  <Tooltip 
                    labelFormatter={(label) => `Hour: ${label}:00`}
                    formatter={(value: any, name: string) => [value, name === 'sessions' ? 'Sessions' : 'Conversions']}
                  />
                  <Line 
                    type="monotone" 
                    dataKey="sessions" 
                    stroke="#8884d8" 
                    strokeWidth={2}
                    dot={{ r: 4 }}
                    activeDot={{ r: 6 }}
                  />
                  <Line 
                    type="monotone" 
                    dataKey="conversions" 
                    stroke="#82ca9d" 
                    strokeWidth={2}
                    dot={{ r: 4 }}
                    activeDot={{ r: 6 }}
                  />
                </LineChart>
              </ResponsiveContainer>
              <div className="flex justify-center gap-4 mt-4 text-sm">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-[#8884d8] rounded-full"></div>
                  <span>Sessions</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-[#82ca9d] rounded-full"></div>
                  <span>Conversions</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Detailed Sources Table */}
          <Card>
            <CardHeader>
              <CardTitle>Detailed Source Analysis</CardTitle>
              <CardDescription>Complete breakdown of all traffic sources with UTM parameters</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full text-sm">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left p-2">Source / Medium</th>
                      <th className="text-left p-2">Campaign</th>
                      <th className="text-left p-2">Type</th>
                      <th className="text-left p-2">Sessions</th>
                      <th className="text-left p-2">Conversions</th>
                      <th className="text-left p-2">Conv. Rate</th>
                      <th className="text-left p-2">Performance</th>
                      <th className="text-left p-2">Status</th>
                    </tr>
                  </thead>
                  <tbody>
                    {(utmSources as UTMSource[])?.map((source: UTMSource, index: number) => (
                      <tr key={index} className="border-b hover:bg-gray-50">
                        <td className="p-2">
                          <ClickableUTMSource source={source} />
                        </td>
                        <td className="p-2">
                          <div className="max-w-40 truncate" title={source.utm_campaign}>
                            {source.utm_campaign}
                          </div>
                        </td>
                        <td className="p-2">
                          <Badge variant={source.isMetaAd ? "default" : "secondary"}>
                            {source.adType}
                          </Badge>
                        </td>
                        <td className="p-2">{source.sessions.toLocaleString()}</td>
                        <td className="p-2">{source.conversions}</td>
                        <td className="p-2">{source.conversionRate}%</td>
                        <td className="p-2">
                          <Badge variant={
                            source.performance === 'High' ? "default" :
                            source.performance === 'Medium' ? "secondary" : "outline"
                          }>
                            {source.performance}
                          </Badge>
                        </td>
                        <td className="p-2">
                          <Badge variant={source.isActive ? "default" : "secondary"}>
                            {source.isActive ? "Active" : "Inactive"}
                          </Badge>
                        </td>
                      </tr>
                    )) || []}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Campaign Analysis Tab */}
        <TabsContent value="campaigns" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Active Campaigns</CardTitle>
              <CardDescription>Real-time campaign performance and metrics</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {(utmCampaigns as UTMCampaign[])?.map((campaign: UTMCampaign, index: number) => (
                  <div key={index} className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex-1">
                        <ClickableUTMCampaign campaign={campaign} />
                      </div>
                      <Badge variant={campaign.status === 'Active' ? "default" : "secondary"}>
                        {campaign.status}
                      </Badge>
                    </div>
                    
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <p className="text-gray-500">Page Views</p>
                        <p className="font-semibold">{campaign.sessions.toLocaleString()}</p>
                      </div>
                      <div>
                        <p className="text-gray-500">Conversions</p>
                        <p className="font-semibold">{campaign.conversions}</p>
                      </div>
                      <div>
                        <p className="text-gray-500">Conv. Rate</p>
                        <p className="font-semibold">{campaign.conversionRate}%</p>
                      </div>
                      <div>
                        <p className="text-gray-500">Last Activity</p>
                        <p className="font-semibold">
                          {new Date(campaign.lastSeen).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                  </div>
                )) || []}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Conversion Funnel Tab */}
        <TabsContent value="funnel" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>UTM Attribution Funnel</CardTitle>
              <CardDescription>Conversion flow from traffic sources to bookings</CardDescription>
            </CardHeader>
            <CardContent>
              {conversionFunnel && (
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4 mb-6">
                    <div>
                      <p className="text-sm text-gray-500">Total Sessions</p>
                      <p className="text-2xl font-bold">{conversionFunnel.totalSessions.toLocaleString()}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Completion Rate</p>
                      <p className="text-2xl font-bold">{conversionFunnel.completionRate.toFixed(2)}%</p>
                    </div>
                  </div>
                  
                  <div className="space-y-3">
                    {conversionFunnel.funnelSteps.map((step, index) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded">
                        <div className="flex items-center gap-3">
                          <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center text-sm font-semibold">
                            {index + 1}
                          </div>
                          <span className="font-medium">{step.step}</span>
                        </div>
                        <div className="flex items-center gap-4">
                          <span className="font-semibold">{step.count.toLocaleString()}</span>
                          <span className="text-sm text-gray-500">{step.percentage.toFixed(1)}%</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Real-Time Tab */}
        <TabsContent value="realtime" className="space-y-4">
          {/* New Campaign Detection Alert */}
          {utmCampaigns?.filter((c: UTMCampaign) => {
            const firstSeen = new Date(c.firstSeen);
            const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
            return firstSeen > twentyFourHoursAgo;
          }).length > 0 && (
            <Card className="border-green-200 bg-green-50">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-green-800">
                  <Activity className="w-5 h-5 text-green-600" />
                  🚨 New Campaign Detection Alert
                  <Badge variant="secondary" className="bg-green-100 text-green-800">
                    {utmCampaigns?.filter((c: UTMCampaign) => {
                      const firstSeen = new Date(c.firstSeen);
                      const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
                      return firstSeen > twentyFourHoursAgo;
                    }).length} New
                  </Badge>
                </CardTitle>
                <CardDescription className="text-green-700">
                  New campaigns detected in the last 24 hours - automatically tracked and analyzed
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {utmCampaigns?.filter((c: UTMCampaign) => {
                    const firstSeen = new Date(c.firstSeen);
                    const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
                    return firstSeen > twentyFourHoursAgo;
                  }).map((campaign: UTMCampaign, index: number) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-white border border-green-200 rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                        <div>
                          <p className="font-semibold text-green-900">{campaign.campaign}</p>
                          <p className="text-sm text-green-700">{campaign.source} / {campaign.medium}</p>
                          <p className="text-xs text-green-600">
                            First detected: {new Date(campaign.firstSeen).toLocaleString()}
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-semibold text-green-900">{campaign.sessions} page views</p>
                        <p className="text-xs text-green-700">{campaign.conversionRate.toFixed(2)}% conversion</p>
                        <Badge variant="outline" className="text-xs mt-1 border-green-300 text-green-700">
                          NEW
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                  Live Campaign Activity
                </CardTitle>
                <CardDescription>Campaigns with activity in the last 24 hours</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {utmCampaigns?.filter((c: UTMCampaign) => c.isActive).map((campaign: UTMCampaign, index: number) => (
                    <div key={index} className="flex items-center justify-between p-2 border rounded">
                      <div>
                        <p className="font-medium">{campaign.campaign}</p>
                        <p className="text-xs text-gray-500">{campaign.source}</p>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-semibold">{campaign.sessions} page views</p>
                        <p className="text-xs text-gray-500">{campaign.conversionRate}% CVR</p>
                      </div>
                    </div>
                  )) || []}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Performance Alerts</CardTitle>
                <CardDescription>Campaigns requiring attention</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {utmSources?.filter((s: UTMSource) => s.performance === 'Low' && s.sessions > 10).map((source: UTMSource, index: number) => (
                    <div key={index} className="flex items-start gap-3 p-3 bg-yellow-50 border border-yellow-200 rounded">
                      <TrendingDown className="w-4 h-4 text-yellow-600 mt-0.5" />
                      <div>
                        <p className="font-medium text-yellow-800">{source.utm_source}/{source.utm_medium}</p>
                        <p className="text-sm text-yellow-700">
                          Low conversion rate: {source.conversionRate}% ({source.sessions} sessions)
                        </p>
                      </div>
                    </div>
                  )) || []}
                  
                  {(!utmSources?.some((s: UTMSource) => s.performance === 'Low' && s.sessions > 10)) && (
                    <p className="text-gray-500 text-center py-4">No performance issues detected</p>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}