import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import { 
  Users, 
  Shield, 
  Settings, 
  Eye, 
  Edit3, 
  X,
  Home,
  Calendar,
  BarChart3,
  Clock,
  MapPin,
  Activity,
  UserCog,
  Palette
} from "lucide-react";

// Dashboard permissions available
// Updated to match current dashboard navigation structure
const AVAILABLE_DASHBOARDS = [
  { id: 'overview', label: 'Overview', icon: Home, description: 'Dashboard overview and stats' },
  { id: 'bookings', label: 'Bookings', icon: Calendar, description: 'Manage customer appointments' },
  { id: 'executive-analytics', label: 'Performance Overview', icon: BarChart3, description: 'Executive dashboard with KPIs and conversion metrics' },
  { id: 'user-behaviour', label: 'User Behaviour', icon: Activity, description: 'Interactive user behaviour analytics and session insights' },
  { id: 'calendar', label: 'Calendar', icon: Clock, description: 'Calendar view of appointments' },
  { id: 'reps', label: 'Sales Reps', icon: Users, description: 'Manage sales representatives' },
  { id: 'postal-codes', label: 'Service Areas', icon: MapPin, description: 'Manage postal codes and areas' },
  { id: 'postal-analytics', label: 'Postal Analytics', icon: BarChart3, description: 'Track postal code entries and service area expansion' },
  { id: 'users', label: 'Users', icon: UserCog, description: 'User management and creation' },
  { id: 'permissions', label: 'Permissions', icon: Shield, description: 'Manage user access levels' },
  { id: 'sessions', label: 'Sessions', icon: Activity, description: 'Admin login activity tracking' },
  { id: 'customization', label: 'Theme', icon: Palette, description: 'Customize appearance and branding' },
  { id: 'settings', label: 'Settings', icon: Settings, description: 'System configuration' },
  { id: 'password', label: 'Security', icon: Shield, description: 'Change password and security' }
];

const ACCESS_LEVELS = [
  { value: 'none', label: 'No Access', color: 'bg-gray-100 text-gray-700' },
  { value: 'view', label: 'View Only', color: 'bg-blue-100 text-blue-700' },
  { value: 'edit', label: 'Full Access', color: 'bg-green-100 text-green-700' }
];

interface User {
  id: number;
  username: string;
  name: string;
  role: string;
  dashboardPermissions: string | null;
  isActive: boolean;
}

export default function UserPermissionsManager() {
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [permissions, setPermissions] = useState<Record<string, string>>({});
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch all users
  const { data: users = [], isLoading } = useQuery<User[]>({
    queryKey: ["/api/admin/users"],
  });

  // Update user permissions mutation
  const updatePermissionsMutation = useMutation({
    mutationFn: async ({ userId, permissions }: { userId: number; permissions: Record<string, string> }) => {
      const response = await fetch(`/api/admin/users/${userId}`, {
        method: "PUT",
        body: JSON.stringify({
          dashboardPermissions: JSON.stringify(permissions)
        }),
        headers: {
          'Content-Type': 'application/json'
        }
      });
      if (!response.ok) throw new Error('Failed to update permissions');
      return response.json();
    },
    onSuccess: () => {
      toast({
        title: "Permissions Updated",
        description: "User permissions have been updated successfully.",
      });
      queryClient.invalidateQueries({ queryKey: ["/api/admin/users"] });
      setSelectedUser(null);
    },
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to update user permissions.",
        variant: "destructive",
      });
    },
  });

  const handleUserSelect = (user: User) => {
    setSelectedUser(user);
    try {
      const userPermissions = user.dashboardPermissions ? JSON.parse(user.dashboardPermissions) : {};
      setPermissions(userPermissions);
    } catch {
      setPermissions({});
    }
  };

  const handlePermissionChange = (dashboardId: string, access: string) => {
    setPermissions(prev => ({
      ...prev,
      [dashboardId]: access
    }));
  };

  const handleSavePermissions = () => {
    if (!selectedUser) return;
    updatePermissionsMutation.mutate({
      userId: selectedUser.id,
      permissions
    });
  };

  const getAccessBadge = (access: string) => {
    const level = ACCESS_LEVELS.find(l => l.value === access) || ACCESS_LEVELS[0];
    return (
      <Badge className={level.color}>
        {level.label}
      </Badge>
    );
  };

  if (isLoading) {
    return <div className="flex justify-center p-8">Loading users...</div>;
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">User Permissions</h1>
          <p className="text-gray-600">Manage dashboard access for team members</p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Users List */}
        <div className="lg:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Team Members
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {users.filter(user => user.role !== 'admin').map((user) => (
                  <div
                    key={user.id}
                    onClick={() => handleUserSelect(user)}
                    className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                      selectedUser?.id === user.id
                        ? 'border-purple-300 bg-purple-50'
                        : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="font-medium text-gray-900">{user.name}</div>
                        <div className="text-sm text-gray-500">{user.username}</div>
                      </div>
                      <Badge variant={user.isActive ? "default" : "secondary"}>
                        {user.role}
                      </Badge>
                    </div>
                  </div>
                ))}
                {users.filter(user => user.role !== 'admin').length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    <Users className="h-8 w-8 mx-auto mb-2 opacity-50" />
                    <p>No team members found</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Permissions Editor */}
        <div className="lg:col-span-2">
          {selectedUser ? (
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center gap-2">
                    <Shield className="h-5 w-5" />
                    Permissions for {selectedUser.name}
                  </CardTitle>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setSelectedUser(null)}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
                <p className="text-sm text-gray-600">
                  Configure dashboard access levels for this user
                </p>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-4">
                  {AVAILABLE_DASHBOARDS.map((dashboard) => {
                    const Icon = dashboard.icon;
                    const currentAccess = permissions[dashboard.id] || 'none';
                    
                    return (
                      <div key={dashboard.id} className="flex items-center justify-between p-4 border rounded-lg">
                        <div className="flex items-center gap-3">
                          <Icon className="h-5 w-5 text-gray-500" />
                          <div>
                            <div className="font-medium text-gray-900">{dashboard.label}</div>
                            <div className="text-sm text-gray-500">{dashboard.description}</div>
                          </div>
                        </div>
                        <div className="flex items-center gap-3">
                          {getAccessBadge(currentAccess)}
                          <Select
                            value={currentAccess}
                            onValueChange={(value) => handlePermissionChange(dashboard.id, value)}
                          >
                            <SelectTrigger className="w-32">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {ACCESS_LEVELS.map((level) => (
                                <SelectItem key={level.value} value={level.value}>
                                  <div className="flex items-center gap-2">
                                    {level.value === 'none' && <X className="h-4 w-4" />}
                                    {level.value === 'view' && <Eye className="h-4 w-4" />}
                                    {level.value === 'edit' && <Edit3 className="h-4 w-4" />}
                                    {level.label}
                                  </div>
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                    );
                  })}
                </div>

                <div className="flex justify-end gap-3 pt-4 border-t">
                  <Button
                    variant="outline"
                    onClick={() => setSelectedUser(null)}
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={handleSavePermissions}
                    disabled={updatePermissionsMutation.isPending}
                  >
                    {updatePermissionsMutation.isPending ? "Saving..." : "Save Permissions"}
                  </Button>
                </div>
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-12">
                <Shield className="h-12 w-12 text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Select a Team Member</h3>
                <p className="text-gray-500 text-center">
                  Choose a user from the list to configure their dashboard permissions
                </p>
              </CardContent>
            </Card>
          )}
        </div>
      </div>

      {/* Permission Levels Guide */}
      <Card>
        <CardHeader>
          <CardTitle>Permission Levels Guide</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {ACCESS_LEVELS.map((level) => (
              <div key={level.value} className="flex items-start gap-3 p-4 border rounded-lg">
                <div className="flex-shrink-0">
                  {level.value === 'none' && <X className="h-5 w-5 text-gray-500" />}
                  {level.value === 'view' && <Eye className="h-5 w-5 text-blue-500" />}
                  {level.value === 'edit' && <Edit3 className="h-5 w-5 text-green-500" />}
                </div>
                <div>
                  <div className="font-medium text-gray-900 mb-1">{level.label}</div>
                  <div className="text-sm text-gray-600">
                    {level.value === 'none' && 'User cannot access this dashboard section'}
                    {level.value === 'view' && 'User can view data but cannot make changes'}
                    {level.value === 'edit' && 'User has full access to view and modify data'}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}