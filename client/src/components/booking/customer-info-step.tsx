import { useState, useEffect } from "react";
import { useMutation, useQuery } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { User, Phone, MapPin, Shield } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useAnalytics } from "@/hooks/use-analytics";
import { useFacebookTracking } from "@/hooks/use-facebook-tracking";
import { trackOTPRequest, trackOTPVerification, trackBookingConfirmed, trackFormStart } from "@/lib/google-analytics";

interface CustomerInfoStepProps {
  value: {
    name: string;
    phone: string;
    address: string;
  };
  onChange: (value: any) => void;
  onBookingConfirmed: (booking: any) => void;
  bookingData: any;
  onNext?: () => void;
}

export default function CustomerInfoStep({ 
  value, 
  onChange, 
  onBookingConfirmed, 
  bookingData,
  onNext 
}: CustomerInfoStepProps) {
  const analytics = useAnalytics();
  const facebookTracking = useFacebookTracking();
  const [formData, setFormData] = useState({
    ...value,
    phone: value.phone || '+91'
  });
  const [cityState, setCityState] = useState("");
  const [isOtpSent, setIsOtpSent] = useState(false);
  const [otp, setOtp] = useState("");
  const [isPhoneVerified, setIsPhoneVerified] = useState(false);
  const [isVerifying, setIsVerifying] = useState(false);
  const [isSendingOtp, setIsSendingOtp] = useState(false);
  const [resendTimer, setResendTimer] = useState(0);
  const [canResend, setCanResend] = useState(false);
  const [isProcessingBooking, setIsProcessingBooking] = useState(false);
  const { toast } = useToast();

  // Timer effect for resend OTP
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (resendTimer > 0) {
      interval = setInterval(() => {
        setResendTimer((prev) => {
          if (prev <= 1) {
            setCanResend(true);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [resendTimer]);

  // Fetch postal code details
  const { data: postalCodeDetails } = useQuery({
    queryKey: ["/api/postal-codes", bookingData.postalCode],
    queryFn: () => {
      if (!bookingData.postalCode) return null;
      return fetch(`/api/postal-codes/${bookingData.postalCode}`).then(res => res.json());
    },
    enabled: !!bookingData.postalCode,
  });

  useEffect(() => {
    if (postalCodeDetails) {
      setCityState(`${postalCodeDetails.city}, ${postalCodeDetails.state}`);
    }
  }, [postalCodeDetails]);

  // Track form start when component mounts
  useEffect(() => {
    analytics.trackFormStart('contact_info');
    analytics.updateSession('contact_info', ['postal_code', 'date_time', 'contact_info']);
  }, [analytics]);

  const sendOtpMutation = useMutation({
    mutationFn: (phone: string) => 
      apiRequest("POST", "/api/otp/send", { phone }).then(res => res.json()),
    onSuccess: () => {
      setIsOtpSent(true);
      setIsSendingOtp(false);
      setResendTimer(30);
      setCanResend(false);
      
      // Track OTP request
      analytics.trackOTPRequest(formData.phone);
      facebookTracking.trackOTPRequested(formData.phone);
      
      toast({
        title: "OTP Sent",
        description: "Please enter the code and click the button again to complete booking.",
      });
    },
    onError: (error: any) => {
      setIsSendingOtp(false);
      setIsProcessingBooking(false);
      toast({
        title: "Failed to Send OTP",
        description: error.message || "Could not send verification code.",
        variant: "destructive",
      });
    }
  });

  const verifyOtpMutation = useMutation({
    mutationFn: ({ phone, otp }: { phone: string; otp: string }) => 
      apiRequest("POST", "/api/otp/verify", { phone, otp }).then(res => res.json()),
    onSuccess: () => {
      setIsPhoneVerified(true);
      setIsVerifying(false);
      
      // Track successful OTP verification
      analytics.trackOTPVerification(true);
      analytics.updateSession('otp_verified', ['postal_code', 'date_time', 'contact_info', 'otp_verification', 'otp_verified']);
      facebookTracking.trackBookMyTrialPressed(formData.phone);
      
      // Don't show verification success toast in streamlined flow
      // The booking will proceed automatically
    },
    onError: (error: any) => {
      setIsVerifying(false);
      setIsProcessingBooking(false);
      
      // Track failed OTP verification
      analytics.trackOTPVerification(false);
      
      toast({
        title: "Verification Failed",
        description: error.message || "Invalid verification code.",
        variant: "destructive",
      });
    }
  });

  const bookingMutation = useMutation({
    mutationFn: async (data: any) => {
      console.log("Frontend: Making booking request with data:", {
        name: data.name,
        phone: data.phone,
        address: data.address,
        postalCode: bookingData.postalCode,
        date: bookingData.date,
        timeSlot: bookingData.timeSlot,
      });
      
      const response = await apiRequest("POST", "/api/bookings", {
        name: data.name,
        phone: data.phone,
        address: data.address,
        postalCode: bookingData.postalCode,
        date: bookingData.date,
        timeSlot: bookingData.timeSlot,
      });
      
      console.log("Frontend: Booking API response status:", response.status);
      const result = await response.json();
      console.log("Frontend: Booking API response data:", result);
      return result;
    },
    onSuccess: (booking) => {
      // Track booking completion
      analytics.trackBookingComplete(booking.id);
      
      // Track Facebook Purchase conversion
      facebookTracking.trackBookingConfirmed({
        bookingId: booking.id.toString(),
        phoneNumber: formData.phone,
        postalCode: bookingData.postalCode,
        city: postalCodeDetails?.city || 'Unknown',
        state: postalCodeDetails?.state || 'Unknown',
        appointmentDate: bookingData.date,
        appointmentTime: bookingData.timeSlot,
        salesRep: booking.salesRepName || 'Unknown'
      });
      
      onBookingConfirmed(booking);
      toast({
        title: "Booking confirmed!",
        description: "Your perfume trial is booked. Check your phone for confirmation.",
      });
      
      // Immediate transition to avoid button state flash
      onNext?.();
    },
    onError: (error: any) => {
      setIsProcessingBooking(false);
      console.error("Frontend booking error:", error);
      console.error("Error details:", {
        message: error.message,
        status: error.status,
        response: error.response,
        stack: error.stack
      });
      toast({
        title: "Booking failed",
        description: error.message || "Unable to create booking. Please try again.",
        variant: "destructive",
      });
    },
  });

  const handleInputChange = (field: string, value: string) => {
    const newData = { ...formData, [field]: value };
    setFormData(newData);
    onChange(newData);
  };

  const handleSendOtp = () => {
    if (!formData.phone) {
      toast({
        title: "Phone number required",
        description: "Please enter your phone number first.",
        variant: "destructive",
      });
      return;
    }

    // Validate phone number format
    const phoneRegex = /^\+[1-9]\d{9,14}$/;
    if (!phoneRegex.test(formData.phone)) {
      toast({
        title: "Invalid phone number",
        description: "Please enter a valid international phone number (e.g., +919876543210).",
        variant: "destructive",
      });
      return;
    }

    setIsSendingOtp(true);
    sendOtpMutation.mutate(formData.phone);
  };

  const handleResendOtp = () => {
    setOtp("");
    setIsSendingOtp(true);
    sendOtpMutation.mutate(formData.phone);
  };

  const handleVerifyOtp = () => {
    if (!otp || otp.length !== 6) {
      toast({
        title: "Invalid OTP",
        description: "Please enter the 6-digit verification code.",
        variant: "destructive",
      });
      return;
    }

    setIsVerifying(true);
    verifyOtpMutation.mutate({ phone: formData.phone, otp });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name || !formData.phone || !formData.address) {
      toast({
        title: "Please fill required fields",
        description: "Name, phone, and address are required.",
        variant: "destructive",
      });
      return;
    }

    setIsProcessingBooking(true);

    try {
      // If phone not verified, send OTP first
      if (!isPhoneVerified) {
        if (!isOtpSent) {
          // Send OTP first
          setIsSendingOtp(true);
          await sendOtpMutation.mutateAsync(formData.phone);
          setIsSendingOtp(false);
          setIsProcessingBooking(false);
          return; // Wait for user to enter OTP
        } else {
          // Verify OTP
          if (!otp || otp.length !== 6) {
            toast({
              title: "OTP Required",
              description: "Please enter the 6-digit verification code.",
              variant: "destructive",
            });
            setIsProcessingBooking(false);
            return;
          }

          setIsVerifying(true);
          await verifyOtpMutation.mutateAsync({ phone: formData.phone, otp });
          setIsVerifying(false);
        }
      }

      // Once verified, proceed with booking
      analytics.trackFormSubmit('contact_info', true);
      await bookingMutation.mutateAsync({
        ...bookingData,
        ...formData,
      });

    } catch (error) {
      console.error('Booking process error:', error);
      setIsProcessingBooking(false);
    }
  };

  return (
    <div className="animate-fade-in">
      <div className="text-center mb-6">
        <h2 className="text-2xl font-butler font-bold text-soft-black mb-2">
          Tell us about you
        </h2>
        <p className="text-gray-600 text-sm">
          We need a few details to confirm your appointment
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="relative">
          <Input
            type="text"
            placeholder="Full Name"
            value={formData.name}
            onChange={(e) => handleInputChange("name", e.target.value)}
            className="w-full px-4 py-4 rounded-2xl border-2 border-gray-200 focus:border-primary-lilac focus:outline-none transition-all duration-300 pr-12"
            required
          />
          <User className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
        </div>

        <div className="space-y-3">
          <div className="relative">
            <div className="flex items-stretch">
              <div className="flex items-center px-3 text-sm text-gray-500 bg-gray-50 border-2 border-r-0 border-gray-200 rounded-l-2xl font-medium min-h-[56px]">
                +91
              </div>
              <Input
                type="tel"
                placeholder="Your mobile number"
                value={formData.phone.startsWith('+91') ? formData.phone.slice(3) : formData.phone}
                onChange={(e) => {
                  const phoneNumber = e.target.value.replace(/\D/g, ''); // Only allow digits
                  handleInputChange("phone", `+91${phoneNumber}`);
                }}
                className="flex-1 px-4 py-4 rounded-r-2xl border-2 border-l-0 border-gray-200 focus:border-primary-lilac focus:outline-none transition-all duration-300 pr-12 h-[56px]"
                required
                disabled={isPhoneVerified}
                maxLength={10}
              />
              <Phone className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              {isPhoneVerified && (
                <div className="absolute right-12 top-1/2 transform -translate-y-1/2">
                  <Shield className="h-5 w-5 text-green-500" />
                </div>
              )}
            </div>
          </div>
          
          {isOtpSent && !isPhoneVerified && (
            <div className="space-y-3">
              <div className="relative">
                <Input
                  type="text"
                  placeholder="Enter 6-digit OTP"
                  value={otp}
                  onChange={(e) => setOtp(e.target.value)}
                  className="w-full px-4 py-4 rounded-2xl border-2 border-gray-200 focus:border-primary-lilac focus:outline-none transition-all duration-300 text-center text-lg tracking-widest"
                  maxLength={6}
                />
              </div>

              {/* Resend OTP Button */}
              <div className="text-center">
                {resendTimer > 0 ? (
                  <p className="text-sm text-gray-600">
                    Resend OTP in {resendTimer}s
                  </p>
                ) : (
                  <Button
                    variant="ghost"
                    onClick={handleResendOtp}
                    disabled={isSendingOtp}
                    className="text-sm text-purple-600 hover:text-purple-800 underline p-0 h-auto font-normal"
                  >
                    {isSendingOtp ? "Sending..." : "Resend OTP"}
                  </Button>
                )}
              </div>
            </div>
          )}
          
          {isPhoneVerified && (
            <div className="text-center text-green-600 font-medium py-2">
              ✓ Phone number verified successfully
            </div>
          )}
        </div>

        <div className="space-y-2">
          <div className="relative">
            <Textarea
              placeholder="House/Flat Number, Building, Street, Area"
              value={formData.address}
              onChange={(e) => handleInputChange("address", e.target.value)}
              className="w-full px-4 py-4 rounded-2xl border-2 border-gray-200 focus:border-primary-lilac focus:outline-none transition-all duration-300 resize-none pr-12"
              rows={3}
              required
            />
            <MapPin className="absolute right-4 top-4 text-gray-400 h-5 w-5" />
          </div>
          {cityState && (
            <div className="text-sm text-gray-600 px-4 py-2 bg-gray-100 rounded-xl">
              📍 {bookingData.postalCode} - {cityState}
            </div>
          )}
        </div>

        <Button
          type="submit"
          disabled={isProcessingBooking || isSendingOtp || isVerifying || bookingMutation.isPending}
          className={`w-full py-4 rounded-2xl font-semibold ${
            !isProcessingBooking && !isSendingOtp && !isVerifying && !bookingMutation.isPending 
              ? 'btn-kult-active' 
              : 'btn-kult-disabled'
          }`}
        >
          {(() => {
            if (bookingMutation.isPending) return "Booking Your Trial...";
            if (isVerifying) return "Verifying and Booking...";
            if (isSendingOtp) return "Getting OTP on WhatsApp...";
            if (isProcessingBooking) return "Processing...";
            if (!isPhoneVerified && !isOtpSent) return "Get OTP on WhatsApp";
            if (!isPhoneVerified && isOtpSent) return "Verify and Book My Trial";
            if (isPhoneVerified) return "Booking Your Trial...";
            return "Book My Trial";
          })()}
        </Button>
      </form>
    </div>
  );
}
