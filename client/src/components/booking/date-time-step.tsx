import { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { format, addDays, startOfTomorrow } from "date-fns";
import { useAnalytics } from "@/hooks/use-analytics";
import { useFacebookTracking } from "@/hooks/use-facebook-tracking";
import { trackDateSelection, trackTimeSlotSelection, trackFormStart } from "@/lib/google-analytics";

interface DateTimeStepProps {
  value: { date: string; timeSlot: string };
  onChange: (value: { date: string; timeSlot: string }) => void;
  onNext?: () => void;
  isRescheduleMode?: boolean;
  onConfirmReschedule?: () => void;
}

export default function DateTimeStep({ value, onChange, onNext, isRescheduleMode, onConfirmReschedule }: DateTimeStepProps) {
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(
    value.date ? new Date(value.date) : undefined
  );
  const [selectedTimeSlot, setSelectedTimeSlot] = useState(value.timeSlot);
  const analytics = useAnalytics();
  const facebookTracking = useFacebookTracking();

  // Track form start when component mounts
  useEffect(() => {
    analytics.trackFormStart('date_time');
    analytics.updateSession('date_time', ['postal_code', 'date_time']);
  }, [analytics]);

  // Get booking configuration for rules
  const { data: bookingConfig = {} } = useQuery<any>({
    queryKey: ["/api/booking-config"],
    staleTime: 0,
    gcTime: 0,
  });

  // Convert selected date to IST format for API call
  const getISTDateString = (date: Date | undefined) => {
    if (!date) return null;
    // Convert to IST by adding 5.5 hours to UTC
    const istDate = new Date(date.getTime() + (5.5 * 60 * 60 * 1000));
    return istDate.toISOString().split('T')[0];
  };
  
  const dateString = getISTDateString(selectedDate);
  
  const { data: availability, isLoading, error } = useQuery<{
    available: boolean;
    timeSlots: string[];
    remaining?: number;
  } | null>({
    queryKey: ["/api/availability", dateString],
    queryFn: async () => {
      if (!dateString) return null;
      const response = await fetch(`/api/availability/${dateString}`);
      if (!response.ok) {
        throw new Error('Failed to fetch availability');
      }
      return response.json();
    },
    enabled: !!dateString,
    staleTime: 0,
    gcTime: 0,
    refetchOnWindowFocus: false,
  });



  const handleDateSelect = (date: Date | undefined) => {
    setSelectedDate(date);
    setSelectedTimeSlot(""); // Reset time slot when date changes
    
    if (date) {
      // Track date selection with UTM attribution
      const istDateString = getISTDateString(date);
      facebookTracking.trackDateSelection(istDateString || date.toISOString(), '');
      trackDateSelection(istDateString || date.toISOString());
    }
  };

  const handleTimeSlotSelect = (timeSlot: string) => {
    setSelectedTimeSlot(timeSlot);
    if (selectedDate) {
      // Convert to IST for consistent backend handling
      const istDateString = getISTDateString(selectedDate);
      onChange({
        date: istDateString || selectedDate.toISOString(),
        timeSlot,
      });
      
      // Track time slot selection with UTM attribution
      analytics.trackTimeSlotSelection(istDateString || selectedDate.toISOString(), timeSlot);
      facebookTracking.trackTimeSelection(timeSlot, istDateString || selectedDate.toISOString(), '');
      trackTimeSlotSelection(timeSlot, istDateString || selectedDate.toISOString());
      
      // Track form submit for date-time step
      analytics.trackFormSubmit('date_time', true);
      
      // Auto-advance to next step after time selection (not in reschedule mode)
      if (!isRescheduleMode && onNext) {
        setTimeout(() => {
          onNext();
        }, 500); // Small delay for visual feedback
      }
    }
  };

  const handleContinue = () => {
    if (selectedDate && selectedTimeSlot) {
      onNext?.();
    }
  };

  // Disable dates in the past and beyond configurable booking window
  const maxDaysAhead = (bookingConfig as any)?.maxBookingDaysAhead || 7;
  const disabledDays = [
    { before: new Date() }, // Disable past dates
    { after: addDays(new Date(), maxDaysAhead) }
  ];
  


  return (
    <div className="animate-fade-in">
      <div className="text-center mb-6">
        <h2 className="text-2xl font-butler font-bold text-soft-black mb-2">
          Pick your perfect time
        </h2>
        <p className="text-gray-600 text-sm">
          Choose when you'd like your perfume trial
        </p>
      </div>

      {/* Calendar Section */}
      <div className="mb-6">
        <h3 className="font-semibold text-soft-black mb-3">Select Date</h3>
        <div className="flex justify-center">
          <Calendar
            mode="single"
            selected={selectedDate}
            onSelect={handleDateSelect}
            disabled={disabledDays}
            className="rounded-2xl border"
          />
        </div>
      </div>

      {/* Time Slots */}
      {selectedDate && (
        <div className="mb-6">
          <h3 className="font-semibold text-soft-black mb-3">Select Time</h3>
          {isLoading ? (
            <div className="text-center text-gray-500">Loading available times...</div>
          ) : error ? (
            <div className="text-center text-red-500 p-4">
              Error loading time slots: {error instanceof Error ? error.message : 'Failed to load time slots'}
            </div>
          ) : availability?.available && availability?.timeSlots?.length > 0 ? (
            <div>

              <div className="grid grid-cols-2 gap-3">
                {(availability.timeSlots || []).map((slot: string) => (
                  <Button
                    key={slot}
                    variant="outline"
                    onClick={() => handleTimeSlotSelect(slot)}
                    className={`py-3 px-4 rounded-xl font-semibold ${
                      selectedTimeSlot === slot ? 'btn-kult-active' : 'btn-kult-inactive'
                    }`}
                  >
                    {slot}
                  </Button>
                ))}
              </div>
            </div>
          ) : (
            <div className="text-center text-gray-500 p-4">
              No available time slots for this date. Please select another date.

            </div>
          )}
        </div>
      )}

      {isRescheduleMode && (
        <Button
          onClick={onConfirmReschedule}
          disabled={!selectedDate || !selectedTimeSlot}
          className={`w-full py-4 rounded-2xl font-semibold ${
            selectedDate && selectedTimeSlot ? 'btn-kult-active' : 'btn-kult-disabled'
          }`}
        >
          Confirm New Time
        </Button>
      )}
    </div>
  );
}
