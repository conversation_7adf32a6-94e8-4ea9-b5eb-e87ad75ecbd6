import { useState } from "react";
import { useMutation } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Calendar, Clock, MapPin, User, CheckCircle, X, Edit3 } from "lucide-react";
import { toDisplayDate } from "@shared/date-utils";
import { useToast } from "@/hooks/use-toast";
import RescheduleStep from "./reschedule-step";

interface ConfirmationStepProps {
  booking: any;
  bookingData: any;
  onCancel?: () => void;
  onStartNewBooking?: (postalCode: string) => void;
}

export default function ConfirmationStep({ booking, bookingData, onCancel, onStartNewBooking }: ConfirmationStepProps) {
  const { toast } = useToast();
  const [showReschedule, setShowReschedule] = useState(false);
  const [currentBooking, setCurrentBooking] = useState(booking);

  console.log('ConfirmationStep rendered with:', { booking, bookingData });

  const cancelMutation = useMutation({
    mutationFn: async () => {
      const bookingToCancel = currentBooking || booking;
      if (!bookingToCancel?.id) {
        throw new Error("No booking ID available");
      }
      const response = await apiRequest("PUT", `/api/bookings/${bookingToCancel.id}`, {
        status: "cancelled"
      });
      return response.json();
    },
    onSuccess: () => {
      toast({
        title: "Booking cancelled",
        description: "Your appointment has been cancelled successfully.",
      });
      // Redirect back to main booking page with postal code pre-filled
      if (onStartNewBooking) {
        onStartNewBooking(bookingData.postalCode);
      }
    },
    onError: () => {
      toast({
        title: "Cancellation failed",
        description: "Unable to cancel booking. Please contact support.",
        variant: "destructive",
      });
    },
  });

  const formatDate = (dateString: string) => {
    return toDisplayDate(dateString);
  };

  // Always allow modifications - no time restrictions
  const canModify = true;

  const handleRescheduleSuccess = (updatedBooking: any) => {
    setCurrentBooking(updatedBooking);
    setShowReschedule(false);
  };

  // If no booking data, show loading or wait message
  if (!booking && !currentBooking) {
    return (
      <div className="text-center animate-fade-in">
        <div className="mb-6">
          <h2 className="text-2xl font-butler font-bold text-soft-black mb-2">
            Complete your booking
          </h2>
          <p className="text-gray-600">Please complete the previous steps to see your confirmation.</p>
        </div>
      </div>
    );
  }

  if (showReschedule) {
    return (
      <RescheduleStep
        booking={currentBooking}
        onRescheduleSuccess={handleRescheduleSuccess}
        onCancel={() => setShowReschedule(false)}
      />
    );
  }

  return (
    <div className="text-center animate-fade-in">
      <div className="mb-6">
        <div className="w-20 h-20 mx-auto mb-4 rounded-full bg-gradient-to-r from-mint to-primary-lilac flex items-center justify-center animate-scale-in">
          <CheckCircle className="h-10 w-10 text-white" />
        </div>
        <h2 className="text-2xl font-butler font-bold text-soft-black mb-2">
          You're all set! 🎉
        </h2>
        <p className="text-gray-600">Your Perfume Trial is booked. See you soon!</p>
      </div>

      {/* Booking Details */}
      <Card className="mb-6 text-left">
        <CardContent className="p-6">
          <h3 className="font-semibold text-soft-black mb-4">Booking Details</h3>
          <div className="space-y-3 text-sm">
            <div className="flex items-center justify-between">
              <div className="flex items-center text-gray-600">
                <User className="h-4 w-4 mr-2" />
                <span>Service:</span>
              </div>
              <span className="font-medium">Perfume Trial at Home</span>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center text-gray-600">
                <Calendar className="h-4 w-4 mr-2" />
                <span>Date:</span>
              </div>
              <span className="font-medium">
                {currentBooking?.date ? formatDate(currentBooking.date) : formatDate(bookingData.date)}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center text-gray-600">
                <Clock className="h-4 w-4 mr-2" />
                <span>Time:</span>
              </div>
              <span className="font-medium">{currentBooking?.timeSlot || bookingData.timeSlot}</span>
            </div>
            <div className="flex items-start justify-between">
              <div className="flex items-center text-gray-600">
                <MapPin className="h-4 w-4 mr-2 mt-0.5" />
                <span>Address:</span>
              </div>
              <span className="font-medium text-right max-w-xs">{bookingData.address}</span>
            </div>
            {booking?.repAssigned && (
              <div className="flex items-center justify-between">
                <div className="flex items-center text-gray-600">
                  <User className="h-4 w-4 mr-2" />
                  <span>Representative:</span>
                </div>
                <span className="font-medium">{booking.repAssigned}</span>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Action Buttons - Always Show */}
      <div className="space-y-3 mt-6">
        {/* Debug Info */}
        <div className="text-xs bg-gray-100 p-2 rounded text-left">
          <strong>Debug:</strong> booking: {booking ? 'exists' : 'null'}, currentBooking: {currentBooking ? 'exists' : 'null'}
          {booking && <div>ID: {booking.id}</div>}
        </div>
        
        <Button
          onClick={() => {
            console.log('Reschedule button clicked, currentBooking:', currentBooking || booking);
            setShowReschedule(true);
          }}
          className="w-full py-4 text-lg font-semibold rounded-2xl"
          style={{
            backgroundColor: 'rgb(170, 138, 219)',
            color: 'white',
            border: 'none'
          }}
        >
          <Edit3 className="h-5 w-5 mr-2" />
          Reschedule Appointment
        </Button>
        <Button
          onClick={() => {
            console.log('Cancel button clicked, booking to cancel:', currentBooking || booking);
            cancelMutation.mutate();
          }}
          disabled={cancelMutation.isPending}
          className="w-full py-4 text-lg font-semibold rounded-2xl"
          style={{
            backgroundColor: cancelMutation.isPending ? '#E5E7EB' : 'rgb(170, 138, 219)',
            color: cancelMutation.isPending ? '#9CA3AF' : 'white',
            border: 'none'
          }}
        >
          <X className="h-5 w-5 mr-2" />
          {cancelMutation.isPending ? "Cancelling..." : "Cancel Booking"}
        </Button>
      </div>


    </div>
  );
}
