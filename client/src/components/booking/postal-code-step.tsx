import { useState, useEffect } from "react";
import { useMutation } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { MapPin } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useAnalytics } from "@/hooks/use-analytics";
import { useFacebookTracking } from "@/hooks/use-facebook-tracking";
import { trackPostalCodeCheck, trackFormStart } from "@/lib/google-analytics";

interface PostalCodeStepProps {
  value: string;
  onChange: (value: string) => void;
  onNext?: () => void;
}

export default function PostalCodeStep({ value, onChange, onNext }: PostalCodeStepProps) {
  const [postalCode, setPostalCode] = useState(value);
  const [showMessage, setShowMessage] = useState(false);
  const [isValid, setIsValid] = useState(false);
  const { toast } = useToast();
  const analytics = useAnalytics();
  const facebookTracking = useFacebookTracking();

  // Update local state when value prop changes (e.g., from URL parameter)
  useEffect(() => {
    if (value && value !== postalCode) {
      setPostalCode(value);
      // Auto-validate if postal code is pre-filled from cancel flow
      if (value.length >= 6) {
        validateMutation.mutate(value);
      }
    }
  }, [value, postalCode]);

  const validateMutation = useMutation({
    mutationFn: async (code: string) => {
      const response = await fetch(`/api/postal-codes/validate/${code}`);
      if (!response.ok) throw new Error("Validation failed");
      return response.json();
    },
    onSuccess: (data) => {
      setShowMessage(true);
      setIsValid(data.valid);
      if (data.valid) {
        // Track valid postal code entry with UTM attribution
        analytics.trackCustomEvent('postal_code_valid', { postalCode });
        facebookTracking.trackCheckAvailability(postalCode, data.city || 'Unknown', data.state || 'Unknown');
        trackPostalCodeCheck(postalCode, true);
        onChange(postalCode);
        toast({
          title: "Great news!",
          description: "We serve your area. Let's continue with your booking.",
        });
        setTimeout(() => {
          onNext?.();
        }, 1000);
      } else {
        // Track invalid postal code entry with UTM attribution
        analytics.trackCustomEvent('postal_code_invalid', { postalCode, attempt: 'user_entered_wrong_code' });
        trackPostalCodeCheck(postalCode, false);
        toast({
          title: "Service area",
          description: "We're coming to your area soon 💜",
          variant: "destructive",
        });
      }
    },
    onError: () => {
      // Track validation error
      analytics.trackCustomEvent('postal_code_error', { postalCode, error: 'validation_failed' });
      toast({
        title: "Error",
        description: "Unable to validate postal code. Please try again.",
        variant: "destructive",
      });
    },
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (postalCode.length === 6) {
      analytics.trackFormSubmit('postal_code');
      validateMutation.mutate(postalCode);
    }
  };

  // Track form start when component mounts
  useEffect(() => {
    analytics.trackFormStart('postal_code');
    analytics.updateSession('postal_code', ['postal_code']);
    facebookTracking.trackPageView('Postal Code Entry');
  }, [analytics, facebookTracking]);

  return (
    <div className="animate-fade-in">
      <div className="text-center mb-6">
        <h2 className="text-2xl font-butler font-bold text-soft-black mb-4">
          Where are you located?
        </h2>
        
        {/* Beautiful pill-shaped service area indicator */}
        <div className="inline-flex items-center gap-2 px-6 py-3 mb-4 rounded-full" style={{
          backgroundColor: '#E3D1FE'
        }}>
          <MapPin className="h-5 w-5 text-black" />
          <span className="text-black font-bold text-sm">
            Currently only in Gurugram & Delhi
          </span>
        </div>
        
        <p className="text-gray-600 text-sm">
          Enter your postal code to check if we serve your area
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="relative">
          <Input
            type="text"
            placeholder="Enter 6-digit postal code"
            value={postalCode}
            onChange={(e) => {
              const value = e.target.value.replace(/\D/g, '').slice(0, 6);
              setPostalCode(value);
              setShowMessage(false);
            }}
            className="w-full px-4 py-4 rounded-2xl border-2 border-gray-200 focus:border-primary-lilac focus:outline-none transition-all duration-300 text-center text-lg font-semibold tracking-widest"
            maxLength={6}
          />
          <div className="absolute right-4 top-1/2 transform -translate-y-1/2">
            <MapPin className="h-5 w-5 text-primary-lilac" />
          </div>
        </div>

        <Button
          type="submit"
          disabled={postalCode.length !== 6 || validateMutation.isPending}
          className="w-full py-4 rounded-2xl font-semibold btn-kult-inactive"
        >
          {validateMutation.isPending ? (
            "Checking..."
          ) : (
            <>
              Check Availability
              <span className="ml-2">→</span>
            </>
          )}
        </Button>
      </form>

      {/* Service Area Message */}
      {showMessage && !isValid && (
        <div className="mt-4 p-4 rounded-2xl bg-coral-sorbet/20 border border-coral-sorbet animate-slide-up">
          <div className="flex items-center text-coral-sorbet">
            <span className="mr-2">💜</span>
            <span className="text-sm">We're coming to your area soon!</span>
          </div>
        </div>
      )}
    </div>
  );
}
