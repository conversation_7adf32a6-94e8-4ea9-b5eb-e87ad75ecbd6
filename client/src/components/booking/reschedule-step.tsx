import { useState } from "react";
import { useMutation } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { Button } from "@/components/ui/button";
import DateTimeStep from "./date-time-step";
import { useToast } from "@/hooks/use-toast";

interface RescheduleStepProps {
  booking: any;
  onRescheduleSuccess: (updatedBooking: any) => void;
  onCancel: () => void;
}

export default function RescheduleStep({ booking, onRescheduleSuccess, onCancel }: RescheduleStepProps) {
  const [newDateTime, setNewDateTime] = useState({ date: "", timeSlot: "" });
  const { toast } = useToast();

  const rescheduleMutation = useMutation({
    mutationFn: async (data: { date: string; timeSlot: string }) => {
      const response = await apiRequest("PUT", `/api/bookings/${booking.id}`, {
        date: data.date, // Send date as string instead of converting to ISO
        timeSlot: data.timeSlot,
        status: "confirmed"
      });
      return response.json();
    },
    onSuccess: (updatedBooking) => {
      toast({
        title: "Booking rescheduled successfully! 🎉",
        description: "Your new appointment time has been confirmed.",
      });
      onRescheduleSuccess(updatedBooking);
    },
    onError: () => {
      toast({
        title: "Reschedule failed",
        description: "Unable to reschedule booking. Please try again.",
        variant: "destructive",
      });
    },
  });

  const handleReschedule = () => {
    if (newDateTime.date && newDateTime.timeSlot) {
      rescheduleMutation.mutate(newDateTime);
    }
  };

  return (
    <div className="animate-fade-in">
      <div className="text-center mb-6">
        <h2 className="text-2xl font-butler font-bold text-soft-black mb-2">
          Reschedule Your Appointment
        </h2>
        <p className="text-gray-600 text-sm">
          Choose a new date and time for your perfume trial
        </p>
      </div>

      <DateTimeStep
        value={newDateTime}
        onChange={setNewDateTime}
        isRescheduleMode={true}
        onConfirmReschedule={handleReschedule}
      />

      <div className="flex justify-center mt-6">
        <Button
          variant="outline"
          onClick={onCancel}
          className="px-8 py-3 rounded-2xl font-semibold border-2 border-gray-300 hover:border-primary-lilac hover:bg-primary-lilac hover:text-white transition-all duration-300"
        >
          Cancel
        </Button>
      </div>
    </div>
  );
}