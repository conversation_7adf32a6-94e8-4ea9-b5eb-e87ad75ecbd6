@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&family=Poppins:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: hsl(0, 0%, 100%);
  --foreground: hsl(20, 14.3%, 4.1%);
  --muted: hsl(60, 4.8%, 95.9%);
  --muted-foreground: hsl(25, 5.3%, 44.7%);
  --popover: hsl(0, 0%, 100%);
  --popover-foreground: hsl(20, 14.3%, 4.1%);
  --card: hsl(0, 0%, 100%);
  --card-foreground: hsl(20, 14.3%, 4.1%);
  --border: hsl(20, 5.9%, 90%);
  --input: hsl(20, 5.9%, 90%);
  --primary: hsl(267, 83%, 85%);
  --primary-foreground: hsl(0, 0%, 100%);
  --secondary: hsl(60, 4.8%, 95.9%);
  --secondary-foreground: hsl(24, 9.8%, 10%);
  --accent: hsl(60, 4.8%, 95.9%);
  --accent-foreground: hsl(24, 9.8%, 10%);
  --destructive: hsl(0, 84.2%, 60.2%);
  --destructive-foreground: hsl(60, 9.1%, 97.8%);
  --ring: hsl(20, 14.3%, 4.1%);
  --radius: 0.5rem;
  
  /* Kult Brand Colors */
  --primary-lilac: hsl(267, 83%, 85%);
  --deep-lilac: hsl(266, 84%, 76%);
  --soft-black: hsl(4, 3%, 20%);
  --coral-sorbet: hsl(16, 71%, 82%);
  --mint: hsl(161, 51%, 79%);
  
  /* Theme Customization Variables */
  --theme-bg-start: #B199E8;
  --theme-bg-end: #8B6FD1;
  --theme-bg-direction: 135deg;
  --theme-btn-inactive: rgb(192, 161, 240);
  --theme-btn-active: rgb(170, 138, 219);
  --theme-btn-disabled: #E5E7EB;
  --theme-btn-text: #FFFFFF;
  --theme-primary-text: #1F2937;
  --theme-secondary-text: #6B7280;
  --theme-heading: #111827;
  --theme-accent: #8B5CF6;
  --theme-input-border: #D1D5DB;
  --theme-input-focus: #D4B9FC;
  --theme-input-bg: #FFFFFF;
  --theme-brand-primary: #D4B9FC;
  --theme-brand-secondary: #AD8FF7;
  --theme-success: #10B981;
  --theme-error: #EF4444;
  --theme-warning: #F59E0B;
  --theme-info: #3B82F6;
  
  /* Preview Mode Variables */
  --preview-bg-start: var(--theme-bg-start);
  --preview-bg-end: var(--theme-bg-end);
  --preview-bg-direction: var(--theme-bg-direction);
  --preview-btn-inactive: var(--theme-btn-inactive);
  --preview-btn-active: var(--theme-btn-active);
  --preview-btn-disabled: var(--theme-btn-disabled);
  --preview-btn-text: var(--theme-btn-text);
  --preview-primary-text: var(--theme-primary-text);
  --preview-secondary-text: var(--theme-secondary-text);
  --preview-heading: var(--theme-heading);
  --preview-accent: var(--theme-accent);
  --preview-input-border: var(--theme-input-border);
  --preview-input-focus: var(--theme-input-focus);
  --preview-input-bg: var(--theme-input-bg);
}

/* Kult Button Styles - No Hover Effects */
.btn-kult-inactive {
  background-color: var(--theme-btn-inactive) !important;
  color: var(--theme-btn-text) !important;
  border: none !important;
  cursor: default !important;
}

.btn-kult-active {
  background-color: var(--theme-btn-active) !important;
  color: var(--theme-btn-text) !important;
  border: none !important;
  cursor: pointer !important;
}

.btn-kult-disabled {
  background-color: var(--theme-btn-disabled) !important;
  color: #9CA3AF !important;
  border: none !important;
  cursor: not-allowed !important;
}

/* Main background gradient */
.main-bg-gradient {
  background: linear-gradient(var(--theme-bg-direction), var(--theme-bg-start) 0%, var(--theme-bg-end) 100%);
}

.dark {
  --background: hsl(240, 10%, 3.9%);
  --foreground: hsl(0, 0%, 98%);
  --muted: hsl(240, 3.7%, 15.9%);
  --muted-foreground: hsl(240, 5%, 64.9%);
  --popover: hsl(240, 10%, 3.9%);
  --popover-foreground: hsl(0, 0%, 98%);
  --card: hsl(240, 10%, 3.9%);
  --card-foreground: hsl(0, 0%, 98%);
  --border: hsl(240, 3.7%, 15.9%);
  --input: hsl(240, 3.7%, 15.9%);
  --primary: hsl(267, 83%, 85%);
  --primary-foreground: hsl(0, 0%, 100%);
  --secondary: hsl(240, 3.7%, 15.9%);
  --secondary-foreground: hsl(0, 0%, 98%);
  --accent: hsl(240, 3.7%, 15.9%);
  --accent-foreground: hsl(0, 0%, 98%);
  --destructive: hsl(0, 62.8%, 30.6%);
  --destructive-foreground: hsl(0, 0%, 98%);
  --ring: hsl(240, 4.9%, 83.9%);
  --radius: 0.5rem;
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
    font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  }

  .font-butler {
    font-family: 'Playfair Display', serif;
    font-weight: 600;
  }

  .font-averta {
    font-family: 'Poppins', sans-serif;
  }
}

@layer utilities {
  .gradient-bg {
    background: linear-gradient(135deg, #D4B9FC 0%, #AD8FF7 100%);
  }

  .glass-effect {
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.9);
  }

  .step-inactive {
    @apply bg-gray-300 text-gray-500;
  }

  .step-active {
    background: linear-gradient(135deg, #D4B9FC, #AD8FF7);
    @apply text-white;
  }

  .step-completed {
    @apply bg-green-500 text-white;
  }

  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  .animate-slide-up {
    animation: slideUp 0.5s ease-out;
  }

  /* Theme customization utility classes */
  .theme-bg-gradient {
    background: var(--custom-bg-gradient);
  }
  
  .theme-primary {
    color: var(--custom-primary);
  }
  
  .theme-bg-primary {
    background-color: var(--custom-primary);
  }
  
  .theme-secondary {
    color: var(--custom-secondary);
  }
  
  .theme-bg-secondary {
    background-color: var(--custom-secondary);
  }
  
  .theme-button {
    background-color: var(--custom-button);
    color: white;
    transition: background-color 0.2s ease;
  }
  
  .theme-button:hover {
    background-color: var(--custom-button-hover);
  }
  
  .theme-text {
    color: var(--custom-text);
  }
  
  .theme-heading {
    color: var(--custom-heading);
  }
  
  .theme-accent {
    color: var(--custom-accent);
  }
  
  .theme-bg-accent {
    background-color: var(--custom-accent);
  }
  
  .theme-border-primary {
    border-color: var(--custom-primary);
  }

  .animate-fade-in {
    animation: fadeIn 0.6s ease-out;
  }

  .animate-scale-in {
    animation: scaleIn 0.3s ease-out;
  }
}

@keyframes float {
  0%, 100% { 
    transform: translateY(0px); 
  }
  50% { 
    transform: translateY(-10px); 
  }
}

@keyframes slideUp {
  from { 
    transform: translateY(30px); 
    opacity: 0; 
  }
  to { 
    transform: translateY(0); 
    opacity: 1; 
  }
}

@keyframes fadeIn {
  from { 
    opacity: 0; 
  }
  to { 
    opacity: 1; 
  }
}

@keyframes scaleIn {
  from { 
    transform: scale(0.9); 
    opacity: 0; 
  }
  to { 
    transform: scale(1); 
    opacity: 1; 
  }
}
