import React, { useEffect } from "react";
import { Switch, Route } from "wouter";
import { queryClient } from "./lib/queryClient";
import { QueryClientProvider } from "@tanstack/react-query";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import { loadThemeFromSettings } from "@/lib/theme";
import { userTracker } from './utils/user-tracking';
import Home from "@/pages/home";
import AdminLogin from "@/pages/admin/login";
import AdminDashboard from "@/pages/admin/dashboard";
import SupportLogin from "@/pages/support/login";
import SupportDashboard from "@/pages/support/dashboard";
import BookingReschedule from "@/pages/booking-reschedule";
import BookingCancel from "@/pages/booking-cancel";
import NotFound from "@/pages/not-found";

function Router() {
  return (
    <Switch>
      <Route path="/" component={Home} />
      <Route path="/admin" component={AdminLogin} />
      <Route path="/admin/login" component={AdminLogin} />
      <Route path="/admin/dashboard" component={AdminDashboard} />
      <Route path="/support" component={SupportLogin} />
      <Route path="/support/dashboard" component={SupportDashboard} />
      <Route path="/booking/reschedule/:id" component={BookingReschedule} />
      <Route path="/booking/cancel/:id" component={BookingCancel} />
      <Route component={NotFound} />
    </Switch>
  );
}

function App() {
  useEffect(() => {
    // Load theme settings on app startup
    loadThemeFromSettings();
    
    // Initialize user tracking and sync with backend
    const initializeUserTracking = async () => {
      try {
        const trackingData = userTracker.initializeTracking();
        
        // Send user tracking data to backend
        await fetch('/api/tracking/user', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(trackingData),
        });

        // Send session tracking data to backend
        await fetch('/api/tracking/session', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            userId: trackingData.userId,
            sessionId: trackingData.sessionId,
            deviceFingerprint: trackingData.deviceFingerprint,
            utmSource: trackingData.utmSource,
            utmMedium: trackingData.utmMedium,
            utmCampaign: trackingData.utmCampaign,
            utmTerm: trackingData.utmTerm,
            utmContent: trackingData.utmContent,
            referrer: trackingData.referrer,
          }),
        });

        console.log('🔄 Long-term user tracking initialized:', {
          userId: trackingData.userId,
          sessionId: trackingData.sessionId,
          visitCount: trackingData.visitCount,
          isReturningUser: userTracker.isReturningUser()
        });
      } catch (error) {
        console.warn('Failed to sync user tracking data:', error);
      }
    };

    initializeUserTracking();
  }, []);

  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <Toaster />
        <Router />
      </TooltipProvider>
    </QueryClientProvider>
  );
}

export default App;
