import { useEffect, useRef, useCallback } from 'react';
import { useLocation } from 'wouter';
import { apiRequest } from '@/lib/queryClient';
import { getCurrentUTMParameters, parseMetaCampaignData, trackUTMAttribution } from '@/lib/utm-tracking';
import { userTracker } from '@/utils/user-tracking';

// Use consistent session ID from sessionStorage, with fallback creation
const getSessionId = () => {
  // First try to get existing session ID from sessionStorage
  let sessionId = sessionStorage.getItem('analytics_session_id');
  
  // If not found, try user tracking system
  if (!sessionId) {
    const trackingData = userTracker.getTrackingData();
    sessionId = trackingData?.sessionId;
  }
  
  // If still not found, create new one and store it
  if (!sessionId) {
    sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    sessionStorage.setItem('analytics_session_id', sessionId);
  }
  
  return sessionId;
};

interface EventData {
  eventType: string;
  eventData?: any;
  step?: string;
}

export const useAnalytics = () => {
  const [location] = useLocation();
  const prevLocationRef = useRef<string>(location);
  const sessionStartTime = useRef<number>(Date.now());
  const completedStepsRef = useRef<string[]>([]);
  const eventCountRef = useRef<number>(0);
  const trackedPagesRef = useRef<Set<string>>(new Set());
  const lastEventTimeRef = useRef<number>(0);

  // Minimum time between events (500ms)
  const EVENT_THROTTLE_MS = 500;

  // Track ALL users - no restrictions (removed admin page exclusion)
  const shouldTrack = () => {
    const path = location;
    const isApiRoute = path.startsWith('/api');
    
    // Only exclude API routes, track everything else including admin pages
    return !isApiRoute;
  };

  // Track page views when routes change (throttled and deduplicated)
  useEffect(() => {
    if (!shouldTrack()) return; // Skip admin/non-user pages
    
    const now = Date.now();
    if (location !== prevLocationRef.current && 
        !trackedPagesRef.current.has(location) &&
        now - lastEventTimeRef.current > EVENT_THROTTLE_MS) {
      trackPageView(location);
      prevLocationRef.current = location;
      trackedPagesRef.current.add(location);
      lastEventTimeRef.current = now;
    }
  }, [location]);

  // Track user session on mount (initialize tracking for ALL users)
  useEffect(() => {
    if (!shouldTrack()) return; // Only skip API routes
    
    const sessionId = getSessionId();
    const hasInitialized = sessionStorage.getItem('analytics_initialized');
    
    // Always store session ID in sessionStorage for API requests
    sessionStorage.setItem('analytics_session_id', sessionId);
    
    if (!hasInitialized) {
      // Initialize session for ALL users - both direct visitors and campaign traffic
      updateSession('landing', []);
      sessionStorage.setItem('analytics_initialized', 'true');
      
      // Track initial page view for all users
      if (!trackedPagesRef.current.has(location)) {
        trackPageView(location);
        trackedPagesRef.current.add(location);
      }
    }
    
    // Clean up on unmount
    return () => {
      if (shouldTrack()) {
        updateSession(getCurrentStep(), completedStepsRef.current, 'session_ended');
      }
    };
  }, []);

  const getCurrentStep = useCallback(() => {
    if (location === '/') return 'landing';
    if (location.includes('reschedule')) return 'reschedule';
    if (location.includes('cancel')) return 'cancel';
    return 'other';
  }, [location]);

  const trackEvent = useCallback(async (eventData: EventData) => {
    try {
      // Track ALL users - only skip API routes
      if (!shouldTrack()) return;
      
      // Throttle events
      const now = Date.now();
      if (now - lastEventTimeRef.current < EVENT_THROTTLE_MS) {
        return;
      }
      lastEventTimeRef.current = now;
      
      const sessionId = getSessionId();
      eventCountRef.current += 1;
      
      // Get UTM parameters and campaign data for attribution
      const utmParams = getCurrentUTMParameters();
      const campaignData = parseMetaCampaignData(utmParams);
      
      await apiRequest('POST', '/api/marketing/event', {
        sessionId,
        eventType: eventData.eventType,
        eventData: {
          ...eventData.eventData,
          // Include UTM attribution data
          utm_source: campaignData.source,
          utm_medium: campaignData.medium,
          utm_campaign: campaignData.campaign,
          utm_term: utmParams.utm_term,
          utm_content: utmParams.utm_content,
          fbclid: utmParams.fbclid,
          gclid: utmParams.gclid,
          attribution_type: campaignData.isQRCode ? 'qr_code' : 
                           campaignData.isMetaAd ? 'meta_ad' :
                           campaignData.isGoogleAd ? 'google_ad' : 'direct',
          adset_name: campaignData.adsetName,
          ad_name: campaignData.adName,
          location: campaignData.location,
          audience: campaignData.audience,
          creative_type: campaignData.creativeType,
          promotion_type: campaignData.promotionType,
        },
        userAgent: navigator.userAgent,
        ipAddress: null, // Will be set by server
        referrer: document.referrer || null
      });

      // Update session with new step if provided
      if (eventData.step) {
        const newCompletedSteps = [...completedStepsRef.current];
        if (!newCompletedSteps.includes(eventData.step)) {
          newCompletedSteps.push(eventData.step);
          completedStepsRef.current = newCompletedSteps;
        }
        updateSession(eventData.step, newCompletedSteps);
      }
    } catch (error) {
      console.error('Failed to track event:', error);
    }
  }, []);

  const trackPageView = useCallback(async (path: string) => {
    await trackEvent({
      eventType: 'page_view',
      eventData: { path },
      step: getCurrentStep()
    });
  }, [trackEvent, getCurrentStep]);

  const trackFormStart = useCallback(async (formType: string) => {
    await trackEvent({
      eventType: 'form_start',
      eventData: { formType },
      step: formType === 'postal_code' ? 'postal_code' : 
           formType === 'date_time' ? 'date_time' :
           formType === 'contact_info' ? 'contact_info' : 
           formType === 'otp_verification' ? 'otp_verification' : 'form_start'
    });
  }, [trackEvent]);

  const trackFormSubmit = useCallback(async (formType: string, success: boolean = true) => {
    await trackEvent({
      eventType: 'form_submit',
      eventData: { formType, success },
      step: formType === 'postal_code' ? 'postal_code' : 
           formType === 'contact_info' ? 'contact_info' : 
           formType === 'date_time' ? 'date_time' : 'form_submit'
    });
  }, [trackEvent]);

  const trackBookingComplete = useCallback(async (bookingId: number) => {
    await trackEvent({
      eventType: 'booking_complete',
      eventData: { bookingId },
      step: 'completed'
    });
    
    // Update session with final outcome including confirmation step
    const allSteps = [...completedStepsRef.current, 'confirmation', 'completed'];
    updateSession('completed', allSteps, 'completed', bookingId);
  }, [trackEvent]);

  const trackError = useCallback(async (errorType: string, errorMessage: string) => {
    await trackEvent({
      eventType: 'error',
      eventData: { errorType, errorMessage }
    });
  }, [trackEvent]);

  const trackCustomEvent = useCallback(async (eventType: string, eventData?: any) => {
    await trackEvent({
      eventType,
      eventData,
      step: getCurrentStep()
    });
  }, [trackEvent, getCurrentStep]);

  const trackTimeSlotSelection = useCallback(async (date: string, timeSlot: string) => {
    await trackEvent({
      eventType: 'time_slot_selected',
      eventData: { date, timeSlot },
      step: 'date_time'
    });
  }, [trackEvent]);

  const trackOTPRequest = useCallback(async (phone: string) => {
    await trackEvent({
      eventType: 'otp_requested',
      eventData: { phone: phone.slice(-4) }, // Only track last 4 digits for privacy
      step: 'otp_verification'
    });
  }, [trackEvent]);

  const trackOTPVerification = useCallback(async (success: boolean) => {
    await trackEvent({
      eventType: 'otp_verified',
      eventData: { success },
      step: success ? 'otp_verified' : 'otp_failed'
    });
  }, [trackEvent]);

  const updateSession = useCallback(async (
    currentStep: string, 
    completedSteps: string[], 
    finalOutcome?: string,
    bookingId?: number
  ) => {
    try {
      const sessionId = getSessionId();
      const timeSpent = Math.floor((Date.now() - sessionStartTime.current) / 1000);
      
      await apiRequest('POST', '/api/marketing/session', {
        sessionId,
        currentStep,
        completedSteps,
        finalOutcome,
        bookingId,
        totalEvents: eventCountRef.current,
        timeSpent
      });
    } catch (error) {
      console.error('Failed to update session:', error);
    }
  }, []);

  return {
    trackEvent,
    trackPageView,
    trackFormStart,
    trackFormSubmit,
    trackBookingComplete,
    trackError,
    trackTimeSlotSelection,
    trackOTPRequest,
    trackOTPVerification,
    trackCustomEvent,
    updateSession
  };
};

export default useAnalytics;