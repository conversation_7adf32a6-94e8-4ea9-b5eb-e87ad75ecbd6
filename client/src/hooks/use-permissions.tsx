import { useQuery } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";

export interface UserPermissions {
  overview?: string;
  bookings?: string;
  'executive-analytics'?: string;
  'user-behaviour'?: string;
  calendar?: string;
  reps?: string;
  'postal-codes'?: string;
  'postal-analytics'?: string;
  users?: string;
  permissions?: string;
  sessions?: string;
  customization?: string;
  settings?: string;
  password?: string;
}

export interface CurrentUser {
  id: number;
  username: string;
  name: string;
  email: string;
  role: string;
  isActive: boolean;
  dashboardPermissions: string | null;
}

export const usePermissions = () => {
  // Get current user info from session
  const { data: currentUser, isLoading } = useQuery<CurrentUser>({
    queryKey: ['/api/admin/current-user'],
    queryFn: () => apiRequest('GET', '/api/admin/current-user'),
  });

  // Parse permissions from JSON string
  const permissions: UserPermissions = (() => {
    if (!currentUser?.dashboardPermissions) {
      // Default admin access for users without explicit permissions
      return {
        overview: 'edit',
        bookings: 'edit',
        'executive-analytics': 'edit',
        'user-behaviour': 'edit',
        calendar: 'edit',
        reps: 'edit',
        'postal-codes': 'edit',
        'postal-analytics': 'edit',
        users: 'edit',
        permissions: 'edit',
        sessions: 'edit',
        customization: 'edit',
        settings: 'edit',
        password: 'edit'
      };
    }
    
    try {
      return JSON.parse(currentUser.dashboardPermissions);
    } catch {
      return {};
    }
  })();

  // Permission checking functions
  const hasAccess = (section: keyof UserPermissions): boolean => {
    const access = permissions[section];
    return access === 'view' || access === 'edit';
  };

  const canEdit = (section: keyof UserPermissions): boolean => {
    return permissions[section] === 'edit';
  };

  const canView = (section: keyof UserPermissions): boolean => {
    const access = permissions[section];
    return access === 'view' || access === 'edit';
  };

  const getAccessLevel = (section: keyof UserPermissions): string => {
    return permissions[section] || 'none';
  };

  // Check if user has any admin permissions
  const hasAnyAccess = (): boolean => {
    return Object.values(permissions).some(access => access === 'view' || access === 'edit');
  };

  // Check if user is super admin (can access user management)
  const isSuperAdmin = (): boolean => {
    return canEdit('users') || currentUser?.role === 'admin';
  };

  return {
    currentUser,
    permissions,
    isLoading,
    hasAccess,
    canEdit,
    canView,
    getAccessLevel,
    hasAnyAccess,
    isSuperAdmin
  };
};