import { useState, useEffect } from "react";
import { useLocation } from "wouter";
import { authService, type AuthUser } from "@/lib/auth";
import { useToast } from "./use-toast";

export function useAuth() {
  const [user, setUser] = useState<AuthUser | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [, setLocation] = useLocation();
  const { toast } = useToast();

  useEffect(() => {
    checkAuthStatus();
  }, []);

  const checkAuthStatus = async () => {
    try {
      const currentUser = await authService.getCurrentUser();
      setUser(currentUser);
    } catch (error) {
      setUser(null);
    } finally {
      setIsLoading(false);
    }
  };

  const login = async (username: string, password: string, userType: 'admin' | 'support' = 'admin') => {
    try {
      const user = await authService.login(username, password, userType);
      setUser(user);
      const dashboardRoute = userType === 'support' ? '/support/dashboard' : '/admin/dashboard';
      setLocation(dashboardRoute);
      toast({
        title: "Login successful",
        description: `Welcome to the ${userType} dashboard`,
      });
      return true;
    } catch (error) {
      toast({
        title: "Login failed",
        description: "Invalid username or password",
        variant: "destructive",
      });
      return false;
    }
  };

  const logout = async () => {
    try {
      const userType = user?.role as 'admin' | 'support' || 'admin';
      await authService.logout(userType);
      setUser(null);
      const loginRoute = userType === 'support' ? '/support' : '/admin';
      setLocation(loginRoute);
      toast({
        title: "Logged out",
        description: "You have been successfully logged out",
      });
    } catch (error) {
      console.error("Logout error:", error);
    }
  };

  return {
    user,
    isLoading,
    isAuthenticated: !!user,
    login,
    logout,
  };
}
