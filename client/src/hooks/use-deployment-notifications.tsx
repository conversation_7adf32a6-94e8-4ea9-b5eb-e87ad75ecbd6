import { useEffect, useRef } from 'react';
import { useToast } from '@/hooks/use-toast';

interface DeploymentNotification {
  type: 'upgrade' | 'maintenance' | 'connected';
  message: string;
  action: 'refresh' | 'none';
  timestamp: string;
}

export const useDeploymentNotifications = () => {
  const { toast } = useToast();
  const eventSourceRef = useRef<EventSource | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    const connectEventSource = () => {
      // Clean up any existing connection
      if (eventSourceRef.current) {
        eventSourceRef.current.close();
      }

      try {
        const eventSource = new EventSource('/api/deployment/events', {
          withCredentials: true
        });

        eventSource.onopen = () => {
          console.log('🔗 Deployment notification service connected');
          // Clear any reconnection timeout
          if (reconnectTimeoutRef.current) {
            clearTimeout(reconnectTimeoutRef.current);
            reconnectTimeoutRef.current = null;
          }
        };

        eventSource.onmessage = (event) => {
          try {
            const notification: DeploymentNotification = JSON.parse(event.data);
            
            switch (notification.type) {
              case 'connected':
                // Silent connection confirmation
                break;
                
              case 'upgrade':
                toast({
                  title: "🚀 System Upgrade",
                  description: notification.message,
                  duration: 8000,
                });
                
                // Auto-refresh after a brief delay to allow users to read the message
                if (notification.action === 'refresh') {
                  setTimeout(() => {
                    window.location.reload();
                  }, 3000);
                }
                break;
                
              case 'maintenance':
                toast({
                  title: "🔧 Maintenance Notice",
                  description: notification.message,
                  duration: 10000,
                });
                break;
                
              default:
                toast({
                  title: "📢 System Notification",
                  description: notification.message,
                  duration: 5000,
                });
            }
          } catch (error) {
            console.warn('Failed to parse deployment notification:', error);
          }
        };

        eventSource.onerror = (error) => {
          console.warn('Deployment notification connection error:', error);
          eventSource.close();
          
          // Attempt to reconnect after 5 seconds
          if (!reconnectTimeoutRef.current) {
            reconnectTimeoutRef.current = setTimeout(() => {
              console.log('🔄 Attempting to reconnect deployment notifications...');
              connectEventSource();
            }, 5000);
          }
        };

        eventSourceRef.current = eventSource;
        
      } catch (error) {
        console.warn('Failed to create deployment notification connection:', error);
      }
    };

    // Only connect for admin/support users in production or when needed
    connectEventSource();

    // Cleanup on unmount
    return () => {
      if (eventSourceRef.current) {
        eventSourceRef.current.close();
      }
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
    };
  }, [toast]);

  return { connected: !!eventSourceRef.current };
};