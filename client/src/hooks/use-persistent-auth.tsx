import { useEffect, useState } from 'react';
import { useToast } from '@/hooks/use-toast';

interface SessionStatus {
  authenticated: boolean;
  role?: string;
  username?: string;
  loginTime?: string;
  lastActivity?: string;
  persistent?: boolean;
}

export const usePersistentAuth = () => {
  const [authStatus, setAuthStatus] = useState<SessionStatus>({ authenticated: false });
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();

  const checkSessionStatus = async () => {
    try {
      const response = await fetch('/api/auth/session-status', {
        credentials: 'include' // Include cookies for session
      });
      
      if (response.ok) {
        const status: SessionStatus = await response.json();
        setAuthStatus(status);
        
        // If session is restored after deployment, show a success message
        if (status.authenticated && status.persistent) {
          const lastActivity = status.lastActivity ? new Date(status.lastActivity) : null;
          const timeSinceActivity = lastActivity ? Date.now() - lastActivity.getTime() : 0;
          
          // If more than 5 minutes since last activity, this might be a session restoration
          if (timeSinceActivity > 5 * 60 * 1000) {
            toast({
              title: "Welcome back!",
              description: "Your session has been automatically restored.",
              duration: 3000,
            });
          }
        }
      } else {
        setAuthStatus({ authenticated: false });
      }
    } catch (error) {
      console.warn('Session status check failed:', error);
      setAuthStatus({ authenticated: false });
    } finally {
      setIsLoading(false);
    }
  };

  // Auto-refresh session every 5 minutes (like Google)
  useEffect(() => {
    // Initial check
    checkSessionStatus();

    // Set up periodic session refresh
    const interval = setInterval(() => {
      if (authStatus.authenticated) {
        checkSessionStatus();
      }
    }, 5 * 60 * 1000); // 5 minutes

    return () => clearInterval(interval);
  }, [authStatus.authenticated]);

  // Check session status on window focus (user returns to tab)
  useEffect(() => {
    const handleFocus = () => {
      if (authStatus.authenticated) {
        checkSessionStatus();
      }
    };

    window.addEventListener('focus', handleFocus);
    return () => window.removeEventListener('focus', handleFocus);
  }, [authStatus.authenticated]);

  // Check session status on page visibility change
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden && authStatus.authenticated) {
        checkSessionStatus();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
  }, [authStatus.authenticated]);

  return {
    isAuthenticated: authStatus.authenticated,
    isLoading,
    sessionInfo: authStatus,
    refreshSession: checkSessionStatus
  };
};