import { useEffect, useState } from 'react';
import { userTracker } from '@/utils/user-tracking';

export interface UserTrackingData {
  userId: string;
  sessionId: string;
  firstVisit: string;
  lastVisit: string;
  visitCount: number;
  utmSource?: string;
  utmMedium?: string;
  utmCampaign?: string;
  utmTerm?: string;
  utmContent?: string;
  referrer?: string;
  deviceFingerprint: string;
}

export function useUserTracking() {
  const [trackingData, setTrackingData] = useState<UserTrackingData | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const initTracking = async () => {
      try {
        const data = userTracker.getTrackingData();
        if (data) {
          setTrackingData(data);
        }
      } catch (error) {
        console.warn('Failed to get tracking data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    initTracking();
  }, []);

  const updateActivity = async () => {
    try {
      userTracker.updateActivity();
      const updatedData = userTracker.getTrackingData();
      if (updatedData) {
        setTrackingData(updatedData);
        
        // Sync with backend
        await fetch('/api/tracking/user', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(updatedData),
        });
      }
    } catch (error) {
      console.warn('Failed to update activity:', error);
    }
  };

  const trackConversion = async (bookingId: number, value?: number) => {
    try {
      const data = userTracker.getTrackingData();
      if (data) {
        // Update session with conversion and UTM data
        await fetch('/api/tracking/session', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            userId: data.userId,
            sessionId: data.sessionId,
            deviceFingerprint: data.deviceFingerprint,
            // Include UTM parameters for proper attribution
            utmSource: data.utmSource,
            utmMedium: data.utmMedium,
            utmCampaign: data.utmCampaign,
            utmTerm: data.utmTerm,
            utmContent: data.utmContent,
            referrer: data.referrer,
            converted: true,
            funnelProgress: 8, // Completed all steps
            completedSteps: [
              'landing',
              'postal_code',
              'date_time',
              'customer_info',
              'otp_verification',
              'booking_confirmation'
            ],
            endedAt: new Date().toISOString(),
          }),
        });

        // Update user tracking with conversion
        await fetch('/api/tracking/user', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            ...data,
            hasBooked: true,
            totalBookings: (data as any).totalBookings + 1,
            totalValue: ((data as any).totalValue || 0) + (value || 0),
          }),
        });
      }
    } catch (error) {
      console.warn('Failed to track conversion:', error);
    }
  };

  const trackFunnelStep = async (step: string, stepNumber: number) => {
    try {
      const data = userTracker.getTrackingData();
      if (data) {
        await fetch('/api/tracking/session', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            userId: data.userId,
            sessionId: data.sessionId,
            deviceFingerprint: data.deviceFingerprint,
            // Include UTM parameters for proper attribution
            utmSource: data.utmSource,
            utmMedium: data.utmMedium,
            utmCampaign: data.utmCampaign,
            utmTerm: data.utmTerm,
            utmContent: data.utmContent,
            referrer: data.referrer,
            funnelProgress: stepNumber,
            bounced: false, // User progressed, so not bounced
          }),
        });
      }
    } catch (error) {
      console.warn('Failed to track funnel step:', error);
    }
  };

  return {
    trackingData,
    isLoading,
    isReturningUser: userTracker.isReturningUser(),
    getUserJourneyDuration: userTracker.getUserJourneyDuration(),
    updateActivity,
    trackConversion,
    trackFunnelStep,
    clearTracking: userTracker.clearTracking,
  };
}