import { useEffect } from 'react';

declare global {
  interface Window {
    fbq: any;
  }
}

interface FacebookEventParams {
  value?: number;
  currency?: string;
  content_type?: string;
  content_ids?: string[];
  content_name?: string;
  content_category?: string;
  postal_code?: string;
  appointment_date?: string;
  appointment_time?: string;
  sales_rep?: string;
  phone_number?: string;
  city?: string;
  state?: string;
  registration_method?: string;
  cancellation_reason?: string;
  [key: string]: any;
}

export const useFacebookTracking = () => {
  useEffect(() => {
    // Don't initialize Facebook Pixel on admin or non-user pages
    const path = window.location.pathname;
    const isAdminPage = path.startsWith('/admin') || path.startsWith('/dashboard');
    const isTestPage = path.includes('trigger-facebook') || path.includes('test-');
    const isApiRoute = path.startsWith('/api');
    const isLoginPage = path.includes('login') || path.includes('auth');
    
    if (isAdminPage || isTestPage || isApiRoute || isLoginPage) {
      console.log(`🚫 Facebook Pixel initialization blocked on ${path} - admin/non-user page`);
      return;
    }
    
    // Initialize Facebook Pixel if not already loaded
    if (typeof window !== 'undefined' && !window.fbq) {
      // Facebook Pixel initialization script
      (window as any).fbq = function() {
        const fbq = (window as any).fbq;
        fbq.callMethod ? fbq.callMethod.apply(fbq, arguments) : fbq.queue.push(arguments);
      };
      
      if (!(window as any)._fbq) (window as any)._fbq = (window as any).fbq;
      (window as any).fbq.push = (window as any).fbq;
      (window as any).fbq.loaded = !0;
      (window as any).fbq.version = '2.0';
      (window as any).fbq.queue = [];
      
      const script = document.createElement('script');
      script.async = true;
      script.src = 'https://connect.facebook.net/en_US/fbevents.js';
      const firstScript = document.getElementsByTagName('script')[0];
      if (firstScript && firstScript.parentNode) {
        firstScript.parentNode.insertBefore(script, firstScript);
      }
      
      // Initialize with your existing pixel ID
      const pixelId = '2478007082599449';
      window.fbq('init', pixelId);
      window.fbq('track', 'PageView');
    }
  }, []);

  const trackEvent = (eventName: string, params: FacebookEventParams = {}) => {
    if (!shouldTrack()) return;
    
    try {
      if (typeof window !== 'undefined' && window.fbq) {
        // Add default parameters
        const eventParams = {
          content_category: 'Beauty',
          ...params
        };
        
        console.log(`🎯 Facebook Event: ${eventName}`, eventParams);
        window.fbq('track', eventName, eventParams);
        
        // Also send custom event to our backend for server-side tracking
        fetch('/api/marketing/facebook-event', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            event_name: eventName,
            event_time: Math.floor(Date.now() / 1000),
            user_data: {
              ph: params.phone_number ? hashPhone(params.phone_number) : undefined,
              ct: params.city,
              st: params.state,
              country: 'IN'
            },
            custom_data: eventParams,
            event_source_url: window.location.href,
            action_source: 'website'
          })
        }).catch(console.error);
      } else {
        console.warn(`❌ Facebook Pixel not loaded for event: ${eventName}`);
      }
    } catch (error) {
      console.error(`❌ Facebook Event Error (${eventName}):`, error);
    }
  };

  // Helper function to check if current page should be tracked
  const shouldTrack = (): boolean => {
    if (typeof window === 'undefined') return false;
    
    const path = window.location.pathname;
    const isAdminPage = path.startsWith('/admin') || path.startsWith('/dashboard');
    const isTestPage = path.includes('trigger-facebook') || path.includes('test-');
    const isApiRoute = path.startsWith('/api');
    const isLoginPage = path.includes('login') || path.includes('auth');
    
    // Only track main booking flow and homepage
    const isTrackablePage = path === '/' || 
                           path.startsWith('/book') || 
                           path.startsWith('/booking/') ||
                           path === '/booking';
    
    return isTrackablePage && !isAdminPage && !isTestPage && !isApiRoute && !isLoginPage;
  };

  const trackCustomEvent = (customName: string, params: FacebookEventParams = {}) => {
    // Don't track admin pages or other non-user pages
    if (!shouldTrack()) {
      console.log(`🚫 Facebook tracking blocked on ${window.location.pathname} - admin/non-user page`);
      return;
    }
    
    try {
      if (typeof window !== 'undefined' && window.fbq) {
        const eventParams = {
          content_category: 'Beauty',
          ...params
        };
        
        console.log(`🎯 Facebook Custom Event: ${customName}`, eventParams);
        // Track as custom event with descriptive name
        window.fbq('trackCustom', customName, eventParams);
        
        // Also send to backend
        fetch('/api/marketing/facebook-event', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            event_name: customName,
            event_time: Math.floor(Date.now() / 1000),
            user_data: {
              ph: params.phone_number ? hashPhone(params.phone_number) : undefined,
              ct: params.city,
              st: params.state,
              country: 'IN'
            },
            custom_data: eventParams,
            event_source_url: window.location.href,
            action_source: 'website'
          })
        }).catch(console.error);
      } else {
        console.warn(`❌ Facebook Pixel not loaded for custom event: ${customName}`);
      }
    } catch (error) {
      console.error(`❌ Facebook Custom Event Error (${customName}):`, error);
    }
  };

  const trackPageView = (pageName?: string) => {
    trackCustomEvent('PageView', {
      content_name: pageName || document.title
    });
  };

  const trackCheckAvailability = (postalCode: string, city: string, state: string) => {
    // Track both custom and standard events
    trackCustomEvent('CheckAvailabilityPressed', {
      content_name: 'Check Availability Button Pressed',
      postal_code: postalCode,
      city,
      state
    });
    
    trackEvent('ViewContent', {
      content_name: 'Postal Code Availability Check',
      content_type: 'service',
      content_ids: [postalCode],
      postal_code: postalCode,
      city,
      state
    });
  };

  const trackDateSelection = (date: string, postalCode: string) => {
    // Track custom event
    trackCustomEvent('DateSelected', {
      content_name: 'Date Selected',
      appointment_date: date,
      postal_code: postalCode
    });
    
    // Track InitiateCheckout to signal start of booking process
    trackEvent('InitiateCheckout', {
      content_name: 'Perfume Trial Booking Started',
      content_type: 'appointment',
      appointment_date: date,
      postal_code: postalCode,
      value: 0,
      currency: 'INR'
    });
  };

  const trackTimeSelection = (time: string, date: string, postalCode: string) => {
    // Track custom event
    trackCustomEvent('TimeSlotSelected', {
      content_name: 'Time Slot Selected',
      appointment_time: time,
      appointment_date: date,
      postal_code: postalCode
    });
    
    // Track standard Facebook events
    trackEvent('AddPaymentInfo', {
      content_name: 'Time Slot Selected for Appointment',
      content_type: 'appointment',
      appointment_time: time,
      appointment_date: date,
      postal_code: postalCode
    });
  };

  const trackOTPRequested = (phoneNumber: string) => {
    // Track custom event
    trackCustomEvent('OTPRequested', {
      content_name: 'OTP Requested',
      phone_number: phoneNumber
    });
    
    // Track standard Facebook event
    trackEvent('CompleteRegistration', {
      content_name: 'Customer Registration Started',
      registration_method: 'whatsapp_otp',
      phone_number: phoneNumber
    });
  };

  const trackBookMyTrialPressed = (phoneNumber: string) => {
    // Track custom event
    trackCustomEvent('BookMyTrialPressed', {
      content_name: 'Book My Trial Button Clicked',
      phone_number: phoneNumber
    });
    
    // Track standard Facebook event
    trackEvent('BeginCheckout', {
      content_name: 'Begin Booking Checkout',
      content_type: 'appointment',
      phone_number: phoneNumber
    });
  };

  const trackBookingConfirmed = (bookingData: {
    bookingId: string;
    phoneNumber: string;
    postalCode: string;
    city: string;
    state: string;
    appointmentDate: string;
    appointmentTime: string;
    salesRep: string;
  }) => {
    // Track custom event
    trackCustomEvent('BookingConfirmed', {
      content_name: 'Booking Confirmed',
      booking_id: bookingData.bookingId,
      postal_code: bookingData.postalCode,
      city: bookingData.city,
      state: bookingData.state,
      appointment_date: bookingData.appointmentDate,
      appointment_time: bookingData.appointmentTime,
      sales_rep: bookingData.salesRep,
      phone_number: bookingData.phoneNumber
    });
    
    // Track standard Facebook Purchase event
    trackEvent('Purchase', {
      content_name: 'Perfume Trial Appointment Booked',
      content_type: 'appointment',
      content_ids: [bookingData.bookingId],
      value: 0, // Free trial, but tracks conversion
      currency: 'INR',
      booking_id: bookingData.bookingId,
      postal_code: bookingData.postalCode,
      city: bookingData.city,
      state: bookingData.state,
      appointment_date: bookingData.appointmentDate,
      appointment_time: bookingData.appointmentTime,
      sales_rep: bookingData.salesRep,
      phone_number: bookingData.phoneNumber
    });
  };

  const trackBookingCancelled = (bookingId: string, reason?: string) => {
    trackCustomEvent('BookingCancelled', {
      content_name: 'Booking Cancelled',
      booking_id: bookingId,
      cancellation_reason: reason || 'user_requested'
    });
  };

  const trackBookingRescheduled = (bookingId: string, newDate: string, newTime: string) => {
    trackCustomEvent('BookingRescheduled', {
      content_name: 'Booking Rescheduled',
      booking_id: bookingId,
      appointment_date: newDate,
      appointment_time: newTime
    });
  };

  return {
    trackPageView,
    trackCheckAvailability,
    trackDateSelection,
    trackTimeSelection,
    trackOTPRequested,
    trackBookMyTrialPressed,
    trackBookingConfirmed,
    trackBookingCancelled,
    trackBookingRescheduled,
    trackEvent
  };
};

// Helper function to hash phone numbers for privacy
function hashPhone(phone: string): string {
  // Simple hash function - in production, use proper hashing
  let hash = 0;
  for (let i = 0; i < phone.length; i++) {
    const char = phone.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  return hash.toString();
}

export default useFacebookTracking;