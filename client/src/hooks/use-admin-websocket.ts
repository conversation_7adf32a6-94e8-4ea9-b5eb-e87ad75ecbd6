import { useEffect, useRef, useState } from 'react';
import { useToast } from './use-toast';
import { queryClient } from '@/lib/queryClient';

interface AdminUpdate {
  type: 'admin_update';
  event: 'booking_created' | 'booking_updated' | 'booking_deleted' | 'user_created' | 'user_updated' | 'settings_updated';
  data: any;
  changes?: any;
  timestamp: string;
}

export function useAdminWebSocket() {
  const { toast } = useToast();
  const wsRef = useRef<WebSocket | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout>();

  const connect = () => {
    try {
      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
      const wsUrl = `${protocol}//${window.location.host}/admin-ws`;
      
      wsRef.current = new WebSocket(wsUrl);
      
      wsRef.current.onopen = () => {
        console.log('Admin WebSocket connected');
        setIsConnected(true);
        
        // Send ping to keep connection alive
        if (wsRef.current) {
          wsRef.current.send(JSON.stringify({ type: 'ping' }));
        }
      };
      
      wsRef.current.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data);
          
          if (message.type === 'pong') {
            return; // Keep-alive response
          }
          
          if (message.type === 'admin_update') {
            handleAdminUpdate(message as AdminUpdate);
          }
        } catch (error) {
          console.error('Failed to parse WebSocket message:', error);
        }
      };
      
      wsRef.current.onclose = () => {
        console.log('Admin WebSocket disconnected');
        setIsConnected(false);
        
        // Attempt to reconnect after 3 seconds
        reconnectTimeoutRef.current = setTimeout(() => {
          connect();
        }, 3000);
      };
      
      wsRef.current.onerror = (error) => {
        console.error('WebSocket error:', error);
      };
      
    } catch (error) {
      console.error('Failed to create WebSocket connection:', error);
    }
  };

  const handleAdminUpdate = (update: AdminUpdate) => {
    console.log('Received admin update:', update);
    
    switch (update.event) {
      case 'booking_created':
        queryClient.invalidateQueries({ queryKey: ['/api/admin/bookings'] });
        queryClient.invalidateQueries({ queryKey: ['/api/admin/bookings/range'] });
        toast({
          title: 'New Booking',
          description: `New booking created for ${update.data.name}`,
        });
        break;
        
      case 'booking_updated':
        queryClient.invalidateQueries({ queryKey: ['/api/admin/bookings'] });
        queryClient.invalidateQueries({ queryKey: ['/api/admin/bookings/range'] });
        queryClient.invalidateQueries({ queryKey: ['/api/bookings', update.data.id] });
        toast({
          title: 'Booking Updated',
          description: `Booking for ${update.data.name} was updated`,
        });
        break;
        
      case 'booking_deleted':
        queryClient.invalidateQueries({ queryKey: ['/api/admin/bookings'] });
        queryClient.invalidateQueries({ queryKey: ['/api/admin/bookings/range'] });
        toast({
          title: 'Booking Deleted',
          description: 'A booking has been deleted',
        });
        break;
        
      case 'user_created':
        queryClient.invalidateQueries({ queryKey: ['/api/admin/users'] });
        toast({
          title: 'New User',
          description: `New user ${update.data.name} created`,
        });
        break;
        
      case 'user_updated':
        queryClient.invalidateQueries({ queryKey: ['/api/admin/users'] });
        toast({
          title: 'User Updated',
          description: `User ${update.data.name} was updated`,
        });
        break;
        
      case 'settings_updated':
        queryClient.invalidateQueries({ queryKey: ['/api/settings'] });
        toast({
          title: 'Settings Updated',
          description: 'System settings have been updated',
        });
        break;
    }
  };

  useEffect(() => {
    connect();
    
    // Send periodic pings to keep connection alive
    const pingInterval = setInterval(() => {
      if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
        wsRef.current.send(JSON.stringify({ type: 'ping' }));
      }
    }, 30000); // Ping every 30 seconds
    
    return () => {
      clearInterval(pingInterval);
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
      if (wsRef.current) {
        wsRef.current.close();
      }
    };
  }, []);

  return {
    isConnected,
    disconnect: () => {
      if (wsRef.current) {
        wsRef.current.close();
      }
    }
  };
}