import { use<PERSON><PERSON><PERSON>, useLocation } from "wouter";
import { useQuery, useMutation } from "@tanstack/react-query";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { Calendar, Clock, MapPin, User, X, Loader2, CheckCircle } from "lucide-react";
import { format } from "date-fns";
import { useEffect, useState } from "react";
import { loadThemeFromSettings } from "@/lib/theme";
import { useAnalytics } from "@/hooks/use-analytics";
import { useFacebookTracking } from "@/hooks/use-facebook-tracking";
import { userTracker } from "@/utils/user-tracking";

// Helper function to convert any time format to 12-hour format
function formatTimeTo12Hour(timeString: string): string {
  if (!timeString) return timeString;
  
  // If already in 12-hour format (contains AM/PM), return as is
  if (timeString.includes('AM') || timeString.includes('PM')) {
    return timeString;
  }
  
  // If in 24-hour format (e.g., "15:30"), convert to 12-hour
  const timeMatch = timeString.match(/^(\d{1,2}):(\d{2})$/);
  if (timeMatch) {
    let hour = parseInt(timeMatch[1]);
    const minute = timeMatch[2];
    
    const period = hour >= 12 ? 'PM' : 'AM';
    if (hour === 0) {
      hour = 12;
    } else if (hour > 12) {
      hour = hour - 12;
    }
    
    return `${hour}:${minute} ${period}`;
  }
  
  return timeString;
}

export default function BookingCancel() {
  const params = useParams();
  const [, setLocation] = useLocation();
  const { toast } = useToast();
  const bookingId = params.id;
  const [showSuccess, setShowSuccess] = useState(false);

  // Load theme settings and initialize tracking when component mounts
  useEffect(() => {
    loadThemeFromSettings();
    
    // Initialize user tracking to capture UTM parameters from WhatsApp links
    userTracker.initializeTracking();
  }, []);
  
  // Initialize analytics and Facebook tracking
  useAnalytics();
  useFacebookTracking();

  const { data: booking, isLoading, error } = useQuery({
    queryKey: ['/api/bookings/token', bookingId],
    queryFn: async () => {
      const response = await fetch(`/api/bookings/token/${bookingId}`);
      if (!response.ok) {
        throw new Error('Booking not found');
      }
      return response.json();
    },
    enabled: !!bookingId,
  });

  const cancelMutation = useMutation({
    mutationFn: async () => {
      const response = await apiRequest("PUT", `/api/bookings/${booking.id}`, {
        status: "cancelled"
      });
      return response.json();
    },
    onSuccess: () => {
      setShowSuccess(true);
    },
    onError: () => {
      toast({
        title: "Cancellation failed",
        description: "Unable to cancel booking. Please contact support.",
        variant: "destructive",
      });
    },
  });

  const formatDate = (dateString: string) => {
    return format(new Date(dateString), "EEEE, MMMM d, yyyy");
  };

  // Show success state after cancellation
  if (showSuccess) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-soft-white via-mint to-soft-white flex items-center justify-center p-4">
        <Card className="w-full max-w-md text-center">
          <CardContent className="p-6">
            <CheckCircle className="h-12 w-12 mx-auto mb-4 text-green-500" />
            <h2 className="text-xl font-semibold mb-2">Appointment Cancelled</h2>
            <p className="text-gray-600 mb-6">
              Your perfume trial appointment has been successfully cancelled.
            </p>
            
            {booking && (
              <div className="bg-gray-50 rounded-lg p-4 mb-6 space-y-3">
                <div className="flex items-center">
                  <User className="h-4 w-4 mr-3 text-gray-500" />
                  <span className="text-sm">
                    <span className="font-medium">Customer:</span> {booking.name}
                  </span>
                </div>
                <div className="flex items-center">
                  <Calendar className="h-4 w-4 mr-3 text-gray-500" />
                  <span className="text-sm">
                    <span className="font-medium">Date:</span> {formatDate(booking.date)}
                  </span>
                </div>
                <div className="flex items-center">
                  <Clock className="h-4 w-4 mr-3 text-gray-500" />
                  <span className="text-sm">
                    <span className="font-medium">Time:</span> {formatTimeTo12Hour(booking.timeSlot)}
                  </span>
                </div>
              </div>
            )}

            <Button 
              onClick={() => setLocation('/')}
              className="w-full btn-kult-active"
            >
              Book New Appointment
            </Button>
            
            <div className="mt-6 text-center">
              <p className="text-sm text-gray-500">
                Need help? Contact us at{" "}
                <a href="mailto:<EMAIL>" className="text-blue-600 hover:underline">
                  <EMAIL>
                </a>{" "}
                or{" "}
                <a href="tel:+919987991000" className="text-blue-600 hover:underline">
                  (+91) 9987991000
                </a>
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!bookingId) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-soft-white via-mint to-soft-white flex items-center justify-center p-4">
        <Card className="w-full max-w-md text-center">
          <CardContent className="p-6">
            <h2 className="text-xl font-semibold mb-2">Invalid Link</h2>
            <p className="text-gray-600">This cancellation link is not valid.</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-soft-white via-mint to-soft-white flex items-center justify-center p-4">
        <Card className="w-full max-w-md text-center">
          <CardContent className="p-6">
            <Loader2 className="h-8 w-8 mx-auto mb-4 animate-spin" />
            <p className="text-gray-600">Loading booking details...</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error || !booking) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-soft-white via-mint to-soft-white flex items-center justify-center p-4">
        <Card className="w-full max-w-md text-center">
          <CardContent className="p-6">
            <h2 className="text-xl font-semibold mb-2">Booking Not Found</h2>
            <p className="text-gray-600">
              This booking could not be found or may have already been cancelled.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (booking.status === 'cancelled') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-soft-white via-mint to-soft-white flex items-center justify-center p-4">
        <Card className="w-full max-w-md text-center">
          <CardContent className="p-6">
            <X className="h-12 w-12 mx-auto mb-4 text-red-500" />
            <h2 className="text-xl font-semibold mb-2">Already Cancelled</h2>
            <p className="text-gray-600">
              This booking has already been cancelled.
            </p>
            <Button 
              onClick={() => setLocation('/')}
              className="mt-4 btn-kult-active"
            >
              Book New Appointment
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-soft-white via-mint to-soft-white flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardContent className="p-6">
          <div className="text-center mb-6">
            <X className="h-12 w-12 mx-auto mb-4 text-red-500" />
            <h2 className="text-xl font-semibold mb-2">Cancel Appointment</h2>
            <p className="text-gray-600">
              Are you sure you want to cancel this appointment?
            </p>
          </div>
          
          {/* Booking Details */}
          <div className="space-y-3 mb-6 p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center">
              <User className="h-4 w-4 mr-3 text-gray-500" />
              <span className="text-sm">
                <span className="font-medium">Service:</span> Perfume Trial at Home
              </span>
            </div>
            <div className="flex items-center">
              <Calendar className="h-4 w-4 mr-3 text-gray-500" />
              <span className="text-sm">
                <span className="font-medium">Date:</span> {formatDate(booking.date)}
              </span>
            </div>
            <div className="flex items-center">
              <Clock className="h-4 w-4 mr-3 text-gray-500" />
              <span className="text-sm">
                <span className="font-medium">Time:</span> {booking.timeSlot}
              </span>
            </div>
            <div className="flex items-center">
              <MapPin className="h-4 w-4 mr-3 text-gray-500" />
              <span className="text-sm">
                <span className="font-medium">Address:</span> {booking.address}
              </span>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="space-y-3">
            <Button
              onClick={() => cancelMutation.mutate()}
              disabled={cancelMutation.isPending}
              className={`w-full py-3 rounded-2xl font-semibold ${
                cancelMutation.isPending ? 'btn-kult-disabled' : 'btn-kult-active'
              }`}
            >
              {cancelMutation.isPending ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Cancelling...
                </>
              ) : (
                <>
                  <X className="h-4 w-4 mr-2" />
                  Yes, Cancel Booking
                </>
              )}
            </Button>
            <Button
              variant="outline"
              onClick={() => setLocation('/')}
              className="w-full py-3 rounded-2xl font-semibold btn-kult-inactive"
            >
              Keep Appointment
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}