import { useEffect } from "react";
import { useAuth } from "@/hooks/use-auth";
import { useQuery } from "@tanstack/react-query";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import BookingsTable from "@/components/admin/bookings-table";
import { Download, TrendingUp, LogOut } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

export default function MarketingDashboard() {
  // Block search engines from indexing marketing pages
  useEffect(() => {
    const metaTag = document.getElementById('seo-meta') as HTMLMetaElement;
    if (metaTag) {
      metaTag.content = 'noindex, nofollow, noarchive, nosnippet';
    }
    document.title = 'Marketing Dashboard';
    
    return () => {
      // Restore default SEO settings when leaving marketing
      if (metaTag) {
        metaTag.content = 'index, follow';
      }
      document.title = 'Kult - Perfume Trial at Home Experience';
    };
  }, []);

  const { user, logout } = useAuth();
  const { toast } = useToast();

  const { data: bookings = [] } = useQuery<any[]>({
    queryKey: ["/api/marketing/bookings"],
  });

  const handleLogout = () => {
    logout();
  };

  const handleExportCSV = async () => {
    try {
      const response = await fetch('/api/marketing/bookings/export', {
        credentials: 'include',
      });
      
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = 'bookings-export.csv';
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        toast({ title: "CSV exported successfully" });
      } else {
        toast({ title: "Failed to export CSV", variant: "destructive" });
      }
    } catch (error) {
      toast({ title: "Export error", variant: "destructive" });
    }
  };

  if (!user || user.role !== 'marketing') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 flex items-center justify-center">
        <Card className="w-full max-w-md text-center">
          <CardContent className="pt-6">
            <TrendingUp className="mx-auto h-12 w-12 text-green-600 mb-4" />
            <h2 className="text-xl font-semibold mb-2">Access Denied</h2>
            <p className="text-gray-600 mb-4">
              You need marketing team access to view this dashboard.
            </p>
            <Button onClick={() => window.location.href = '/marketing'}>
              Go to Marketing Login
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                <TrendingUp className="w-4 h-4 text-green-600" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Marketing Dashboard</h1>
                <p className="text-sm text-gray-600">Welcome, {user.name}</p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <Button
                onClick={handleExportCSV}
                className="flex items-center space-x-2"
              >
                <Download className="w-4 h-4" />
                <span>Export CSV</span>
              </Button>
              <Button
                variant="outline"
                onClick={handleLogout}
                className="flex items-center space-x-2"
              >
                <LogOut className="w-4 h-4" />
                <span>Sign Out</span>
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Bookings</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{bookings.length}</div>
              <p className="text-xs text-muted-foreground">
                All time bookings
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Confirmed Bookings</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {bookings.filter(b => b.status === 'confirmed').length}
              </div>
              <p className="text-xs text-muted-foreground">
                Active bookings
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Conversion Rate</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {bookings.length > 0 
                  ? Math.round((bookings.filter(b => b.status === 'confirmed').length / bookings.length) * 100)
                  : 0}%
              </div>
              <p className="text-xs text-muted-foreground">
                Booking confirmation rate
              </p>
            </CardContent>
          </Card>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Booking Analytics</CardTitle>
            <p className="text-sm text-gray-600">
              View all customer bookings and download data for analysis
            </p>
          </CardHeader>
          <CardContent>
            <BookingsTable />
          </CardContent>
        </Card>
      </div>
    </div>
  );
}