import { useEffect, useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { useAuth } from "@/hooks/use-auth";
import { useAdminWebSocket } from "@/hooks/use-admin-websocket";
import { useDeploymentNotifications } from "@/hooks/use-deployment-notifications";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import BookingsTable from "@/components/admin/bookings-table";
import CalendarView from "@/components/admin/calendar-view";
import RepsManagement from "@/components/admin/reps-management";
import SettingsManagement from "@/components/admin/settings-management";
import PostalCodesManagement from "@/components/admin/postal-codes-management";
import FrontendCustomization from "@/components/admin/frontend-customization";
import ThemeDashboard from "@/components/admin/theme-dashboard";
import ChangePassword from "@/components/admin/change-password";

import OverviewDashboard from "@/components/admin/simple-overview-dashboard";
import UserPermissions from "@/components/admin/user-permissions";
import UsersManagement from "@/components/admin/users-management";
import UserPermissionsManager from "@/components/admin/user-permissions-manager";
import { AdminSessions } from "@/components/admin/admin-sessions";
import EnhancedUTMDashboard from "@/components/admin/enhanced-utm-dashboard";
import UTMCampaignFunnels from "@/components/admin/utm-campaign-funnels";
import UTMRawData from "@/components/admin/utm-raw-data";
import UserBehaviourDashboard from "@/components/admin/user-behaviour-dashboard";
import ExecutiveAnalyticsDashboard from "@/components/admin/executive-analytics-dashboard";
import PostalCodeAnalyticsDashboard from "@/components/admin/postal-code-analytics-dashboard";




import { 
  Calendar, 
  Users, 
  MapPin, 
  Settings, 
  LogOut, 
  UserCog, 
  Palette, 
  TrendingUp,
  BarChart3,
  Clock,
  Shield,
  Home,
  Wifi,
  WifiOff,
  Menu,
  X,
  Activity
} from "lucide-react";

const navigationItems = [
  {
    id: 'overview',
    label: 'Overview',
    icon: Home,
    description: 'Dashboard overview and stats'
  },
  {
    id: 'bookings',
    label: 'Bookings',
    icon: Calendar,
    description: 'Manage customer appointments'
  },

  {
    id: 'executive-analytics',
    label: 'Performance Overview',
    icon: TrendingUp,
    description: 'Executive dashboard with KPIs and conversion metrics'
  },


  {
    id: 'user-behaviour',
    label: 'User Behaviour',
    icon: Activity,
    description: 'Interactive user behaviour analytics and session insights'
  },

  {
    id: 'calendar',
    label: 'Calendar',
    icon: Clock,
    description: 'Calendar view of appointments'
  },
  {
    id: 'reps',
    label: 'Sales Reps',
    icon: Users,
    description: 'Manage sales representatives'
  },
  {
    id: 'postal-codes',
    label: 'Service Areas',
    icon: MapPin,
    description: 'Manage postal codes and areas'
  },
  {
    id: 'postal-analytics',
    label: 'Postal Analytics',
    icon: BarChart3,
    description: 'Track postal code entries and service area expansion'
  },
  {
    id: 'users',
    label: 'Users',
    icon: UserCog,
    description: 'User management and creation'
  },
  {
    id: 'permissions',
    label: 'Permissions',
    icon: Shield,
    description: 'Manage user access levels'
  },
  {
    id: 'sessions',
    label: 'Sessions',
    icon: Activity,
    description: 'Admin login activity tracking'
  },
  {
    id: 'customization',
    label: 'Theme',
    icon: Palette,
    description: 'Customize appearance and branding'
  },
  {
    id: 'settings',
    label: 'Settings',
    icon: Settings,
    description: 'System configuration'
  },
  {
    id: 'password',
    label: 'Security',
    icon: Shield,
    description: 'Change password and security'
  }
];

export default function AdminDashboard() {
  const [activeTab, setActiveTab] = useState('overview');
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const { logout, user } = useAuth();
  const { isConnected } = useAdminWebSocket();
  const { isUpgrading } = useDeploymentNotifications();

  // Fetch user details with permissions
  const { data: currentUser } = useQuery<any>({
    queryKey: ["/api/admin/current-user"],
    enabled: !!user,
  });

  // Parse user permissions
  const getUserPermissions = () => {
    try {
      if (!currentUser) return {};
      return currentUser.dashboardPermissions 
        ? JSON.parse(currentUser.dashboardPermissions) 
        : {};
    } catch {
      return {};
    }
  };

  // Check if user has access to a dashboard
  const hasAccess = (dashboardId: string) => {
    // Super admin role always has access
    if (user?.role === 'admin') return true;
    
    const permissions = getUserPermissions();
    const access = permissions[dashboardId];
    
    // If no permissions are set, deny access (except for super admins)
    if (!access || access === 'none') {
      return false;
    }
    
    return access === 'view' || access === 'edit';
  };

  // Check if user has edit access
  const hasEditAccess = (dashboardId: string) => {
    // Super admin role always has edit access
    if (user?.role === 'admin') return true;
    
    const permissions = getUserPermissions();
    return permissions[dashboardId] === 'edit';
  };

  // Check if user has view-only access
  const hasViewOnlyAccess = (dashboardId: string) => {
    if (user?.role === 'admin') return false; // Admin always has edit access
    
    const permissions = getUserPermissions();
    return permissions[dashboardId] === 'view';
  };

  // Render access restricted message
  const renderAccessRestricted = (section: string) => (
    <Card>
      <CardHeader>
        <CardTitle>Access Restricted</CardTitle>
      </CardHeader>
      <CardContent>
        <p className="text-gray-500">
          You don't have permission to access {section}. Contact your administrator to request access.
        </p>
      </CardContent>
    </Card>
  );

  // Block search engines from indexing admin pages
  useEffect(() => {
    const metaTag = document.getElementById('seo-meta') as HTMLMetaElement;
    if (metaTag) {
      metaTag.content = 'noindex, nofollow, noarchive, nosnippet';
    }
    document.title = 'Admin Dashboard';
    
    return () => {
      // Restore default SEO settings when leaving admin
      if (metaTag) {
        metaTag.content = 'index, follow';
      }
      document.title = 'Kult - Perfume Trial at Home Experience';
    };
  }, []);

  const handleLogout = () => {
    logout();
  };

  const renderContent = () => {
    switch (activeTab) {
      case 'overview':
        return <OverviewDashboard setActiveTab={setActiveTab} />;
      case 'bookings':
        if (!hasAccess('bookings')) return renderAccessRestricted('bookings management');
        return (
          <Card>
            <CardHeader>
              <CardTitle>All Bookings</CardTitle>
            </CardHeader>
            <CardContent>
              <BookingsTable readOnly={hasViewOnlyAccess('bookings')} />
            </CardContent>
          </Card>
        );

      case 'executive-analytics':
        if (!hasAccess('executive-analytics')) return renderAccessRestricted('executive analytics dashboard');
        return <ExecutiveAnalyticsDashboard />;
        


        
      case 'user-behaviour':
        if (!hasAccess('user-behaviour')) return renderAccessRestricted('user behaviour analytics');
        return <UserBehaviourDashboard />;
        

      case 'reps':
        if (!hasAccess('reps')) return renderAccessRestricted('sales representatives management');
        return (
          <Card>
            <CardHeader>
              <CardTitle>Sales Representatives</CardTitle>
            </CardHeader>
            <CardContent>
              <RepsManagement readOnly={hasViewOnlyAccess('reps')} />
            </CardContent>
          </Card>
        );
        
      case 'postal-codes':
        if (!hasAccess('postal-codes')) return renderAccessRestricted('service areas management');
        return <PostalCodesManagement readOnly={hasViewOnlyAccess('postal-codes')} />;
        
      case 'postal-analytics':
        if (!hasAccess('postal-analytics')) return renderAccessRestricted('postal analytics');
        return <PostalCodeAnalyticsDashboard />;
        
      case 'calendar':
        if (!hasAccess('calendar')) return renderAccessRestricted('calendar view');
        return (
          <Card>
            <CardHeader>
              <CardTitle>Calendar View</CardTitle>
            </CardHeader>
            <CardContent>
              <CalendarView readOnly={hasViewOnlyAccess('calendar')} />
            </CardContent>
          </Card>
        );
      case 'users':
        if (!hasAccess('users')) return renderAccessRestricted('user management');
        return <UsersManagement readOnly={hasViewOnlyAccess('users')} />;
        
      case 'permissions':
        if (!hasAccess('permissions')) return renderAccessRestricted('permission management');
        return <UserPermissionsManager readOnly={hasViewOnlyAccess('permissions')} />;
        
      case 'sessions':
        if (!hasAccess('sessions')) return renderAccessRestricted('session tracking');
        return <AdminSessions />;
      case 'customization':
        if (!hasAccess('customization')) return renderAccessRestricted('theme customization');
        return <ThemeDashboard readOnly={hasViewOnlyAccess('customization')} />;
        
      case 'settings':
        if (!hasAccess('settings')) return renderAccessRestricted('system settings');
        return (
          <Card>
            <CardHeader>
              <CardTitle>System Settings</CardTitle>
            </CardHeader>
            <CardContent>
              <SettingsManagement readOnly={hasViewOnlyAccess('settings')} />
            </CardContent>
          </Card>
        );
      case 'password':
        return <ChangePassword />;
      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Mobile overlay */}
      {sidebarOpen && (
        <div 
          className="fixed inset-0 z-40 bg-black bg-opacity-50 md:hidden" 
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Mobile header */}
      <div className="md:hidden bg-white border-b border-gray-200 sticky top-0 z-30">
        <div className="flex items-center justify-between px-4 py-3">
          <div className="flex items-center gap-3">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setSidebarOpen(true)}
              className="p-2"
            >
              <Menu className="h-5 w-5" />
            </Button>
            <div className="flex items-center">
              <span className="text-lg font-butler font-bold text-purple-600">&</span>
              <span className="ml-2 text-base font-butler font-bold text-gray-900">kult admin</span>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <div className={cn(
              "flex items-center gap-1 text-xs font-medium px-2 py-1 rounded-full",
              isConnected 
                ? "bg-green-100 text-green-700" 
                : "bg-red-100 text-red-700"
            )}>
              {isConnected ? <Wifi className="h-3 w-3" /> : <WifiOff className="h-3 w-3" />}
              <span className="hidden sm:inline">{isConnected ? "Live" : "Offline"}</span>
            </div>
            <Button 
              variant="outline" 
              size="sm"
              onClick={handleLogout}
              className="flex items-center gap-1"
            >
              <LogOut className="h-4 w-4" />
              <span className="hidden sm:inline">Logout</span>
            </Button>
          </div>
        </div>
      </div>

      <div className="flex">
        {/* Desktop Sidebar */}
        <div className="hidden md:flex md:flex-shrink-0">
          <div className="flex flex-col w-64 h-screen">
            <div className="flex flex-col h-full bg-white border-r border-gray-200">
              {/* Header - Fixed at top */}
              <div className="flex items-center flex-shrink-0 px-4 py-5 border-b border-gray-100">
                <span className="text-2xl font-butler font-bold text-purple-600">&</span>
                <span className="ml-2 text-xl font-butler font-bold text-gray-900">kult admin</span>
              </div>
              
              {/* Navigation - Scrollable middle section */}
              <div className="flex-1 overflow-y-auto px-2 py-4">
                <nav className="space-y-2">
                  {navigationItems.filter(item => hasAccess(item.id)).map((item) => {
                    const Icon = item.icon;
                    return (
                      <button
                        key={item.id}
                        onClick={() => setActiveTab(item.id)}
                        className={cn(
                          'group flex items-center px-3 py-2 text-sm font-medium rounded-lg w-full text-left transition-colors duration-200',
                          activeTab === item.id
                            ? 'bg-purple-50 text-purple-700 border-r-2 border-purple-700'
                            : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
                        )}
                      >
                        <Icon
                          className={cn(
                            'mr-3 h-5 w-5 flex-shrink-0',
                            activeTab === item.id ? 'text-purple-500' : 'text-gray-400 group-hover:text-gray-500'
                          )}
                        />
                        {item.label}
                      </button>
                    );
                  })}
                </nav>
              </div>
              
              {/* Footer - Fixed at bottom */}
              <div className="flex-shrink-0 px-2 pb-4 pt-4 border-t border-gray-100 space-y-3">
                <div className={cn(
                  "flex items-center justify-center gap-2 text-xs font-medium px-3 py-2 rounded-lg",
                  isConnected 
                    ? "bg-green-100 text-green-700" 
                    : "bg-red-100 text-red-700"
                )}>
                  {isConnected ? <Wifi className="h-3 w-3" /> : <WifiOff className="h-3 w-3" />}
                  {isConnected ? "Live Updates" : "Offline"}
                </div>
                <Button 
                  variant="outline" 
                  onClick={handleLogout}
                  className="w-full flex items-center gap-2"
                >
                  <LogOut className="h-4 w-4" />
                  Logout
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Mobile Sidebar */}
        <div className={cn(
          "fixed inset-y-0 left-0 z-50 w-64 bg-white border-r border-gray-200 transform transition-transform duration-300 ease-in-out md:hidden",
          sidebarOpen ? "translate-x-0" : "-translate-x-full"
        )}>
          <div className="flex flex-col h-full">
            <div className="flex items-center justify-between px-4 py-3 border-b border-gray-200">
              <div className="flex items-center">
                <span className="text-xl font-butler font-bold text-purple-600">&</span>
                <span className="ml-2 text-lg font-butler font-bold text-gray-900">kult admin</span>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setSidebarOpen(false)}
                className="p-2"
              >
                <X className="h-5 w-5" />
              </Button>
            </div>
            <div className="flex-grow overflow-y-auto">
              <nav className="px-2 py-4 space-y-2">
                {navigationItems.filter(item => hasAccess(item.id)).map((item) => {
                  const Icon = item.icon;
                  return (
                    <button
                      key={item.id}
                      onClick={() => {
                        setActiveTab(item.id);
                        setSidebarOpen(false);
                      }}
                      className={cn(
                        'group flex items-center px-3 py-2 text-sm font-medium rounded-lg w-full text-left transition-colors duration-200',
                        activeTab === item.id
                          ? 'bg-purple-50 text-purple-700 border-r-2 border-purple-700'
                          : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
                      )}
                    >
                      <Icon
                        className={cn(
                          'mr-3 h-5 w-5 flex-shrink-0',
                          activeTab === item.id ? 'text-purple-500' : 'text-gray-400 group-hover:text-gray-500'
                        )}
                      />
                      {item.label}
                    </button>
                  );
                })}
              </nav>
            </div>
            <div className="flex-shrink-0 px-2 pb-4 space-y-3 border-t border-gray-200 pt-4">
              <div className={cn(
                "flex items-center justify-center gap-2 text-xs font-medium px-3 py-2 rounded-lg",
                isConnected 
                  ? "bg-green-100 text-green-700" 
                  : "bg-red-100 text-red-700"
              )}>
                {isConnected ? <Wifi className="h-3 w-3" /> : <WifiOff className="h-3 w-3" />}
                {isConnected ? "Live Updates" : "Offline"}
              </div>
              <Button 
                variant="outline" 
                onClick={handleLogout}
                className="w-full flex items-center gap-2"
              >
                <LogOut className="h-4 w-4" />
                Logout
              </Button>
            </div>
          </div>
        </div>

        {/* Main content */}
        <div className="flex-1 flex flex-col min-w-0">
          <main className="flex-1 overflow-y-auto">
            <div className="py-4 md:py-6">
              <div className="max-w-7xl mx-auto px-2 sm:px-4 md:px-6 lg:px-8">
                {renderContent()}
              </div>
            </div>
          </main>
        </div>
      </div>
    </div>
  );
}
