import { useParams, useLocation } from "wouter";
import { useQuery } from "@tanstack/react-query";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import RescheduleStep from "@/components/booking/reschedule-step";
import { Loader2, CheckCircle, Calendar, Clock, MapPin, User } from "lucide-react";
import { format } from "date-fns";
import { useState, useEffect } from "react";
import { loadThemeFromSettings } from "@/lib/theme";
import { useAnalytics } from "@/hooks/use-analytics";
import { useFacebookTracking } from "@/hooks/use-facebook-tracking";
import { userTracker } from "@/utils/user-tracking";

export default function BookingReschedule() {
  const params = useParams();
  const [, setLocation] = useLocation();
  const bookingId = params.id;
  const [updatedBooking, setUpdatedBooking] = useState<any>(null);
  const [showConfirmation, setShowConfirmation] = useState(false);

  // Load theme settings and initialize tracking when component mounts
  useEffect(() => {
    loadThemeFromSettings();
    
    // Initialize user tracking to capture UTM parameters from WhatsApp links
    userTracker.initializeTracking();
  }, []);
  
  // Initialize analytics and Facebook tracking
  useAnalytics();
  useFacebookTracking();

  const { data: booking, isLoading, error } = useQuery({
    queryKey: ['/api/bookings/token', bookingId],
    queryFn: async () => {
      const response = await fetch(`/api/bookings/token/${bookingId}`);
      if (!response.ok) {
        throw new Error('Booking not found');
      }
      return response.json();
    },
    enabled: !!bookingId,
  });

  const handleRescheduleSuccess = (newBooking: any) => {
    setUpdatedBooking(newBooking);
    setShowConfirmation(true);
  };

  const handleCancel = () => {
    setLocation('/');
  };

  const formatDate = (dateString: string) => {
    return format(new Date(dateString), "EEEE, MMMM d, yyyy");
  };

  if (!bookingId) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-soft-white via-mint to-soft-white flex items-center justify-center p-4">
        <Card className="w-full max-w-md text-center">
          <CardContent className="p-6">
            <h2 className="text-xl font-semibold mb-2">Invalid Link</h2>
            <p className="text-gray-600">This reschedule link is not valid.</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-soft-white via-mint to-soft-white flex items-center justify-center p-4">
        <Card className="w-full max-w-md text-center">
          <CardContent className="p-6">
            <Loader2 className="h-8 w-8 mx-auto mb-4 animate-spin" />
            <p className="text-gray-600">Loading booking details...</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error || !booking) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-soft-white via-mint to-soft-white flex items-center justify-center p-4">
        <Card className="w-full max-w-md text-center">
          <CardContent className="p-6">
            <h2 className="text-xl font-semibold mb-2">Booking Not Found</h2>
            <p className="text-gray-600">
              This booking could not be found or may have been cancelled.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (showConfirmation && updatedBooking) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-soft-white via-mint to-soft-white flex items-center justify-center p-4">
        <Card className="w-full max-w-md text-center">
          <CardContent className="p-6">
            <CheckCircle className="h-12 w-12 mx-auto mb-4 text-green-500" />
            <h2 className="text-xl font-semibold mb-2">Appointment Rescheduled</h2>
            <p className="text-gray-600 mb-6">
              Your perfume trial has been successfully rescheduled.
            </p>
            
            {/* Updated Booking Details */}
            <div className="space-y-3 mb-6 p-4 bg-gray-50 rounded-lg text-left">
              <div className="flex items-center">
                <User className="h-4 w-4 mr-3 text-gray-500" />
                <span className="text-sm">
                  <span className="font-medium">Service:</span> Perfume Trial at Home
                </span>
              </div>
              <div className="flex items-center">
                <Calendar className="h-4 w-4 mr-3 text-gray-500" />
                <span className="text-sm">
                  <span className="font-medium">New Date:</span> {formatDate(updatedBooking.date)}
                </span>
              </div>
              <div className="flex items-center">
                <Clock className="h-4 w-4 mr-3 text-gray-500" />
                <span className="text-sm">
                  <span className="font-medium">New Time:</span> {updatedBooking.timeSlot}
                </span>
              </div>
              <div className="flex items-center">
                <MapPin className="h-4 w-4 mr-3 text-gray-500" />
                <span className="text-sm">
                  <span className="font-medium">Address:</span> {updatedBooking.address}
                </span>
              </div>
            </div>

            <div className="space-y-3">
              <Button 
                onClick={() => setLocation('/')}
                className="w-full btn-kult-active"
              >
                Book Another Appointment
              </Button>
              <Button 
                variant="outline"
                onClick={() => setShowConfirmation(false)}
                className="w-full btn-kult-inactive"
              >
                Reschedule Again
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-soft-white via-mint to-soft-white p-4">
      <div className="max-w-2xl mx-auto">
        <div className="text-center mb-6">
          <h1 className="text-3xl font-butler font-bold text-soft-black mb-2">
            Reschedule Your Appointment
          </h1>
          <p className="text-gray-600">
            Select a new date and time for your perfume trial
          </p>
        </div>

        <RescheduleStep
          booking={booking}
          onRescheduleSuccess={handleRescheduleSuccess}
          onCancel={handleCancel}
        />
      </div>
    </div>
  );
}