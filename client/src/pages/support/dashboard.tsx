import { useEffect } from "react";
import { useAuth } from "@/hooks/use-auth";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import BookingsTable from "@/components/admin/bookings-table";
import SettingsManagement from "@/components/admin/settings-management";

import { Calendar, Settings, LogOut, HeadphonesIcon } from "lucide-react";

export default function SupportDashboard() {
  // Block search engines from indexing support pages
  useEffect(() => {
    const metaTag = document.getElementById('seo-meta') as HTMLMetaElement;
    if (metaTag) {
      metaTag.content = 'noindex, nofollow, noarchive, nosnippet';
    }
    document.title = 'Support Dashboard';
    
    return () => {
      // Restore default SEO settings when leaving support
      if (metaTag) {
        metaTag.content = 'index, follow';
      }
      document.title = 'Kult - Perfume Trial at Home Experience';
    };
  }, []);

  const { user, logout } = useAuth();

  const handleLogout = () => {
    logout();
  };

  if (!user || !user.isSupport) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-50 to-pink-50 flex items-center justify-center">
        <Card className="w-full max-w-md text-center">
          <CardContent className="pt-6">
            <HeadphonesIcon className="mx-auto h-12 w-12 text-purple-600 mb-4" />
            <h2 className="text-xl font-semibold mb-2">Access Denied</h2>
            <p className="text-gray-600 mb-4">
              You need support team access to view this dashboard.
            </p>
            <Button onClick={() => window.location.href = '/support'}>
              Go to Support Login
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                <HeadphonesIcon className="w-4 h-4 text-purple-600" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Support Dashboard</h1>
                <p className="text-sm text-gray-600">Customer Support Team</p>
              </div>
            </div>
            <Button
              variant="outline"
              onClick={handleLogout}
              className="flex items-center space-x-2"
            >
              <LogOut className="w-4 h-4" />
              <span>Sign Out</span>
            </Button>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Tabs defaultValue="bookings" className="space-y-6">
          <TabsList className="grid w-full grid-cols-2 max-w-md">
            <TabsTrigger value="bookings" className="flex items-center space-x-2">
              <Calendar className="w-4 h-4" />
              <span>Bookings</span>
            </TabsTrigger>
            <TabsTrigger value="settings" className="flex items-center space-x-2">
              <Settings className="w-4 h-4" />
              <span>Notifications</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="bookings" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Customer Bookings</CardTitle>
                <p className="text-sm text-gray-600">
                  View and update customer booking information
                </p>
              </CardHeader>
              <CardContent>
                <BookingsTable />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="settings" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Notification Management</CardTitle>
                <p className="text-sm text-gray-600">
                  Manage email templates and notification timing
                </p>
              </CardHeader>
              <CardContent>
                <SettingsManagement />
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}