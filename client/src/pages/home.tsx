import { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { MultiStepForm } from "@/components/ui/multi-step-form";
import PostalCodeStep from "@/components/booking/postal-code-step";
import DateTimeStep from "@/components/booking/date-time-step";
import CustomerInfoStep from "@/components/booking/customer-info-step";
import ConfirmationStep from "@/components/booking/confirmation-step";
import RescheduleStep from "@/components/booking/reschedule-step";
import Footer from "@/components/ui/footer";
import { useAnalytics } from "@/hooks/use-analytics";

export default function Home() {
  const analytics = useAnalytics();
  const [bookingData, setBookingData] = useState({
    postalCode: "",
    date: "",
    timeSlot: "",
    name: "",
    phone: "",
    email: "",
    address: "",
  });

  const [confirmedBooking, setConfirmedBooking] = useState(null);
  const [rescheduleBookingToken, setRescheduleBookingToken] = useState<string | null>(null);
  const [formKey, setFormKey] = useState(0);

  // Handle URL parameters for cancel/reschedule flows
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const rescheduleToken = urlParams.get('reschedule');
    const postalCode = urlParams.get('postalCode');
    
    if (rescheduleToken) {
      setRescheduleBookingToken(rescheduleToken);
    } else if (postalCode) {
      // Pre-fill postal code for new booking (from cancel flow)
      setBookingData(prev => ({ ...prev, postalCode }));
    }
  }, []);

  // Fetch existing booking for reschedule using secure token
  const { data: existingBooking } = useQuery({
    queryKey: ['/api/bookings/token', rescheduleBookingToken],
    queryFn: async () => {
      if (!rescheduleBookingToken) return null;
      const response = await fetch(`/api/bookings/token/${rescheduleBookingToken}`);
      if (!response.ok) throw new Error('Booking not found');
      return response.json();
    },
    enabled: !!rescheduleBookingToken,
  });

  const handleStartNewBooking = (postalCode: string) => {
    // Reset state for new booking
    setConfirmedBooking(null);
    setRescheduleBookingToken(null);
    setBookingData({
      postalCode,
      date: "",
      timeSlot: "",
      name: "",
      phone: "",
      email: "",
      address: "",
    });
    // Force form reset by incrementing key
    setFormKey(prev => prev + 1);
    // Clear URL parameters
    window.history.replaceState({}, '', '/');
  };

  const handleRescheduleSuccess = (updatedBooking: any) => {
    setConfirmedBooking(updatedBooking);
    setRescheduleBookingToken(null);
    // Clear URL parameters
    window.history.replaceState({}, '', '/');
  };

  // If we're in reschedule mode and have the booking data, show reschedule component
  if (rescheduleBookingToken && existingBooking) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-soft-white via-mint to-soft-white">
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-md mx-auto">
            <RescheduleStep
              booking={existingBooking}
              onRescheduleSuccess={handleRescheduleSuccess}
              onCancel={() => {
                setRescheduleBookingToken(null);
                window.history.replaceState({}, '', '/');
              }}
            />
          </div>
        </div>
      </div>
    );
  }

  const steps = [
    {
      title: "Location",
      component: (
        <PostalCodeStep
          value={bookingData.postalCode}
          onChange={(value) => setBookingData(prev => ({ ...prev, postalCode: value }))}
        />
      ),
    },
    {
      title: "Date & Time",
      component: (
        <DateTimeStep
          value={{ date: bookingData.date, timeSlot: bookingData.timeSlot }}
          onChange={(value) => setBookingData(prev => ({ ...prev, ...value }))}
        />
      ),
    },
    {
      title: "Your Details",
      component: (
        <CustomerInfoStep
          value={{
            name: bookingData.name,
            phone: bookingData.phone,
            address: bookingData.address,
          }}
          onChange={(value) => setBookingData(prev => ({ ...prev, ...value }))}
          onBookingConfirmed={setConfirmedBooking}
          bookingData={bookingData}
        />
      ),
    },
    {
      title: "Confirmation",
      component: (
        <ConfirmationStep
          booking={confirmedBooking}
          bookingData={bookingData}
          onStartNewBooking={handleStartNewBooking}
        />
      ),
    },
  ];

  return (
    <div className="min-h-screen flex flex-col">
      {/* Background Pattern */}
      <div className="fixed inset-0 main-bg-gradient"></div>
      <div 
        className="fixed inset-0 opacity-10" 
        style={{
          backgroundImage: `url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="white"/></svg>')`,
          backgroundSize: '50px 50px'
        }}
      ></div>

      <div className="relative flex-1 flex flex-col">
        <div className="flex-1 flex items-center justify-center p-4">
          <div className="w-full max-w-md">
            {/* Logo Section */}
            <div className="text-center mb-8 animate-fade-in">
              <div className="mb-4">
                <img 
                  src="/attached_assets/KULT_LogoBrandmarks-1_1750232754843.png" 
                  alt="Kult Logo" 
                  className="w-32 h-auto mx-auto object-contain filter brightness-0 invert"
                />
              </div>
              <h1 className="text-2xl font-butler font-bold text-white mb-2">Perfumes - Trial at Home Experience</h1>
              <h2 className="text-xl font-butler font-semibold text-white mb-3">Try Before You Buy</h2>
              <p className="text-white/90 text-sm leading-relaxed px-4">
                Explore from over 150 luxury perfumes in the comfort of your home. We bring the scents, you pick what you love.
              </p>
            </div>

            <MultiStepForm key={formKey} steps={steps} />
            
            {/* Social Experience Section */}
            <div className="text-center mt-4 animate-fade-in">
              <h3 className="text-lg font-butler font-semibold text-white mb-2">
                It's more fun with friends and family.
              </h3>
              <p className="text-white/80 text-sm leading-relaxed px-4">
                Make it a shared experience—invite loved ones and enjoy discovering your favorite perfumes together.
              </p>
            </div>
          </div>
        </div>
        <Footer />
      </div>
    </div>
  );
}
