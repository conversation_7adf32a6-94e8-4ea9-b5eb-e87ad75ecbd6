// Google Analytics 4 Enhanced Tracking with UTM Attribution
// Provides comprehensive event tracking with campaign attribution

declare global {
  interface Window {
    gtag?: (...args: any[]) => void;
    dataLayer?: any[];
  }
}

interface GAEventParams {
  event_category?: string;
  event_label?: string;
  value?: number;
  currency?: string;
  [key: string]: any;
}

// Send event to Google Analytics with UTM attribution
export const trackGAEvent = (eventName: string, parameters: GAEventParams = {}) => {
  if (typeof window === 'undefined' || !window.gtag) return;
  
  // Get current UTM parameters
  const urlParams = new URLSearchParams(window.location.search);
  const sessionUTM = sessionStorage.getItem('utm_parameters');
  
  let utmData: any = {};
  
  // Try to get UTM from URL first, then from session
  if (urlParams.toString()) {
    utmData = {
      utm_source: urlParams.get('utm_source'),
      utm_medium: urlParams.get('utm_medium'),
      utm_campaign: urlParams.get('utm_campaign'),
      utm_term: urlParams.get('utm_term'),
      utm_content: urlParams.get('utm_content'),
      fbclid: urlParams.get('fbclid'),
      gclid: urlParams.get('gclid')
    };
  } else if (sessionUTM) {
    try {
      utmData = JSON.parse(sessionUTM);
    } catch (e) {
      console.warn('Failed to parse stored UTM data');
    }
  }
  
  // Determine attribution type
  const source = utmData.utm_source || 'direct';
  const isQRCode = source?.toLowerCase() === 'qr';
  const isMetaAd = source?.toLowerCase() === 'meta' || !!utmData.fbclid;
  const isGoogleAd = source?.toLowerCase() === 'google' || !!utmData.gclid;
  
  const attributionType = isQRCode ? 'qr_code' : 
                         isMetaAd ? 'meta_ad' :
                         isGoogleAd ? 'google_ad' : 'direct';
  
  // Parse Meta campaign data if applicable
  let adsetName, adName, location, audience;
  if (isMetaAd) {
    const medium = utmData.utm_medium || '';
    const campaign = utmData.utm_campaign || '';
    
    // Parse adset from medium (|Kult|Gurugram|Advantage+|Men&Women|Fragrance|)
    if (medium.includes('|')) {
      const parts = medium.split('|').filter(p => p.trim());
      adsetName = medium;
      location = parts.find(p => 
        p.toLowerCase().includes('gurugram') || 
        p.toLowerCase().includes('delhi') ||
        p.toLowerCase().includes('mumbai')
      );
      audience = parts.find(p => 
        p.toLowerCase().includes('men') || 
        p.toLowerCase().includes('women') ||
        p.toLowerCase().includes('advantage')
      );
    }
    
    // Parse ad from campaign (Video|Kult|Perfume|Discount|Gurugram|Calendar_Old–)
    if (campaign.includes('|')) {
      adName = campaign;
    }
  }
  
  // Enhanced event parameters with UTM attribution
  const enhancedParams = {
    ...parameters,
    // UTM attribution
    utm_source: source,
    utm_medium: utmData.utm_medium || '(none)',
    utm_campaign: utmData.utm_campaign || '(none)',
    utm_term: utmData.utm_term,
    utm_content: utmData.utm_content,
    fbclid: utmData.fbclid,
    gclid: utmData.gclid,
    attribution_type: attributionType,
    // Meta-specific attribution
    adset_name: adsetName,
    ad_name: adName,
    location: location,
    audience: audience,
    // Session data
    session_id: sessionStorage.getItem('analytics_session_id'),
    page_path: window.location.pathname,
    page_title: document.title
  };
  
  // Send to Google Analytics
  window.gtag('event', eventName, enhancedParams);
  
  console.log(`🎯 GA4 Event: ${eventName}`, enhancedParams);
};

// Specific tracking functions for booking flow
export const trackFormStart = (formType: string) => {
  trackGAEvent('form_start', {
    event_category: 'engagement',
    event_label: formType,
    form_type: formType
  });
};

export const trackFormSubmit = (formType: string, success: boolean = true) => {
  trackGAEvent('form_submit', {
    event_category: 'engagement',
    event_label: formType,
    form_type: formType,
    success: success
  });
};

export const trackPostalCodeCheck = (postalCode: string, isValid: boolean) => {
  trackGAEvent('postal_code_check', {
    event_category: 'booking_flow',
    event_label: isValid ? 'valid' : 'invalid',
    postal_code: postalCode,
    is_valid: isValid
  });
};

export const trackDateSelection = (selectedDate: string) => {
  trackGAEvent('date_selected', {
    event_category: 'booking_flow',
    event_label: selectedDate,
    selected_date: selectedDate
  });
};

export const trackTimeSlotSelection = (timeSlot: string, date: string) => {
  trackGAEvent('time_slot_selected', {
    event_category: 'booking_flow',
    event_label: `${date}_${timeSlot}`,
    time_slot: timeSlot,
    appointment_date: date
  });
};

export const trackOTPRequest = (phoneNumber: string) => {
  trackGAEvent('otp_requested', {
    event_category: 'authentication',
    event_label: 'whatsapp_otp',
    phone_country: phoneNumber.startsWith('+91') ? 'IN' : 'other'
  });
};

export const trackOTPVerification = (success: boolean, attempts: number = 1) => {
  trackGAEvent('otp_verified', {
    event_category: 'authentication',
    event_label: success ? 'success' : 'failed',
    success: success,
    attempts: attempts
  });
};

export const trackBookingConfirmed = (bookingData: {
  bookingId: string;
  phoneNumber: string;
  postalCode: string;
  appointmentDate: string;
  appointmentTime: string;
  salesRep: string;
}) => {
  trackGAEvent('purchase', {
    event_category: 'ecommerce',
    transaction_id: bookingData.bookingId,
    value: 0, // Free trial
    currency: 'INR',
    items: [{
      item_id: 'perfume_trial',
      item_name: 'Perfume Trial at Home',
      item_category: 'Beauty',
      item_category2: 'Fragrance',
      quantity: 1,
      price: 0
    }],
    postal_code: bookingData.postalCode,
    appointment_date: bookingData.appointmentDate,
    appointment_time: bookingData.appointmentTime,
    sales_rep: bookingData.salesRep,
    phone_country: bookingData.phoneNumber.startsWith('+91') ? 'IN' : 'other'
  });
};

export const trackBookingCancellation = (bookingId: string, reason?: string) => {
  trackGAEvent('booking_cancelled', {
    event_category: 'booking_management',
    event_label: reason || 'user_cancelled',
    booking_id: bookingId,
    cancellation_reason: reason
  });
};

export const trackBookingReschedule = (bookingId: string, newDate: string, newTime: string) => {
  trackGAEvent('booking_rescheduled', {
    event_category: 'booking_management',
    event_label: 'customer_reschedule',
    booking_id: bookingId,
    new_appointment_date: newDate,
    new_appointment_time: newTime
  });
};

export const trackPageView = (pagePath: string) => {
  trackGAEvent('page_view', {
    page_path: pagePath,
    page_title: document.title
  });
};

export const trackError = (errorType: string, errorMessage: string) => {
  trackGAEvent('exception', {
    event_category: 'error',
    description: errorMessage,
    fatal: false,
    error_type: errorType
  });
};