// UTM Parameter Tracking Utility
// Captures and processes UTM parameters from URL for campaign attribution

export interface UTMParameters {
  utm_source?: string;
  utm_medium?: string;
  utm_campaign?: string;
  utm_term?: string;
  utm_content?: string;
  fbclid?: string;
  gclid?: string;
}

export interface ProcessedCampaignData {
  source: string;
  medium: string;
  campaign: string;
  adsetName?: string;
  adName?: string;
  location?: string;
  audience?: string;
  creativeType?: string;
  promotionType?: string;
  fbclid?: string;
  gclid?: string;
  isDirectTraffic: boolean;
  isQRCode: boolean;
  isMetaAd: boolean;
  isGoogleAd: boolean;
}

// Parse UTM parameters from URL
export const getUTMParameters = (): UTMParameters => {
  const urlParams = new URLSearchParams(window.location.search);
  
  return {
    utm_source: urlParams.get('utm_source') || undefined,
    utm_medium: urlParams.get('utm_medium') || undefined,
    utm_campaign: urlParams.get('utm_campaign') || undefined,
    utm_term: urlParams.get('utm_term') || undefined,
    utm_content: urlParams.get('utm_content') || undefined,
    fbclid: urlParams.get('fbclid') || undefined,
    gclid: urlParams.get('gclid') || undefined,
  };
};

// Parse Meta campaign structure from UTM parameters
// Format: utm_medium=|Kult|Gurugram|Advantage+|Men&Women|Fragrance|
// Format: utm_campaign=Video|Kult|Perfume|Discount|Gurugram|Calendar_Old–
export const parseMetaCampaignData = (utmParams: UTMParameters): ProcessedCampaignData => {
  const source = utmParams.utm_source || 'direct';
  const medium = utmParams.utm_medium || '(none)';
  const campaign = utmParams.utm_campaign || '(none)';
  
  // Default campaign data
  const campaignData: ProcessedCampaignData = {
    source,
    medium,
    campaign,
    fbclid: utmParams.fbclid,
    gclid: utmParams.gclid,
    isDirectTraffic: source === 'direct' && !utmParams.fbclid && !utmParams.gclid,
    isQRCode: source?.toLowerCase() === 'qr',
    isMetaAd: source?.toLowerCase() === 'meta' || !!utmParams.fbclid,
    isGoogleAd: source?.toLowerCase() === 'google' || !!utmParams.gclid,
  };
  
  // Parse Meta adset data from medium (format: |Kult|Gurugram|Advantage+|Men&Women|Fragrance|)
  if (campaignData.isMetaAd && medium.includes('|')) {
    const mediumParts = medium.split('|').filter(part => part.trim());
    if (mediumParts.length >= 3) {
      campaignData.adsetName = medium;
      // Extract meaningful parts
      campaignData.location = mediumParts.find(part => 
        part.toLowerCase().includes('gurugram') || 
        part.toLowerCase().includes('delhi') ||
        part.toLowerCase().includes('mumbai')
      );
      campaignData.audience = mediumParts.find(part => 
        part.toLowerCase().includes('men') || 
        part.toLowerCase().includes('women') ||
        part.toLowerCase().includes('advantage')
      );
    }
  }
  
  // Parse Meta ad data from campaign (format: Video|Kult|Perfume|Discount|Gurugram|Calendar_Old–)
  if (campaignData.isMetaAd && campaign.includes('|')) {
    const campaignParts = campaign.split('|').filter(part => part.trim());
    if (campaignParts.length >= 2) {
      campaignData.adName = campaign;
      // Extract creative type
      campaignData.creativeType = campaignParts.find(part => 
        part.toLowerCase().includes('video') || 
        part.toLowerCase().includes('image') ||
        part.toLowerCase().includes('carousel')
      );
      // Extract promotion type
      campaignData.promotionType = campaignParts.find(part => 
        part.toLowerCase().includes('discount') || 
        part.toLowerCase().includes('trial') ||
        part.toLowerCase().includes('offer')
      );
    }
  }
  
  return campaignData;
};

// Store UTM parameters in sessionStorage for cross-page tracking
export const storeUTMParameters = (utmParams: UTMParameters): void => {
  try {
    sessionStorage.setItem('utm_parameters', JSON.stringify(utmParams));
    sessionStorage.setItem('utm_timestamp', Date.now().toString());
  } catch (error) {
    console.warn('Failed to store UTM parameters:', error);
  }
};

// Retrieve stored UTM parameters
export const getStoredUTMParameters = (): UTMParameters | null => {
  try {
    const stored = sessionStorage.getItem('utm_parameters');
    const timestamp = sessionStorage.getItem('utm_timestamp');
    
    if (!stored || !timestamp) return null;
    
    // Check if parameters are still valid (within 24 hours)
    const age = Date.now() - parseInt(timestamp);
    const maxAge = 24 * 60 * 60 * 1000; // 24 hours
    
    if (age > maxAge) {
      sessionStorage.removeItem('utm_parameters');
      sessionStorage.removeItem('utm_timestamp');
      return null;
    }
    
    return JSON.parse(stored);
  } catch (error) {
    console.warn('Failed to retrieve UTM parameters:', error);
    return null;
  }
};

// Get current or stored UTM parameters
export const getCurrentUTMParameters = (): UTMParameters => {
  // First try to get from current URL
  const currentUTM = getUTMParameters();
  
  // If we have UTM parameters in URL, store them and return
  if (Object.values(currentUTM).some(value => value !== undefined)) {
    storeUTMParameters(currentUTM);
    return currentUTM;
  }
  
  // Otherwise, try to get stored parameters
  const storedUTM = getStoredUTMParameters();
  return storedUTM || {};
};

// Generate campaign attribution string for analytics
export const getCampaignAttribution = (): string => {
  const utmParams = getCurrentUTMParameters();
  const campaignData = parseMetaCampaignData(utmParams);
  
  if (campaignData.isQRCode) {
    return 'QR Code Scan';
  }
  
  if (campaignData.isMetaAd) {
    return `Meta Ad: ${campaignData.campaign}`;
  }
  
  if (campaignData.isGoogleAd) {
    return `Google Ad: ${campaignData.campaign}`;
  }
  
  if (campaignData.source && campaignData.source !== 'direct') {
    return `${campaignData.source} / ${campaignData.medium}`;
  }
  
  return 'Direct Traffic';
};

// Send UTM data to analytics
export const trackUTMAttribution = (trackingFunction: (eventName: string, data: any) => void): void => {
  const utmParams = getCurrentUTMParameters();
  const campaignData = parseMetaCampaignData(utmParams);
  
  trackingFunction('campaign_attribution', {
    utm_source: campaignData.source,
    utm_medium: campaignData.medium,
    utm_campaign: campaignData.campaign,
    utm_term: utmParams.utm_term,
    utm_content: utmParams.utm_content,
    fbclid: campaignData.fbclid,
    gclid: campaignData.gclid,
    attribution_type: campaignData.isQRCode ? 'qr_code' : 
                     campaignData.isMetaAd ? 'meta_ad' :
                     campaignData.isGoogleAd ? 'google_ad' : 'direct',
    adset_name: campaignData.adsetName,
    ad_name: campaignData.adName,
    location: campaignData.location,
    audience: campaignData.audience,
    creative_type: campaignData.creativeType,
    promotion_type: campaignData.promotionType,
  });
};