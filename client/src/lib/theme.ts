interface ThemeConfig {
  backgroundGradientStart: string;
  backgroundGradientEnd: string;
  backgroundGradientDirection: string;
  buttonInactiveColor: string;
  buttonActiveColor: string;
  buttonDisabledColor: string;
  buttonTextColor: string;
  primaryTextColor: string;
  secondaryTextColor: string;
  headingColor: string;
  accentTextColor: string;
  inputBorderColor: string;
  inputFocusColor: string;
  inputBackgroundColor: string;
  primaryBrandColor: string;
  secondaryBrandColor: string;
  successColor: string;
  errorColor: string;
  warningColor: string;
  infoColor: string;
}

export const applyTheme = (themeConfig: Partial<ThemeConfig>) => {
  const root = document.documentElement;
  
  if (themeConfig.backgroundGradientStart) {
    root.style.setProperty('--theme-bg-start', themeConfig.backgroundGradientStart);
  }
  if (themeConfig.backgroundGradientEnd) {
    root.style.setProperty('--theme-bg-end', themeConfig.backgroundGradientEnd);
  }
  if (themeConfig.backgroundGradientDirection) {
    root.style.setProperty('--theme-bg-direction', themeConfig.backgroundGradientDirection);
  }
  if (themeConfig.buttonInactiveColor) {
    root.style.setProperty('--theme-btn-inactive', themeConfig.buttonInactiveColor);
  }
  if (themeConfig.buttonActiveColor) {
    root.style.setProperty('--theme-btn-active', themeConfig.buttonActiveColor);
  }
  if (themeConfig.buttonDisabledColor) {
    root.style.setProperty('--theme-btn-disabled', themeConfig.buttonDisabledColor);
  }
  if (themeConfig.buttonTextColor) {
    root.style.setProperty('--theme-btn-text', themeConfig.buttonTextColor);
  }
  if (themeConfig.primaryTextColor) {
    root.style.setProperty('--theme-primary-text', themeConfig.primaryTextColor);
  }
  if (themeConfig.secondaryTextColor) {
    root.style.setProperty('--theme-secondary-text', themeConfig.secondaryTextColor);
  }
  if (themeConfig.headingColor) {
    root.style.setProperty('--theme-heading', themeConfig.headingColor);
  }
  if (themeConfig.accentTextColor) {
    root.style.setProperty('--theme-accent', themeConfig.accentTextColor);
  }
  if (themeConfig.inputBorderColor) {
    root.style.setProperty('--theme-input-border', themeConfig.inputBorderColor);
  }
  if (themeConfig.inputFocusColor) {
    root.style.setProperty('--theme-input-focus', themeConfig.inputFocusColor);
  }
  if (themeConfig.inputBackgroundColor) {
    root.style.setProperty('--theme-input-bg', themeConfig.inputBackgroundColor);
  }
  if (themeConfig.primaryBrandColor) {
    root.style.setProperty('--theme-brand-primary', themeConfig.primaryBrandColor);
  }
  if (themeConfig.secondaryBrandColor) {
    root.style.setProperty('--theme-brand-secondary', themeConfig.secondaryBrandColor);
  }
  if (themeConfig.successColor) {
    root.style.setProperty('--theme-success', themeConfig.successColor);
  }
  if (themeConfig.errorColor) {
    root.style.setProperty('--theme-error', themeConfig.errorColor);
  }
  if (themeConfig.warningColor) {
    root.style.setProperty('--theme-warning', themeConfig.warningColor);
  }
  if (themeConfig.infoColor) {
    root.style.setProperty('--theme-info', themeConfig.infoColor);
  }
};

export const loadThemeFromSettings = async () => {
  try {
    const response = await fetch('/api/settings');
    if (response.ok) {
      const settings = await response.json();
      applyTheme(settings);
    }
  } catch (error) {
    console.log('Using default theme - settings not available');
  }
};

export default { applyTheme, loadThemeFromSettings };