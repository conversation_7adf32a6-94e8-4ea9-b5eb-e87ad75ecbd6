import { apiRequest } from "./queryClient";

export interface AuthUser {
  username: string;
  isAdmin: boolean;
  isSupport: boolean;
  role: string;
}

export class AuthService {
  async login(username: string, password: string, userType: 'admin' | 'support' = 'admin'): Promise<AuthUser> {
    const endpoint = userType === 'support' ? '/api/support/login' : '/api/admin/login';
    const response = await apiRequest("POST", endpoint, {
      username,
      password,
    });
    
    if (!response.ok) {
      throw new Error("Invalid credentials");
    }
    
    return { 
      username, 
      isAdmin: userType === 'admin', 
      isSupport: userType === 'support',
      role: userType
    };
  }

  async logout(userType: 'admin' | 'support' = 'admin'): Promise<void> {
    const endpoint = userType === 'support' ? '/api/support/logout' : '/api/admin/logout';
    await apiRequest("POST", endpoint, {});
  }

  async getCurrentUser(): Promise<AuthUser | null> {
    try {
      // Use the current-user endpoint to get actual user details
      const currentUserResponse = await fetch("/api/admin/current-user", {
        credentials: "include",
      });
      
      if (currentUserResponse.ok) {
        const userData = await currentUserResponse.json();
        return { 
          username: userData.username,
          isAdmin: userData.role === "admin",
          isSupport: userData.role === "support",
          role: userData.role
        };
      }
      
      // Fallback: Check support authentication
      const supportResponse = await fetch("/api/support/settings", {
        credentials: "include",
      });
      
      if (supportResponse.ok) {
        return { username: "support", isAdmin: false, isSupport: true, role: "support" };
      }
      
      return null;
    } catch {
      return null;
    }
  }
}

export const authService = new AuthService();
