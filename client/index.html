<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1" />
    <meta id="seo-meta" name="robots" content="index, follow" />
    <title>Kult - Perfume Trial at Home Experience</title>
    <meta name="description" content="Try before you buy with <PERSON><PERSON>'s perfume trial service. Explore 150+ luxury perfumes at home." />
    
    <!-- Conditional Tracking Scripts -->
    <script>
      // Only load tracking scripts on user-facing pages
      const path = window.location.pathname;
      const isAdminPage = path.startsWith('/admin') || path.startsWith('/dashboard');
      const isTestPage = path.includes('trigger-facebook') || path.includes('test-');
      const isApiRoute = path.startsWith('/api');
      const isLoginPage = path.includes('login') || path.includes('auth');
      
      const shouldLoadTracking = !isAdminPage && !isTestPage && !isApiRoute && !isLoginPage;
      
      if (shouldLoadTracking) {
        console.log('🎯 Loading tracking scripts on user page:', path);
        
        // Load Google Analytics with UTM tracking
        const measurementId = 'G-VSN3082DCB'; // Updated to use environment variable
        const gtag = document.createElement('script');
        gtag.async = true;
        gtag.src = `https://www.googletagmanager.com/gtag/js?id=${measurementId}`;
        document.head.appendChild(gtag);
        
        gtag.onload = function() {
          window.dataLayer = window.dataLayer || [];
          function gtag(){dataLayer.push(arguments);}
          gtag('js', new Date());
          
          // Enhanced GA4 configuration with UTM tracking
          gtag('config', measurementId, {
            // Enhanced measurement
            enhanced_measurement: true,
            // Capture UTM parameters automatically
            campaign_id: new URLSearchParams(window.location.search).get('utm_campaign'),
            campaign_source: new URLSearchParams(window.location.search).get('utm_source'),
            campaign_medium: new URLSearchParams(window.location.search).get('utm_medium'),
            campaign_term: new URLSearchParams(window.location.search).get('utm_term'),
            campaign_content: new URLSearchParams(window.location.search).get('utm_content'),
            // Custom parameters for attribution
            custom_map: {
              'custom_parameter_1': 'attribution_type',
              'custom_parameter_2': 'adset_name',
              'custom_parameter_3': 'ad_name',
              'custom_parameter_4': 'location',
              'custom_parameter_5': 'fbclid'
            }
          });
          
          // Track initial UTM parameters on page load
          const urlParams = new URLSearchParams(window.location.search);
          const utmData = {
            utm_source: urlParams.get('utm_source'),
            utm_medium: urlParams.get('utm_medium'),
            utm_campaign: urlParams.get('utm_campaign'),
            utm_term: urlParams.get('utm_term'),
            utm_content: urlParams.get('utm_content'),
            fbclid: urlParams.get('fbclid'),
            gclid: urlParams.get('gclid')
          };
          
          // Send UTM attribution event if parameters exist
          if (Object.values(utmData).some(value => value !== null)) {
            gtag('event', 'campaign_attribution', {
              utm_source: utmData.utm_source || 'direct',
              utm_medium: utmData.utm_medium || '(none)',
              utm_campaign: utmData.utm_campaign || '(none)',
              utm_term: utmData.utm_term,
              utm_content: utmData.utm_content,
              fbclid: utmData.fbclid,
              gclid: utmData.gclid,
              attribution_type: utmData.utm_source === 'QR' ? 'qr_code' :
                               utmData.utm_source === 'Meta' || utmData.fbclid ? 'meta_ad' :
                               utmData.gclid ? 'google_ad' : 'direct'
            });
          }
        };
        
        // Load Facebook Pixel (will be controlled by useFacebookTracking hook)
        // This is just the noscript fallback protection - wait for DOM to be ready
        window.addEventListener('DOMContentLoaded', function() {
          const fbPixel = document.createElement('noscript');
          fbPixel.innerHTML = '<img height="1" width="1" style="display:none" src="https://www.facebook.com/tr?id=2478007082599449&ev=PageView&noscript=1" />';
          document.body.appendChild(fbPixel);
        });
      } else {
        console.log('🚫 Tracking scripts blocked on admin page:', path);
      }
    </script>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
    <!-- This is a replit script which adds a banner on the top of the page when opened in development mode outside the replit environment -->
    <script type="text/javascript" src="https://replit.com/public/js/replit-dev-banner.js"></script>
  </body>
</html>