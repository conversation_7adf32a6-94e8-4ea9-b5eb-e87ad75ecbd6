#!/usr/bin/env node

/**
 * Deployment notification trigger script
 * This demonstrates how deployment systems can notify users of upgrades
 * without logging them out.
 */

import http from 'http';

// Configuration
const API_URL = 'http://localhost:5000';
const NOTIFICATION_ENDPOINT = '/api/deployment/notify';

// Admin credentials for authentication
const ADMIN_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'Admin2025!'
};

/**
 * Authenticate and get session cookie
 */
async function authenticate() {
  return new Promise((resolve, reject) => {
    const data = JSON.stringify(ADMIN_CREDENTIALS);
    
    const options = {
      hostname: 'localhost',
      port: 5000,
      path: '/api/auth/login',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': data.length
      }
    };

    const req = http.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        if (res.statusCode === 200) {
          // Extract session cookie
          const cookies = res.headers['set-cookie'];
          const sessionCookie = cookies?.find(cookie => cookie.startsWith('connect.sid='));
          resolve(sessionCookie || '');
        } else {
          reject(new Error(`Authentication failed: ${res.statusCode} ${responseData}`));
        }
      });
    });

    req.on('error', reject);
    req.write(data);
    req.end();
  });
}

/**
 * Send deployment notification
 */
async function sendDeploymentNotification(sessionCookie) {
  return new Promise((resolve, reject) => {
    const notification = {
      type: 'upgrade',
      message: 'New perfume campaign tracking features deployed! Dashboard refreshing...',
      version: '2.1.0'
    };
    
    const data = JSON.stringify(notification);
    
    const options = {
      hostname: 'localhost',
      port: 5000,
      path: NOTIFICATION_ENDPOINT,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': data.length,
        'Cookie': sessionCookie
      }
    };

    const req = http.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        if (res.statusCode === 200) {
          console.log('✅ Deployment notification sent successfully');
          console.log('Response:', responseData);
          resolve(responseData);
        } else {
          reject(new Error(`Notification failed: ${res.statusCode} ${responseData}`));
        }
      });
    });

    req.on('error', reject);
    req.write(data);
    req.end();
  });
}

/**
 * Main execution
 */
async function main() {
  try {
    console.log('🔐 Authenticating...');
    const sessionCookie = await authenticate();
    
    console.log('📡 Sending deployment notification...');
    await sendDeploymentNotification(sessionCookie);
    
    console.log('🚀 Deployment notification complete!');
    console.log('');
    console.log('Users will now see:');
    console.log('- Toast notification about system upgrade');
    console.log('- Automatic dashboard refresh after 3 seconds');
    console.log('- Stay logged in throughout the process');
    
  } catch (error) {
    console.error('❌ Deployment notification failed:', error.message);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = { authenticate, sendDeploymentNotification };