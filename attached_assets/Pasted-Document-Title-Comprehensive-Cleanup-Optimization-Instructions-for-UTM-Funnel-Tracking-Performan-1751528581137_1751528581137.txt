Document Title: Comprehensive Cleanup & Optimization Instructions for UTM Funnel Tracking, Performance Dashboards, and User Journey Analytics

⸻

Objective:
Fix inconsistencies in UTM tracking, long-session user behavior tracking, funnel reporting, and the dashboard overview to ensure we have clean, usable, and logically segmented performance and behavior data. This document outlines the changes needed for both frontend and backend, segmented cleanly for ease of implementation.

⸻

SECTION 1: UTM & Campaign Tracking System Cleanup

1.1 Normalize UTM Source Case Handling

Issue: Inconsistent UTM source casing (e.g., Meta vs meta) causes campaigns to disappear in funnel views.
Fix:
	•	In backend (routes.ts, eventData.utm_source), normalize with .toLowerCase() wherever UTM source is referenced.

const eventSource = (eventData.utm_source || 'direct').toLowerCase();

1.2 Improve Dynamic Meta Campaign Parsing

Current Parsing: Pipe-delimited logic extracts location, audience, and creative type.
To Do:
	•	Make this parsing a shared helper (e.g., parseMetaUTM(utmParams)) to avoid logic duplication.
	•	Log full matched values for QA purposes.

1.3 Track ALL UTM Keys
	•	Capture and store: utm_source, utm_medium, utm_campaign, utm_term, utm_content, fbclid, gclid, referrer.
	•	Store them on session creation (first visit), not just booking.

⸻

SECTION 2: User Session Tracking Normalization

2.1 Session Tracking Start Date

Critical Step: Identify when long-session tracking was enabled.
	•	Check sessionStorage logic implementation date in use-analytics.tsx or backend session logic.
	•	Log this exact datetime.
	•	Use it to split all analytics dashboards into Pre and Post session-tracking-launch views.

2.2 Filter Dirty Data Before Cutoff
	•	In all dashboards (Performance, Funnels, Behavior), add logic:

if (event.timestamp < sessionTrackingStartDate) {
  mark as pre-session era or filter out
}

	•	For now, default view should only show Post session-tracking data to maintain clean analytics.

⸻

SECTION 3: Funnel Dashboard Fixes

3.1 Campaign Funnel Filtering Fixes
	•	Funnel dashboard must group all events by lowercased utm_source, utm_campaign.
	•	Split funnels for each campaign (exact string match, case-insensitive).
	•	Add firstSeen, lastSeen, activeConversionWindow timestamps.

3.2 Real Funnel Stages to Show
	•	Sessions Started
	•	Viewed Homepage
	•	Reached Calendar Page
	•	Filled Address
	•	OTP Entered
	•	Booking Submitted
	•	Booking Confirmed
	•	Trial Happened (optional - if tracked via status)
	•	Order Placed (if separate)

Use event name or page_slug to map funnel stages.

⸻

SECTION 4: Performance Dashboard Fixes

4.1 Top-Level Metrics to Show
	•	Total Sessions
	•	UTM Campaigns (with source filter)
	•	Conversion Rate (Confirmed bookings / Sessions)
	•	Booking Rate (Submissions / Sessions)
	•	Bounce Rate (Session with <2 steps)
	•	Average Steps per User

4.2 Filter by:
	•	Date Range
	•	Campaign
	•	Location (from campaign parsing)
	•	Creative Type (e.g., Video, Image)
	•	Salesperson (from booking assignment)

4.3 Must Fix:
	•	Remove duplicate/undefined campaigns (nulls should be grouped under Direct / Unknown)
	•	Fix incorrect sessions showing undefined UTM
	•	Always show campaign funnels for each unique UTM campaign, dynamically created.

⸻

SECTION 5: User Behavior Dashboard Fixes

5.1 Group by Session ID
	•	For each session, show the complete journey:
	•	Entry URL (with UTM params)
	•	Steps Taken (events/pages)
	•	Duration
	•	Exit Point

5.2 Normalize Date Display
	•	Always show timestamp in IST
	•	Show time since first step (e.g., 5 mins later) for drop-off analysis

5.3 Filter by:
	•	UTM Campaign
	•	Date Range
	•	Device Type (if stored)
	•	Pages Visited (dropdown or search)

⸻

SECTION 6: Admin Recommendations

6.1 Add Toggle for “Clean Data Only”
	•	Let admin view only post-session tracking data (after cutoff date)
	•	Optional: add toggle to view both sets with a warning for pre-data

6.2 Export CSV per Campaign Funnel
	•	Allow 1-click download of session-to-conversion logs for each campaign

⸻

Example: UTM Campaign Funnel Card

Campaign: Video | Kult | Perfume | Discount | Gurugram
Source: Meta
Location: Gurugram
Audience: Men&Women
Creative: Video

Sessions: 189
Calendar Views: 165
Bookings Started: 121
Confirmed: 88 (46.5%)
Avg Steps: 4.7
Bounce Rate: 31.3%


⸻

NOTE FOR DEV:
Please apply type-checking and fallbacks throughout the dashboard. Fallbacks should be consistent:
	•	Empty UTM => Direct
	•	Missing campaign => Unknown Campaign

⸻

END