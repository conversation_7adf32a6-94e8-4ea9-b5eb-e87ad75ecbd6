🔐 Problem: Booking Link Vulnerability Due to Sequential IDs

Currently, the system generates booking links for cancellation and rescheduling using predictable sequential booking IDs (e.g., /cancel/1, /reschedule/2, etc.). This creates a major security vulnerability:
	•	Anyone can increment or decrement the booking ID in the URL and access someone else’s cancel or reschedule page.
	•	There is no user-specific or session-based protection tied to these links.
	•	It could result in unauthorized cancellations or appointment tampering.

⸻

✅ Solution: Use Unique, Non-Sequential Secure Tokens for Links

Step-by-Step Implementation
	1.	Add a New Column in the Bookings Table
	•	Column name: secure_token
	•	Type: VARCHAR(64) (or equivalent in your DB)
	•	It should be unique and securely generated for each booking

ALTER TABLE bookings ADD COLUMN secure_token VARCHAR(64);

	2.	Generate Token When Creating a Booking
	•	Generate a secure, random token (e.g., UUID v4 or crypto random bytes)
	•	Save this token alongside the booking record

import { randomBytes } from 'crypto';

function generateSecureToken(): string {
  return randomBytes(24).toString('hex'); // 48-char secure token
}

// Usage in booking creation flow:
const secureToken = generateSecureToken();
await db.insert('bookings', {
  name, phone, ..., secure_token: secureToken
});

	3.	Update Booking URLs
	•	Use /cancel/:secure_token and /reschedule/:secure_token in all communications
	•	Replace any reference to /cancel/:id or /reschedule/:id

const cancelLink = `https://perfumestrial.com/cancel/${secureToken}`;
const rescheduleLink = `https://perfumestrial.com/reschedule/${secureToken}`;

	4.	Modify Backend Routes
	•	Update your API endpoints to fetch bookings using the token instead of ID

// Instead of finding by ID:
const booking = await db.bookings.findFirst({ where: { id: req.params.id } });

// Do this:
const booking = await db.bookings.findFirst({ where: { secure_token: req.params.token } });

	5.	Restrict Actions if Token is Invalid
	•	If secure_token is not found or expired (optional), reject the request
	•	Return a 404 or an appropriate message to protect against guessing

if (!booking) {
  return res.status(404).send('Invalid or expired link');
}

	6.	Backfill Existing Records
	•	Generate and update tokens for past bookings where missing

const bookings = await db.bookings.findMany();
for (const booking of bookings) {
  if (!booking.secure_token) {
    const token = generateSecureToken();
    await db.bookings.update({
      where: { id: booking.id },
      data: { secure_token: token },
    });
  }
}


⸻

✅ Result
	•	URLs will now look like: https://perfumestrial.com/reschedule/87e9f87cd9fbdcc2f89394b020dfe283b28ab9ef
	•	Impossible to guess or manipulate.
	•	Fully protects users from link tampering.

⸻

Developer Instruction Summary:
	1.	Add secure_token to bookings table.
	2.	Generate secure_token when creating bookings.
	3.	Use secure_token in WhatsApp links and frontend buttons.
	4.	Refactor API logic to find bookings via secure_token.
	5.	Backfill tokens for existing bookings.
	6.	Test with expired or incorrect tokens to verify safety.

This implementation ensures secure, non-guessable booking URLs while keeping UX frictionless.