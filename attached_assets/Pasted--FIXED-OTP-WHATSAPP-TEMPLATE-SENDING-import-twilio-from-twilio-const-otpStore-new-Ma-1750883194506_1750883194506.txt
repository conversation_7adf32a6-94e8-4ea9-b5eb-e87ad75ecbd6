// --- FIXED OTP WHATSAPP TEMPLATE SENDING ---
import twilio from 'twilio';

const otpStore = new Map\<string, { otp: string; expires: number }>();

const accountSid = process.env.TWILIO\_ACCOUNT\_SID;
const authToken = process.env.TWILIO\_AUTH\_TOKEN;
const whatsappNumber = 'whatsapp:+***********';

if (!accountSid || !authToken) {
console.error('Missing Twilio credentials. Please set TWILIO\_ACCOUNT\_SID and TWILIO\_AUTH\_TOKEN');
}

const client = twilio(accountSid, authToken);

const WHATSAPP\_TEMPLATES = {
OTP\_VERIFICATION: {
contentSid: 'HX2ac9a333f19d4ecec2a59b9b7e09c8f8', // Correct template SID (use ContentSid not messagingServiceSid)
language: 'en'
}
};

function generateOTP(): string {
return Math.floor(100000 + Math.random() \* 900000).toString();
}

function formatWhatsAppNumber(phone: string): string {
const cleanPhone = phone.replace(/\D/g, '');
let formattedNumber = '';
if (cleanPhone.startsWith('91') && cleanPhone.length === 12) {
const mobileNumber = cleanPhone.substring(2);
if (/^\[6-9]\d{9}\$/.test(mobileNumber)) {
formattedNumber = `+${cleanPhone}`;
}
} else if (cleanPhone.length === 10 && /^\[6-9]\d{9}\$/.test(cleanPhone)) {
formattedNumber = `+91${cleanPhone}`;
} else {
const last10 = cleanPhone.slice(-10);
if (/^\[6-9]\d{9}\$/.test(last10)) {
formattedNumber = `+91${last10}`;
}
}
if (!formattedNumber) {
throw new Error('Invalid Indian mobile number format');
}
return `whatsapp:${formattedNumber}`;
}

export async function sendWhatsAppOTP(phone: string): Promise<{ success: boolean; message: string; optInRequired?: boolean }> {
try {
if (!accountSid || !authToken) {
return {
success: false,
message: 'WhatsApp service not configured properly'
};
}

```
const otp = generateOTP();
const formattedPhone = formatWhatsAppNumber(phone);
otpStore.set(phone, {
  otp,
  expires: Date.now() + 5 * 60 * 1000
});

console.log(`Sending WhatsApp OTP to ${formattedPhone} using Template SID ${WHATSAPP_TEMPLATES.OTP_VERIFICATION.contentSid}`);

const message = await client.messages.create({
  from: whatsappNumber,
  to: formattedPhone,
  contentSid: WHATSAPP_TEMPLATES.OTP_VERIFICATION.contentSid,
  contentVariables: JSON.stringify({
    '1': otp
  })
});

console.log('WhatsApp OTP sent successfully:', message.sid);
return {
  success: true,
  message: 'OTP sent via WhatsApp successfully'
};
```

} catch (error: any) {
console.error('WhatsApp OTP sending error:', error);

```
if (error.code === 63016 || error.code === 63015) {
  return {
    success: false,
    message: 'User has not opted in. Please tap "Verify OTP" on WhatsApp to complete opt-in.',
    optInRequired: true
  };
}

return {
  success: false,
  message: 'Failed to send WhatsApp OTP'
};
```

}
}

export async function verifyWhatsAppOTP(phone: string, userOtp: string): Promise<{ success: boolean; message: string }> {
try {
const stored = otpStore.get(phone);
if (!stored) {
return { success: false, message: 'OTP not found or expired' };
}
if (Date.now() > stored.expires) {
otpStore.delete(phone);
return { success: false, message: 'OTP has expired' };
}
if (stored.otp !== userOtp) {
return { success: false, message: 'Invalid OTP' };
}
otpStore.delete(phone);
return { success: true, message: 'Phone number verified successfully' };
} catch (error) {
console.error('OTP verification error:', error);
return { success: false, message: 'Failed to verify OTP' };
}
}

export function cleanupExpiredOTPs() {
const now = Date.now();
const entries = Array.from(otpStore.entries());
for (const \[phone, data] of entries) {
if (now > data.expires) {
otpStore.delete(phone);
}
}
}

setInterval(cleanupExpiredOTPs, 5 \* 60 \* 1000);
