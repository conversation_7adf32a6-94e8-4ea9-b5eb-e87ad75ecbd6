**Developer Implementation Instructions**

---

## ✅ FEATURE: Booking Table – Filter, Navigate, and Sort

Target View: Admin > All Bookings (`/admin/bookings`)

---

### 1. 🔍 DATE RANGE FILTER (Today, This Week, This Month, All Time)

**Frontend:**

* Add a dropdown labeled "Date Filter" next to "All Statuses"
* Options: `Today`, `This Week`, `This Month`, `All Time`
* On change, trigger a `GET /bookings?dateFilter=today|week|month|all` API call

**Backend:**

* Endpoint: `GET /bookings?dateFilter=[value]`
* Logic:

```js
const { dateFilter } = req.query;
let startDate, endDate;

if (dateFilter === 'today') {
  startDate = startOfToday();
  endDate = endOfToday();
} else if (dateFilter === 'week') {
  startDate = startOfWeek(new Date());
  endDate = endOfWeek(new Date());
} else if (dateFilter === 'month') {
  startDate = startOfMonth(new Date());
  endDate = endOfMonth(new Date());
}

const bookings = await db.booking.findMany({
  where: {
    ...(startDate && endDate ? {
      date: {
        gte: startDate,
        lte: endDate,
      }
    } : {})
  },
  orderBy: { date: 'asc' }
});
res.json(bookings);
```

---

### 2. ⬅️➡️ DATE NAVIGATION ARROWS (When “Today” Filter is Active)

**Frontend:**

* Show left (`←`) and right (`→`) arrow buttons next to the date label
* Clicking left arrow subtracts 1 day, right arrow adds 1 day
* Request: `GET /bookings?date=YYYY-MM-DD`
* Update booking table UI with the new date's results

**Backend:**

* Endpoint: `GET /bookings?date=YYYY-MM-DD`
* Logic:

```js
const { date } = req.query;
const selectedDate = new Date(date);

const start = startOfDay(selectedDate);
const end = endOfDay(selectedDate);

const bookings = await db.booking.findMany({
  where: {
    date: {
      gte: start,
      lte: end
    }
  },
  orderBy: { date: 'asc' }
});

res.json(bookings);
```

---

### 3. �� SORT BY DATE & TIME (Ascending)

**Frontend:**

* Make the "Date & Time" column header clickable
* On click, toggle `asc` or `desc` state
* Send: `GET /bookings?sort=dateAsc|dateDesc`

**Backend:**

* Endpoint: `GET /bookings?sort=dateAsc|dateDesc`
* Logic:

```js
const { sort } = req.query;
const order = sort === 'dateDesc' ? 'desc' : 'asc';

const bookings = await db.booking.findMany({
  orderBy: {
    date: order
  }
});

res.json(bookings);
```

---

### 4. 📄 EXPORT FILTERED BOOKINGS TO CSV

**Frontend:**

* Trigger API with current filters & sorting:

```
GET /bookings/export?dateFilter=week&sort=dateAsc
```

* Receive `.csv` file as download

**Backend:**

* Reuse same filter logic as above
* Use `json2csv` or similar library to format results and set:

```js
res.setHeader('Content-Type', 'text/csv');
res.setHeader('Content-Disposition', 'attachment; filename="bookings.csv"');
```

---

## 🔐 SECURITY

* All endpoints must be protected by admin auth middleware
* Mask any sensitive customer details (like email) on export if needed

---

Let me know if you also want:

* UX wireframes
* Mobile responsiveness specs
* API schema definitions for each object
