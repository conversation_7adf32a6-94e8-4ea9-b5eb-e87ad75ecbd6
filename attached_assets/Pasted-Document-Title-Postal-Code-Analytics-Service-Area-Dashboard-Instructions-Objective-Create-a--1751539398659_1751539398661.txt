Document Title: Postal Code Analytics & Service Area Dashboard Instructions

⸻

Objective:
Create a dedicated dashboard for postal code analysis to help:
	1.	Track invalid or mistyped PIN codes.
	2.	Identify session-wise behavior around wrong vs corrected PIN code entries.
	3.	Measure drop-offs or successful bookings tied to postal code issues.
	4.	Provide easy control to add valid, unlisted PIN codes to the service area.

⸻

Part 1: Data Capture Requirements

1. Capture every postal code entry event:
	•	On homepage and appointment booking page.
	•	Log each entry in the database with the following fields:
	•	session_id
	•	timestamp
	•	postal_code_entered
	•	is_valid (true/false)
	•	matched_service_area (if applicable)
	•	entry_page (homepage vs form step)

2. On booking form submission:
	•	If the user books after initially entering an invalid postal code:
	•	Log both the invalid and final postal code.
	•	Track if the same session_id eventually submitted a form.

3. Maintain a validated list of serviceable PIN codes in backend (e.g., service_areas table):
	•	Include fields: postal_code, city, state, added_by, added_on, active_status.

⸻

Part 2: Dashboard Design Features

Section A: Invalid Postal Code Sessions Table
	•	Columns:
	•	Session ID
	•	Entered Postal Code
	•	Timestamp
	•	Entry Page (homepage/booking form)
	•	Final Postal Code (if corrected)
	•	Outcome (Dropped / Booked)
	•	Booking Rep (if booked)
	•	Filters:
	•	Date Range
	•	Final Outcome
	•	Valid/Invalid toggle

Section B: Postal Code Funnel Summary
	•	Total invalid entries
	•	% corrected
	•	% drop-off
	•	% that led to bookings

Section C: Add to Service Area Module
	•	Show all invalid but frequently entered PIN codes (min 5 sessions)
	•	Provide admin checkbox to “Add to Service Area”
	•	Auto-fill form with detected postal code and location if available
	•	Save to service_areas table

⸻

Part 3: Backend API & Storage Logic

Add New APIs:
	•	POST /api/analytics/postal-entry
	•	Logs every attempt
	•	GET /api/analytics/postal-dashboard
	•	Serves session-wise table for admin
	•	POST /api/service-areas/add
	•	Add a new postal code to service area list

Database Tables:
	1.	postal_code_attempts
	•	id, session_id, postal_code, timestamp, page, is_valid, final_code, booking_id
	2.	service_areas
	•	id, postal_code, city, state, added_by, added_on, active_status

⸻

Part 4: Debugging & Monitoring Suggestions
	•	Track % of users who drop off after invalid PIN code
	•	Alert when a new PIN code has more than 10 invalid attempts in a day
	•	Allow export of all invalid PIN attempts
	•	Link booking logs with UTM source for attribution

⸻

Conclusion:
This dashboard is critical for both user conversion and territory expansion. It helps identify where leads are lost, which PIN codes need servicing, and gives the team a quick control panel to act on new demand.