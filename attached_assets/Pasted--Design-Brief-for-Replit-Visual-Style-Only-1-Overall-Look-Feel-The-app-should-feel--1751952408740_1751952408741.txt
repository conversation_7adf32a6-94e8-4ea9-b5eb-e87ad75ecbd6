🧾🧠 Design Brief for Replit (Visual Style Only)

⸻

🎨 1. Overall Look & Feel

The app should feel clean, modern, and premium — like a wellness or tech-forward health brand.
Use lots of white space, soft gradients, and minimalist design. It should feel calm, organized, and intentional, not cluttered or flashy.

⸻

🌈 2. Brand Colors

These are our primary and accent shades. Use them consistently:
	•	Primary Lilac: A soft, light lavender — this is our main brand color.
	•	Deep Lilac: A slightly stronger version of the primary — used for active buttons or highlights.
	•	Coral Sorbet: Use as a subtle accent in illustrations or icons.
	•	Mint: For light touches of freshness (e.g., success states, secondary details).
	•	Soft Black: For primary text — not fully black, but deep and elegant.

Use lilac gradients for section backgrounds or cards where we want to highlight a feature or CTA.

⸻

🖋️ 3. Typography

Use a combination of serif and sans-serif fonts:
	•	Headlines: Elegant serif font like Playfair Display — makes the product feel premium.
	•	Body Text: Clean, modern sans-serif like Poppins — easy to read and feels tech-savvy.
	•	Font Sizes: Headings should feel bold and elevated; body text should be medium weight and spacious.

⸻

🧱 4. Buttons
	•	Active State: Rounded corners, filled with deep lilac, white text.
	•	Inactive State: Light lilac (pastel tone), white text, no hover effect.
	•	Disabled State: Light gray background, gray text.
	•	Buttons should feel soft and clickable — not too sharp or harsh.

⸻

🧼 5. Background
	•	Most of the interface should be on a white background
	•	Occasionally, use soft lilac-to-purple gradients for hero sections or highlighted modules
	•	Optionally, use glassmorphism (blurred glass-like cards) in areas like popups or overlays

⸻

📦 6. Components
	•	Cards: White background, slightly rounded, subtle drop shadow or border
	•	Inputs: White background, gray borders, rounded, padding inside
	•	Headings/Subheadings: Use hierarchy with weight and size, not too many colors
	•	Icons/Illustrations: Use soft tones — avoid primary reds/blues unless it’s an alert