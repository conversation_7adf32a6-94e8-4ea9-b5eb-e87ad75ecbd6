**🛠 WhatsApp Template Debug & Fix Instructions**

This document captures the recent WhatsApp message delivery issues and fixes for <PERSON><PERSON>'s Trial at Home experience via Twilio.

---

### ✅ Templates in Use (All Approved)

All the following templates are approved by Meta and visible in Twilio Console:

* `kult_booking_confirmation` – for appointment confirmation
* `kult_booking_reschedule` – for reschedules
* `kult_booking_reminder` – for reminders
* `kult_booking_cancelled` – for cancellations (working successfully)

All templates follow this structure:

* **{1}** = Customer Name
* **{2}** = Appointment Date
* **{3}** = Appointment Time
* **{4}** = Reschedule Link (Button URL)
* **{5}** = Cancel Link (Button URL)

---

### ⚠️ Observed Issue

* Cancellation messages are **sending successfully**.
* Confirmation, Reschedule, and Reminder messages are **failing** with Twilio error `63005`.
* This error usually indicates a mismatch in template formatting or variable handling at runtime.

---

### 🔍 Likely Root Cause

* **Buttons {4} and {5}** are dynamic URLs passed into template **buttons**, not in the message body.
* If these URLs are malformed, empty, or improperly mapped in the payload, <PERSON><PERSON> rejects the message.
* Template body variables `{1}`, `{2}`, `{3}` are working fine.

---

### ✅ What to Check & Fix

1. **In Twilio Template Usage Call** (for `client.messages.create`):

   * Ensure both `{4}` and `{5}` (button URLs) are passed correctly as part of `contentVariables`.
   * Example:

     ```json
     contentVariables: JSON.stringify({
       "1": "Devesh Singh",
       "2": "30 June, 2025",
       "3": "3:00 PM",
       "4": "https://perfumetrials.com/reschedule/123",
       "5": "https://perfumetrials.com/cancel/123"
     })
     ```

2. **Do NOT use hardcoded button URLs in the template preview** in Meta. Keep buttons dynamic by mapping `{4}` and `{5}` to correct user-specific URLs via API.

3. **Sanitize Links**

   * Ensure links passed do **not contain special characters**, `#`, query parameters without encoding, or trailing whitespace.

4. **Log Complete Payloads**

   * Add debug logs in Replit for each failed message including:

     ```ts
     console.log('Sending WhatsApp Template:', templateSid);
     console.log('Variables:', contentVariables);
     ```

5. **Re-Test with Static Valid URLs**

   * Temporarily try replacing `{4}` and `{5}` with working placeholder URLs (e.g., `https://kult.app/r` and `https://kult.app/c`) to validate if the issue is only link-specific.

6. **Use Twilio Content Template Debugger**

   * Navigate to `Messaging → Content Template Builder → Test → Fill variables`
   * Paste in `{1}`, `{2}`, `{3}`, `{4}`, `{5}` manually and run the test.

7. **If Error Persists:**

   * Contact Twilio Support and raise a request referencing the Message SID and template ID for error `63005`, confirming that:

     * Template is approved
     * Button URLs are dynamically injected
     * No variables are missing

---

### 🧪 Working Template Example (Cancel)

```json
Template: kult_booking_cancelled
SID: HX300db6bbc12129393900f43c018ec94b
{1} = "Devesh Singh"
{2} = "30 June, 2025"
{3} = "3:00 PM"
{4} = "https://perfumetrials.com/reschedule/123"
{5} = "https://perfumetrials.com/cancel/123"
```

This confirms the structure and dynamic link usage works as long as content is clean and mapped properly.

---

### ✅ Action Items for Devs

* [ ] Validate that `{4}` and `{5}` are **non-empty** URLs in runtime payload
* [ ] Test templates in Twilio with mock payloads
* [ ] Replace problematic variables temporarily with static links for validation
* [ ] Add logging for every `.create()` call to diagnose failures
* [ ] Ensure `.templateSid` and `contentVariables` are not mismatched
* [ ] Do NOT send any `.body` text. Use only `.template`

Once links are debugged, all templates should work without needing re-approval.

---

Let me know if you’d like a Figma preview or template test payloads prepared.
