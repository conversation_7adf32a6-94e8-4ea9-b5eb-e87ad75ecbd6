Booking Dashboard Spec

⸻

🔧 Known Issue: Sorting by Appointment Date & Time Not Working

🟣 Description

Currently, on the “All Bookings” admin dashboard, the Date & Time column does not sort correctly when clicked, even though the UI indicates sort toggle is present (↑↓ icon).

⸻

🧠 Logical Explanation for Developer

✅ What’s Working:
	•	Filtering by date using the Date Filter and exact day picker (e.g., Jul 1, 2025) correctly narrows down the list.
	•	Sorting/filtering on other columns (e.g., status or rep assigned) works fine.
	•	Displaying all fields, phone numbers, PIN codes, and status chips is working correctly.

❌ What’s Not Working:
	•	Clicking the column header on Date & Time does not reorder the entries in ascending or descending order.
	•	Even if appointments are all for Jul 1, 2025, they are not ordered chronologically (e.g., 10:00 AM, 11:00 AM, 2:00 PM, 3:00 PM, 4:00 PM are randomly placed).

✅ What Needs to Happen:
	•	Enable client-side or server-side sorting logic on the Date & Time column.
	•	Ensure the sort function uses a properly parsed datetime object combining both date and time fields.
	•	Support both ascending and descending order toggles on click.

⸻

🧩 Developer Implementation Notes
	1.	Data Format to Parse
	•	Combine both fields:
	•	Date: Jul 1, 2025
	•	Time: 11:00 AM
	•	Convert into a standard datetime string: 2025-07-01T11:00:00
	•	Sort by this datetime in either ascending or descending order.
	2.	Tech Stack Suggestion
	•	If using a JS frontend: use new Date(datetimeString) and apply .sort() accordingly.
	•	If sorting server-side (PostgreSQL), sort by:

ORDER BY appointment_datetime ASC|DESC


	3.	Validation
	•	Click once = ascending
	•	Click twice = descending
	•	Third click = clear sort (optional)
	4.	Test Cases
	•	Bookings on same date, different times.
	•	Bookings across dates (e.g., Jun 30, Jul 1, Jul 6).
	•	Mixed statuses (confirmed/cancelled) should not affect sort order.

⸻

📌 Summary for Dev Team
	•	Fix the sort function on Date & Time so bookings appear in correct chronological order.
	•	Ensure toggle behavior works with visual feedback.
	•	Validate via test bookings with known date-time values.

Let us know if you need JSON structure or database schema access!