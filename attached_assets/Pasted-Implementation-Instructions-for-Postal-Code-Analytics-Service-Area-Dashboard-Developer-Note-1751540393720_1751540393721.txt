Implementation Instructions for Postal Code Analytics & Service Area Dashboard

⸻

🔍 Developer Notes

Please do not treat this as a fresh dashboard from scratch. We already had a working database for tracking incorrect and corrected postal codes submitted by users. This includes:
	•	Users entering a wrong or unsupported postal code
	•	User correcting it later and successfully booking
	•	Timestamps, user session ID, and context of entry (e.g. landing page or booking step)
	•	Final successful code if any, associated with a booking ID

You must locate this already existing database or dataset, and:
	1.	Integrate the dashboard logic with this historical data.
	2.	Ensure any new postal code events also populate into this same dataset — do not maintain two sources.

⸻

✅ Features to Implement

1. Postal Code Analytics Dashboard (Admin Panel)
	•	Add new tab in Admin panel: Postal Analytics
	•	Use existing component <PostalCodeAnalyticsDashboard />
	•	Must show complete historical data from our existing logs
	•	Use proper filters for:
	•	Date range
	•	Valid / Invalid entries
	•	Session vs Booking outcomes

2. Dashboard View

Each row of invalid postal code data must show:
	•	Entered postal code
	•	Whether it matched a known area
	•	Timestamp of entry
	•	Page where entered (e.g., form, lead capture, popup)
	•	Was the session converted later with a valid code?
	•	Final code (if corrected)
	•	Booking ID (if completed)

3. Add-to-Service Area Action

Next to every unique invalid postal code:
	•	Show a button “Add to Service Area”
	•	This should open a pre-filled form (like: postal code, city, state)
	•	On submission, add this to service_areas table

4. Grouping & Sorting
	•	Group rows by postal code (count number of users who entered it)
	•	Sort by most entered invalid codes
	•	Highlight postal codes with high drop-off (entered but never converted)

5. Missing Data Fix

Currently the dashboard is showing empty.
Action:
	•	Debug frontend query — it’s likely pointing to an empty or new table.
	•	Point it to the old log table where we already store postal codes.

6. Future Logging Logic (If Needed)

If old logs aren’t usable directly:
	•	Create a migration script to move past entries into new structure.
	•	Implement backend logging middleware that captures postal code entry, status, match, and booking linkage.
	•	Log IP address and user-agent to identify unique users if session ID is missing.

⸻

🎯 Developer Task Summary
	•	Locate and connect to the existing postal code log data
	•	Refactor dashboard to load and visualize historical + new entries
	•	Add real-time add-to-service area CTA per code
	•	Show full funnel from postal entry → final code → booking
	•	Build filters, sorting, and session insights

⸻

If anything seems unclear, confirm with the product team before building fresh schema or tables.