📌 Objective: Fix OTP Delivery via WhatsApp using <PERSON>wi<PERSON> (Meta Template Error 63016)

⸻

🎯 Purpose

This document outlines the issue and step-by-step resolution for enabling OTP-based phone verification via WhatsApp using a Meta-approved authentication template through <PERSON>wilio.

The OTP template (whatsapp_optin) is already approved, but messages are failing with error 63016, indicating that Met<PERSON> thinks the user hasn’t opted in.

However, since this is an Authentication category template, opt-in should not be required per Meta policy. Therefore, the error is most likely due to template misconfiguration.

⸻

⚠️ Problem Summary

Issue	Explanation
WhatsApp message not sent	Message failed with Twilio error 63016
Error meaning	User has not opted in to receive WhatsApp business messages
Expected behavior	OTP templates do not require opt-in as per Meta’s official policy
Suspected cause	Template was created as Authentication but possibly misconfigured


⸻

✅ Correct Meta Policy

According to Meta’s documentation, Authentication templates are exempt from opt-in requirements, including OTP flows.

⸻

🛠️ Required Fix: Verify Template Configuration in Twilio Console

1. Recheck the Template Type
	•	Navigate to Twilio Console ➔ Messaging ➔ Content Templates
	•	Open the template: whatsapp_optin
	•	Verify that:
	•	Content type is set to Authentication (✅ Confirmed)
	•	Template SID: HX2ac9a33f19d4ecec2a59b9b7e09c8f8 (✅ Correct)

2. Button Configuration (Fix Required)
	•	Current setting is:
	•	Action: Copy Code
	•	Button Text: Verify OTP
	•	✅ Fix: For OTP templates, remove any CTA button or change button type to none

CTA buttons on OTPs are known to break authentication use-case routing and trigger opt-in enforcement mistakenly.

3. Send from Meta-Approved Number
	•	Ensure you’re sending via the exact WhatsApp Business number tied to Meta WABA.
	•	Confirm +*********** is same number shown in Meta WhatsApp Manager.

4. Avoid Using Templates in Sandbox Mode
	•	Meta often blocks authentication flows in sandbox/test mode.
	•	Test with live number and real phone.

5. Retry With New Authentication Template (If Issues Persist)
	•	Create a new template named otp_auth_test with:
	•	No buttons
	•	Simple body text: “{1} is your Kult verification code.”
	•	Category: Authentication
	•	Submit for approval and retest using that template SID.

⸻

✅ Summary for Developer
	•	The issue is not due to user opt-in, but due to button/format misconfiguration in OTP template.
	•	Meta does not require opt-in for authentication templates.
	•	Action Required:
	•	Either remove button from whatsapp_optin, or
	•	Create new clean authentication template without button and use that for OTP

⸻

Once fixed, OTP messages will go through without opt-in, and all future bookings can be confirmed using WhatsApp verification reliably.

For further issues, reference Meta error code 63016 docs or raise a ticket with Meta support directly through the WhatsApp Manager account.