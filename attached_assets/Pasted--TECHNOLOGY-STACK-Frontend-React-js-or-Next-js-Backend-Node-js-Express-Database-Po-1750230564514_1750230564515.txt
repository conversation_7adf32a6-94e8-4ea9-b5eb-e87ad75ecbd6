🧰 TECHNOLOGY STACK
	•	Frontend: React.js (or Next.js)
	•	Backend: Node.js + Express
	•	Database: PostgreSQL (via Prisma ORM)
	•	Styling: TailwindCSS
	•	Deployment: Replit
	•	Email/SMS: Placeholder for Send<PERSON><PERSON> & Twilio
	•	Fonts: <PERSON> (headers) + <PERSON><PERSON> (body text)
	•	Colors:
	•	Primary Lilac: #D4B9FC
	•	Deep Lilac: #AD8FF7
	•	Soft Black: #333132
	•	Coral Sorbet: #EFC1B7
	•	Mint: #BDE7DC

⸻

🌸 FRONTEND: CUSTOMER FLOW

1. Landing Page
	•	Shows Kult logo and tagline
	•	CTA: “Book Your Perfume Trial”
	•	Mobile-friendly with playful micro-interactions

2. Postal Code Entry
	•	User types 6-digit pin
	•	If allowed → proceed
	•	If not allowed → show message: “We’re coming to your area soon 💜”

3. Date & Time Selection
	•	Fetch availability dynamically from backend
	•	Only show slots where both rep and kit are available

4. Customer Info Form
	•	Name (required)
	•	Phone (required)
	•	Email (optional)
	•	Address (required)
	•	Validate all inputs

5. Confirmation Screen
	•	Show booking details
	•	Trigger SMS + Email
	•	Thank you message: “Your Perfume Trial is booked 🎉 See you soon!”

⸻

🧠 BACKEND: BOOKING & LOGIC

Core Logic:
	•	One fixed service: Perfume Trial at Home
	•	All bookings are at the customer’s address
	•	Limit 20 perfume kits (i.e. max 20 active slots at a time)
	•	20–30 salespeople, each with custom working days and shift timings
	•	Each appointment is assigned to a salesperson round-robin, from the pool of reps available at the chosen time
	•	Appointments are only confirmed if:
	•	Postal code is in the valid list
	•	A rep is available
	•	A kit is available

⸻

🔒 ADMIN DASHBOARD

Protected via login.
Features include:
	1.	View All Bookings
	•	Search, filter by date, rep, or customer
	•	Show name, time, address, rep assigned
	2.	Calendar View
	•	Visual day/week/month layout with appointments per rep
	3.	Manage Sales Reps
	•	Add/edit/delete reps
	•	Define working days and shift timing
	4.	Edit Notification Templates
	•	For email and SMS confirmations
	•	Use variables like {{name}}, {{date}}, {{time}}
	5.	Manage Postal Codes
	•	Upload/modify allowed pincode list
	6.	Toggle Reminders
	•	Enable/disable auto-reminders (e.g. 2 hours before)
	7.	Brand Config
	•	Upload logo
	•	Update color hex codes and fonts
	•	Apply changes across frontend
