Booking Dashboard Spec

🔒 Feature: Block Time for Sales Reps (with Date, Time Range & Calendar Selection)

🎯 Objective

Allow admin to block specific time ranges for one or more sales reps (e.g., for leaves, meetings, unavailability) so that no customer appointments can be booked during those blocked slots.

⸻

🔧 UI Flow (Backend Admin Dashboard)
	1.	Click on “Block Time” Button
	•	This opens a modal or a full-screen form titled “Block Calendar Time”.
	2.	Step 1: Select Sales Reps
	•	Multi-select dropdown with checkboxes.
	•	Display list of all reps with their calendar names/IDs.
	•	Option to “Select All”.
	3.	Step 2: Select Date
	•	Simple date picker to choose a specific date (future or today).
	4.	Step 3: Select Time Range
	•	Two dropdowns or time pickers:
	•	From Time (e.g., 2:00 PM)
	•	To Time (e.g., 5:00 PM)
	•	Validate: To Time must be after From Time.
	5.	Step 4 (Optional): Add Note/Reason
	•	Input text field for internal reference (e.g., “Sales meeting” / “Personal leave”).
	6.	Step 5: Block Time
	•	A “Block Time” button at the bottom.
	•	On click:
	•	Time range is marked unavailable for selected reps.
	•	Any customer trying to book during this time will see it greyed out or blocked on the frontend.

⸻

🔍 Example Use Case

Admin wants to block 3:00 PM to 5:00 PM on July 3, 2025, for reps <PERSON><PERSON> and <PERSON><PERSON><PERSON> because of a training session.
They select both reps, choose the date, time range, add the note “Internal Training” and click Block Time.

This time is now unavailable for bookings for both reps on that day.

⸻

🔁 Developer Notes
	•	Blocked slots should reflect instantly in both:
	•	Customer booking interface (availability removed).
	•	Admin calendar views (marked as “Blocked” or greyed out).
	•	Store blocked times as a new table in DB (e.g., blocked_times) with fields:

id | rep_id | date | start_time | end_time | reason | created_by


	•	If needed later, enable edit/delete for blocked time entries.

⸻

📅 Calendar View Enhancements (Like Acuity)
	1.	Daily View per Sales Executive
	•	Calendar should show one full day per view, with all sales executives listed horizontally.
	•	Each rep gets a separate column with their name.
	2.	Appointment Duration
	•	Show actual duration blocks for each customer appointment per sales rep.
	•	Include basic booking info (e.g., customer name + time).
	3.	Visual Gap Display
	•	Time gaps between appointments should appear greyed out or marked as unavailable.
	•	Blocked times should also show visually (e.g., shaded or red).
	4.	Individual Rep Calendar Blocking
	•	Admin should be able to block specific rep calendars from the calendar view directly.

⸻

📊 UTM Funnel Tracking per Campaign
	1.	For each UTM Campaign (from utm_campaign or parsed Meta links):
	•	Create a dedicated conversion funnel view in the admin dashboard.
	•	Each funnel shows stages like:
	•	Landing page visit
	•	Postal code entry
	•	Date selection
	•	Time selection
	•	Address submission
	•	Booking confirmation
	2.	Funnel Metrics to Show:
	•	Total sessions
	•	Drop-offs at each stage
	•	Conversion rate (overall and stage-wise)
	•	Avg. time spent between stages
	3.	Funnel Filtering Options
	•	Filter by:
	•	utm_source, utm_medium, utm_campaign
	•	Parsed fields: location, audience, promotionType, creativeType
	4.	Auto-add New Campaigns
	•	As new UTM links are detected (either fixed or dynamic), automatically create a funnel view for them.
	5.	Dashboard Layout Suggestion:
	•	Section: “UTM Campaign Funnels”
	•	Table or dropdown list of campaigns
	•	Clicking a campaign opens full funnel chart + metrics

This enables real-time, per-campaign performance tracking across the full booking flow.