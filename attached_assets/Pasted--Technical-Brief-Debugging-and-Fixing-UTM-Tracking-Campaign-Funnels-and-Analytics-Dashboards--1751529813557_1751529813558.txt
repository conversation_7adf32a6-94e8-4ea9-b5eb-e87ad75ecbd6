🔍 Technical Brief: Debugging and Fixing UTM Tracking, Campaign Funnels, and Analytics Dashboards

⸻

🧭 CONTEXT & PROBLEM STATEMENT

We have recently implemented UTM tracking, user session capture, campaign funnel logging, and user behavior dashboards. However, there are several inconsistencies, inaccuracies, and usability gaps across performance and behavior dashboards. We need to:
	1.	Fix date and event-level inconsistencies in data rendering.
	2.	Separate traffic and funnel tracking pre-UTM tracking vs post-UTM tracking (July 1, 2025 onward).
	3.	Render accurate, clean funnel and campaign views per UTM source.
	4.	Sort out messy and duplicate traffic source naming (e.g., Facebook, Meta, meta).
	5.	Track campaign-to-booking journeys reliably across sessions.

⸻

📅 IMPORTANT DATES
	•	Full UTM tracking with sessionStorage + funnel logging began: June 26, 2025
	•	Dynamic Meta campaign links with parsed pipe values deployed: June 28, 2025
	•	Accurate session-to-booking attribution (with funnel logging): July 1, 2025

⸻

🔧 THINGS TO FIX

1. 🚧 Traffic Source Inconsistency
	•	Traffic is shown as “Meta”, “meta”, “Facebook”, sometimes even split separately or showing 0.02%.
	•	Direct traffic and unknown traffic show inflated or inconsistent numbers.

Fix Instructions:
	•	Create a canonical normalization layer where all utm_source values like Meta, facebook, meta, FB, etc. are unified as Meta.
	•	Apply this normalization in both:
	•	UTM capture layer (utm-tracking.ts) AND
	•	Analytics dashboard aggregators (routes.ts, dashboard queries)

2. 📊 Booking Funnel Tracking Broken
	•	Funnel page is missing for Meta campaigns even though bookings exist.
	•	Booking funnel shows 0 bookings in user behavior dashboard, while performance dashboard shows 183 bookings.

Fix Instructions:
	•	Make sure all funnel logs and final booking submissions are tied to sessionId + utm data + timestamp
	•	Fix capitalization bug in routes.ts:

const eventSource = eventData.utm_source?.toLowerCase() || 'direct';

Must match the actual source tag used in stored data (normalize them all!)

	•	All conversion events should be matched to utm_campaign, utm_medium, utm_source if sessionStorage exists.

3. 📅 Separate Data Pre/Post UTM Launch
	•	We started full tracking from July 1, 2025.
	•	Earlier sessions before this were mostly untracked (marked as unknown).

Fix Instructions:
	•	Create toggle filters or at least a cut-off filter to separate:
	•	Pre-July 1 traffic (limited tracking)
	•	Post-July 1 sessions (clean, reliable tracking)
	•	In dashboard query logic, allow filters like:

WHERE session.timestamp >= '2025-07-01T00:00:00Z'



4. 📈 Campaign Funnel Visualization Broken or Missing
	•	For dynamic UTM campaign links (e.g., utm_campaign=Video|Kult|Perfume|Gurugram|Trial) — campaigns are parsed but funnel data is not shown

Fix Instructions:
	•	Fix funnel query grouping logic:
	•	Strip or format long pipe-separated values
	•	Group by campaign + creative type (Video/Image/Carousel) as one layer
	•	Funnel stages:
	•	Sessions (first hit)
	•	OTP started
	•	Booking info filled
	•	Booking created
	•	Booking confirmed

⸻

✅ WHAT GOOD LOOKS LIKE
	1.	Top Campaign Table
	•	Meta | Kult | Gurugram | Trial | Video → 420 sessions, 32 bookings, 7.6% CR
	•	App | Banner → 112 sessions, 4 bookings, 3.6% CR
	2.	Conversion Funnel (Per Campaign)





Meta | Trial Video
	•	Sessions: 420
	•	OTP sent: 280
	•	Booking started: 174
	•	Booking confirmed: 32

3. **Clean Source Labels**
   - Meta: 2,000
   - App: 1,100
   - Direct: 1,800
   - Unknown: 200 (pre-July 1 only)

4. **Total Conversion Rate**
   - Display as `Bookings / Sessions` only for `post-July 1` sessions

5. **Source Attribution Timeline (Session-Based)**

	•	First seen: June 28, 2025
	•	Last seen: July 3, 2025
	•	Active: Yes

---

### 📥 EXTRA INSTRUCTIONS TO THE DEVELOPER

- Log exact timestamp when each of these data layers were deployed in codebase and use them to cut dashboard data accordingly.
- Add debug logs or console outputs on funnel load if any campaigns are being excluded or filtered out.
- Add fallback handling if sessionStorage fails in mobile Safari (e.g., use cookies as fallback)
- Link funnel stages visually across booking flow events

---

### 🧠 FINAL QUESTIONS FOR DEBUGGING
- Are we matching sessions with bookings using session ID + UTM values + phone number/email?
- Are sessions with `utm_source=meta` always being stored before booking?
- Are we sure all `Meta` campaigns are using UTM links in ads?
- Can we isolate session-based journeys by campaign and sort them by dropout %?

---

**This doc can be handed to any dev or AI agent to investigate the full flow and fix analytics issues from root to dashboard presentation.**